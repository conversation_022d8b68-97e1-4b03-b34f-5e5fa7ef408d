# Documentação - Importação CEST (Portaria 142/2018)

## Visão Geral

Este documento descreve a implementação modernizada do método `import_cest()` no controller `<PERSON>ron` para atualização das tabelas `cest` e `cest_ncm` com base na Portaria 142/2018.

## Principais Melhorias Implementadas

### 1. **Logs Detalhados**
- Logs em tempo real durante toda a execução
- Relatório final com estatísticas completas
- Log persistente em arquivo (`application/logs/log-import-cest.txt`)
- Indicadores de progresso a cada 100 registros processados

### 2. **Validações Robustas**
- Verificação de existência e legibilidade do arquivo CSV
- Validação da estrutura de cada linha do CSV
- Verificação de dados obrigatórios (CEST, NCM, Descrição)
- Tratamento de erros com continuidade do processo

### 3. **Transações de Banco**
- Uso de transações para garantir consistência dos dados
- Rollback automático em caso de erro crítico
- Limpeza completa das tabelas antes da importação (conforme especificação)

### 4. **Tratamento de Erros**
- Try/catch para captura de exceções
- Logs específicos para cada tipo de erro
- Contadores de erros para relatório final
- Fechamento seguro de recursos (arquivo CSV)

### 5. **Configurações de Performance**
- `set_time_limit(0)` para execuções longas
- Desabilitação de debug e queries log para economia de memória
- Flush de output para feedback em tempo real

## Estrutura do Arquivo CSV

O arquivo `CEST_142_2018.csv` deve ter a seguinte estrutura:

```
CEST;NCM_SH;Descricao_CEST
01.001.00;3815.12.10;Catalisadores em colmeia cerâmica...
01.001.00;3815.12.90;Catalisadores em colmeia cerâmica...
```

### Campos:
- **CEST**: Código CEST no formato XX.XXX.XX (pontos são removidos na importação)
- **NCM_SH**: Código NCM (formatado removendo caracteres não numéricos)
- **Descricao_CEST**: Descrição do código CEST

## Como Executar

### Via Linha de Comando (Recomendado)
```bash
cd /caminho/para/projeto
php index.php cron import_cest
```

### Pré-requisitos
1. Arquivo `assets/cest/CEST_142_2018.csv` deve existir e ser legível
2. Permissões de escrita na pasta `application/logs/`
3. Acesso ao banco de dados configurado

## Processo de Importação

### 1. **Validações Iniciais**
- Verifica existência do arquivo CSV
- Valida permissões de leitura
- Exibe informações do arquivo (tamanho, localização)

### 2. **Limpeza das Tabelas**
```sql
TRUNCATE TABLE cest_ncm;
TRUNCATE TABLE cest;
```

### 3. **Processamento dos Dados**
Para cada linha do CSV:
- Valida estrutura (3 campos obrigatórios)
- Formata códigos CEST (remove pontos) e NCM (remove caracteres não numéricos)
- Insere/atualiza na tabela `cest`
- Insere na tabela `cest_ncm` (apenas se houver NCM)

### 4. **Relatório Final**
Exibe estatísticas completas:
- Total de linhas processadas
- Registros CEST inseridos/atualizados
- Registros CEST_NCM inseridos
- Quantidade de erros encontrados

## Logs Gerados

### Console (Tempo Real)
```
[2025-09-09 16:17:58] === INÍCIO DA IMPORTAÇÃO CEST ===
[2025-09-09 16:17:58] Arquivo encontrado: /path/to/CEST_142_2018.csv
[2025-09-09 16:17:58] Tamanho do arquivo: 158,575 bytes
[2025-09-09 16:17:58] Processadas 100 linhas...
[2025-09-09 16:17:58] Processadas 200 linhas...
...
[2025-09-09 16:17:58] === RELATÓRIO FINAL ===
[2025-09-09 16:17:58] Total de linhas no arquivo: 1365
[2025-09-09 16:17:58] Linhas processadas: 1362
[2025-09-09 16:17:58] Registros CEST inseridos: 450
[2025-09-09 16:17:58] Registros CEST atualizados: 0
[2025-09-09 16:17:58] Registros CEST_NCM inseridos: 1362
[2025-09-09 16:17:58] Erros encontrados: 2
```

### Arquivo de Log (`application/logs/log-import-cest.txt`)
```
--------------------------------------------------------------
[2025-09-09 16:17:58] - Importação CEST (Portaria 142/2018)
 - Total de linhas: 1365
 - Linhas processadas: 1362
 - CEST inseridos: 450
 - CEST atualizados: 0
 - CEST_NCM inseridos: 1362
 - Erros: 2
--------------------------------------------------------------
```

## Tratamento de Erros

### Tipos de Erros Tratados:
1. **Arquivo não encontrado**: Para execução com erro
2. **Arquivo não legível**: Para execução com erro
3. **Erro ao abrir arquivo**: Para execução com erro
4. **Linha com estrutura inválida**: Pula linha e continua
5. **Dados obrigatórios vazios**: Pula linha e continua
6. **NCM inválido após formatação**: Pula linha e continua
7. **Erro na transação do banco**: Rollback e para execução

### Recuperação de Erros:
- Erros de linha individual não param a execução
- Transação é revertida apenas em erros críticos
- Arquivo é fechado automaticamente em caso de erro

## Teste da Implementação

Um script de teste foi criado (`test_import_cest.php`) para validar:
- Existência e legibilidade do arquivo
- Estrutura do CSV
- Formatação dos dados

Execute o teste antes da importação:
```bash
php test_import_cest.php
```

## Considerações Importantes

1. **Backup**: Sempre faça backup das tabelas antes da importação
2. **Ambiente**: Teste primeiro em ambiente de desenvolvimento
3. **Monitoramento**: Acompanhe os logs durante a execução
4. **Performance**: A importação pode demorar alguns minutos dependendo do volume de dados
5. **Encoding**: O arquivo CSV deve estar em UTF-8 ou encoding compatível

## Estrutura das Tabelas

### Tabela `cest`
```sql
cod_cest VARCHAR(7) PRIMARY KEY  -- Formato: 0100100 (sem pontos)
descricao TEXT
```

### Tabela `cest_ncm`
```sql
cod_cest VARCHAR(7)  -- Formato: 0100100 (sem pontos)
cod_ncm VARCHAR(8)   -- Formato: 38151210 (apenas números)
PRIMARY KEY (cod_cest, cod_ncm)
```

## Suporte e Manutenção

Para problemas ou dúvidas:
1. Verifique os logs de erro
2. Execute o script de teste
3. Valide a estrutura do arquivo CSV
4. Confirme permissões de arquivo e banco de dados

---

**Última atualização**: 09/09/2025  
**Versão**: 1.0  
**Autor**: Sistema de Gestão Tarifária
