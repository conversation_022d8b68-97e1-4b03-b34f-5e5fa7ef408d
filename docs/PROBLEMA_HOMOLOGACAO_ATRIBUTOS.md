# Problema: "Não é possível homologar" após salvar atributos

## Descrição do Problema

Quando um usuário faz edições em atributos e tenta homologar diretamente (sem salvar primeiro), o sistema:

1. Detecta que há edições não salvas (`hasBeenEditedAttr = true`)
2. Mostra um SweetAlert perguntando se deseja salvar
3. Usuário clica "Sim" 
4. Sistema salva os atributos com sucesso
5. Mostra modal de sucesso
6. **PROBLEMA**: Abre o modal de homologação com a mensagem "Não é possível homologar"

## Causa Raiz

O problema é um **race condition** entre o salvamento dos atributos e a validação para homologação:

1. O método `save()` salva os atributos no banco
2. Imediatamente após, chama `openModalHomologSelecteds()` recursivamente
3. O modal chama `loadItems()` que faz requisição para `ajax_get_lista_itens_status`
4. O endpoint chama `validar_respostas_attr()` que verifica se os atributos obrigatórios estão preenchidos
5. **Devido ao timing**, a validação pode ainda ver os dados antigos (não salvos) no banco
6. Resultado: item é considerado inválido para homologação

## Arquivos Afetados

- `assets/vuejs/src/components/WfAtributos/WfTabelaAtributos.vue`
- `assets/vuejs/src/components/WfAtributos/components/ModalHomologSelecteds.vue`
- `application/controllers/wf/atributos.php`
- `application/models/cad_item_wf_atributo_model.php`

## Soluções Implementadas

### 1. Delay na Chamada Recursiva (Frontend)

**Arquivo**: `WfTabelaAtributos.vue`

Adicionado um delay de 500ms entre o salvamento e a chamada recursiva para garantir que os dados sejam persistidos no banco:

```javascript
.then(() => {
  // Aguardar um tempo para garantir que os dados foram persistidos no banco
  return new Promise(resolve => setTimeout(resolve, 500));
})
.then(() => {
  // Chamar novamente a função como recursividade
  this.openModalHomologSelecteds(modalActions);
})
```

### 2. Retry Logic no Backend

**Arquivo**: `application/controllers/wf/atributos.php`

Implementado retry automático no endpoint `ajax_get_lista_itens_status`:

```php
// Se não retornou dados e temos IDs específicos, tentar novamente após um pequeno delay
if (empty($data) && !empty($idItens) && is_array($idItens)) {
    usleep(200000); // 200ms
    $data = $this->cad_item_wf_atributo_model->get_itens_validados(...);
}
```

### 3. Melhor Tratamento de Erro e Logs

**Arquivo**: `ModalHomologSelecteds.vue`

- Adicionados logs detalhados para debug
- Melhor tratamento de erros na requisição
- Mensagem mais informativa para o usuário

### 4. Botão "Tentar Novamente"

**Arquivo**: `ModalHomologSelecteds.vue`

Adicionado botão que permite ao usuário tentar recarregar a lista de itens:

```html
<button v-if="listItensLength == 0 && !carregandoLista" type="button" class="btn btn-warning" @click="loadItems()">
  <i class="glyphicon glyphicon-refresh icon"></i> Tentar Novamente
</button>
```

### 5. Mensagem Explicativa

Adicionada mensagem que explica ao usuário o que pode estar acontecendo:

```html
<p v-if="listItensLength == 0 && this.carregandoLista == false" class="text-muted">
  Os itens selecionados podem não atender aos critérios de homologação ou ainda estão sendo processados. 
  Tente novamente em alguns instantes ou verifique se todos os atributos obrigatórios foram preenchidos.
</p>
```

## Como Testar

1. Selecione itens na tabela de atributos
2. Faça edições em alguns atributos (não salve)
3. Clique em "Homologar selecionados"
4. Quando aparecer o alerta, clique em "Sim" para salvar
5. Aguarde o modal de homologação abrir
6. Verifique se os itens aparecem corretamente

## Monitoramento

Para monitorar se o problema ainda ocorre:

1. Verifique os logs do navegador (console.warn/error)
2. Verifique os logs do PHP (log_message debug)
3. Se o problema persistir, use o botão "Tentar Novamente"

## Próximos Passos

Se o problema continuar ocorrendo mesmo com essas melhorias, considere:

1. Aumentar o delay no frontend (de 500ms para 1000ms)
2. Implementar polling no backend até os dados estarem disponíveis
3. Usar transações de banco de dados para garantir consistência
4. Implementar cache invalidation mais agressivo
