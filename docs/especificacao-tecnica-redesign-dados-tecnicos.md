# Especificação Técnica - Redesign da Tela de Dados Técnicos

## 📋 Resumo Executivo

**Objetivo**: Redesign da tela de Dados Técnicos mantendo todas as funcionalidades existentes com novo layout moderno e componentes reutilizáveis.

**Estimativa Total**: 44 horas de desenvolvimento

**Principais Mudanças**:

- Filtros movidos para modal com contador e badges
- Ações agrupadas em dropdown
- Adição de campos SLA Hrs restantes e Farol
- Modal de atribuição redesenhado
- Componentes reutilizáveis para outras telas

**Arquivos Principais Afetados**:

- `application/controllers/atribuir_grupo.php`
- `application/views/atribuir_grupo/index.php`
- `assets/js/atribuir_grupos/atribuir_grupos.js`

## 1. Visão Geral

Este documento detalha a especificação técnica para o redesign da tela de Dados Técnicos (controller `atribuir_grupo.php`), mantendo todas as funcionalidades existentes mas com um novo layout baseado nas imagens de referência fornecidas.

## 2. Análise do Estado Atual

### 2.1 Arquivos Principais

- **Controller**: `application/controllers/atribuir_grupo.php`
- **View Principal**: `application/views/atribuir_grupo/index.php`
- **JavaScript**: `assets/js/atribuir_grupos/atribuir_grupos.js`
- **Modelos**: `item_model`, `ncm_model`, `empresa_model`, etc.

### 2.2 Funcionalidades Atuais

- Pesquisa por part_number
- Filtros diversos (status, prioridade, owner, evento, etc.)
- Listagem de itens com paginação
- Atribuição de grupos (modal atual)
- Ações em lote (exportar, transferir, etc.)
- Integração com DIANA
- Carregamento assíncrono de filtros

## 3. Requisitos do Redesign

### 3.1 Layout Principal

- **Filtros**: Movidos para modal acionado por botão com ícone
- **Contador de filtros**: Badge no ícone do filtro
- **Badges de filtros ativos**: Exibidos abaixo do campo de pesquisa
- **Ações**: Agrupadas em dropdown
- **Novos campos**: SLA Hrs restantes e Farol (do controller `geral_sla.php`)

### 3.2 Modal de Filtros

- Todos os filtros atuais movidos para modal
- Contador visual de filtros aplicados
- Aplicação de filtros em tempo real

### 3.3 Modal de Atribuição

- Guias Diana e Atribuir no modal
- Manter funcionalidades existentes
- Design consistente com novo layout

## 4. Especificação Técnica Detalhada

### 4.1 Refatoração do Controller

#### 4.1.1 Método `apply_default_filters`

Implementar método similar ao `mestre_itens.php` para organizar filtros:

```php
private function apply_default_filters($post = null, $per_page = null)
{
    // Implementação baseada no padrão do mestre_itens
    // Organizar filtros de forma estruturada
}
```

#### 4.1.2 Integração com SLA

Adicionar métodos para buscar dados de SLA:

```php
public function ajax_get_sla_data()
{
    // Buscar dados de SLA Hrs restantes e Farol
    // Baseado no geral_sla_model
}
```

### 4.2 Componentes Reutilizáveis

#### 4.2.1 Modal de Filtros

Criar componente reutilizável para outras telas:

- `application/views/components/modal_filtros.php`
- JavaScript modular para gerenciar filtros
- CSS específico para o modal

#### 4.2.2 Badge de Filtros

Componente para exibir filtros ativos:

- `application/views/components/filter_badges.php`
- Funcionalidade de remoção individual

### 4.3 Estrutura de Arquivos

#### 4.3.1 Novos Arquivos

```
application/views/components/
├── modal_filtros.php
├── filter_badges.php
└── dropdown_acoes.php

assets/js/components/
├── modal-filtros.js
├── filter-badges.js
└── dropdown-acoes.js

assets/css/components/
├── modal-filtros.css
├── filter-badges.css
└── dropdown-acoes.css
```

#### 4.3.2 Arquivos Modificados

```
application/controllers/atribuir_grupo.php
application/views/atribuir_grupo/index.php
application/views/atribuir_grupo/modal-atribuir-grupo.php
assets/js/atribuir_grupos/atribuir_grupos.js
```

### 4.4 Integração com Dados de SLA

#### 4.4.1 Campos Adicionais na Listagem

- **SLA Hrs restantes**: `tempo_restante` do `geral_sla_model`
- **Farol**: Cálculo baseado em percentual de SLA consumido
  - Verde: ≤ 75%
  - Amarelo: 75% < x < 100%
  - Vermelho: ≥ 100%

#### 4.4.2 Query Modificada

Adaptar query do `ajax_get_itens` para incluir dados de SLA:

```sql
-- Adicionar campos de SLA à query existente
-- Baseado na lógica do geral_sla_model
```

## 5. Implementação por Fases

### Fase 1: Refatoração do Controller (8h)

- Implementar `apply_default_filters`
- Organizar métodos de filtros
- Adicionar integração com SLA

### Fase 2: Componentes Reutilizáveis (12h)

- Criar modal de filtros
- Implementar sistema de badges
- Desenvolver dropdown de ações

### Fase 3: Integração Frontend (10h)

- Modificar view principal
- Adaptar JavaScript existente
- Implementar novo layout

### Fase 4: Modal de Atribuição (8h)

- Redesign do modal atual
- Manter funcionalidades Diana/Atribuir
- Testes de integração

### Fase 5: Testes e Ajustes (6h)

- Testes funcionais
- Ajustes de performance
- Validação com usuários

## 6. Considerações Técnicas

### 6.1 Performance

- Manter carregamento assíncrono de filtros
- Otimizar queries com dados de SLA
- Cache de dados quando possível

### 6.2 Compatibilidade

- Manter compatibilidade com funcionalidades existentes
- Preservar permissões e roles
- Garantir funcionamento em diferentes navegadores

### 6.3 Reutilização

- Componentes preparados para uso em outras telas
- Padrões consistentes de código
- Documentação para futura manutenção

## 7. Estimativa Total de Horas

| Fase      | Descrição                 | Horas   |
| --------- | ------------------------- | ------- |
| 1         | Refatoração do Controller | 8h      |
| 2         | Componentes Reutilizáveis | 12h     |
| 3         | Integração Frontend       | 10h     |
| 4         | Modal de Atribuição       | 8h      |
| 5         | Testes e Ajustes          | 6h      |
| **Total** |                           | **44h** |

## 8. Lista Detalhada de Tasks

### 8.1 Fase 1: Refatoração do Controller (8h)

#### Task 1.1: Implementar método apply_default_filters (3h)

- Criar método baseado no padrão do mestre_itens.php
- Organizar filtros boolean e array
- Implementar handlers para cada tipo de filtro
- Testes unitários do método

#### Task 1.2: Refatorar métodos de filtros existentes (2h)

- Limpar código duplicado nos métodos ajax_get_itens
- Padronizar tratamento de filtros
- Documentar métodos refatorados

#### Task 1.3: Integração com dados de SLA (3h)

- Criar método ajax_get_sla_data
- Modificar query do ajax_get_itens para incluir SLA
- Implementar cálculo do Farol
- Testes de performance da query

### 8.2 Fase 2: Componentes Reutilizáveis (12h)

#### Task 2.1: Criar modal de filtros (4h)

- Desenvolver application/views/components/modal_filtros.php
- Implementar estrutura HTML responsiva
- Adicionar validações de formulário
- Testes de acessibilidade

#### Task 2.2: Sistema de badges de filtros (3h)

- Criar application/views/components/filter_badges.php
- Implementar funcionalidade de remoção individual
- Adicionar contador de filtros ativos
- Estilização CSS

#### Task 2.3: Dropdown de ações (2h)

- Desenvolver application/views/components/dropdown_acoes.php
- Migrar ações existentes para dropdown
- Implementar permissões por ação
- Testes funcionais

#### Task 2.4: JavaScript modular (3h)

- Criar assets/js/components/modal-filtros.js
- Desenvolver assets/js/components/filter-badges.js
- Implementar assets/js/components/dropdown-acoes.js
- Integração com código existente

### 8.3 Fase 3: Integração Frontend (10h)

#### Task 3.1: Modificar view principal (4h)

- Atualizar application/views/atribuir_grupo/index.php
- Implementar novo layout baseado nas imagens
- Integrar componentes reutilizáveis
- Responsividade mobile

#### Task 3.2: Adaptar JavaScript existente (3h)

- Modificar assets/js/atribuir_grupos/atribuir_grupos.js
- Integrar com novos componentes
- Manter compatibilidade com funcionalidades existentes
- Otimizar performance

#### Task 3.3: Estilização CSS (2h)

- Criar assets/css/components/ específicos
- Adaptar estilos existentes
- Garantir consistência visual
- Testes cross-browser

#### Task 3.4: Integração de dados SLA na listagem (1h)

- Adicionar colunas SLA Hrs restantes e Farol
- Implementar indicadores visuais do Farol
- Formatação de dados
- Testes de exibição

### 8.4 Fase 4: Modal de Atribuição (8h)

#### Task 4.1: Redesign do modal atual (4h)

- Modificar application/views/atribuir_grupo/modal-atribuir-grupo.php
- Implementar guias Diana e Atribuir
- Manter funcionalidades existentes
- Melhorar UX/UI

#### Task 4.2: Integração com componentes (2h)

- Adaptar modal para novo padrão
- Integrar com sistema de filtros
- Testes de funcionalidade
- Validações de formulário

#### Task 4.3: Funcionalidade Diana no modal (2h)

- Manter integração existente com DIANA
- Adaptar para novo layout
- Testes de integração
- Tratamento de erros

### 8.5 Fase 5: Testes e Ajustes (6h)

#### Task 5.1: Testes funcionais (2h)

- Testar todas as funcionalidades existentes
- Validar novos recursos
- Testes de regressão
- Documentar bugs encontrados

#### Task 5.2: Testes de performance (2h)

- Avaliar tempo de carregamento
- Otimizar queries se necessário
- Testes de carga
- Monitoramento de memória

#### Task 5.3: Ajustes finais e documentação (2h)

- Correção de bugs encontrados
- Ajustes de UX baseados em feedback
- Documentação técnica
- Preparação para deploy

## 9. Dependências e Riscos

### 9.1 Dependências

- Acesso ao ambiente de desenvolvimento
- Dados de teste atualizados
- Aprovação de mudanças no banco de dados (se necessário)
- Coordenação com equipe de QA

### 9.2 Riscos Identificados

- **Alto**: Quebra de funcionalidades existentes durante refatoração
- **Médio**: Performance degradada com integração de SLA
- **Baixo**: Incompatibilidade com navegadores antigos
- **Baixo**: Resistência dos usuários ao novo layout

### 9.3 Plano de Mitigação

- Testes extensivos em cada fase
- Backup completo antes de modificações
- Deploy gradual com rollback preparado
- Treinamento de usuários se necessário

## 10. Próximos Passos

1. Aprovação da especificação técnica
2. Criação de branch específica para desenvolvimento
3. Implementação seguindo as fases definidas
4. Testes em ambiente de desenvolvimento
5. Deploy em ambiente de homologação
6. Validação com usuários finais
7. Deploy em produção

---

_Documento criado em: 2025-08-20_
_Versão: 1.0_
