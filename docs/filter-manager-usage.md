# Filter Manager Library - <PERSON><PERSON><PERSON> <PERSON> Uso

## Visão Geral

A `Filter_Manager` library foi criada para centralizar e reutilizar a lógica de filtros em diferentes telas do sistema. Ela abstrai toda a complexidade do gerenciamento de filtros, permitindo configurações flexíveis e reutilização de código.

## Instalação e Configuração

### 1. Carregamento da Library

No construtor do seu controller, carregue a library:

```php
public function __construct()
{
    parent::__construct();
    
    // Outros carregamentos...
    $this->load->library('filter_manager');
    
    // Carregar models necessários...
}
```

### 2. Uso Básico

```php
private function apply_default_filters($post = null)
{
    // Usar configuração padrão para dados técnicos
    $config = Filter_Manager::get_dados_tecnicos_config();
    
    $result = $this->filter_manager
        ->initialize($this->item_model)  // Inicializar com o model
        ->configure($config)             // Configurar filtros
        ->apply_default_filters($post);  // Aplicar filtros
        
    // Definir filtros adicionais se necessário
    $this->item_model->set_state('filter.id_empresa', sess_user_company());
    
    return $result;
}
```

## Tipos de Filtros Suportados

### 1. Filtros Booleanos (Checkboxes)

```php
'boolean' => [
    'triagem_diana_falha' => ['is_checkbox_checked'],
    'item_ativo' => ['is_checkbox_checked']
]
```

### 2. Filtros de Array (Selects múltiplos)

```php
'array' => [
    'status' => ['is_not_empty'],
    'prioridade' => ['is_array', 'not_contains_negative_one'],
    'owner' => ['is_array', 'not_contains_negative_one']
]
```

### 3. Filtros Simples (Inputs de texto)

```php
'simple' => [
    'item_input' => ['is_not_empty'],
    'tag' => ['is_not_empty']
]
```

### 4. Filtros Customizados (Callbacks)

```php
'custom' => [
    'data_range' => function($model, $input, $post) {
        $data_inicio = $input->post('data_inicio');
        $data_fim = $input->post('data_fim');
        
        if (!empty($data_inicio) && !empty($data_fim)) {
            $model->set_state('filter.data_inicio', $data_inicio);
            $model->set_state('filter.data_fim', $data_fim);
        } else {
            $model->unset_state('filter.data_inicio');
            $model->unset_state('filter.data_fim');
        }
    }
]
```

## Condições de Validação Disponíveis

- `is_checkbox_checked`: Verifica se checkbox está marcado
- `is_not_empty`: Verifica se valor não está vazio
- `is_array`: Verifica se é um array
- `not_contains_negative_one`: Verifica se array não contém -1
- `not_equals_negative_one`: Verifica se valor não é -1
- `no_events`: Verifica se não é 'sem_evento'

## Exemplos de Uso para Diferentes Telas

### Tela de Relatórios

```php
private function apply_relatorio_filters($post = null)
{
    $config = [
        'boolean' => [
            'incluir_inativos' => ['is_checkbox_checked']
        ],
        'array' => [
            'empresas' => ['is_array', 'not_contains_negative_one'],
            'periodos' => ['is_array', 'not_contains_negative_one']
        ],
        'simple' => [
            'codigo_relatorio' => ['is_not_empty']
        ],
        'custom' => [
            'periodo_personalizado' => function($model, $input, $post) {
                // Lógica customizada para período
            }
        ]
    ];
    
    return $this->filter_manager
        ->initialize($this->relatorio_model)
        ->configure($config)
        ->apply_default_filters($post);
}
```

### Tela de Usuários

```php
private function apply_usuario_filters($post = null)
{
    $config = [
        'boolean' => [
            'usuarios_ativos' => ['is_checkbox_checked']
        ],
        'array' => [
            'perfis' => ['is_array', 'not_contains_negative_one'],
            'departamentos' => ['is_array', 'not_contains_negative_one']
        ],
        'simple' => [
            'nome_usuario' => ['is_not_empty'],
            'email' => ['is_not_empty']
        ]
    ];
    
    return $this->filter_manager
        ->initialize($this->usuario_model)
        ->configure($config)
        ->apply_default_filters($post);
}
```

## Configurações Pré-definidas

### Dados Técnicos (Padrão)

```php
$config = Filter_Manager::get_dados_tecnicos_config();
```

Retorna:
```php
[
    'boolean' => [
        'triagem_diana_falha' => ['is_checkbox_checked']
    ],
    'array' => [
        'evento' => ['is_not_empty', 'no_events'],
        'status' => ['is_not_empty'],
        'owner' => ['is_array', 'not_contains_negative_one'],
        'prioridade' => ['is_array', 'not_contains_negative_one'],
        'atribuido_para' => ['is_not_empty', 'not_equals_negative_one'],
        'sistemas_origens' => ['is_array', 'not_contains_negative_one']
    ],
    'simple' => [
        'item_input' => ['is_not_empty'],
        'tag' => ['is_not_empty']
    ]
]
```

## Adicionando Condições Customizadas

```php
// Adicionar nova condição de validação
$this->filter_manager->add_condition('is_valid_email', function($value) {
    return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
});

// Usar a condição customizada
$config = [
    'simple' => [
        'email_usuario' => ['is_not_empty', 'is_valid_email']
    ]
];
```

## Vantagens da Library

1. **Reutilização**: Código centralizado para todas as telas
2. **Flexibilidade**: Configurações específicas por tela
3. **Manutenibilidade**: Mudanças em um local afetam todas as telas
4. **Consistência**: Comportamento padronizado de filtros
5. **Extensibilidade**: Fácil adição de novos tipos de filtros

## Migração de Código Existente

Para migrar uma tela existente:

1. Carregue a library no construtor
2. Substitua o método `apply_default_filters` existente
3. Configure os filtros específicos da tela
4. Remova métodos auxiliares duplicados
5. Teste todas as funcionalidades

## Troubleshooting

### Erro: "Model não foi inicializado"
- Certifique-se de chamar `initialize($model)` antes de `configure()`

### Filtros não funcionam
- Verifique se as condições estão corretas
- Confirme se o model tem os métodos `set_state` e `unset_state`

### Performance
- A library mantém a mesma performance do código original
- Filtros customizados podem impactar performance se mal implementados

---

*Documentação criada em: 2025-08-20*
*Versão: 1.0*
