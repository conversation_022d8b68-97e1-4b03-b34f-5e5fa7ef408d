# Funcionalidade de Exportação - Index New

## Descrição
Nova implementação da funcionalidade de exportação para a tela redesenhada de Dados Técnicos (`index_new`). Mantém a compatibilidade com os filtros aplicados e oferece duas opções de exportação: **Padrão** e **Multi Países**.

## Arquivos Envolvidos

### JavaScript
- `assets/js/index_new_export.js` - Módulo principal de exportação
- Integração com `filter_modal_integration.js` para capturar filtros ativos

### PHP (Controller)
- `application/controllers/atribuir_grupo.php`
  - `log_xls_new()` - Método otimizado para exportação padrão
  - `xls_multipaises_new()` - Método otimizado para exportação multi países
  - `ajax_xls_log_status_new()` - Status para exportação padrão
  - `ajax_xls_log_status_multipaises_new()` - Status para exportação multi países

### View
- `application/views/atribuir_grupo/index_new.php` - Inclui o script de exportação

## Como Funciona

### 1. Coleta de Filtros
- Captura automaticamente todos os filtros aplicados via modal de filtros
- Coleta o texto de pesquisa principal
- Mapeia filtros para o formato esperado pelo backend

### 2. Geração do Arquivo
- Usa `apply_default_filters()` para aplicar a mesma lógica de filtros da pesquisa
- Gera arquivo Excel (.xlsx) com dados filtrados
- Suporte para campos dinâmicos baseados na configuração da empresa

### 3. Download
- Submete formulário via GET para iniciar download
- Exibe overlay de loading durante processamento
- Controla estado do botão para evitar múltiplos cliques

## Métodos de Exportação

### Exportação Padrão
```javascript
// Acionado pelo botão "Padrão"
IndexNewExport.startExport('padrao');
```
- Endpoint: `atribuir_grupo/log_xls_new`
- Status: `atribuir_grupo/ajax_xls_log_status_new`

### Exportação Multi Países
```javascript
// Acionado pelo botão "Multi Países"
IndexNewExport.startExport('multipaises');
```
- Endpoint: `atribuir_grupo/xls_multipaises_new`
- Status: `atribuir_grupo/ajax_xls_log_status_multipaises_new`

## Compatibilidade

### Fallback Automático
O sistema detecta automaticamente se os novos métodos estão disponíveis:
- **Métodos Novos**: Usa `log_xls_new` e `xls_multipaises_new` (otimizados)
- **Fallback**: Usa `log_xls` e `xls_multipaises` (originais) se novos não estão disponíveis

### Mapeamento de Filtros
- **Métodos Novos**: Usa nomes diretos dos filtros (ex: `pacotes_eventos`)
- **Métodos Antigos**: Mapeia para nomes esperados (ex: `evento`)

## Vantagens dos Novos Métodos

1. **Reutilização de Código**: Usa `apply_default_filters()` para evitar duplicação
2. **Consistência**: Garante que exportação usa exatamente os mesmos filtros da pesquisa
3. **Manutenibilidade**: Mudanças nos filtros automaticamente refletem na exportação
4. **Tratamento de Erros**: Melhor handling de erros e logging
5. **Performance**: Código otimizado e mais limpo

## Uso

### Para o Usuário
1. Aplicar filtros desejados no modal de filtros
2. Realizar pesquisa (opcional - filtros são salvos automaticamente)
3. Clicar em "Exportar" > "Padrão" ou "Multi Países"
4. Aguardar download do arquivo

### Para Desenvolvedor
```javascript
// Obter filtros ativos
var filters = IndexNewExport.getActiveFilters();

// Iniciar exportação programaticamente
IndexNewExport.startExport('padrao'); // ou 'multipaises'

// Verificar se está exportando
if (IndexNewExport.isExporting) {
    console.log('Exportação em andamento...');
}
```

## Estrutura do Arquivo Exportado

### Colunas Principais
- Part number, Descrição, Datas (Criação/Modificação)
- Motivo, Estabelecimento, NCM
- Status, Prioridades, Responsáveis
- Perguntas & Respostas, Observações

### Colunas Dinâmicas
- **Descrição Global**: Incluída se empresa tiver campo configurado
- **PN Secundário**: Incluída se empresa tiver campo configurado
- **Dados COMEX**: Indicador, DI, Drawback, etc.

## Configurações

### Timeout de Status
```javascript
// Aguarda 1.5s antes de verificar status da exportação
this.exportTimer = setTimeout(..., 1500);
```

### Limites de Memória/Tempo (PHP)
```php
set_time_limit(0);
ini_set('memory_limit', '2048M');
```

## Troubleshooting

### Problema: Exportação não funciona
1. Verificar se métodos novos existem no controller
2. Verificar logs do servidor para erros PHP
3. Sistema fará fallback automático se necessário

### Problema: Filtros não aplicados
1. Verificar se `FilterModalIntegration` está carregado
2. Verificar se filtros foram salvos corretamente
3. Verificar mapeamento entre novos/antigos métodos

### Problema: Download não inicia
1. Verificar se token está sendo gerado
2. Verificar headers HTTP na resposta
3. Verificar se overlay está sendo exibido/ocultado corretamente
