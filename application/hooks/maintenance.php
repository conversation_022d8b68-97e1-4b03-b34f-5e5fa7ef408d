<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class maintenance
{
   public function maintenance()
   {
		$CI = & get_instance();

        if(file_exists(FCPATH . "MAINTENANCE") && !$CI->input->is_cli_request())
        {
            $error =& load_class('Exceptions', 'core');
            echo $error->show_error("", "", 'error_maintenance', 200);

            exit();
        }
    }
}