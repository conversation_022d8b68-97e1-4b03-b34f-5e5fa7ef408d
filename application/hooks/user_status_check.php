<?php if (! defined('BASEPATH')) exit('No direct script access allowed');

class User_status_check_hook
{
    public function check_user_status()
    {
        $CI = &get_instance();

        if ($CI->input->is_cli_request()) {
            return;
        }

        $router = $CI->router;
        $class = strtolower($router->class);
        $method = strtolower($router->method);

        $public_controllers = ['login', 'auth'];
        if (in_array($class, $public_controllers)) {
            return;
        }

        if (!function_exists('is_logged')) {
            $CI->load->helper('account');
        }

        if (!is_logged()) {
            return $this->deny_request($CI, 'Usuário não logado');
        }

        $user_id = $CI->session->userdata('user_id');
        if (!$user_id) {
            return $this->deny_request($CI, 'Sessão inválida');
        }

        $CI->db->where('id_usuario', $user_id);
        $query = $CI->db->get('usuario');
        if ($query->num_rows() === 0) {
            return $this->deny_request($CI, 'Usuário não encontrado');
        }

        $usuario = $query->row();

        if ($usuario->status != '1') {
            return $this->deny_request($CI, 'Usuário inativo');
        }
    }

    private function deny_request($CI, $message)
    {
        $logoutUrl = base_url('login/logout');
        if ($this->is_ajax_or_json($CI)) {
            // XHR / API request → tell frontend to redirect
            $CI->output
                ->set_status_header(401) // Unauthorized
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error'    => $message,
                    'redirect' => $logoutUrl
                ]))
                ->_display();
        } else {
            // Normal browser request → redirect immediately
            redirect($logoutUrl);
        }
        exit();
    }

    /**
     * Detect AJAX or API/JSON requests
     */
    private function is_ajax_or_json($CI)
    {
        // Normal AJAX detection
        if ($CI->input->is_ajax_request()) {
            return true;
        }

        // Accept: application/json
        $accept = $CI->input->server('HTTP_ACCEPT');
        if ($accept && strpos($accept, 'application/json') !== false) {
            return true;
        }

        return false;
    }

}


