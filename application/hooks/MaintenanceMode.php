<?php
defined('BASEPATH') || exit('No direct script access allowed');

class MaintenanceMode {
    public function check_maintenance() {

        var_dump(APPPATH . 'config/maintenance_mode'); exit;

        // Verifica se existe o arquivo de manutenção
        if (file_exists(APPPATH . 'config/maintenance_mode')) {

            header('HTTP/1.1 503 Service Temporarily Unavailable');
            header('Status: 503 Service Temporarily Unavailable');
            header('Retry-After: 3600');

            // Inclui a view de manutenção
            include(APPPATH . 'views/maintenance.php');
            exit;
        }
    }
}
