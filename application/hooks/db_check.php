<?php
defined('BASEPATH') || exit('No direct script access allowed');

class Db_check_hook {

    public function check_db_connection() {
        include(APPPATH . 'config/database.php');

        // Verificar se existe o arquivo config/maintenance. Se sim, exibir a página de manutenção
        // if (file_exists(APPPATH . 'config/maintenance_mode')) {
        //     include(APPPATH . 'views/maintenance.php');
        //     exit();
        // }

        // Obtém as configurações do banco de dados
        $db_config = $db['default'];

        // Tenta conectar ao banco de dados manualmente
        $connection = @mysqli_connect(
            $db_config['hostname'],
            $db_config['username'],
            $db_config['password']
        );

        if (!$connection) {
            // Se a conexão falhar, exibe a página de manutenção
            include(APPPATH . 'views/maintenance.php');
            exit();
        }

        // Fecha a conexão manual
        if (is_resource($connection)) {
            mysqli_close($connection);
        }
    }
}
