<?php
class SpreadsheetService
{
    private $CI;

    public function __construct()
    {
        $this->CI = &get_instance();
        $this->CI->load->helper('text');
        include APPPATH . 'libraries/xlsxreader.php';
    }

    /**
     * Processa uma planilha XLSX e extrai os dados
     *
     * @param string $filePath Caminho do arquivo
     * @param array $columnMapping Mapeamento de colunas
     * @return array Resultado do processamento
     */
    public function processSpreadsheet($filePath, $columnMapping, $hasPrioridade = false)
    {
        $xlsx = new XLSXReader($filePath);
        $sheetNames = $xlsx->getSheetNames();
        $sheetActive = current($sheetNames);
        $sheet = $xlsx->getSheet($sheetActive);

        $data = [];
        $errors = [];
        $mappingResult = [];

        foreach ($sheet->getData() as $i => $row) {
            if ($i == 0) {
                $mappingResult = $this->mapColumns($row, $columnMapping);
                $validation = $this->validateColumns($row, $mappingResult, $hasPrioridade);
                if (!empty($validation['errors'])) {
                    // Adicionar informações de linha para erros de validação de colunas
                    foreach ($validation['errors'] as &$error) {
                        if (is_array($error) && !isset($error['linha'])) {
                            $error['linha'] = 1; // Linha de cabeçalho é sempre 1
                        }
                    }
                    return ['data' => [], 'errors' => $validation['errors']];
                }
            } else {
                $processedRow = $this->processRow($row, $mappingResult['idx'], $i);

                if (!empty($processedRow['errors'])) {
                    // Adicionar informações de linha e estabelecimento para cada erro
                    foreach ($processedRow['errors'] as &$error) {
                        if (is_string($error)) {
                            // Converter para formato detalhado
                            $estabelecimento = isset($processedRow['data']['estabelecimento']) && !empty($processedRow['data']['estabelecimento'])
                                ? $processedRow['data']['estabelecimento']
                                : 'N/A';

                            $error = [
                                'mensagem' => $error,
                                'part_number' => isset($processedRow['data']['part_number']) ? $processedRow['data']['part_number'] : 'N/A',
                                'estabelecimento' => $estabelecimento,
                                'linha' => $i + 1, // +1 porque o índice começa em 0, mas na planilha começa em 1
                                'coluna' => 'N/A'
                            ];
                        } else {
                            // Já está no formato de array, apenas adicionar a linha e estabelecimento se não existirem
                            if (!isset($error['linha'])) {
                                $error['linha'] = $i + 1;
                            }

                            if (!isset($error['estabelecimento']) || empty($error['estabelecimento'])) {
                                $error['estabelecimento'] = isset($processedRow['data']['estabelecimento']) && !empty($processedRow['data']['estabelecimento'])
                                    ? $processedRow['data']['estabelecimento']
                                    : 'N/A';
                            }

                            if (!isset($error['part_number']) || empty($error['part_number'])) {
                                $error['part_number'] = isset($processedRow['data']['part_number']) ? $processedRow['data']['part_number'] : 'N/A';
                            }
                        }
                    }
                    $errors = array_merge($errors, $processedRow['errors']);
                } else {
                    // Adicionar informação de linha ao item processado
                    $processedRow['data']['_linha'] = $i + 1;
                    $data[] = $processedRow['data'];
                }
            }
        }

        return ['data' => $data, 'errors' => $errors, 'idx' => $mappingResult['idx']];
    }

    private function mapColumns($headerRow, $columnMapping)
    {
        $idx = [];
        $cols = array_map('convert_accented_characters', array_values($headerRow));
        foreach ($cols as $k => $col) {
            foreach ($columnMapping as $key => $patterns) {
                $preg = '/^(' . implode("|", $patterns) . ')$/i';
                if (preg_match($preg, $col) && !array_key_exists($key, $idx)) {
                    $idx[$key] = $k;
                    unset($cols[$k]);
                    break;
                }
            }
        }
        return ['idx' => $idx, 'invalid_cols' => $cols];
    }

    /**
     * Valida as colunas da planilha
     *
     * @param array $headerRow Linha de cabeçalho
     * @param array $mappingResult Resultado do mapeamento de colunas
     * @return array Erros de validação
     */
    private function validateColumns($headerRow, $mappingResult, $hasPrioridade)
    {
        $idx = $mappingResult['idx'];
        $cols = $mappingResult['invalid_cols'];
        $errors = [];

        if (count($cols) > 0) {
            $lista_colunas = '';
            foreach ($cols as $kv => $colv) {
                $alpha = $this->num2alpha($kv);
                $col_info = (empty($headerRow[$kv]) ? '<em>Sem título</em>' : $headerRow[$kv]);
                $lista_colunas .= '<li>' . $col_info . ' (<b>' . $alpha . '</b>)</li>';
            }
            $errors[] = [
                'mensagem' => "Colunas inválidas ou duplicadas: <b>" . count($cols) . "</b></li><ul>" . $lista_colunas . "</ul>",
                'tipo' => 'estrutura_planilha',
                'part_number' => 'N/A',
                'estabelecimento' => 'N/A',
                'linha' => 1, // Linha de cabeçalho
                'coluna' => implode(',', array_map([$this, 'num2alpha'], array_keys($cols)))
            ];
        }

        if (!isset($idx['part_number'])) {
            $errors[] = [
                'mensagem' => 'Coluna <b>Part Number</b> não encontrada.',
                'tipo' => 'coluna_obrigatoria',
                'part_number' => 'N/A',
                'estabelecimento' => 'N/A',
                'linha' => 1,
                'coluna' => 'N/A'
            ];
        }
        if (!isset($idx['cnpj'])) {
            $errors[] = [
                'mensagem' => 'Coluna <b>CNPJ</b> não encontrada.',
                'tipo' => 'coluna_obrigatoria',
                'part_number' => 'N/A',
                'estabelecimento' => 'N/A',
                'linha' => 1,
                'coluna' => 'N/A'
            ];
        }
        if (!isset($idx['descricao'])) {
            $errors[] = [
                'mensagem' => 'Coluna <b>Descrição</b> não encontrada.',
                'tipo' => 'coluna_obrigatoria',
                'part_number' => 'N/A',
                'estabelecimento' => 'N/A',
                'linha' => 1,
                'coluna' => 'N/A'
            ];
        }

        if ($hasPrioridade && !isset($idx['prioridade'])) {
            $errors[] = [
                'mensagem' => 'Coluna <b>Prioridade</b> não encontrada.',
                'tipo' => 'coluna_obrigatoria',
                'part_number' => 'N/A',
                'estabelecimento' => 'N/A',
                'linha' => 1,
                'coluna' => 'N/A'
            ];
        }

        return ['errors' => $errors];
    }

    private function processRow($row, $idx, $rowIndex)
    {
        array_walk($row, function (&$v) {
            $v = clean_str($v);
        });

        // Obter part_number e estabelecimento, mesmo que estejam vazios
        $part_number = isset($idx['part_number']) && isset($row[$idx['part_number']]) ?
            clean_str($row[$idx['part_number']], true) : '';

        $cnpj = isset($idx['cnpj']) && isset($row[$idx['cnpj']]) ?
            trim($row[$idx['cnpj']]) : '';

        $estabelecimento = '';
        if (isset($idx['estabelecimento']) && isset($row[$idx['estabelecimento']])) {
            $estabelecimento = preg_replace('/[\xA0]/u', '', trim($row[$idx['estabelecimento']]));
            $estabelecimento = convert_accented_characters($estabelecimento);
        }

        $data = [
            'part_number' => $part_number,
            'cnpj' => $cnpj,
            'estabelecimento' => $estabelecimento,
        ];

        $errors = [];

        if (empty($part_number)) {
            $errors[] = [
                'mensagem' => "Part Number não informado na linha <b>" . ($rowIndex + 1) . "</b>",
                'part_number' => 'N/A',
                'estabelecimento' => !empty($estabelecimento) ? $estabelecimento : 'N/A',
                'linha' => $rowIndex + 1,
                'coluna' => isset($idx['part_number']) ? $this->num2alpha($idx['part_number']) : 'N/A'
            ];
        }

        // Se não tiver descrição, informar o erro
        if (empty($row[$idx['descricao']])) {
            $errors[] = [
                'mensagem' => "Descrição não informada na linha <b>" . ($rowIndex + 1) . "</b>",
                'part_number' => $part_number,
                'estabelecimento' => !empty($estabelecimento) ? $estabelecimento : 'N/A',
                'linha' => $rowIndex + 1,
                'coluna' => $this->num2alpha($idx['descricao'])
            ];
        }

        foreach ($idx as $key => $colIdx) {
            if ($key !== 'part_number' && $key !== 'cnpj' && $key !== 'estabelecimento') {
                $data[$key] = isset($row[$colIdx]) ? trim($row[$colIdx]) : null;
            }
        }

        return ['data' => $data, 'errors' => $errors];
    }

    /**
     * Converte número de coluna para letra (ex: 0 -> A, 1 -> B)
     *
     * @param int $n Número da coluna
     * @return string Letra da coluna
     */
    private function num2alpha($n)
    {
        $r = '';
        for ($i = 1; $n >= 0 && $i < 10; $i++) {
            $r = chr(0x41 + ($n % 26)) . $r;
            $n = floor($n / 26) - 1;
        }
        return $r;
    }

    public function readHomologationSpreadsheet($file_path, $columnMapping)
    {
        $reader = \Box\Spout\Reader\ReaderFactory::create(\Box\Spout\Common\Type::XLSX);
        $reader->open($file_path);

        $data = [];
        $errors = [];
        $idx = [];
        $colunas_originais = [];

        foreach ($reader->getSheetIterator() as $sheet) {
            if ($sheet->getIndex() === 0) {
                foreach ($sheet->getRowIterator() as $i => $row) {
                    if ($i == 1) {
                        //  Usar o novo método privado para validar as colunas
                        $mappingResult = $this->mapColumns($row, $columnMapping);
                        $validation = $this->validateColumnsHomologation($row, $mappingResult);
                        if (!empty($validation['errors'])) {
                            // Adicionar informações de linha para erros de validação de colunas
                            foreach ($validation['errors'] as &$error) {
                                if (is_array($error) && !isset($error['linha'])) {
                                    $error['linha'] = 1; // Linha de cabeçalho é sempre 1
                                }
                            }
                            return ['data' => [], 'errors' => $validation['errors']];
                        } else {
                            $idx = $mappingResult['idx'];
                            $colunas_originais = array_values($row);
                        }
                    } else {
                        $data[] = [
                            'row' => $row,
                            'line_number' => $i
                        ];
                    }
                }
                break;
            }
        }
        $reader->close();

        return [
            'data' => $data,
            'idx' => $idx,
            'colunas_originais' => $colunas_originais,
            'errors' => $errors
        ];
    }

    private function validateColumnsHomologation($headerRow, $mappingResult)
    {
        $idx = $mappingResult['idx'];
        $cols = $mappingResult['invalid_cols'];
        $errors = [];

        if (count($cols) > 0) {
            $lista_colunas = '';
            foreach ($cols as $kv => $colv) {
                $alpha = $this->num2alpha($kv);
                $col_info = (empty($headerRow[$kv]) ? '<em>Sem título</em>' : $headerRow[$kv]);
                $lista_colunas .= '<li>' . $col_info . ' (<b>' . $alpha . '</b>)</li>';
            }
            $errors[] = [
                'mensagem' => "Colunas inválidas ou duplicadas: <b>" . count($cols) . "</b></li><ul>" . $lista_colunas . "</ul>",
                'tipo' => 'estrutura_planilha',
                'part_number' => 'N/A',
                'estabelecimento' => 'N/A',
                'linha' => 1, // Linha de cabeçalho
                'coluna' => implode(',', array_map([$this, 'num2alpha'], array_keys($cols)))
            ];
        }

        if (!isset($idx['part_number'])) {
            $errors[] = [
                'mensagem' => 'Coluna <b>Part Number</b> não encontrada.',
                'tipo' => 'coluna_obrigatoria',
                'part_number' => 'N/A',
                'estabelecimento' => 'N/A',
                'linha' => 1,
                'coluna' => 'N/A'
            ];
        }

        return ['errors' => $errors];
    }
}
