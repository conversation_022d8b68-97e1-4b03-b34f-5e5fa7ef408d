<?php

class Cad_item_homologacao_model extends MY_Model
{

    public $_table = 'cad_item_homologacao';

    public function __construct()
    {
        parent::__construct();
    }

    public function get_entries_by_pn($id_item, $tipo_homologacao = NULL)
    {
        $this->db->select('c.*, u.nome as responsavel');
        $this->db->where('id_item', $id_item);

        // if (!empty($tipo_homologacao)) {
        //     $this->db->where('tipo_homologacao', $tipo_homologacao);
        // }

        $this->db->join('usuario u', 'c.id_usuario = u.id_usuario', 'inner');
        $this->db->order_by('tipo_homologacao');

        $query = $this->db->get($this->_table . ' c');

        return $query->result();
    }

    public function drop_item($id_item)
    {
        if (empty($id_item)) return false;

        $this->db->where('id_item', $id_item);

        return $this->db->delete($this->_table);
    }

    public function get_entries_by_id_item($id_item)
    {
        $this->db->select('u.nome');
        $this->db->where('id_item', $id_item);
        $this->db->where('homologado', 1);
        $this->db->join('usuario u', 'u.id_usuario = ih.id_usuario', 'inner');

        return $this->db->get($this->_table . ' ih');

    }


    public function get_entry_by_id_item($id_item)
    {
        $this->db->where('id_item', $id_item);
        $query = $this->db->get($this->_table);
        
        return $query->row();
    }
}
