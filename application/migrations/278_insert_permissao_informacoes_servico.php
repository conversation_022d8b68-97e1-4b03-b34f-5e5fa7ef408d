<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Migration_Insert_Permissao_Informacoes_Servico extends CI_Migration {

    public function up()
    {

        $this->db->where('slug', 'visualizar_informacoes_servico');
        $g_query = $this->db->get('permissao', 1);

        if (!$g_query->num_rows()) {
            $this->db->insert('permissao', array(
                'descricao' => 'Visualizar informacoes de serviço',
                'slug'      => 'visualizar_informacoes_servico'
            ));
        }

    }
 
 
    public function down()
    {
        $this->db->where_in('slug', 'visualizar_informacoes_servico');
        $query = $this->db->get('permissao');

        $id_permissao = array();

        foreach ($query->result() as $row) {
            $id_permissao[] = $row->id_permissao;
        }
        if (count($id_permissao)) {
            $this->db->where_in('id_permissao', $id_permissao);
            $this->db->delete('visualizar_informacoes_servico');
        }
    }

}
