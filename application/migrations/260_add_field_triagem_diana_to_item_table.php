<?php

defined('BASEPATH') || exit('No direct script access allowed');

/**
 * Migration to add 'status_triagem_diana' field to 'item' table.
 */
class Migration_Add_Field_Triagem_Diana_To_Item_Table extends CI_Migration
{
    public function up()
    {
        // Adiciona o campo 'status_triagem_diana' como ENUM com os valores permitidos
        if (!$this->db->field_exists('status_triagem_diana', 'item')) {
            $this->dbforge->add_column('item', array(
                'status_triagem_diana' => array(
                    'type' => "ENUM(
                        'Pendente de triagem',
                        'Triagem aprovada',
                        'Triagem reprovada',
                        'Falha na triagem'
                    )",
                    'default' => 'Pendente de triagem',
                    'null' => false
                )
            ));
        }

        // Adiciona um índice ao campo 'status_triagem_diana' para otimizar consultas
        $this->add_index_if_not_exists('item', 'idx_status_triagem_diana', 'status_triagem_diana');
    }

    public function down()
    {
        // Remove o campo 'status_triagem_diana' (o índice será removido automaticamente)
        if ($this->db->field_exists('status_triagem_diana', 'item')) {
            $this->dbforge->drop_column('item', 'status_triagem_diana');
        }

        // Remove o índice 'idx_status_triagem_diana' (se existir)
        $this->drop_index_if_exists('item', 'idx_status_triagem_diana');
    }

    private function add_index_if_not_exists($table, $index_name, $column_name)
    {
        $sql = "SHOW INDEX FROM $table WHERE Key_name = '$index_name'";
        $query = $this->db->query($sql);
        if ($query->num_rows() == 0) {
            $this->db->query("ALTER TABLE $table ADD INDEX $index_name ($column_name)");
        }
    }

    private function drop_index_if_exists($table, $index_name)
    {
        $sql = "SHOW INDEX FROM $table WHERE Key_name = '$index_name'";
        $query = $this->db->query($sql);
        if ($query->num_rows() > 0) {
            $this->db->query("ALTER TABLE $table DROP INDEX $index_name");
        }
    }
}
