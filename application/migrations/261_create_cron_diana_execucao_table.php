<?php

if (!defined('BASEPATH'))
{
    exit('No direct script access allowed');
}

class Migration_Create_cron_diana_execucao_table extends CI_Migration
{
    public function up()
    {
        // Criar tabela para controle de execuções da cron DIANA
        $this->dbforge->add_field([
            'id_execucao' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => TRUE,
                'auto_increment' => TRUE
            ],
            'data_inicio' => [
                'type' => 'DATETIME',
                'null' => FALSE
            ],
            'data_fim' => [
                'type' => 'DATETIME',
                'null' => TRUE
            ],
            'status_execucao' => [
                'type' => "ENUM('iniciada', 'finalizada', 'erro', 'interrompida')",
                'default' => 'iniciada',
                'null' => FALSE
            ],
            'pid_processo' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => TRUE,
                'comment' => 'Process ID para controle de execução'
            ],
            'total_empresas' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
                'null' => FALSE
            ],
            'total_itens_processados' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
                'null' => FALSE
            ],
            'total_sucessos' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
                'null' => FALSE
            ],
            'total_erros' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
                'null' => FALSE
            ],
            'tempo_execucao_segundos' => [
                'type' => 'DECIMAL',
                'constraint' => '10,3',
                'null' => TRUE
            ],
            'log_resumo' => [
                'type' => 'TEXT',
                'null' => TRUE,
                'comment' => 'Log resumido da execução'
            ],
            'observacoes' => [
                'type' => 'TEXT',
                'null' => TRUE,
                'comment' => 'Observações sobre a execução'
            ]
        ]);

        $this->dbforge->add_key('id_execucao', TRUE);
        $this->dbforge->add_key('data_inicio');
        $this->dbforge->add_key('status_execucao');
        $this->dbforge->create_table('cron_diana_execucao');

        // Criar índices adicionais
        $this->db->query('CREATE INDEX idx_status_data ON cron_diana_execucao (status_execucao, data_inicio)');
        $this->db->query('CREATE INDEX idx_data_inicio_desc ON cron_diana_execucao (data_inicio DESC)');
    }

    public function down()
    {
        $this->dbforge->drop_table('cron_diana_execucao');
    }
}
