<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Migration_Add_Index_Log_Wf_Atributos_Part_Number_Idx_Table_Log_Wf_Atributos extends CI_Migration {

    public function up()
    {
        if ($this->db->table_exists('log_wf_atributos')) {
            $this->db->query("CREATE INDEX log_wf_atributos_part_number_IDX ON log_wf_atributos (part_number, id_empresa, estabelecimento, criado_em)");
        }
    }
    
    public function down()
    {
        if ($this->db->table_exists('log_wf_atributos')) {
            $this->db->query("DROP INDEX `log_wf_atributos_part_number_IDX` ON `log_wf_atributos`");
        }
    }

}