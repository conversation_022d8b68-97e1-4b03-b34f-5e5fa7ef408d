<?php if ( ! defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Migration_Alter_Table_Usuario_Add_Column_Status extends CI_Migration {

    private $_table = "usuario";

    public function up()
    {
        if (!$this->db->field_exists('status', $this->_table)) {
            $field = array(
                'status' => array(
                    'type'           => 'TINYINT',
                    'constraint'     => 1,
                    'default'        => 1
                ),
            );

            if (!empty($field)) $this->dbforge->add_column($this->_table, $field);
        }
    }

    public function down()
    {
        if ($this->db->table_exists($this->_table)) {
            if ($this->db->field_exists('status', $this->_table)) $this->dbforge->drop_column($this->_table, 'status');
        }
    }

}
