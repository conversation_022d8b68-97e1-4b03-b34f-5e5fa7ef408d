<?php

class Migration_Alter_Table_Permissao_Cockpit_Consultores extends CI_Migration
{
    public function up()
    {
        $this->db->where('slug', 'visualizar_classificacao_consultores');
        $g_query = $this->db->get('permissao', 1);

        if (!$g_query->num_rows()) {
            $this->db->insert('permissao', array(
                'descricao' => 'Visualização de classificação de consultores',
                'slug'      => 'visualizar_classificacao_consultores',
                'pagina'      => '1'
            ));
        }

    }

    public function down()
    {
        $permissoes = array('visualizar_classificacao_consultores' );

        $this->db->where_in('slug', $permissoes);
        $query = $this->db->get('permissao');

        $id_permissao = array();

        foreach ($query->result() as $row) {
            $id_permissao[] = $row->id_permissao;
        }

        if (count($id_permissao)) {
            $this->db->where_in('id_permissao', $id_permissao);
            $this->db->delete('perfil_permissao');

            $this->db->where_in('id_permissao', $id_permissao);
            $this->db->delete('permissao');
        }
    }
}