<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Create_Anotacoes_Table extends CI_Migration {

    private $_table = "empresa";

    public function up()
    {
        $this->dbforge->add_field(array(
            'id' => array(
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => TRUE,
                'auto_increment' => TRUE
            ),
            'consultor_id' => array(
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => TRUE,
                'null' => FALSE,
            ),
            'data' => array(
                'type' => 'DATETIME',
                'null' => FALSE,
            ),
            'anotacao' => array(
                'type' => 'TEXT',
                'null' => FALSE,
            ),
            'part_number' => array(
                'type' => 'VARCHAR(255)',
                'null' => FALSE,
            ),
            'estabelecimento' => array(
                'type' => 'VARCHAR(50)',
                'null' => FALSE,
            ),
            'id_empresa' => array(
                'type' => 'BIGINT(20)',
                'null' => FALSE,
            ),
            'created_at' => array(
                'type' => 'DATETIME',
                'null' => TRUE,
            ),
            'updated_at' => array(
                'type' => 'DATETIME',
                'null' => TRUE,
            ),
        ));

        $this->dbforge->add_key('id', TRUE);
        $this->dbforge->create_table('anotacoes');



        if (!$this->db->field_exists('servicos_contratados', $this->_table)) {
            $fields = array(
                'servicos_contratados' => array(
                    'type' => 'TEXT'
                ),
                'acordos_premissas' => array(
                    'type' => 'TEXT'
                )
            );            

            $this->dbforge->add_column($this->_table, $fields);
        }
    }

    public function down()
    {
        $this->dbforge->drop_table('anotacoes');

        
        if ($this->db->field_exists('servicos_contratados', $this->_table)) {
            $this->dbforge->drop_column($this->_table, 'servicos_contratados');
        }
        if ($this->db->field_exists('acordos_premissas', $this->_table)) {
            $this->dbforge->drop_column($this->_table, 'acordos_premissas');
        }
    }
}