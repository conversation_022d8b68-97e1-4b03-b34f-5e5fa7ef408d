<?php

class Migration_Alter_Table_Item_Remove_On_Update_Data_Modificacao extends CI_Migration
{
    private $_table = "item";

    public function up()
    {
        if ($this->db->table_exists($this->_table)) {
            if ($this->db->field_exists('data_modificacao', $this->_table)) {
                // Remove ON UPDATE CURRENT_TIMESTAMP
                $this->db->query("ALTER TABLE `{$this->_table}` MODIFY COLUMN `data_modificacao` TIMESTAMP NULL DEFAULT NULL");
            }
        }
    }

    public function down()
    {
        if ($this->db->table_exists($this->_table)) {
            if ($this->db->field_exists('data_modificacao', $this->_table)) {
                // Restore ON UPDATE CURRENT_TIMESTAMP
                $this->db->query("ALTER TABLE `{$this->_table}` MODIFY COLUMN `data_modificacao` TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP");
            }
        }
    }
}