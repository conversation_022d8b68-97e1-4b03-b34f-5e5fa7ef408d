<?php

class Migration_Alter_Table_Empresa_Prioridades_Add_Column_Valor extends CI_Migration
{
    private $_table = "empresa_prioridades";

    public function up()
    {
        if ($this->db->table_exists($this->_table)) {

            if (!$this->db->field_exists('valor_padrao', $this->_table)) {
                $fields = array(
                    'valor_padrao' => array(
                        'type' => 'decimal(17,2)',
                        'null' => TRUE
                    ),
                );            

                $this->dbforge->add_column($this->_table, $fields);
            }

            if (!$this->db->field_exists('valor_quimico', $this->_table)) {
                $fields = array(
                    'valor_quimico' => array(
                        'type' => 'decimal(17,2)',
                        'null' => TRUE
                    ),
                );           

                $this->dbforge->add_column($this->_table, $fields);
            }

        }
    }

    public function down()
    {
        if ($this->db->table_exists($this->_table)) {

            if ($this->db->field_exists('valor_padrao', $this->_table))
            {
                $this->dbforge->drop_column($this->_table, 'valor_padrao');
            }

            if ($this->db->field_exists('valor_quimico', $this->_table))
            {
                $this->dbforge->drop_column($this->_table, 'valor_quimico');
            }

        }
    }
}