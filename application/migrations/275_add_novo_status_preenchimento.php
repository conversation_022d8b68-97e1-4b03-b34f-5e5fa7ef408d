<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Migration_Add_Novo_Status_Preenchimento extends CI_Migration {

    public function up()
    {
        $this->db->insert('status_attr', array(
            'id_status' => 5,
            'slug'      => 'ncm_sem_atributo'
        ));

        $this->db->query("ALTER TABLE cad_item ADD CONSTRAINT uk_cad_item_unico UNIQUE KEY (part_number, estabelecimento, id_empresa);");

    }

 

    public function down()
    {
 
        $this->db->where_in('slug', 'ncm_sem_atributo');
        $this->db->delete('status_attr');

        $this->db->query("ALTER TABLE cad_item 
        ADD CONSTRAINT uk_cad_item_unico 
        UNIQUE KEY (part_number, estabelecimento, id_empresa);");

        $this->db->query("DROP INDEX `uk_cad_item_unico` ON `cad_item`");

 
 
    }
 

}
