<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Migration_Add_Index_Item_Log_Part_Number_Idx_Table_Item_Log extends CI_Migration {

    public function up()
    {
        if ($this->db->table_exists('item_log')) {
            $this->db->query("CREATE INDEX item_log_part_number_IDX ON item_log (part_number, id_empresa, estabelecimento, criado_em)");
        }
    }
    
    public function down()
    {
        if ($this->db->table_exists('item_log')) {
            $this->db->query("DROP INDEX `item_log_part_number_IDX` ON `item_log`");
        }
    }

}