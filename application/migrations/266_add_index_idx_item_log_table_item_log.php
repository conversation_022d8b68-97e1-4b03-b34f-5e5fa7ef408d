<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Migration_Add_Index_Idx_Item_Log_Table_Item_Log extends CI_Migration {

    public function up()
    {
        if ($this->db->table_exists('item_log')) {
            $this->db->query("CREATE INDEX idx_item_log ON item_log (part_number, id_empresa, estabelecimento)");
        }
    }
    
    public function down()
    {
        if ($this->db->table_exists('item_log')) {
            $this->db->query("DROP INDEX `idx_item_log` ON `item_log`");
        }
    }

}
