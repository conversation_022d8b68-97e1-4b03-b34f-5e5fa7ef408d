<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Migration_Insert_Permissoes_Novas extends CI_Migration {

    private $_table = "permissao";

    public function up()
    {

        if (!$this->db->field_exists('permissao_exportacao', $this->_table)) {

            $fields = [
                'permissao_exportacao' => [
                    'type' => 'INT',
                    'constraint' => 1,
                    'default' => 0,
                    'null' => FALSE
                ]
            ];

            $this->dbforge->add_column($this->_table, $fields);
        }

        $this->novas_permissoes();

        $this->db->where('admin', 1);
        $query = $this->db->get('perfil');
        $perfis = $query->result();

        foreach ($perfis as $perfil)
        {

            $this->ativar_permissao($perfil, 'alterar_criticidade');
            $this->ativar_permissao($perfil, 'alterar_owner');
            $this->ativar_permissao($perfil, 'atribuir_grupo_tarifario_part_number');
            $this->ativar_permissao($perfil, 'criar_grupos_perguntas');
            $this->ativar_permissao($perfil, 'devolver_pn_ajuste_descricao');
            $this->ativar_permissao($perfil, 'excluir_perguntas_indevidas');
            $this->ativar_permissao($perfil, 'selecionar_grupo_perguntas');

            $this->ativar_permissao($perfil, 'classificar_itens_sem_pn');
            $this->ativar_permissao($perfil, 'inserir_informacoes_tecnicas');
            $this->ativar_permissao($perfil, 'inserir_itens_sem_pn');



            $this->ativar_permissao($perfil, 'exportar_atributos_cockpit');
            $this->ativar_permissao($perfil, 'exportar_controle_pendencias');
            $this->ativar_permissao($perfil, 'exportar_planilha_upload');
            $this->ativar_permissao($perfil, 'exportar_monitor_ex');
            $this->ativar_permissao($perfil, 'exportar_padrao_dados_tecnicos');
            $this->ativar_permissao($perfil, 'exportar_consultores');
            $this->ativar_permissao($perfil, 'exportar_mestre_itens');
            $this->ativar_permissao($perfil, 'exportar_lista_impacto');
            $this->ativar_permissao($perfil, 'exportar_multi_paises_homologacao');
            $this->ativar_permissao($perfil, 'exportar_relatorio_franquia');
            $this->ativar_permissao($perfil, 'exportar_multi_paises_dados_tecnicos');
            $this->ativar_permissao($perfil, 'exportar_consulta_diana');
            $this->ativar_permissao($perfil, 'exportar_itens_mestre_itens');
            $this->ativar_permissao($perfil, 'exportar_class_paises_mestre_itens');
            $this->ativar_permissao($perfil, 'exportar_padrao');
            $this->ativar_permissao($perfil, 'exportar_planilha_atributos');
            $this->ativar_permissao($perfil, 'exportar_usuarios');


            $this->verificar_permissao($perfil, 'homologar_atributos_workflow', 'homologar_atributos');
            $this->verificar_permissao($perfil, 'homologar_atributos_homologacao', 'homologar_atributos');
            $this->verificar_permissao($perfil, 'preencher_atributos_workflow', 'preencher_atributos');
            $this->verificar_permissao($perfil, 'preencher_atributos_dados_tecnicos', 'preencher_atributos');
            $this->verificar_permissao($perfil, 'preencher_atributos_homologacao', 'preencher_atributos');
            $this->verificar_permissao($perfil, 'movimentar_itens_workflow', 'movimentar_itens');

        }

        $this->db->where('slug', 'movimentar_itens');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $permissao_delete = [
                'movimentar_itens',
                'homologar_atributos',
                'preencher_atributos'
            ];

            $this->db->where_in('slug', $permissao_delete);
            $this->db->delete('permissao');
        }

    }

    protected function novas_permissoes()
    {
        $this->db->where('slug', 'classificar_itens_sem_pn');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Classificar Itens sem Part Number',
                'slug'      => 'classificar_itens_sem_pn',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'inserir_informacoes_tecnicas');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Inserir Informações Tecnicas',
                'slug'      => 'inserir_informacoes_tecnicas',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'inserir_itens_sem_pn');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Inserir Itens Sem Part Number (não codificado)',
                'slug'      => 'inserir_itens_sem_pn',
                'permissao_especial'      => '1'
            ));
        }


        $this->db->where('slug', 'exportar_atributos_cockpit');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Exportar atributos (Cockpit)',
                'slug'      => 'exportar_atributos_cockpit',
                'permissao_especial'        => '0',
                'permissao_exportacao'      => '1'
            ));
        }

        $this->db->where('slug', 'exportar_consultores');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Exportar consultores (Cockpit)',
                'slug'      => 'exportar_consultores',
                'permissao_especial'        => '0',
                'permissao_exportacao'      => '1'
            ));
        }

        $this->db->where('slug', 'exportar_controle_pendencias');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Exportar controle de pendências (Controle de pendências)',
                'slug'      => 'exportar_controle_pendencias',
                'permissao_especial'        => '0',
                'permissao_exportacao'      => '1'
            ));
        }

        
        $this->db->where('slug', 'exportar_planilha_upload');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Exportar planilha de upload (Homologação)',
                'slug'      => 'exportar_planilha_upload',
                'permissao_especial'        => '0',
                'permissao_exportacao'      => '1'
            ));
        }

        $this->db->where('slug', 'exportar_multi_paises_homologacao');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Exportar multi países (Homologação)',
                'slug'      => 'exportar_multi_paises_homologacao',
                'permissao_especial'        => '0',
                'permissao_exportacao'      => '1'
            ));
        }

        $this->db->where('slug', 'exportar_lista_impacto');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Exportar lista de impacto (Homologação)',
                'slug'      => 'exportar_lista_impacto',
                'permissao_especial'        => '0',
                'permissao_exportacao'      => '1'
            ));
        }

        $this->db->where('slug', 'exportar_padrao');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Exportar padrão (Homologação)',
                'slug'      => 'exportar_padrao',
                'permissao_especial'        => '0',
                'permissao_exportacao'      => '1'
            ));
        }

        $this->db->where('slug', 'exportar_planilha_atributos');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Exportar planilha de atributos (Homologação)',
                'slug'      => 'exportar_planilha_atributos',
                'permissao_especial'        => '0',
                'permissao_exportacao'      => '1'
            ));
        }

        $this->db->where('slug', 'exportar_monitor_ex');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Exportar monitor ex (Monitor EX)',
                'slug'      => 'exportar_monitor_ex',
                'permissao_especial'        => '0',
                'permissao_exportacao'      => '1'
            ));
        }

        
        $this->db->where('slug', 'exportar_padrao_dados_tecnicos');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Exportar padrão (Dados técnicos)',
                'slug'      => 'exportar_padrao_dados_tecnicos',
                'permissao_especial'        => '0',
                'permissao_exportacao'      => '1'
            ));
        }

        $this->db->where('slug', 'exportar_multi_paises_dados_tecnicos');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Exportar multi países (Dados técnicos)',
                'slug'      => 'exportar_multi_paises_dados_tecnicos',
                'permissao_especial'        => '0',
                'permissao_exportacao'      => '1'
            ));
        }

        $this->db->where('slug', 'exportar_mestre_itens');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Exportar mestre de itens (Mestre de Itens)',
                'slug'      => 'exportar_mestre_itens',
                'permissao_especial'        => '0',
                'permissao_exportacao'      => '1'
            ));
        }

        $this->db->where('slug', 'exportar_itens_mestre_itens');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Exportar itens (Mestre de itens)',
                'slug'      => 'exportar_itens_mestre_itens',
                'permissao_especial'        => '0',
                'permissao_exportacao'      => '1'
            ));
        }


        
        $this->db->where('slug', 'exportar_class_paises_mestre_itens');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Exportar class. países (Mestre de itens)',
                'slug'      => 'exportar_class_paises_mestre_itens',
                'permissao_especial'        => '0',
                'permissao_exportacao'      => '1'
            ));
        }


        $this->db->where('slug', 'exportar_relatorio_franquia');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Exportar relatório de franquia (Empresas)',
                'slug'      => 'exportar_relatorio_franquia',
                'permissao_especial'        => '0',
                'permissao_exportacao'      => '1'
            ));
        }


     
        $this->db->where('slug', 'exportar_consulta_diana');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Exportar consulta diana (Consulta Diana)',
                'slug'      => 'exportar_consulta_diana',
                'permissao_especial'        => '0',
                'permissao_exportacao'      => '1'
            ));
        }

        $this->db->where('slug', 'exportar_usuarios');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Exportar usuários (Usuários)',
                'slug'      => 'exportar_usuarios',
                'permissao_especial'        => '0',
                'permissao_exportacao'      => '1'
            ));
        }

    }

    protected function verificar_permissao($perfil, $slug, $slug_verificar)
    {
        $this->db->where('slug', $slug);
        $query = $this->db->get('permissao');
        $permissao = $query->row();


        $this->db->where('slug', $slug_verificar);
        $query_permissao = $this->db->get('permissao');
        $permissao_verificar = $query_permissao->row();

        if (!$query->num_rows())
        {
            $this->db->where('id_perfil', $perfil->id_perfil);
            $this->db->where('id_permissao', $permissao_verificar->id_permissao);
            $perfil_permissao = $this->db->get('perfil_permissao');

            if ($perfil_permissao->num_rows())
            {
                $this->db->where('id_perfil', $perfil->id_perfil);
                $this->db->where('id_permissao', $permissao->id_permissao);
                $perfil_permissao_add = $this->db->get('perfil_permissao');
                
                if (!$perfil_permissao_add->num_rows())
                {
                    $this->db->insert('perfil_permissao', array(
                        'id_perfil' => $perfil->id_perfil,
                        'id_permissao'  => $permissao->id_permissao
                    ));
                }
            }
        }
    }

    protected function ativar_permissao($perfil, $slug)
    {
        $this->db->where('slug', $slug);
        $query = $this->db->get('permissao');
        $permissao = $query->row();

        if ($query->num_rows())
        {
            $this->db->where('id_perfil', $perfil->id_perfil);
            $this->db->where('id_permissao', $permissao->id_permissao);
            $perfil_permissao = $this->db->get('perfil_permissao');

            if (!$perfil_permissao->num_rows())
            {
                $this->db->insert('perfil_permissao', array(
                    'id_perfil' => $perfil->id_perfil,
                    'id_permissao'  => $permissao->id_permissao
                ));
            }
        }
    }
 
    public function down()
    {

        $this->db->where('slug', 'exportar_usuarios');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'slug'      => 'exportar_usuarios'
            ));
        }

        $this->db->where('slug', 'exportar_consulta_diana');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'slug'      => 'exportar_consulta_diana'
            ));
        }

        $this->db->where('slug', 'exportar_relatorio_franquia');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'slug'      => 'exportar_relatorio_franquia'
            ));
        }

        $this->db->where('slug', 'exportar_class_paises_mestre_itens');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'slug'      => 'exportar_class_paises_mestre_itens'
            ));
        }

        $this->db->where('slug', 'exportar_itens_mestre_itens');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'slug'      => 'exportar_itens_mestre_itens'
            ));
        }

        $this->db->where('slug', 'exportar_mestre_itens');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'slug'      => 'exportar_mestre_itens'
            ));
        }

        $this->db->where('slug', 'exportar_multi_paises_dados_tecnicos');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'slug'      => 'exportar_multi_paises_dados_tecnicos'
            ));
        }

        $this->db->where('slug', 'exportar_padrao_dados_tecnicos');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'slug'      => 'exportar_padrao_dados_tecnicos'
            ));
        }


        $this->db->where('slug', 'exportar_monitor_ex');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'slug'      => 'exportar_monitor_ex'
            ));
        }

        $this->db->where('slug', 'exportar_planilha_atributos');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'slug'      => 'exportar_planilha_atributos'
            ));
        }


        $this->db->where('slug', 'exportar_padrao');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'slug'      => 'exportar_padrao'
            ));
        }


        $this->db->where('slug', 'exportar_lista_impacto');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'slug'      => 'exportar_lista_impacto'
            ));
        }

        
        $this->db->where('slug', 'exportar_multi_paises_homologacao');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'slug'      => 'exportar_multi_paises_homologacao'
            ));
        }


        $this->db->where('slug', 'exportar_planilha_upload');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'slug'      => 'exportar_planilha_upload'
            ));
        }

        $this->db->where('slug', 'exportar_controle_pendencias');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'slug'      => 'exportar_controle_pendencias'
            ));
        }

        $this->db->where('slug', 'exportar_consultores');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'slug'      => 'exportar_consultores'
            ));
        }


        $this->db->where('slug', 'exportar_atributos_cockpit');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'slug'      => 'exportar_atributos_cockpit'
            ));
        }

 

        $this->db->where('slug', 'alterar_criticidade');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Alterar Criticidade',
                'slug'      => 'alterar_criticidade',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'alterar_owner');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Alterar owner',
                'slug'      => 'alterar_owner',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'atribuir_grupo_tarifario_part_number');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Atribuir grupo tarifario aos Part Number',
                'slug'      => 'atribuir_grupo_tarifario_part_number',
                'permissao_especial'      => '1'
            ));
        }


        $this->db->where('slug', 'criar_grupos_perguntas');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Criar Grupos Perguntas',
                'slug'      => 'criar_grupos_perguntas',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'devolver_pn_ajuste_descricao');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Devolver PNs ajuste de desc. do setor',
                'slug'      => 'devolver_pn_ajuste_descricao',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'excluir_perguntas_indevidas');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Excluir Perguntas Indevidas',
                'slug'      => 'excluir_perguntas_indevidas',
                'permissao_especial'      => '1'
            ));
        }
 
        $this->db->where('slug', 'selecionar_grupo_perguntas');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Selecionar Grupo de Perguntas',
                'slug'      => 'selecionar_grupo_perguntas',
                'permissao_especial'      => '1'
            ));
        }
 
        $this->db->where('slug', 'homologar_atributos_workflow');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Homologar Atributos (Workflow)',
                'slug'      => 'homologar_atributos_workflow',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'homologar_atributos_homologacao');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Homologar Atributos (Homologação)',
                'slug'      => 'homologar_atributos_homologacao',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'preencher_atributos_homologacao');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Preencher Atributos (Homologação)',
                'slug'      => 'preencher_atributos_homologacao',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'preencher_atributos_workflow');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Preencher Atributos (Workflow)',
                'slug'      => 'preencher_atributos_workflow',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'preencher_atributos_dados_tecnicos');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Preencher Atributos (Dados Técnicos)',
                'slug'      => 'preencher_atributos_dados_tecnicos',
                'permissao_especial'      => '1'
            ));
        }

        $this->db->where('slug', 'movimentar_itens_workflow');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Movimentar Itens (Atributos - Workflow)',
                'slug'      => 'movimentar_itens_workflow',
                'permissao_especial'      => '1'
            ));
        }
    }

}
