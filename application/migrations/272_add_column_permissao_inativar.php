<?php

class Migration_Add_Column_Permissao_Inativar extends CI_Migration
{
    private $_table = "permissao";
    private $_table_item  = "item";
    public function up()
    {
        $this->db->where('slug', 'inativar_pns');
        $g_query = $this->db->get($this->_table, 1);

        if (!$g_query->num_rows())
        {
            $this->db->insert('permissao', array(
                'descricao' => 'Inativar PNs',
                'slug'      => 'inativar_pns',
                'permissao_especial'      => '1'
            ));
        }


        if (!$this->db->field_exists('status_anterior', $this->_table_item)) {
            $fields = array(
                'status_anterior' => array(
                    'type'           => 'INT',
                    'constraint'     => 2,
                    'null'           => TRUE
                )
            );            

            $this->dbforge->add_column($this->_table_item, $fields);
        }

        if (!$this->db->field_exists('motivo', $this->_table_item)) {
            $fields = array(
                'motivo' => array(
                    'type'           => 'LONGTEXT',
                    'null'           => TRUE
                )
            );            

            $this->dbforge->add_column($this->_table_item, $fields);
        }

 

    }

    public function down()
    {
        $this->db->where('slug', 'inativar_pns');
        $g_query = $this->db->get($this->_table, 1);

        if ($g_query->num_rows())
        {
            $this->db->delete($this->_table, array(
                'descricao' => 'Inativar PNs',
                'slug'      => 'inativar_pns',
                'permissao_especial'      => '1'
            ));
        }

        if ($this->db->field_exists('status_anterior', $this->_table_item)) {
            $this->dbforge->drop_column($this->_table_item, 'status_anterior');
        }

        if ($this->db->field_exists('motivo', $this->_table_item)) {
            $this->dbforge->drop_column($this->_table_item, 'motivo');
        }
    }
}
