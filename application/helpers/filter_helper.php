<?php
if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

if (!function_exists('get_filter_value')) {
    function get_filter_value($key, $model, $default = null)
    {
        $CI = &get_instance();
        $post_value = $CI->input->post($key);

        // Verificações mais robustas para valores inválidos
        if (
            $post_value === '' ||
            $post_value === null ||
            (is_array($post_value) && empty($post_value)) ||
            (is_array($post_value) && $post_value === array('')) ||
            (is_array($post_value) && count($post_value) === 1 && $post_value[0] === '') ||
            (is_array($post_value) && count($post_value) === 1 && $post_value[0] === '-1')
        ) {
            // Se POST é inválido, tentar obter do estado do modelo
            $state_value = $model->get_state("filter.$key");

            // Verificar se o valor do estado também é inválido
            if (
                $state_value === '' ||
                $state_value === null ||
                $state_value === false ||
                (is_array($state_value) && empty($state_value)) ||
                (is_array($state_value) && $state_value === array('')) ||
                (is_array($state_value) && count($state_value) === 1 && $state_value[0] === '') ||
                (is_array($state_value) && count($state_value) === 1 && $state_value[0] === '-1')
            ) {
                return null;
            }

            return $state_value;
        }

        // Se o valor do POST não for false (ou seja, existe e é válido), retornamos ele
        if ($post_value !== false) {
            return $post_value;
        }

        // Caso contrário, obtemos o valor do estado do modelo
        $state_value = $model->get_state("filter.$key");

        // Se o valor do estado for válido, retornamos ele
        if ($state_value !== false && $state_value !== null && $state_value !== '') {
            return $state_value;
        }

        // Se chegamos aqui, retornamos o valor padrão
        return $default;
    }
}

if (!function_exists('set_filter_value')) {
    function set_filter_value($key, $value, $model)
    {
        $CI = &get_instance();
        $model->set_state("filter.$key", $value);
    }
}

if (!function_exists('get_filter_order')) {
    function get_filter_order($model)
    {
        $CI = &get_instance();
        return $CI->input->post('order') ?: $model->get_state('filter.order');
    }
}

if (!function_exists('set_filter_order')) {
    function set_filter_order($value, $model)
    {
        $CI = &get_instance();
        $model->set_state('filter.order', $value);
    }
}

/* End of file filter_helper.php */
