<?php

if (!function_exists('generate_item_log')) {
    function generate_item_log($type, $item, $item_old = null)
    {
        $CI = &get_instance();
        $CI->load->model('item_log_model');
        $CI->load->model('item_model');

        $motivo = '';

        $item_entry = $CI->item_model->get_entry($item['part_number'], $item['id_empresa'], $item['estabelecimento']);

        $descricao = (!empty($item_entry->descricao) ? $item_entry->descricao : '<em>Não informado</em>');

        switch ($type) {
            case 'criacao':
            case 'exclusao':
                $motivo .= '<b>Descrição:</b> ' . $descricao;
                break;

            case 'atualizacao':
                $item_orig = array(
                    'descricao'                   => $item_old->descricao,
                    'part_number_similar'         => $item_old->part_number_similar,
                    'ncm'                         => $item_old->ncm,
                    'evento'                      => $item_old->evento,
                    'funcao'                      => $item_old->funcao,
                    'aplicacao'                   => $item_old->aplicacao,
                    'marca'                       => $item_old->marca,
                    'material_constitutivo'       => $item_old->material_constitutivo,
                    'memoria_classificacao'       => $item_old->memoria_classificacao,
                    'inf_adicionais'              => $item_old->inf_adicionais,
                    'tag'                         => $item_old->tag,
                    'descricao_proposta_completa' => $item_old->descricao_proposta_completa,
                    'cod_cest'                    => $item_old->cod_cest,
                    'peso'                        => $item_old->peso,
                    'prioridade'                  => !empty($item_old->Prioridade) ? $item_old->Prioridade : '',
                    'cod_owner'                   => $item_old->cod_owner,
                );

                $arr_label = array(
                    'descricao'                   => 'Descrição',
                    'part_number_similar'         => 'Part Number Similar',
                    'ncm'                         => 'NCM',
                    'evento'                      => 'Evento',
                    'funcao'                      => 'Função',
                    'aplicacao'                   => 'Aplicação',
                    'marca'                       => 'Marca',
                    'material_constitutivo'       => 'Material Constitutivo',
                    'memoria_classificacao'       => 'Memória de Classificação',
                    'inf_adicionais'              => 'Informações Adicionais',
                    'tag'                         => 'Tag',
                    'descricao_proposta_completa' => 'Descrição Proposta Completa',
                    'cod_cest'                    => 'Código CEST',
                    'peso'                        => 'Peso',
                    'prioridade'                  => 'Prioridade',
                    'cod_owner'                   => 'Código Owner',
                );

                foreach ($item_orig as $key => $val) {
                    if (isset($item_entry->{$key})) {
                        if ($val != $item_entry->{$key}) {
                            $val_html = (empty($val) ? '<em>Não informado</em>' : $val);
                            $item_html = (empty($item_entry->{$key}) ? '<em>Não informado</em>' : $item_entry->{$key});

                            $motivo .= '<strong>' . $arr_label[$key] . ' de: </strong>' . $val_html . ' <strong> para: </strong>' . $item_html . '<br>';
                        }
                    }
                }
                break;

            default:
                throw new Exception('Tipo de log informado não é valido');
        }
        if($type == 'criacao' && $item_entry->id_status == 13){
            $CI->load->library("Item/Status");
            
            $CI->status->set_status("em_analise");
            $CI->status->update_item($item['part_number'], $item['estabelecimento']);
        }
        if (!empty($motivo)) {
            $id_empresa = sess_user_company();
            $id_usuario = sess_user_id();

            return $CI->item_log_model->save(array(
                'part_number'     => $item_entry->part_number,
                'estabelecimento' => $item_entry->estabelecimento,
                'id_empresa'      => $id_empresa,
                'id_usuario'      => $id_usuario,
                'criado_em'       => date('Y-m-d H:i:s'),
                'titulo'          => $type,
                'motivo'          => $motivo
            ));
        }
    }
}
