<?php

if (!function_exists('is_logged')) {
	function is_logged()
	{
		$CI = &get_instance();
		//if(!$CI->session->userdata('has_sql_group')){
		$CI->db->query('SET sql_mode="NO_ENGINE_SUBSTITUTION";');
		$CI->db->query('SET SESSION group_concat_max_len = 10000;');
		
			//$CI->session->set_userdata('has_sql_group', TRUE);
		//}
		return $CI->session->userdata('is_logged');
	}
}

if (!function_exists('set_logon')) {
	function set_logon()
	{
		$CI = &get_instance();
		$CI->session->set_userdata('is_logged', TRUE);
		// if(!$CI->session->userdata('has_sql_group')){
		// 	$CI->db->query("SET sql_mode=(SELECT REPLACE(@@sql_mode,'ONLY_FULL_GROUP_BY',''));");
		// 	$CI->session->set_userdata('has_sql_group', TRUE);
		// }
		
	}
}

if (!function_exists('sess_user_id')) {
	function sess_user_id()
	{
		$CI = &get_instance();
		return $CI->session->userdata('user_id');
	}
}

if (!function_exists('sess_user_nome')) {
	function sess_user_nome()
	{
		$CI = &get_instance();
		return $CI->session->userdata('user_nome');
	}
}

if (!function_exists('sess_user_company')) {
	function sess_user_company()
	{

		$CI = &get_instance();

		if (!$CI->session->userdata('user_company')) {
			if (!isset($CI->usuario_model)) {
				$CI->load->model('usuario_model');
			}

			$company_id = $CI->usuario_model->get_user_company(sess_user_id());

			return $company_id;
		} else {
			return $CI->session->userdata('user_company');
		}
	}
}

if (!function_exists('is_demo_user')) {
	function is_demo_user()
	{
		if (sess_user_id() == 13) {
			return TRUE;
		}

		return FALSE;
	}
}

function has_role($role_name = 'sysadmin')
{
	static $roles = array();

	if (isset($roles[$role_name])) {
		return $roles[$role_name];
	} else {
		$roles = array();

		$roles[] = 'sysadmin';

		if ($role_name != 'sysadmin') {
			$roles[] = $role_name;
		}

		$user_id = sess_user_id();

		$CI 	 = &get_instance();
		$CI->db->join('perfil p', 'p.id_perfil = u.id_perfil', 'inner');
		$CI->db->join('perfil_permissao pp', 'p.id_perfil = pp.id_perfil', 'inner');
		$CI->db->join('permissao per', 'pp.id_permissao = per.id_permissao', 'inner');
		$CI->db->where('u.id_usuario', $user_id);
		$CI->db->where_in('per.slug', $roles);
		$query = $CI->db->get('usuario u', 1);

		if ($query->num_rows()) {
			$roles[$role_name] = TRUE;
			return TRUE;
		}

		$roles[$role_name] = FALSE;

		return FALSE;
	}
}

function customer_has_role($role_name, $user_id)
{
	$CI = &get_instance();
	$CI->db->join('perfil p', 'p.id_perfil = u.id_perfil', 'inner');
	$CI->db->join('perfil_permissao pp', 'p.id_perfil = pp.id_perfil', 'inner');
	$CI->db->join('permissao per', 'pp.id_permissao = per.id_permissao', 'inner');
	$CI->db->where('u.id_usuario', $user_id);
	$CI->db->where('per.slug', $role_name);
	$query = $CI->db->get('usuario u', 1);

	if ($query->num_rows() > 0) {
		return TRUE;
	}

	return FALSE;
}

function show_permission() {
	show_error('Você não possui permissão para acessar esta área!', 500, 'Permissão negada');
}

if (!function_exists('customer_can')) {
	function customer_can($role, $force_reload = false, $concat = true)
	{
		$CI = &get_instance();

		if ($concat)
			$role = 'vinculacao_' . $role;

		$array = [];

		if (!$CI->session->userdata('vinculacao') || $force_reload == true) {
			$CI->load->model('empresa_model');

			$resp = $CI->empresa_model->get_funcoes_adicionais($role, sess_user_company());

			$CI->session->set_userdata(['funcoes_adicionais' => $resp]);

			$CI->session->set_userdata(['vinculacao' => true]);

			$array = $resp;
		} else {
			$funcoes_adicionais = $CI->session->userdata('funcoes_adicionais');
			$array = $funcoes_adicionais;
		}

		return in_array($role, $array);
	}
}


function get_company_separator($id_empresa)
{
	static $separator = null;

	if (empty($id_empresa)) {
		$id_empresa = sess_user_company();
	}

	if (isset($separator) && $separator != null) {
		return $separator;
	} else {
		$CI = &get_instance();
		$CI->db->where('e.id_empresa', $id_empresa);
		
		$query = $CI->db->get('empresa e', 1);

		$row = $query->row();


		// $separator = $row->separador_pesquisa;

		// Refatoração para caso o separador não esteja cadastrado
		// para o update para o PHP 7.2
		if (isset($row->separador_pesquisa)) {
			$separator = $row->separador_pesquisa;
		} else {
			$separator = ' ';
		}

		return $separator;
		// return $row->separador_pesquisa == NULL ? ' ' : $row->separador_pesquisa;
	}
}

if (!function_exists('company_can')) {
	function company_can($slug, $funcoes_adicionais = array(), $id_empresa = NULL)
	{
		if (!is_string($slug)) {
			return false;
		}

		if (!empty($funcoes_adicionais)) {
			return in_array($slug, $funcoes_adicionais);
		}

		$CI = &get_instance();
		
		$CI->load->model('empresa_model');

		$empresa = $CI->empresa_model->get_entry(!empty($id_empresa) ? $id_empresa : sess_user_company());        
		
		$funcoes_adicionais = explode('|', $empresa->funcoes_adicionais);
		
		return in_array($slug, $funcoes_adicionais);
	}

	if (!function_exists('sess_user_owner')) {
		function sess_user_owner()
		{
			$CI = &get_instance();
			
			return $CI->session->userdata('owner_id');
			
		}
	}

	if (!function_exists('sess_user_owners')) {
		function sess_user_owners()
		{
			$CI = &get_instance();
			
			return $CI->session->userdata('owners_id');
			
		}
	}
}