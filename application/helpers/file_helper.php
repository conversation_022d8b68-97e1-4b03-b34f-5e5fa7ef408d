<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

/**
 * Verifica e corrige permissões de um diretório
 * 
 * @param string $dir Caminho do diretório
 * @param int $permissions Permissões a serem aplicadas (octal)
 * @return bool True se o diretório está pronto para uso, False caso contrário
 */
function ensure_directory($dir, $permissions = 0777) {
    // Verificar se o diretório existe
    if (!is_dir($dir)) {
        // Tentar criar o diretório
        if (!@mkdir($dir, $permissions, true)) {
            log_message('error', "Não foi possível criar o diretório: $dir");
            return false;
        }
    }
    
    // Verificar se o diretório é gravável
    if (!is_writable($dir)) {
        // Tentar alterar as permissões
        if (!@chmod($dir, $permissions)) {
            log_message('error', "Não foi possível alterar as permissões do diretório: $dir");
            return false;
        }
        
        // Verificar novamente se é gravável
        if (!is_writable($dir)) {
            log_message('error', "Diretório ainda não é gravável após chmod: $dir");
            return false;
        }
    }
    
    return true;
}

/**
 * Encontra um diretório gravável para salvar arquivos
 *
 * @param array $directories Lista de diretórios para tentar
 * @return string Caminho do diretório encontrado ou string vazia se nenhum for encontrado
 */
function find_writable_directory($directories = []) {
    if (empty($directories)) {
        $directories = [
            FCPATH . 'assets/reports/',
            FCPATH . 'assets/tmp/',
            FCPATH . 'assets/uploads/',
            sys_get_temp_dir() . '/'
        ];
    }
    
    foreach ($directories as $dir) {
        if (ensure_directory($dir)) {
            return $dir;
        }
    }
    
    return '';
}
