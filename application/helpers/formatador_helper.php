<?php

if (!function_exists('money_split')) {

    /**
     * Centraliza a separação do símbolo monetário e valor
     * @param    string $currency    Símbolo monetário (R$, USD, etc)
     * @param    string $value       Valor já formatado
     * @return   string $data        HTML formatado
     */
    function money_split($currency, $value) {

        $data = '';

        $data .= '<div class="pull-left">';
        $data .= $currency;
        $data .= '</div>';

        $data .= '<div class="pull-right">';
        $data .= $value;
        $data .= '</div>';

        $data .= '<div class="clearfix"></div>';

        return $data;

    }

}

if (!function_exists('format_number')) {

    /**
     * Padroniza a formatação de quantidade e valor monetário
     */
	function format_number($value, $params = array()) {
        $data = '';
        $decimals = 2;
        $dec_point = ',';
        $thousands_sep = '.';

        foreach ($params as $k=>$v) {
            switch ($k) {
                case 'decimals':
                    $decimals = $v;
                    break;
                case 'dec_point':
                    $dec_point = $v;
                    break;
                case 'thousands_sep':
                    $thousands_sep = $v;
                    break;
            }
        }

        $data = number_format($value, $decimals, $dec_point, $thousands_sep);

        return $data;
	}

}

if (!function_exists('formatar_cpf_cnpj'))
{
    function formatar_cpf_cnpj($campo, $formatado = true)
    {
        //retira formato
        $codigo_limpo = preg_replace("[' '-./ t]",'',$campo);
        $tamanho = strlen($codigo_limpo);

        if ($formatado) {
            // seleciona a máscara para cpf ou cnpj
            switch ($tamanho)
            {
                case 8: // CNPJ Raiz
                    $mascara = '##.###.###';
                    break;

                case 11: // CPF
                    $mascara = '###.###.###-##';
                    break;

                case 14: // CNPJ
                    $mascara = '##.###.###/####-##';
                    break;

                default:
                    return $campo;
            }

            $indice = -1;

            for ($i=0; $i < strlen($mascara); $i++) {
                if ($mascara[$i]=='#') $mascara[$i] = $codigo_limpo[++$indice];
            }

            //retorna o campo formatado
            $retorno = $mascara;
        } else {
            //se não quer formatado, retorna o campo limpo
            $retorno = $codigo_limpo;
        }

        return $retorno;
    }
}

if (!function_exists('remove_acentos')) {

    function remove_acentos($str) {
        $from = array("Á", "À", "Â", "Ä", "Ă", "Ā", "Ã", "Å", "Ą", "Æ", "Ć", "Ċ", "Ĉ", "Č", "Ç", "Ď", "Đ", "Ð", "É", "È", "Ė", "Ê", "Ë", "Ě", "Ē", "Ę", "Ə", "Ġ", "Ĝ", "Ğ", "Ģ", "á", "à", "â", "ä", "ă", "ā", "ã", "å", "ą", "æ", "ć", "ċ", "ĉ", "č", "ç", "ď", "đ", "ð", "é", "è", "ė", "ê", "ë", "ě", "ē", "ę", "ə", "ġ", "ĝ", "ğ", "ģ", "Ĥ", "Ħ", "I", "Í", "Ì", "İ", "Î", "Ï", "Ī", "Į", "Ĳ", "Ĵ", "Ķ", "Ļ", "Ł", "Ń", "Ň", "Ñ", "Ņ", "Ó", "Ò", "Ô", "Ö", "Õ", "Ő", "Ø", "Ơ", "Œ", "ĥ", "ħ", "ı", "í", "ì", "i", "î", "ï", "ī", "į", "ĳ", "ĵ", "ķ", "ļ", "ł", "ń", "ň", "ñ", "ņ", "ó", "ò", "ô", "ö", "õ", "ő", "ø", "ơ", "œ", "Ŕ", "Ř", "Ś", "Ŝ", "Š", "Ş", "Ť", "Ţ", "Þ", "Ú", "Ù", "Û", "Ü", "Ŭ", "Ū", "Ů", "Ų", "Ű", "Ư", "Ŵ", "Ý", "Ŷ", "Ÿ", "Ź", "Ż", "Ž", "ŕ", "ř", "ś", "ŝ", "š", "ş", "ß", "ť", "ţ", "þ", "ú", "ù", "û", "ü", "ŭ", "ū", "ů", "ų", "ű", "ư", "ŵ", "ý", "ŷ", "ÿ", "ź", "ż", "ž");
        $to = array("A", "A", "A", "A", "A", "A", "A", "A", "A", "AE", "C", "C", "C", "C", "C", "D", "D", "D", "E", "E", "E", "E", "E", "E", "E", "E", "G", "G", "G", "G", "G", "a", "a", "a", "a", "a", "a", "a", "a", "a", "ae", "c", "c", "c", "c", "c", "d", "d", "d", "e", "e", "e", "e", "e", "e", "e", "e", "g", "g", "g", "g", "g", "H", "H", "I", "I", "I", "I", "I", "I", "I", "I", "IJ", "J", "K", "L", "L", "N", "N", "N", "N", "O", "O", "O", "O", "O", "O", "O", "O", "CE", "h", "h", "i", "i", "i", "i", "i", "i", "i", "i", "ij", "j", "k", "l", "l", "n", "n", "n", "n", "o", "o", "o", "o", "o", "o", "o", "o", "o", "R", "R", "S", "S", "S", "S", "T", "T", "T", "U", "U", "U", "U", "U", "U", "U", "U", "U", "U", "W", "Y", "Y", "Y", "Z", "Z", "Z", "r", "r", "s", "s", "s", "s", "B", "t", "t", "b", "u", "u", "u", "u", "u", "u", "u", "u", "u", "u", "w", "y", "y", "y", "z", "z", "z");

        $str = str_replace($from, $to, $str);

        return $str;
    }

}

if (!function_exists('to_slug')) {

    function to_slug($str) {
        $str = preg_replace('/\s+/', ' ', $str); // remove whitespace
        $str = str_replace(' ', '_', $str);
        $str = mb_strtolower($str);
        $str = remove_acentos($str);

        return $str;
    }

}

if (!function_exists('format_byte')) {

    function format_byte($size) {

        # menor que 1kb = Byte
        if ($size < 1024) {
            return $size . ' B';
        }

        # menor que 1mb
        if ($size < 1048576) {
            return sprintf("%4.2f KB", $size/1024);
        }

        # menor que 1gb
        if ($size < 1073741824) {
            return sprintf("%4.2f MB", $size/1048576);
        }

        # menor que 1tb
        if ($size < 1099511627776) {
            return sprintf("%4.2f GB", $size/1073741824);
        }

        # maior que 1tb
        return sprintf("%4.2f TB", $size/1073741824);

    }

}

if (!function_exists('format_total_as_html')) {

    function format_total_as_table_row($total, $cols, $tipo = null)
    {
        $html = '';

        if (empty($total) || empty($cols)) {
            return '';
        }

        if ($tipo) {
            $html .= '<tr class="tr-' . $tipo . '">';
        } else {
            $html .= '<tr>';
        }

        foreach ($cols as $k => $v) {
            // se coluna está selecionada no filtro atual
            if ($v['selected'] === true) {
                if ($v['show_total'] === true) {
                    $html .= '<td class="tipo-valor">';
                    $html .= format_number($total->{$v['field']});

                    if ($tipo) {
                        $html .= '&nbsp;<i';
                        $html .= ' class="glyphicon glyphicon-info-sign"';
                        $html .= ' data-toggle="tooltip" data-placement="top" title="' . ucfirst($tipo) . '"';
                        $html .= '></i>';
                    }

                    $html .= '</td>';
                } else {
                    $html .= '<td></td>';
                }
            }
        }

        $html .= '</tr>';

        return $html;
    }

}

if (!function_exists('format_orderby')) {

    function format_orderby($filter_suffix, $field, $label, $class = null)
    {
        $html = '';
        $class_asc = '';
        $class_desc = '';
        $u = current_url();
        $u .=  '/?order_field_'  . $filter_suffix . '=' . $field;

        $CI = & get_instance();
        $filter_field = $CI->session->userdata('state.filter.order_field_' . $filter_suffix);
        $filter_order = $CI->session->userdata('state.filter.order_' . $filter_suffix);
        $filter_order = strtolower($filter_order);

        if ($filter_field == $field && $filter_order == 'asc') {
            $class_asc .= ' selected';
        }

        if ($filter_field == $field && $filter_order == 'desc') {
            $class_desc .= ' selected';
        }

        $html .= '<th class="orderby ' . $class . '">';
        $html .= '<a href="' . $u . '&order_'  . $filter_suffix . '=asc"';
        $html .= ' title="Ordem crescente" class="asc ' . $class_asc . '">';
        $html .= '<i class="glyphicon glyphicon-arrow-up"></i>';
        $html .= '</a>';
        $html .= '<a href="' . $u . '&order_'  . $filter_suffix . '=desc"';
        $html .= ' title="Ordem decrescente" class="desc ' . $class_desc . '">';
        $html .= '<i class="glyphicon glyphicon-arrow-down"></i>';
        $html .= '</a>';
        $html .= '<span>' . $label . '</span>';
        $html .= '</th>';

        return $html;
    }
}

// Recebe valor alfanumérico: A .. B .. AA .. BA .. a .. b
// Retorna o equivalente em int
if (!function_exists('alpha2num')) {
    function alpha2num($a) {
        $l = strlen($a);
        $n = 0;
        for ($i = 0; $i < $l; $i++) {
            $n = $n * 26 + ord($a[$i]) - 0x40;
        }

        return $n - 1;
    }
}

// Recebe int
// Retorna o valor alfanumérico: A, B .. AA
if (!function_exists('num2alpha')) {
    function num2alpha($n) {
        for ($r = ""; $n >= 0; $n = intval($n / 26) - 1) {
            $r = chr($n % 26 + 0x41) . $r;
        }

        return $r;
    }
}

if (!function_exists('format_peso')) {
    function format_peso($peso) {
        $peso = str_replace('+', '', $peso);
        
        if (!empty(strpos($peso, ","))) {
            $peso = str_replace(',', '.', str_replace('.', '', $peso));
        } elseif (!empty(strpos($peso, "."))) {
            if (!empty(strpos($peso, ","))) {
                $peso = str_replace(',', '', $peso);
            }
        }

        return number_format(round(floatval(
            $peso
        ), 3, PHP_ROUND_HALF_UP), 3, ',', '');
    }
}

if (!function_exists('formatar_texto')) {
    function formatar_texto($can, $text) {
        return $can ? mb_strtoupper(remove_acentos($text)) : $text;
    }
}

if (!function_exists('excel_date')) {
    function excel_date($value) {
        $php_excel_date = $value - 25569;

        return strtotime("+{$php_excel_date} days", mktime(0,0,0,1,1,1970));
    }
}