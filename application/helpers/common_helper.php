<?php

if (!function_exists('sql_format')) {
    function sql_format($query)
    {
        $keywords = array("select", "from", "where", "order by", "group by", "insert into", "update");
        foreach ($keywords as $keyword) {
            if (preg_match("/($keyword *)/i", $query, $matches)) {
                $query = str_replace($matches[1], "\n" . strtoupper($matches[1]) . "\n  ", $query);
            }
        }
        return $query;
    }
}

if (!function_exists('display_alert_message')) {
    function display_alert_message()
    {
        $CI = &get_instance();
        if ($CI->session->flashdata('message_on_render')) {
            return $CI->session->flashdata('message_on_render');
        }
    }
}

if (!function_exists('response_json')) {
    function response_json($data, $status_code = 200)
    {
        set_status_header($status_code);
        $data = json_encode($data);
        $CI = &get_instance();
        return $CI->output->set_content_type('application/json')->set_output($data);
    }
}

if (!function_exists('describe_class')) {
    function describe_class($className)
    {
        if ($className == 'int') return NULL;

        $reflectionClass = new ReflectionClass($className);

        $class = new $className();
        $class_vars = get_class_vars($className);
        $objectDescription = '';

        if (preg_match('/@description (.*)/i', $reflectionClass->getDocComment(), $matches)) {
            $objectDescription = $matches[1];
        }


        foreach ($class_vars as $name => $value) {
            $propertyInfo = $reflectionClass->getProperty($name);
            $propertyDescAux = $propertyInfo->getDocComment();
            $propertyDesc = '';

            if (preg_match('/@param (.*)/i', $propertyDescAux, $matches)) {
                $propertyDesc = $matches[1];
            }

            if (is_object($class->$name)) {
                $type = 'tns:' . get_class($class->$name);
            } else {
                $type = "xsd:" . (getType($class->$name) == "integer" ? "int" : getType($class->$name));
            }

            $vars[$name] = array('name' => $name, 'type' => $type, 'documentation' => $propertyDesc);
        }

        $return = array($className, 'complexType', 'struct', 'all', '', $vars, array(), '', $objectDescription);

        return $return;
    }
}

if (!function_exists('format_log_as_html')) {
    function format_log_as_html($log)
    {
        $html = '';

        if (!is_array($log)) {
            show_error('Não foi possível processar o log', 500, 'Erro');
        }

        $html .= '<ul>';

        foreach ($log as $k => $v) {
            if (is_array($v)) {
                if (!empty($v['estabelecimento'])) {
                    $html .= '<li>' . $v['part_number'] . ' - ' . $v['estabelecimento'] . '</li>';
                } else {
                    $html .= '<li>' . $v['part_number'] . '</li>';
                }
            } else {
                $html .= '<li>' . $v . '</li>';
            }
        }

        $html .= '</ul>';

        return $html;
    }
}

if (!function_exists('clean_str')) {
    function clean_str($str, $strip_accented_chars = false)
    {
        /*  Remove os caracteres DEC0 -> DEC31 */
        $str = preg_replace('/[\x00-\x1F]/', '', $str);

        if ($strip_accented_chars == true) {
            $str = filter_var(
                $str,
                FILTER_SANITIZE_STRING,
                FILTER_FLAG_STRIP_LOW
            );

            $str = preg_replace('/[\x80-\xFF]/', '', $str);
        }

        /* Realiza a limpeza de caracteres do Excel (_x000D_) */
        $str = preg_replace('/_x[0-9a-f]+_/i', '', $str);

        /* Retorna a string limpa para o sistema */
        return $str;
    }
}

if (!function_exists('num2alpha')) {
    function num2alpha($num)
    {
        return chr(substr("000" . ($num + 65), -3));
    }
}

if (!function_exists('array_unique_multidimensional')) {
    function array_unique_multidimensional($array)
    {
        $serialized = array_map('serialize', $array);
        $unique     = array_unique($serialized);

        return array_intersect_key($array, $unique);
    }
}


if (!function_exists('text_tooltip_alt')) {
    function text_tooltip_alt($text, $tooltip = NULL)
    {
        $final  = '';

        if (!empty($tooltip)) {
            $final .= '<a href="javascript: void(0)" data-toggle="tooltip" data-placement="top" title="' . $tooltip . '">';
        }

        $final .= $text;

        if (!empty($tooltip)) {
            $final .= '</a>';
        }

        return $final;
    }
}

if (!function_exists('remove_special_char_str')) {
    function remove_special_char_str($value)
    {
        $result  = preg_replace('/[^a-zA-Z0-9_ -]/s', '', $value);
        return $result;
    }
}

if (!function_exists('calc_perc')) 
{
    function calc_perc($value, $total)
    {
        return $total !== 0 ? round(($value * 100 / $total), 2) : 100;
    }
}

if (!function_exists('get_axios_post')) 
{
    function get_axios_post($object = false) 
    {
        return json_decode(file_get_contents('php://input'), !$object);
    }
}

/**
 * Pluck an array of values from an array. (Only for PHP 5.3+)
 *
 * @param  $array - data
 * @param  $key - value you want to pluck from array
 *
 * @return array array only with key data
 */
function array_pluck($array, $key) {
    return array_map(function($v) use ($key) {
        return is_object($v) ? $v->$key : $v[$key];
    }, $array);
}