<?php

class Monitor_ex_apuracao_ganhos extends MY_Controller
{

    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }

        $this->load->model('monitor_ex_apuracao_ganhos_model');

        if ($this->input->is_set('reset_filters')) {
            $this->monitor_ex_apuracao_ganhos_model->clear_states();
        }

        $this->load->library('breadcrumbs');

        if (!customer_can('ii')) {
            show_error('MONITOR DE EX DESABILITADO PARA EMPRESA ATUAL (Entre em contato com os consultores Becomex).', 401);
        }

        if (!has_role('monitor_ex_apuracao')) {
            show_permission();
        }
    }

    public function index($page = 0)
    {
        $this->include_js(
            array(
                'bootstrap-select/bootstrap-select.js',
                'b3-datetimepicker.min.js?v=3.1.1',
                'jquery.maskedinput.js',
                'highcharts/highcharts.js',
                'highcharts/highcharts-3d.js',
                'highcharts/modules/exporting.js'
            )
        );

        $this->include_css(
            array(
                'bootstrap-select/bootstrap-select.css',
                'monitor_ex.css',
                'b3-datetimepicker.min.css'
            )
        );

        if ($post = $this->input->post()) {
            $post['cnpj'] = isset($post['cnpj']) ? $post['cnpj'] : '';
            if(!empty($post['cnpj'])) {
                $post['cnpj'] = formatar_cpf_cnpj($post['cnpj']);                    
                $this->monitor_ex_apuracao_ganhos_model->set_state('filter.cnpj', $post['cnpj']);
            }

            $post['dt_inicio'] = isset($post['dt_inicio']) ? $post['dt_inicio'] : '';
            if(!empty($post['dt_inicio'])) {
                $post['dt_inicio'] = date("d-M-Y", strtotime(str_replace("/", "-", $post['dt_inicio'])));
                $this->monitor_ex_apuracao_ganhos_model->set_state('filter.dt_inicio', $post['dt_inicio']);
            }

            $post['dt_fim'] = isset($post['dt_fim']) ? $post['dt_fim'] : '';
            if(!empty($post['dt_fim'])) {
                $post['dt_fim'] = date("d-M-Y", strtotime(str_replace("/", "-", $post['dt_fim'])));
                $this->monitor_ex_apuracao_ganhos_model->set_state('filter.dt_fim', $post['dt_fim']);
            }

            $post['periodo'] = isset($post['periodo']) ? $post['periodo'] : array();
            $this->monitor_ex_apuracao_ganhos_model->set_state('filter.periodo', $post['periodo']);

            $post['tipo_beneficio'] = isset($post['tipo_beneficio']) ? $post['tipo_beneficio'] : array();
            $this->monitor_ex_apuracao_ganhos_model->set_state('filter.tipo_beneficio', $post['tipo_beneficio']);

            $post['faturavel'] = isset($post['faturavel']) ? $post['faturavel'] : '';           
            $this->monitor_ex_apuracao_ganhos_model->set_state('filter.faturavel', $post['faturavel']);

            if ($post['submit'] == 'export') {
                $dados = $this->monitor_ex_apuracao_ganhos_model->get_entries();
                $this->export($dados);
            }
        }

        $data['economia_descarte'] = $this->monitor_ex_apuracao_ganhos_model->get_entries_economia_descarte();
        $data['imposto_nao_recolhido_dispendio'] = $this->monitor_ex_apuracao_ganhos_model->get_entries_imposto_nao_recolhido_dispendio();
        $data['economia_tipo_de_beneficio'] = $this->monitor_ex_apuracao_ganhos_model->get_entries_economia_tipo_de_beneficio();
        $data['economia_cliente_parcela_becomex'] = $this->monitor_ex_apuracao_ganhos_model->get_entries_economia_cliente_parcela_becomex();
        $data['periodos'] = $this->monitor_ex_apuracao_ganhos_model->get_periodos();

        $periodos_ano_atual = [];
        foreach ($data['periodos'] as $periodo) {
            if ($periodo->YYYY == date('Y')) {
                $periodos_ano_atual[] = $periodo->PERIODO;
            }
        }

        if (!$this->input->post('periodo')) {
            $data['periodos_ano_atual'] = $periodos_ano_atual;
            $this->monitor_ex_apuracao_ganhos_model->set_state('filter.periodo', $periodos_ano_atual);
        }

        $this->render('monitor_ex_apuracao_ganhos/default', $data);
    }

    public function export($itens)
    {
        ini_set('memory_limit', '2048M');
        
        $this->load->helper('formatador');

        if (!empty($itens))
        {
            $this->load->library('Excel');

            $this->excel->setActiveSheetIndex(0);
            $this->excel->getActiveSheet()->setTitle('Apuracao de ganhos');

            $this->excel->getActiveSheet()->setCellValue('A1', 'FATURAVEL');
            $this->excel->getActiveSheet()->setCellValue('B1', 'CNPJ');
            $this->excel->getActiveSheet()->setCellValue('C1', 'FORNECEDOR');
            $this->excel->getActiveSheet()->setCellValue('D1', 'DI_AD_SEQ');
            $this->excel->getActiveSheet()->setCellValue('E1', 'SEQ_PRODUTO');
            $this->excel->getActiveSheet()->setCellValue('F1', 'LOG_FATURADA');
            $this->excel->getActiveSheet()->setCellValue('G1', 'DAT_DI');
            $this->excel->getActiveSheet()->setCellValue('H1', 'DAT_DESEMBARACO');
            $this->excel->getActiveSheet()->setCellValue('I1', 'PERIODO');
            $this->excel->getActiveSheet()->setCellValue('J1', 'PART_NUMBER');
            $this->excel->getActiveSheet()->setCellValue('K1', 'DES_PART_NUMBER');
            $this->excel->getActiveSheet()->setCellValue('L1', 'NCM');
            $this->excel->getActiveSheet()->setCellValue('M1', 'DIVERGENCIA_NCM');
            $this->excel->getActiveSheet()->setCellValue('N1', 'TIPO_EX');
            $this->excel->getActiveSheet()->setCellValue('O1', 'COD_EX');
            $this->excel->getActiveSheet()->setCellValue('P1', 'COD_EX_DI');
            $this->excel->getActiveSheet()->setCellValue('Q1', 'COD_FUND_LEGAL');
            $this->excel->getActiveSheet()->setCellValue('R1', 'DES_FUND_LEGAL');
            $this->excel->getActiveSheet()->setCellValue('S1', 'LOG_BK');
            $this->excel->getActiveSheet()->setCellValue('T1', 'LOG_BIT');
            $this->excel->getActiveSheet()->setCellValue('U1', 'LOG_AUTOMOTICO');
            $this->excel->getActiveSheet()->setCellValue('V1', 'VAL_BASE_CALC');
            $this->excel->getActiveSheet()->setCellValue('W1', 'PCT_ALIQ_DI');
            $this->excel->getActiveSheet()->setCellValue('X1', 'VAL_IMPOSTO_DI');
            $this->excel->getActiveSheet()->setCellValue('Y1', 'PCT_ALIQ_PN');
            $this->excel->getActiveSheet()->setCellValue('Z1', 'VAL_IMPOSTO_PN');
            $this->excel->getActiveSheet()->setCellValue('AA1', 'PCT_ALIQ_TEC');
            $this->excel->getActiveSheet()->setCellValue('AB1', 'VAL_IMPOSTO_TEC');
            $this->excel->getActiveSheet()->setCellValue('AC1', 'PCT_ALIQ_EX');
            $this->excel->getActiveSheet()->setCellValue('AD1', 'VAL_IMPOSTO_EX');
            $this->excel->getActiveSheet()->setCellValue('AE1', 'TAX_DOLAR');
            $this->excel->getActiveSheet()->setCellValue('AF1', 'CANAL');
            $this->excel->getActiveSheet()->setCellValue('AG1', 'REGIME');
            $this->excel->getActiveSheet()->setCellValue('AH1', 'COBERTURA_CAMBIAL');
            $this->excel->getActiveSheet()->setCellValue('AI1', 'NOME_APLICACAO');
            $this->excel->getActiveSheet()->setCellValue('AJ1', 'VAL_GANHO_ESTIMADO');
            $this->excel->getActiveSheet()->setCellValue('AK1', 'VAL_SALDO_NAO_EFETIVADO');
            $this->excel->getActiveSheet()->setCellValue('AL1', 'VAL_IMPOSTO_NAO_PAGO');
            $this->excel->getActiveSheet()->setCellValue('AM1', 'VAL_DISPENDIO');
            $this->excel->getActiveSheet()->setCellValue('AN1', 'VAL_IMPOSTO_DI_MAIOR');
            $this->excel->getActiveSheet()->setCellValue('AO1', 'VAL_FOB_USD');
            $this->excel->getActiveSheet()->setCellValue('AP1', 'QTD_UMC');
            $this->excel->getActiveSheet()->setCellValue('AQ1', 'UMC');
            $this->excel->getActiveSheet()->setCellValue('AR1', 'COD_ACORDO_ALADI');
            $this->excel->getActiveSheet()->setCellValue('AS1', 'NOM_ACORDO_ALADI');
            $this->excel->getActiveSheet()->setCellValue('AT1', 'DAT_GANHO_INI');
            $this->excel->getActiveSheet()->setCellValue('AU1', 'DAT_GANHO_FIN');
            $this->excel->getActiveSheet()->setCellValue('AV1', 'NOME_BENEFICIO');


            $this->excel->getActiveSheet()->getStyle('A1:AV1')->getFont()->setBold(true);
            $this->excel->getActiveSheet()->getStyle('A1:AV1')->getFill()
                    ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
                    ->getStartColor()->setARGB('F9FF00');
            $this->excel->getActiveSheet()->getStyle('A1:AV1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

            $alfabeto = range('A', 'Z');
            foreach($alfabeto as $letra_1) {
                if ($letra_1 == 'C' || 
                    $letra_1 == 'K' || 
                    $letra_1 == 'R' || 
                    $letra_1 == 'E' || 
                    $letra_1 == 'I' || 
                    $letra_1 == 'J' ||
                    $letra_1 == 'U'
                ) {
                    $this->excel->getActiveSheet()->getColumnDimension($letra_1)->setAutoSize(false);
                } else {
                    $this->excel->getActiveSheet()->getColumnDimension($letra_1)->setAutoSize(true);
                }    
                if ($letra_1 == 'A') {
                    foreach ($alfabeto as $letra_2) {
                        if($letra_2 != 'Y') {
                            if ($letra_2 == 'H' ||
                                $letra_2 == 'I' ||
                                $letra_2 == 'J' ||
                                $letra_2 == 'L' || 
                                $letra_2 == 'S' 
                            ) {
                                $this->excel->getActiveSheet()->getColumnDimension($letra_1 . $letra_2)->setAutoSize(false);    
                            } else {
                                $this->excel->getActiveSheet()->getColumnDimension($letra_1 . $letra_2)->setAutoSize(true);
                            }
                        }
                    }
                }
                $this->excel->getActiveSheet()->getColumnDimension('C')->setWidth('20');
                $this->excel->getActiveSheet()->getColumnDimension('J')->setWidth('20');
                $this->excel->getActiveSheet()->getColumnDimension('E')->setWidth('20');
                $this->excel->getActiveSheet()->getColumnDimension('I')->setWidth('30');
                $this->excel->getActiveSheet()->getColumnDimension('K')->setWidth('20');
                $this->excel->getActiveSheet()->getColumnDimension('R')->setWidth('20');
                $this->excel->getActiveSheet()->getColumnDimension('U')->setWidth('20');
                $this->excel->getActiveSheet()->getColumnDimension('AH')->setWidth('30');
                $this->excel->getActiveSheet()->getColumnDimension('AI')->setWidth('30');
                $this->excel->getActiveSheet()->getColumnDimension('AJ')->setWidth('30');
                $this->excel->getActiveSheet()->getColumnDimension('AL')->setWidth('30');
                $this->excel->getActiveSheet()->getColumnDimension('AS')->setWidth('30');
            }

            foreach ($itens as $key => $item) {
                $i = $key+2;

                $this->excel->getActiveSheet()->getCell($cell = 'A' . $i)->setValue($item->FATURAVEL);
                $this->excel->getActiveSheet()->getCell($cell = 'B' . $i)->setValue($item->CNPJ);
                $this->excel->getActiveSheet()->getCell($cell = 'C' . $i)->setValue($item->FORNECEDOR);
                $this->excel->getActiveSheet()->getCell($cell = 'D' . $i)->setValue($item->DI_AD_SEQ);
                $this->excel->getActiveSheet()->getCell($cell = 'E' . $i)->setValue($item->SEQ_PRODUTO);
                $this->excel->getActiveSheet()->getCell($cell = 'F' . $i)->setValue($item->LOG_FATURADA);
                $this->excel->getActiveSheet()->getCell($cell = 'G' . $i)->setValue(date("d/m/Y", strtotime($item->DAT_DI)));
                $this->excel->getActiveSheet()->getCell($cell = 'H' . $i)->setValue(date("d/m/Y", strtotime($item->DAT_DESEMBARACO)));
                $this->excel->getActiveSheet()->getCell($cell = 'I' . $i)->setValue($item->PERIODO);
                $this->excel->getActiveSheet()->getCell($cell = 'J' . $i)->setValue($item->PART_NUMBER);
                $this->excel->getActiveSheet()->getCell($cell = 'K' . $i)->setValue($item->DES_PART_NUMBER);
                $this->excel->getActiveSheet()->getCell($cell = 'L' . $i)->setValue($item->NCM);
                $this->excel->getActiveSheet()->getCell($cell = 'M' . $i)->setValue($item->DIVERGENCIA_NCM);
                $this->excel->getActiveSheet()->getCell($cell = 'N' . $i)->setValue($item->TIPO_EX);
                $this->excel->getActiveSheet()->getCell($cell = 'O' . $i)->setValue($item->COD_EX);
                $this->excel->getActiveSheet()->getCell($cell = 'P' . $i)->setValue($item->COD_EX_DI);
                $this->excel->getActiveSheet()->getCell($cell = 'Q' . $i)->setValue($item->COD_FUND_LEGAL);
                $this->excel->getActiveSheet()->getCell($cell = 'R' . $i)->setValue($item->DES_FUND_LEGAL);
                $this->excel->getActiveSheet()->getCell($cell = 'S' . $i)->setValue($item->LOG_BK);
                $this->excel->getActiveSheet()->getCell($cell = 'T' . $i)->setValue($item->LOG_BIT);
                $this->excel->getActiveSheet()->getCell($cell = 'U' . $i)->setValue($item->LOG_AUTOMOTICO);
                $this->excel->getActiveSheet()->getCell($cell = 'V' . $i)->setValue(format_number($item->VAL_BASE_CALC));
                $this->excel->getActiveSheet()->getCell($cell = 'W' . $i)->setValue($item->PCT_ALIQ_DI);
                $this->excel->getActiveSheet()->getCell($cell = 'X' . $i)->setValue(format_number($item->VAL_IMPOSTO_DI));
                $this->excel->getActiveSheet()->getCell($cell = 'Y'. $i)->setValue($item->PCT_ALIQ_PN);
                $this->excel->getActiveSheet()->getCell($cell = 'Z'. $i)->setValue(format_number($item->VAL_IMPOSTO_PN));
                $this->excel->getActiveSheet()->getCell($cell = 'AA'. $i)->setValue($item->PCT_ALIQ_TEC);
                $this->excel->getActiveSheet()->getCell($cell = 'AB'. $i)->setValue(format_number($item->VAL_IMPOSTO_TEC));
                $this->excel->getActiveSheet()->getCell($cell = 'AC'. $i)->setValue($item->PCT_ALIQ_EX);
                $this->excel->getActiveSheet()->getCell($cell = 'AD'. $i)->setValue(format_number($item->VAL_IMPOSTO_EX));
                $this->excel->getActiveSheet()->getCell($cell = 'AE'. $i)->setValue(format_number($item->TAX_DOLAR));
                $this->excel->getActiveSheet()->getCell($cell = 'AF'. $i)->setValue($item->CANAL);
                $this->excel->getActiveSheet()->getCell($cell = 'AG'. $i)->setValue($item->REGIME);
                $this->excel->getActiveSheet()->getCell($cell = 'AH'. $i)->setValue($item->COBERTURA_CAMBIAL);
                $this->excel->getActiveSheet()->getCell($cell = 'AI'. $i)->setValue($item->NOME_APLICACAO);
                $this->excel->getActiveSheet()->getCell($cell = 'AJ'. $i)->setValue(format_number($item->VAL_GANHO_ESTIMADO));
                $this->excel->getActiveSheet()->getCell($cell = 'AK'. $i)->setValue(format_number($item->VAL_SALDO_NAO_EFETIVADO));
                $this->excel->getActiveSheet()->getCell($cell = 'AL'. $i)->setValue(format_number($item->VAL_IMPOSTO_NAO_PAGO));
                $this->excel->getActiveSheet()->getCell($cell = 'AM'. $i)->setValue(format_number($item->VAL_DISPENDIO));
                $this->excel->getActiveSheet()->getCell($cell = 'AN'. $i)->setValue(format_number($item->VAL_IMPOSTO_DI_MAIOR));
                $this->excel->getActiveSheet()->getCell($cell = 'AO'. $i)->setValue(format_number($item->VAL_FOB_USD));
                $this->excel->getActiveSheet()->getCell($cell = 'AP'. $i)->setValue($item->QTD_UMC);
                $this->excel->getActiveSheet()->getCell($cell = 'AQ'. $i)->setValue($item->UMC);
                $this->excel->getActiveSheet()->getCell($cell = 'AR'. $i)->setValue($item->COD_ACORDO_ALADI);
                $this->excel->getActiveSheet()->getCell($cell = 'AS'. $i)->setValue($item->NOM_ACORDO_ALADI);
                $this->excel->getActiveSheet()->getCell($cell = 'AT'. $i)->setValue(date("d/m/Y", strtotime($item->DAT_GANHO_INI)));
                $this->excel->getActiveSheet()->getCell($cell = 'AU'. $i)->setValue(date("d/m/Y", strtotime($item->DAT_GANHO_FIN)));
                $this->excel->getActiveSheet()->getCell($cell = 'AV'. $i)->setValue($item->NOME_BENEFICIO);
            }

            $filename = 'apuracao_de_ganhos_'.date('Y-m-d_H-i-s').'.xlsx';
            $objWriter = PHPExcel_IOFactory::createWriter($this->excel, 'Excel2007');

            header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment;filename="'.$filename.'"');
            header('Cache-Control: max-age=0');

            $objWriter->save('php://output');
            exit();
        }
    }
}