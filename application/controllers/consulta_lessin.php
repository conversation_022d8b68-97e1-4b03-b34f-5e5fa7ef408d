<?php

class Consulta_lessin extends MY_Controller {
    public function __construct()
    {
        parent::__construct();

        $this->load->library('breadcrumbs');

        if (! is_logged()) {
            redirect('login');
        }

        if (!company_can("lessin")) {
            show_permission();
        }

        $this->load->library("Item/LessinUseCases");
    }

    public function index()
    {
        $data = array();

        $this->load->library('pagination');

        $data['consulta'] = TRUE;

        if (!empty($this->input->get("id")) || !empty($this->input->get("ncm"))) {
            $this->lessinusecases->apply_filters($this->input->get());
        }

        $per_page = $this->input->get('per_page');
        $offset = $per_page;
        $limit = 15;
        
        $query_string = !empty($this->input->get()) ? "?" . http_build_query(array("id" => $this->input->get("id"), "ncm" => $this->input->get("ncm"))) : "";
        $data['items'] = $this->lessinusecases->get_items($limit, ($per_page > 0 ? $per_page - 1 : 0) * $limit);
        $data['query_string'] = $query_string;
        
        $result = $this->lessinusecases->get_total_items();

        $this->pagination->initialize(array(
            'base_url' => base_url("consulta_lessin/index{$query_string}"),
            'use_page_numbers' => TRUE,
            'per_page' => $limit,
            'page_query_string' => TRUE,
            'total_rows' => $result->total
        ));
        
        $data['pagination'] = $this->pagination->create_links();

        $this->title = "Consulta Lessin";
        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Consulta Lessin', '/consulta_lessin/');

        $this->include_js(array(
            'bootstrap-select/bootstrap-select.js',
        ));

        $this->include_css(array(
            'bootstrap-select/bootstrap-select.css'
        ));

        $this->render('lessin/default', $data);
    }

    public function unapply_filters()
    {
        $this->lessinusecases->unapply_filters();

        redirect("consulta_lessin");
    }

    public function get_related_items()
    {
        return response_json(array(
            "items" => $this->lessinusecases->get_related_items($this->input->get("id_lessin"))
        ));
    } 
}