<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

class Integracao_cat_prod extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }

        if (!has_role('gerenciar_integracao_cat_prod')) {
            show_permission();
        }

        $this->load->library('breadcrumbs');
        $this->load->library('pagination');

        $this->load->model([
            'cad_item_model'
        ]);

        $this->cad_item_model->set_namespace('integracao_cat_prod');
    }

    public function index()
    {
        $limit = 20;
        $page = $this->input->get('per_page');
        $queryString  = '';
        
        $this->setStates();

        $totalEntries = $this->cad_item_model->getEntriesIntegracao(NULL,NULL,TRUE);
        $data['itens'] = $this->cad_item_model->getEntriesIntegracao($limit, ($page > 0 ? $page - 1 : 0) * $limit);

        $config['page_query_string'] = TRUE;
        $config['use_page_numbers']  = TRUE;
        $config['total_rows']        = $totalEntries;
        $config['base_url']          = base_url("integracao_cat_prod/index?{$queryString}");
        $config['per_page']          = $limit;

        $this->pagination->initialize($config);

        $data['pagination'] = $this->pagination->create_links();
        $data['query_string'] = !empty($this->input->get()) ? "?" . http_build_query(["part_number" => $this->input->get("part_number"), "ncm" => $this->input->get("ncm")]) : "";
        
        $this->title = "Integração Catálogo de Produtos";

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Integração Catálogo de Produtos', '/integracao_cat_prod/');

        $this->include_js(array(
            'bootstrap-select/bootstrap-select.js',
            'bootstrap-notify.min.js',
            'jquery.cookie.js',
            'sweetalert.min.js',
        ));

        $this->include_css(array(
            'bootstrap-select/bootstrap-select.css',
            'animate.css',
            'sweetalert.css'
        ));

        $this->render('integracao_cat_prod/default', $data);
    }

    public function export()
    {
        $this->load->library('Excel');
        $this->load->library('Item/IntegracaoCatProdUseCases');

        $itens = $this->cad_item_model->getEntriesIntegracao(null, null, false, $this->input->post('item_checkbox'));

        $xlsx_writer = IntegracaoCatProdUseCases::generate_xlsx($this->excel, $itens);

        $filename = 'itens_cat_prod_' . date('Y-m-d_H-i-s') . '.xlsx';

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $objWriter = PHPExcel_IOFactory::createWriter($xlsx_writer, 'Excel2007');
        $objWriter->save('php://output');
    }

    public function setStates()
    {
        $unsetFilters = $this->input->is_set('unsetFilters'); 
        $pesquisar = $this->input->is_set('pesquisar');
        $ncm = $this->input->is_set('ncm');
        $codigo_receita = $this->input->is_set('codigo_receita');
        $versao = $this->input->is_set('versao');
        $modalidade = $this->input->is_set('modalidade');

        if ($unsetFilters) {
            $this->cad_item_model->set_state_store_session(TRUE);
            $this->cad_item_model->clear_states();
        } else {
            $this->cad_item_model->set_state_store_session(TRUE);
            $this->cad_item_model->restore_state_from_session('filter.', 'post');
        }

        if ($pesquisar) {
            $this->cad_item_model->set_state('filter.pesquisar', $this->input->post('pesquisar'));
        }

        if ($ncm) {
            $this->cad_item_model->set_state('filter.ncm', $this->input->post('ncm'));
        }

        if ($codigo_receita) {
            $this->cad_item_model->set_state('filter.codigo_receita', $this->input->post('codigo_receita'));
        }

        if ($versao) {
            $this->cad_item_model->set_state('filter.versao', $this->input->post('versao'));
        }

        if ($modalidade) {
            $this->cad_item_model->set_state('filter.modalidade', $this->input->post('modalidade'));
        }
    }
}