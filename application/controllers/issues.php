<?php

class Issues extends MY_Controller {

	public function __construct() {
		parent::__construct();

		if (!is_logged()) {
			redirect('/login');
		}

        if (!has_role('gerenciar_fotos')) {
            show_permission();
        }

        $this->load->library('breadcrumbs');

        $this->load->model('item_model');
	}

	public function index() {

        $this->item_model->set_state('filter.id_empresa', sess_user_company());

        $data['itens'] = $this->item_model->get_entries();

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Fotos com problemas', '/issues/');

        $this->load->model('empresa_model');
        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $data['multi_estabelecimentos'] = $empresa->multi_estabelecimentos;

		$this->render('issues', $data);
	}
}