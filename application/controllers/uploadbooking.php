<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Uploadbooking extends MY_Controller {

	public function __construct()
    {
		parent::__construct();

		if (!is_logged()) {
			redirect('/login');
		}

		if (!has_role('arquivos_booking')) {
			show_permission();
		}

        $this->load->library('breadcrumbs');
	}

	public function index()
	{
		set_time_limit(0);

		$data = array();

        $this->load->model('usuario_model');
        $this->load->model('cad_item_model');

        $this->load->model('empresa_model');

        $data['empresas'] = $this->empresa_model->get_all_entries();

		if ($this->input->post('submit'))
		{
			$upload_path = config_item('upload_tmp_path');

			$this->load->library('form_validation');

			$this->form_validation->set_rules('id_empresa', 'Empresa', 'trim|required');

			if ($this->form_validation->run() == TRUE)
			{
				$id_empresa = $this->input->post('id_empresa');

    			$config['upload_path'] = $upload_path;
    			$config['allowed_types'] = 'xlsx';
    			$config['max_size'] = 2147483648;

    			$this->load->library('unzip');
    			$this->load->library('upload', $config);

    			if ( ! $this->upload->do_upload('arquivo'))
    			{
    				$err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . $this->upload->display_errors('<li>', '</li>') . "</ul>";
    				$this->message_on_render($err, 'error');
    			}
    			else
    			{
    				$upload_data = $this->upload->data();

    				$file_ext = strtolower($upload_data['file_ext']);

    				// xlsx file
    				if ($file_ext == '.xlsx') {

    					include APPPATH . 'libraries/xlsxreader.php';

    					$xlsx = new XLSXReader($upload_data['full_path']);
    					$sheetNames = $xlsx->getSheetNames();
    					$sheetActive = current($sheetNames);
    					$sheet = $xlsx->getSheet($sheetActive);

    					$i = 0;

    					$log_adicional = '';

                        date_default_timezone_set('UTC');

    					foreach($sheet->getData() as $row) 
                        {
    						if ($i > 0) 
                            {
    							$dbdata = array();
                                $partnumber = clean_str($row[0], true);

                                // Limpeza de Caracteres Especiais
                                array_walk($row, function(&$v) {
                                    $v = clean_str($v);
                                });

                                try
                                {
                                    $item = $this->cad_item_model->get_entry_by_pn($partnumber, $id_empresa);

                                    if (isset($row[1]) && $row[1]) {
                                        $id_usuario = $this->usuario_model->get_user_id_by_email($row[1]);
                                    }

                                    // se o usuário existir ...
                                    if ($id_usuario) {
                                        $tipo_homologacao = $row[2];
                                        $is_homologado = intval($row[3]);
                                        $motivo     = $row[4];
                                        $criado_em  = date("Y-m-d H:i:s", $xlsx->toUnixTimeStamp($row[5]));

                                        $this->cad_item_model->set_item_homologado($item->id_item, $tipo_homologacao, $id_usuario, $is_homologado, $motivo, $criado_em);
                                    } else {
                                        $log_adicional .= "<li>Usuário com o e-mail: [<strong>".$row[1]."</strong>] não foi encontrado";
                                    }

                                } catch (Exception $e) {
                                    $log_adicional .= "<li>Nenhum item encontrado para o partnumber: [<strong>".$partnumber."</strong>]";
                                }

    						}

    						$i++;
    					}

    					$message_on_render = $this->message_config('<h4>Sucesso</h4> <p>Arquivo XLSX <strong>'.$upload_data['orig_name'].'</strong> recebido e processado.</p>');

    					if (!empty($log_adicional)) {
    						$message_on_render .= $this->message_config("<h4>Atenção: </h4><ul>{$log_adicional}</ul>", "warning");
    					}

    					$this->message_next_render($message_on_render, NULL, TRUE);
    				}


    				unlink($upload_data['full_path']);

                    redirect('/uploadbooking/');
    			}
            } else {
				$err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
				$this->message_on_render($err, 'error');
            }
		}

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Enviar para booking eletrônico', '/uploadbooking/');

		$this->render('uploadbooking', $data);
	}
}

