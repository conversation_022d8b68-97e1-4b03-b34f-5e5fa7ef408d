<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Uploadpreco extends MY_Controller {

	public function __construct() {
		parent::__construct();

		if (!is_logged()) {
			redirect('/login');
		}

		if (!has_role('sysadmin')) {
            show_permission();
		}

        $this->load->library('breadcrumbs');
	}

    private function clean_cnpj($cnpj) {
        return $cnpj ? str_pad(
                                    str_replace(array('/', '-', '.'), '', $cnpj),
                                    14,
                                    '0', STR_PAD_LEFT) : '';
    }

	public function index()
	{
		set_time_limit(0);

		$data = array();

		$this->load->model('empresa_model');
        $this->load->model('usuario_model');
        $this->load->model('grupo_tarifario_model');

		$data['empresas'] = $this->empresa_model->get_all_entries();

        $id_empresa = sess_user_company();
        $data['empresa'] = $this->empresa_model->get_entry($id_empresa);

		if ($this->input->post('submit'))
		{

			$upload_path = config_item('upload_tmp_path');

			$config['upload_path'] = $upload_path;
			$config['allowed_types'] = 'xlsx';
			$config['max_size'] = 2147483648;

			$this->load->library('unzip');
			$this->load->library('upload', $config);

			if ( ! $this->upload->do_upload('arquivo'))
			{
				$err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . $this->upload->display_errors('<li>', '</li>') . "</ul>";
				$this->message_on_render($err, 'error');
			}
			else
			{

                $log_adicional = '';

				$upload_data = $this->upload->data();

				$file_ext = strtolower($upload_data['file_ext']);

				// xlsx file
				if ($file_ext == '.xlsx') {

					include APPPATH . 'libraries/xlsxreader.php';

					$xlsx = new XLSXReader($upload_data['full_path']);
					$sheetNames = $xlsx->getSheetNames();
					$sheetActive = current($sheetNames);
					$sheet = $xlsx->getSheet($sheetActive);

					$i = 0;

                    if ($this->input->post('remove_values')) {
                        $id_empresa = sess_user_company();
                        $empresa = $this->empresa_model->get_entry($id_empresa);

                        $this->db->delete('preco', array('cnpj_cliente' => $this->clean_cnpj($empresa->cnpj)));
                    }

                    $insert_batch = $update_batch = array();

					foreach($sheet->getData() as $row) {

                        if ($this->input->post('ignore_first_line') && $i == 0) {
                            $i++;
                            continue;
                        }

                        $dbdata['part_number'] = $part_number = $row[0] ? $row[0] : 'SEMPN';
                        $dbdata['data_ini_validade'] = $data_ini_validade = date('Y-m-d', $xlsx->toUnixTimeStamp($row[1]));
                        $dbdata['valor'] = $valor = $row[2];
                        $dbdata['cnpj_fornecedor'] = $cnpj_fornecedor = $this->clean_cnpj($row[3]);
                        $dbdata['cnpj_cliente'] = $cnpj_cliente = $this->clean_cnpj($row[4]);
                        $dbdata['po'] = $po = $row[5];
                        $dbdata['seq'] = $seq = $row[6];

                        $dbdata['qtde'] = $qtde = $row[7];
                        $dbdata['um'] = $um = $row[8];
                        $dbdata['contrato'] = $contrato = $row[9];
                        $dbdata['pgr'] = $pgr = $row[10];
                        $dbdata['tax_code'] = $tax_code = $row[11];

                        // $where = array(
                        //      'part_number' => $part_number,
                        //      'data_ini_validade' => $data_ini_validade,
                        //      'cnpj_fornecedor' => $cnpj_fornecedor,
                        //      'cnpj_cliente' => $cnpj_cliente,
                        //      'po' => $po,
                        //      'seq' => $seq
                        // );
                        //
                        // $this->db->where($where);
                        // $count = $this->db->count_all_results('preco');


                        $this->db->replace('preco', $dbdata);

                        $i++;
					}
				}

                $this->message_next_render('Arquivo XLSX [<b>'.$upload_data['orig_name'].'</b>] recebido e processado com sucesso!');

				unlink($upload_data['full_path']);

				redirect('/uploadpreco/');
			}
		}

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Planilha de Preço (Monitor NFe)', '/uploadpreco/');

		$this->render('uploadpreco', $data);
	}

    public function download() {

        $this->load->model('empresa_model');
        $empresa_entry = $this->empresa_model->get_entry(sess_user_company());


        $this->load->library('xlsxwriter');

        $this->xlsxwriter->setAuthor('Becomex Consulting');

        $header = array(
          'PN'=>'string',
          'Data'=>'date',
          'Valor'=>'float',
          'CNPJ_F'=>'string',
          'CNPJ_C'=>'string',
          'PO'=>'string',
          'Item PO'=>'string',
          'Qtde'=>'string',
          'UM'=>'string',
          'Contrato' => 'string',
          'PGr' => 'string',
          'TX Code' => 'string'
        );

        $data = array();

        $this->db->where('cnpj_cliente', $this->clean_cnpj($empresa_entry->cnpj));
        $query = $this->db->get('preco');
        $result = $query->result();

        if (!empty($result))
        {
            foreach ($result as $item)
            {
                $data[] = array(
                    (string) $item->part_number,
                    (string) $item->data_ini_validade,
                    (float) $item->valor, (string) $item->cnpj_fornecedor,
                    (string) $item->cnpj_cliente, (string) $item->po,
                    (string) $item->seq, $item->qtde, $item->um, $item->contrato, $item->pgr, (string) $item->tax_code
                );
            }
        }

        $writer = new XLSXWriter();
        $writer->writeSheet($data, 'Preco', $header);

        $filename = 'GT-TABELA-PRECO-'.date('d-m-Y').'.xlsx';

        header('Content-disposition: attachment; filename='.$filename);
        header('Content-type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

        echo $writer->writeToStdOut();
    }
}

