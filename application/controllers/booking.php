<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

class Booking extends MY_Controller
{

    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }

        if (!has_role('booking_eletronico')) {
            show_permission();
        }

        $this->load->model('cad_item_model');
        $this->load->model('usuario_model');
        $this->load->model('foto_model');

        $this->load->library('breadcrumbs');

        $this->cad_item_model->set_namespace('booking');
    }

    public function index()
    {
        if ($this->input->is_set('reset_filters')) {
            $this->cad_item_model->set_state_store_session(TRUE);
            $this->cad_item_model->clear_states();
        } else {
            $this->cad_item_model->set_state_store_session(TRUE);
            $this->cad_item_model->restore_state_from_session();
        }
       

        $data = array();

        $data['status_implementacao'] = array('R', 'I', 'N');

        $this->title = "Booking Eletrônico";

        $this->load->model('empresa_model');
        $data['status_exportacao'] = array(0, 1);
        
        $per_page = $this->input->get('per_page');
        
        $this->cad_item_model->set_state('filter.list_opt', 'homologado');

        $this->cad_item_model->set_state('filter.busca_part_number', TRUE);
        $this->cad_item_model->set_state('filter.busca_descricao', TRUE);

        $id_empresa = sess_user_company();
        $this->cad_item_model->set_state('filter.id_empresa', $id_empresa);

        //Código do produto
        if ($this->input->is_set('part_numbers')) {
            $part_numbers = $this->input->post('part_numbers');

            if (!is_array($part_numbers) && !empty($part_numbers)) {
                $this->cad_item_model->set_state('filter.part_numbers_view', $part_numbers);

                $separator = get_company_separator(sess_user_company());

                $part_numbers = str_replace(array("\t", "\r\n", "\s", "\n"), $separator, $this->input->post('part_numbers'));

                $matches = array();
                preg_match_all('/"([^"]*)"/', $part_numbers, $matches);
                $btw_quotes = $matches[1];

                $part_numbers = str_replace("*", "%", $part_numbers);
                $part_numbers = preg_replace('/"([^"]*)"/', "", $part_numbers);

                //Acha todos os partnumbers entre aspas simples
                $matches_simple = array();
                preg_match_all('~\'(.*?)\'~', $part_numbers, $matches_simple);
                $btw_simple_quotes = $matches_simple[1];

                //Retira da string todos os partnumbers entre aspas simples
                $part_numbers = preg_replace('~\'(.*?)\'~', "", $part_numbers);

                $part_numbers = explode($separator, addslashes($part_numbers));
                $part_numbers = array_filter($part_numbers);

                if (!empty($btw_quotes)) {
                    $addslashes_btw_quotes = implode(',', $btw_quotes);
                    $btw_quotes = explode(",", addslashes($addslashes_btw_quotes));
                }

                $part_numbers = array_merge($part_numbers, $btw_quotes);
                $part_numbers = array_merge($part_numbers, $btw_simple_quotes);

                $generic_part_numbers = array();

                foreach ($part_numbers as $key => $part_number) {
                    if (strpos($part_number, "%")) {
                        $generic_part_numbers[] = $part_number;
                        unset($part_numbers[$key]);
                    }
                }

                if (!empty($part_numbers)) {
                    $this->cad_item_model->set_state('filter.part_numbers', $part_numbers);
                } else {
                    $this->cad_item_model->unset_state('filter.part_numbers');
                }

                if (!empty($generic_part_numbers)) {
                    $this->cad_item_model->set_state('filter.generic_part_numbers', $generic_part_numbers);
                } else {
                    $this->cad_item_model->unset_state('filter.generic_part_numbers', $generic_part_numbers);
                }
            } else {
                $this->cad_item_model->unset_state('filter.part_numbers_view');
                $this->cad_item_model->unset_state('filter.part_numbers');
                $this->cad_item_model->unset_state('filter.generic_part_numbers');
            }
        }

        //Status Exportação
        if ($this->input->is_set('status_exportacao')) {
            $this->cad_item_model->set_state('filter.status_exportacao', $this->input->post('status_exportacao'));
            $data['status_exportacao'] = $this->input->post('status_exportacao');
        }

        //Status Implementação
        if ($this->input->is_set('status_implementacao')) {
            $this->cad_item_model->set_state('filter.status_implementacao', $this->input->post('status_implementacao'));
            $data['status_implementacao'] = $this->input->post('status_implementacao');
        }

        //Filtred
        if ($this->input->is_set('filtered')) {
            $this->cad_item_model->set_state('filter.filtered', $this->input->post('filtered'));
        } else {
            $this->cad_item_model->set_state('filter.filtered', 0);
        }

        $total_entries = 0;
        $data['itens'] = [];
        $data['pagination'] = [];
        if($this->cad_item_model->get_state('filter.filtered')){
            
            $this->load->library('pagination');
            $limit = 20;
            $offset = $per_page;

            $homologacoes =  $this->empresa_model->get_homologacao_by_id_empresa($id_empresa);
            $data['homologacoes'] = $homologacoes;

            if ($this->input->is_set('export')) {
                $data['itens'] = $this->cad_item_model->get_entries($limit = NULL, $offset = NULL, $count = FALSE, $export_data = TRUE, $homologacoes);
                $this->export($data['itens'], $homologacoes);
            } else {
                $data['itens'] = $this->cad_item_model->get_entries($limit, $offset, $count = FALSE, $export_data = FALSE, $homologacoes);
            }

            $total_entries = $this->cad_item_model->get_total_entries($homologacoes);
            $config['total_rows'] = $total_entries;

            $data['total_rows'] = $config['total_rows'];

            $config['base_url'] = base_url("booking");
            $config['uri_segment'] = 3;
            $config['per_page'] = $limit;
            $config['page_query_string'] = TRUE;
            //$config['reuse_query_string'] = TRUE;
            $config['num_links'] = 5;
            $this->pagination->initialize($config);

            $data['pagination'] = $this->pagination->create_links();

        } else {
            $data['itens'] = [];
            $data['query_homolog'] = '';
            $data['total_rows'] = 0;
            
        }


        $funcoes_adicionais = $this->empresa_model->get_funcoes_adicionais(null, sess_user_company());
        $data['funcoes_adicionais'] = $funcoes_adicionais;
        

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Booking Eletrônico', '/booking/');

        $empresa = $this->empresa_model->get_entry($id_empresa);

        $data['multi_estabelecimentos'] = $empresa->multi_estabelecimentos;

        $data['has_status_exportacao'] = in_array('status_exportacao', $funcoes_adicionais) ? TRUE : FALSE;

        $this->include_js(array('jquery.cookie.js', 'bootstrap-select/bootstrap-select.js'));
        $this->include_css('bootstrap-select/bootstrap-select.css');

        $this->render('booking/default', $data);
    }

    public function view($id_item)
    {
        if (!has_role('booking_visualizacao')) {
            show_permission();
        }
        
        $data = array();

        $this->load->helper('formatador');

        $this->load->model(array(
            'ncm_model',
            'ncm_capitulo_model',
            'ncm_capitulo_grupos_model',
            'cest_model',
            'anexo_model',
            'ex_tarifario_model',
            'empresa_model',
            'cad_item_attr_model'
        ));

        $id_empresa = sess_user_company();

        $this->cad_item_model->set_state('filter.id_empresa', $id_empresa);

        $this->title = "Booking Eletrônico";

        try {
            $item = $this->cad_item_model->get_entry($id_item);
        } catch (Exception $e) {
            show_404();
        }

        $homologacoes = $this->empresa_model->get_homologacao_by_id_empresa($id_empresa);

        if (isset($homologacoes['homologacao_fiscal']) && $homologacoes['homologacao_fiscal'] == true) {
            $homologado_fiscal        = $this->cad_item_model->check_item_homologado_by_tipo($id_item, 'Fiscal');
        } else {
            $homologado_fiscal = true;
        }

        if (isset($homologacoes['homologacao_engenharia']) && $homologacoes['homologacao_engenharia'] == true) {
            $homologado_engenharia = $this->cad_item_model->check_item_homologado_by_tipo($id_item, 'Engenharia');
        } else {
            $homologado_engenharia = true;
        }

        if (!$homologado_fiscal || !$homologado_engenharia) {
            show_404();
        }

        $homolog_info = $this->cad_item_model->get_homolog_info($id_item);

        $item->peso = format_peso($item->peso);

        $data['item'] = $item;
        $data['homolog_info'] = $homolog_info;

        if ($impostos_ncm_proposto = $this->ncm_model->get_impostos($item->ncm_proposto)) {
            $data['impostos_ncm_proposto'] = $impostos_ncm_proposto;
        } else if ($impostos_ncm_proposto_hist = $this->ncm_model->get_impostos($item->ncm_proposto, TRUE)) {
            $data['impostos_ncm_proposto_hist'] = $impostos_ncm_proposto_hist;
        }

        $data['part_number_similar'] = $this->cad_item_model->get_similar_pn($item->part_number);
        $data['part_number'] = $data['part_number_similar'] ? $data['part_number_similar'] : $item->part_number;

        $this->load->model('empresa_model');
        $empresa = $this->empresa_model->get_entry($id_empresa);
        $data['homologacoes'] = $this->empresa_model->get_homologacao_by_id_empresa($id_empresa);

        $data['campos_adicionais'] = explode('|', $empresa->campos_adicionais);
        $data['multi_estabelecimentos'] = $empresa->multi_estabelecimentos;

        $this->anexo_model->set_state('filter.tipo_anexo', 'ficha_tecnica');
        $data['ficha_tecnica'] = $this->anexo_model->get_entries($item->part_number, $item->estabelecimento, $item->id_empresa);

        if (!empty($item->num_ex_ii)) {
            $data['item_ex_ii'] = $this->ex_tarifario_model->get_ex_ii_by_ncm($item->num_ex_ii, $item->ncm_proposto);
        }

        if (!empty($item->num_ex_ipi)) {
            $data['item_ex_ipi'] = $this->ex_tarifario_model->get_ex_ipi_by_ncm($item->num_ex_ipi, $item->ncm_proposto);
        }

        $data['has_attr'] = $this->cad_item_attr_model->has_attr($id_item);

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Booking Eletrônico', '/booking/');
        $this->breadcrumbs->push('Detalhe do item', '/booking/view/' . $id_item);

        $this->render('booking/view', $data);
    }

    private function addValueExToCell($id_item)
    {
        $item_impostos = $this->ex_tarifario_model->get_item_impostos_by_id_item($id_item);

        $value_cell = '';
        if (isset($item_impostos->pct_ii) && !empty($item_impostos->pct_ii)) {
            $value_cell .= 'II: ' . $item_impostos->pct_ii . '% ';
        }

        if (isset($item_impostos->pct_ipi) && !empty($item_impostos->pct_ipi)) {
            $value_cell .= 'IPI: ' . $item_impostos->pct_ipi . '% ';
        }

        return $value_cell;
    }

    public function export($itens, $homologacoes = NULL) 
    {
        $this->load->helper(array(
            'common', 'formatador', 'text'
        ));

        $this->load->model(array(
            'ex_tarifario_model',
            'cad_item_nve_model',
            'ncm_model'
        ));

        $this->load->library(array(
            'Excel'
        ));

        $funcoesAdicionais = $this->empresa_model->get_funcoes_adicionais(null, sess_user_company());
        $showIdGt = in_array('exibir_id_gpt', $funcoesAdicionais);

        if (!in_array('exportar', $funcoesAdicionais)) {
            return false;
        }

        $this->excel->setActiveSheetIndex(0);

        $sheet = $this->excel->getActiveSheet();

        $sheet->setTitle('Booking - Itens');

        if (!array_key_exists('homologacao_engenharia', $homologacoes)) {
            $sheet->getColumnDimension('S')->setVisible(false);
        }

        if (!array_key_exists('homologacao_fiscal', $homologacoes)) {
            $sheet->getColumnDimension('R')->setVisible(false);
        }

        $customer_can_ncm_fornecedor = customer_can('ncm_fornecedor');
        $customer_can_suframa = customer_can('suframa');
        $customer_can_ex_ipi = customer_can('ipi');
        $customer_can_ex_ii = customer_can('ii');
        $customer_can_nve = customer_can('nve');

        $headerRow = array(
            'Part number',
            'Part number similar',
            'Empresa',
            'Estabelecimento',
            'NCM Atual',
            'NCM Proposto',
            'NCM Fornecedor',
            'Descrição',
            'Descrição prop. resumida',
            'Descrição prop. completa',
            'Função',
            'Peso',
            'Aplicação',
            'Marca',
            'Material Constitutivo',
            'Dispositivos legais',
            'Características',
            'Subsidio',
            'Responsável fiscal',
            'Responsável engenheiro',
            'Grupo Tarifario',
            'Status de Implementação',
            'Motivo de Implementação',
            'Solucao de Consulta',
            'Memoria de classificação',
            'Num. EX II',
            'Num. EX IPI',
            'Evento',
            'TAG',
            'Status da homologação',
            'Impostos NCM Atual',
            'Impostos NCM Proposto',
            'CEST',
            'Impostos NCM - EX de II/IPI'
        );

        if ($customer_can_ex_ipi) {
            array_push($headerRow, 'EX-IPI');
            array_push($headerRow, 'TEXTO DO EX-IPI');
        }

        if ($customer_can_ex_ii) {
            array_push($headerRow, 'EX-II');
            array_push($headerRow, 'TEXTO DO EX-II');
        }

        if ($customer_can_nve) {
            array_push($headerRow, 'NVE ATRIBUTO AA');
            array_push($headerRow, 'NVE VALOR AA');
            array_push($headerRow, 'NVE ATRIBUTO AB');
            array_push($headerRow, 'NVE VALOR AB');
            array_push($headerRow, 'NVE ATRIBUTO AC');
            array_push($headerRow, 'NVE VALOR AC');
            array_push($headerRow, 'NVE ATRIBUTO AD');
            array_push($headerRow, 'NVE VALOR AD');
            array_push($headerRow, 'NVE ATRIBUTO AE');
            array_push($headerRow, 'NVE VALOR AE');
            array_push($headerRow, 'NVE ATRIBUTO AF');
            array_push($headerRow, 'NVE VALOR AF');
            array_push($headerRow, 'NVE ATRIBUTO AG');
            array_push($headerRow, 'NVE VALOR AG');
            array_push($headerRow, 'NVE ATRIBUTO AH');
            array_push($headerRow, 'NVE VALOR AH');
            array_push($headerRow, 'NVE ATRIBUTO U');
            array_push($headerRow, 'NVE VALOR U');
            array_push($headerRow, 'NVE ATRIBUTO UE');
            array_push($headerRow, 'NVE VALOR UE');
        }

        array_push($headerRow, 'LICENCIAMENTO NÃO AUTOMÁTICO');
        array_push($headerRow, 'ORGÃO ANUENTE');
        array_push($headerRow, 'DESTAQUE LI');
        array_push($headerRow, 'ANTIDUMPING ATIVO');

        if ($customer_can_suframa) {
            array_push($headerRow, 'SUFRAMA CÓDIGO');
            array_push($headerRow, 'SUFRAMA DESTAQUE');
            array_push($headerRow, 'SUFRAMA PPB');
            array_push($headerRow, 'DESCRIÇÃO SUFRAMA');
            array_push($headerRow, 'CÓDIGO PRODUTO SUFRAMA');
        }

        $itemRows = array();
        $max_attr = 0;
        $max_nve = 20;

        foreach ($itens as $k => $item) {
            $itemRows[$k] = array(
                $item->part_number,
                $item->part_number_similar,
                $item->razao_social,
                $item->estabelecimento,
                $item->ncm_atual,
                $item->ncm_proposto,
                $customer_can_ncm_fornecedor ? $item->ncm_fornecedor : '',
                $item->descricao_atual,
                $item->descricao_mercado_local,
                $item->descricao_proposta_completa,
                $item->funcao,
                $item->peso,
                $item->aplicacao,
                $item->marca,
                $item->material_constitutivo,
                $item->dispositivo_legal,
                $item->caracteristicas,
                $item->subsidio
            );

            $respFiscalEmail = '';

            if (!empty($item->id_resp_fiscal)) {
                try {
                    $resp_fiscal = $this->usuario_model->get_entry($item->id_resp_fiscal);
                    $respFiscalEmail = $resp_fiscal->email;
                } catch (Exception $e) {
                    $respFiscalEmail = 'Usuário não encontrado';
                }
            }

            $respEngenhariaEmail = '';

            if (!empty($item->id_resp_engenharia)) {
                try {
                    $resp_engenharia = $this->usuario_model->get_entry($item->id_resp_engenharia);
                    $respEngenhariaEmail = $resp_engenharia->email;
                } catch (Exception $e) {
                    $respEngenhariaEmail = 'Usuário não encontrado';
                }
            }

            array_push($itemRows[$k], $respFiscalEmail);
            array_push($itemRows[$k], $respEngenhariaEmail);
            array_push($itemRows[$k], $showIdGt ? $item->id_grupo_tarifario : $item->grupo_tarifario);

            $labelStatus = '';

            switch ($item->status_implementacao) {
                case 'I':
                    $labelStatus = 'Implementado';
                    break;
                case 'N':
                    $labelStatus = 'Não implementado';
                    break;
                case 'R':
                    $labelStatus = 'Revisão';
                    break;
            }

            array_push($itemRows[$k], $labelStatus);
            array_push($itemRows[$k], $item->motivo_implementacao);
            array_push($itemRows[$k], $item->solucao_consulta);
            array_push($itemRows[$k], $item->memoria_classificacao);
            array_push($itemRows[$k], !empty($item->num_ex_ii) && $item->num_ex_ii > 0 ? $item->num_ex_ii : '');
            array_push($itemRows[$k], !empty($item->num_ex_ipi) && $item->num_ex_ipi > 0 ? $item->num_ex_ipi : '');
            array_push($itemRows[$k], $item->evento);
            array_push($itemRows[$k], $item->tag);

            $statusHomologacao = '';

            switch ($item->status_homologacao) {
                case 0:
                    $statusHomologacao = 'Não homologado';
                    break;
                case 1:
                    $statusHomologacao = 'Homologado';
                    break;
                case 2:
                    $statusHomologacao = 'Obsoleto';
                    break;
            }

            array_push($itemRows[$k], $statusHomologacao);
            
            $ncmAtualImpostosVal = '';

            if (!empty($item->ncm_atual)) {
                $ncmAtualImpostosVal = $this->ncm_model->getStringByNcmImposto($item->ncm_atual);                
            }

            array_push($itemRows[$k], $ncmAtualImpostosVal);

            $ncmPropostoImpostosVal = '';

            if (!empty($item->ncm_proposto)) {
                $ncmPropostoImpostosVal = $this->ncm_model->getStringByNcmImposto($item->ncm_proposto);                
            }

            array_push($itemRows[$k], $ncmPropostoImpostosVal);
            $codCest = '';

            if ($item->cod_cest !== null) {
                if ($item->cod_cest == '-1') {
                    $codCest = 'N/D - Não atende';
                } else {
                    $codCest = $item->cod_cest;
                }
            }

            array_push($itemRows[$k], $codCest);
            
            //seguindo lógica já existente no modelo antigo
            if ((!empty($item->num_ex_ii) || !empty($item->num_ex_ipi)) && (!empty($item->num_ex_ii) > 0 || $item->num_ex_ipi > 0)) {
                $exVal = $this->addValueExToCell($item->id_item);
            }
            
            array_push($itemRows[$k], $exVal);


            if ($customer_can_ex_ipi) {
                $item_ex_ipi = false;
                if (!empty($item->num_ex_ipi)) {
                    $item_ex_ipi = $this->ex_tarifario_model->get_ex_ipi_by_ncm($item->num_ex_ipi, $item->ncm_proposto);
                }
                array_push($itemRows[$k], $item_ex_ipi ? $item_ex_ipi->num_ex : '');
                array_push($itemRows[$k], $item_ex_ipi ? $item_ex_ipi->descricao_linha1 : '');
            }
    
            if ($customer_can_ex_ii) {
                $item_ex_ii = false;
                if (!empty($item->num_ex_ii)) {
                    $item_ex_ii = $this->ex_tarifario_model->get_ex_ii_by_ncm($item->num_ex_ii, $item->ncm_proposto);
                }
                array_push($itemRows[$k], $item_ex_ii ? $item_ex_ii->num_ex : '');
                array_push($itemRows[$k], $item_ex_ii ? $item_ex_ii->descricao_linha1 : '');
            }
    
            if ($customer_can_nve) {
                if ($item->has_nve > 0) {
                    $nves = $this->cad_item_nve_model->get_entries($item->id_item);
                    foreach($nves as $nve) {
                        array_push($itemRows[$k], $nve->nve_atributo);
                        array_push($itemRows[$k], $nve->nve_valor);
                    }
    
                    for ($i=0; $i < $max_nve - (count($nves) * 2); $i++) { 
                        array_push($itemRows[$k], '');
                    }
                } else {
                    for ($i=0; $i < $max_nve; $i++) { 
                        array_push($itemRows[$k], '');
                    }
                }
            }
    
            array_push($itemRows[$k], !empty($item->li) ? $item->li : '');
            array_push($itemRows[$k], !empty($item->li_orgao_anuente) ? $item->li_orgao_anuente : '');
            array_push($itemRows[$k], !empty($item->li_destaque) ? $item->li_destaque : '');
            array_push($itemRows[$k], !empty($item->antidumping) ? $item->antidumping : '');
    
            if ($customer_can_suframa) {
                array_push($itemRows[$k], $item->suframa_codigo);
                array_push($itemRows[$k], $item->suframa_destaque);
                array_push($itemRows[$k], $item->suframa_ppb);
                array_push($itemRows[$k], $item->suframa_descricao);
                array_push($itemRows[$k], $item->suframa_produto);
            }
    
            if (!empty($item->atributos)) {
                $atributos = explode('||', $item->atributos);
                foreach($atributos as $attr) {
                    $valores = explode(':|:', $attr);
                    array_push($itemRows[$k], $valores[0]);
                    array_push($itemRows[$k], $valores[1]);
                }
    
                if (count($atributos) > $max_attr) {
                    $max_attr = count($atributos);
                }
            }
        }

        for ($i=0; $i < $max_attr; $i++) { 
            array_push($headerRow, 'Atributo');
            array_push($headerRow, 'Valor');
        }

        $sheet->fromArray($headerRow, NULL, 'A1');

        // Corrige HTML Chars
        array_walk_recursive($itemRows, function (&$item) {
            $item = html_entity_decode($item, ENT_QUOTES);
        });

        // Inserindo os dados
        list($startColumn, $startRow) = PHPExcel_Cell::coordinateFromString('A2');

        foreach ($itemRows as $rowData) {
            $currentColumn = $startColumn;

            foreach ($rowData as $cellValue) {
                $sheet->setCellValueExplicit($currentColumn . $startRow, $cellValue, PHPExcel_Cell_DataType::TYPE_STRING);
                ++$currentColumn;
            }

            ++$startRow;
        }

        // Tamanho das colunas
        $sheet->getDefaultColumnDimension()->setWidth('40');

        $last_column = $this->num2alpha(count($headerRow) - 1);

        $sheet->getStyle('A1:' . $last_column . '1')->applyFromArray(
            array(
                'fill' => array(
                    'type' => PHPExcel_Style_Fill::FILL_SOLID,
                    'color' => array('rgb' => 'FFFF00')
                ),
                'font'  => array(
                    'bold'  => true
                )
            )
        );

        $filename = 'booking_' . date('Y-m-d_H-i-s') . '.xlsx';

        // Tamanho das colunas
        $sheet->getDefaultColumnDimension()->setWidth('40');

        $writer = PHPExcel_IOFactory::createWriter($this->excel, 'Excel2007');

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }

    public function exportAntigo($itens, $homologacoes = NULL)
    {
        $this->load->helper(array(
            'common',
            'formatador'
        ));
        $this->load->model('ex_tarifario_model');
        $funcoes_adicionais = $this->empresa_model->get_funcoes_adicionais(null, sess_user_company());

        $show_id_grupo_tarifario = false;
        if (in_array('exibir_id_gpt', $funcoes_adicionais)) {
            $show_id_grupo_tarifario = true;
        }

        if (in_array('exportar', $funcoes_adicionais)) {
            if (!empty($itens)) {
                $this->load->library('Excel');

                $this->excel->setActiveSheetIndex(0);
                $this->excel->getActiveSheet()->setTitle('Booking - Itens');

                $this->excel->getActiveSheet()->setCellValue('A1', 'Part number');
                $this->excel->getActiveSheet()->setCellValue('B1', 'Part number similar');
                $this->excel->getActiveSheet()->setCellValue('C1', 'Empresa');
                $this->excel->getActiveSheet()->setCellValue('D1', 'Estabelecimento');
                $this->excel->getActiveSheet()->setCellValue('E1', 'NCM Atual');
                $this->excel->getActiveSheet()->setCellValue('F1', 'NCM Proposto');
                $this->excel->getActiveSheet()->setCellValue('G1', 'NCM Fornecedor');
                $this->excel->getActiveSheet()->setCellValue('H1', 'Descricao');
                $this->excel->getActiveSheet()->setCellValue('I1', 'Descricao prop. resumida');
                $this->excel->getActiveSheet()->setCellValue('J1', 'Descricao prop. completa');
                $this->excel->getActiveSheet()->setCellValue('K1', 'Função');
                $this->excel->getActiveSheet()->setCellValue('L1', 'Aplicacao');
                $this->excel->getActiveSheet()->setCellValue('M1', 'Marca');
                $this->excel->getActiveSheet()->setCellValue('N1', 'Material Constitutivo');
                $this->excel->getActiveSheet()->setCellValue('O1', 'Dispositivos legais');
                $this->excel->getActiveSheet()->setCellValue('P1', 'Caracteristicas');
                $this->excel->getActiveSheet()->setCellValue('Q1', 'Subsidio');
                $this->excel->getActiveSheet()->setCellValue('R1', 'Responsavel fiscal');
                $this->excel->getActiveSheet()->setCellValue('S1', 'Responsavel engenheiro');
                $this->excel->getActiveSheet()->setCellValue('T1', 'Grupo Tarifario');
                $this->excel->getActiveSheet()->setCellValue('U1', 'Status de Implementação');
                $this->excel->getActiveSheet()->setCellValue('V1', 'Motivo da Implementação');
                $this->excel->getActiveSheet()->setCellValue('W1', 'Solucao de consulta');
                $this->excel->getActiveSheet()->setCellValue('X1', 'Memoria de classificacao');
                $this->excel->getActiveSheet()->setCellValue('Y1', 'Num. EX II');
                $this->excel->getActiveSheet()->setCellValue('Z1', 'Num. EX IPI');
                $this->excel->getActiveSheet()->setCellValue('AA1', 'Evento');
                $this->excel->getActiveSheet()->setCellValue('AB1', 'TAG');
                $this->excel->getActiveSheet()->setCellValue('AC1', 'Status da homologacao');
                $this->excel->getActiveSheet()->setCellValue('AD1', 'Impostos NCM Atual');
                $this->excel->getActiveSheet()->setCellValue('AE1', 'Impostos NCM Proposto');
                $this->excel->getActiveSheet()->setCellValue('AF1', 'CEST');

                // VERIFICA SE ADICIONA EX DE II/IPI

                $last_letter = 'G';
                $this->excel->getActiveSheet()->setCellValue('AG1', 'Impostos NCM - EX de II/IPI');

                $customer_can_suframa = customer_can('suframa');

                if ($customer_can_suframa) {
                    $last_letter = 'L';

                    $this->excel->getActiveSheet()->setCellValue('AH1', 'SUFRAMA CÓDIGO');
                    $this->excel->getActiveSheet()->setCellValue('AI1', 'SUFRAMA DESTAQUE');
                    $this->excel->getActiveSheet()->setCellValue('AJ1', 'SUFRAMA PPB');
                    $this->excel->getActiveSheet()->setCellValue('AK1', 'SUFRAMA DESCRIÇÃO');
                    $this->excel->getActiveSheet()->setCellValue('AL1', 'SUFRAMA PRODUTO');
                }

                $customer_can_ncm_fornecedor = customer_can('ncm_fornecedor');

                $last_column_num = alpha2num("A${last_letter}") + 1;

                $max_attrs = 0;
                $attrs = array();
                foreach($itens as $key => $item) {
                    $atributos = explode('||', $item->atributos);
                    if (!empty($atributos) && count($atributos) > $max_attrs) {
                        $max_attrs = count($atributos);
                    }

                    foreach($atributos as $atributo) {
                        $valores = explode(':|:', $atributo);
                        $attrs[$key][] = array(
                            'key' => $valores[0],
                            'value' => $valores[1]
                        );
                    }
                }

                for ($i=0; $i < $max_attrs; $i++) { 
                    $this->excel->getActiveSheet()->setCellValue($this->num2alpha($last_column_num++)."1", 'Atributo');
                    $this->excel->getActiveSheet()->setCellValue($this->num2alpha($last_column_num++)."1", 'Valor');
                }

                $last_column = $this->num2alpha($last_column_num - 1);

                $this->excel->getActiveSheet()->getStyle("A1:${last_column}1")->getFont()->setBold(true);
                $this->excel->getActiveSheet()->getStyle("A1:${last_column}1")->getFill()
                    ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
                    ->getStartColor()->setARGB('F9FF00');
                $this->excel->getActiveSheet()->getStyle("A1:${last_column}1")->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

                if (!array_key_exists('homologacao_engenharia', $homologacoes)) {
                    $this->excel->getActiveSheet()->getColumnDimension('S')->setVisible(false);
                }

                if (!array_key_exists('homologacao_fiscal', $homologacoes)) {
                    $this->excel->getActiveSheet()->getColumnDimension('R')->setVisible(false);
                }

                foreach ($itens as $key => $item) {
                    $i = $key + 2;

                    $cell = 'A' . $i;
                    $this->excel->getActiveSheet()->setCellValueExplicit($cell, $item->part_number, PHPExcel_Cell_DataType::TYPE_STRING);

                    $cell = 'B' . $i;
                    $this->excel->getActiveSheet()->setCellValueExplicit($cell, $item->part_number_similar, PHPExcel_Cell_DataType::TYPE_STRING);

                    $cell = 'C' . $i;
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($item->razao_social);

                    $cell = 'D' . $i;
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($item->estabelecimento);

                    $cell = 'E' . $i;
                    $this->excel->getActiveSheet()->setCellValueExplicit($cell, $item->ncm_atual, PHPExcel_Cell_DataType::TYPE_STRING);

                    $cell = 'F' . $i;
                    $this->excel->getActiveSheet()->setCellValueExplicit($cell, $item->ncm_proposto, PHPExcel_Cell_DataType::TYPE_STRING);
                    
                    $cell = 'G' . $i;
                    $this->excel->getActiveSheet()->setCellValueExplicit($cell, $customer_can_ncm_fornecedor ? $item->ncm_fornecedor : '', PHPExcel_Cell_DataType::TYPE_STRING);

                    $cell = 'H' . $i;
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($item->descricao_atual);

                    $cell = 'I' . $i;
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($item->descricao_mercado_local);

                    $cell = 'J' . $i;
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($item->descricao_proposta_completa);

                    $cell = 'K' . $i;
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($item->funcao);

                    $cell = 'L' . $i;
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($item->aplicacao);

                    $cell = 'M' . $i;
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($item->marca);

                    $cell = 'N' . $i;
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($item->material_constitutivo);

                    $cell = 'O' . $i;
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($item->dispositivo_legal);

                    $cell = 'P' . $i;
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($item->caracteristicas);

                    $cell = 'Q' . $i;
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($item->subsidio);

                    /*
                     * Responsáveis Fiscal e Engenharia
                     *
                    */

                    $resp_fiscal_val = '';

                    if (!empty($item->id_resp_fiscal)) {
                        try {
                            $resp_fiscal = $this->usuario_model->get_entry($item->id_resp_fiscal);
                            $resp_fiscal_val = $resp_fiscal->email;
                        } catch (Exception $e) {
                            $resp_fiscal_val = 'Usuário não encontrado';
                        }
                    }

                    $cell = 'R' . $i;
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($resp_fiscal_val);

                    $resp_engenharia_val = '';

                    if (!empty($item->id_resp_engenharia)) {
                        try {
                            $resp_engenharia = $this->usuario_model->get_entry($item->id_resp_engenharia);
                            $resp_engenharia_val = $resp_engenharia->email;
                        } catch (Exception $e) {
                            $resp_engenharia_val = 'Usuário não encontrado';
                        }
                    }

                    $cell = 'S' . $i;
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($resp_engenharia_val);

                    /**/

                    $cell = 'T' . $i;
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($show_id_grupo_tarifario ? $item->id_grupo_tarifario : $item->grupo_tarifario);

                    $cell = 'U' . $i;

                    switch ($item->status_implementacao) {
                        case 'I':
                            $label_status = 'Implementado';
                            break;

                        case 'N':
                            $label_status = 'Não implementado';
                            break;

                        case 'R':
                            $label_status = 'Revisão';
                            break;

                        default:
                            $label_status = NULL;
                            break;
                    }
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($label_status);

                    $cell = 'V' . $i;
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($item->motivo_implementacao);

                    $cell = 'W' . $i;
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($item->solucao_consulta);

                    $cell = 'X' . $i;
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($item->memoria_classificacao);

                    $cell = 'Y' . $i;
                    $this->excel->getActiveSheet()->setCellValueExplicit($cell, !empty($item->num_ex_ii) && $item->num_ex_ii > 0, PHPExcel_Cell_DataType::TYPE_STRING);

                    $cell = 'Z' . $i;
                    $this->excel->getActiveSheet()->setCellValueExplicit($cell, !empty($item->num_ex_ipi) && $item->num_ex_ipi > 0, PHPExcel_Cell_DataType::TYPE_STRING);

                    $cell = 'AA' . $i;
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($item->evento);

                    $cell = 'AB' . $i;
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($item->tag);

                    $cell = 'AC' . $i;
                    switch ($item->status_homologacao) {
                        case 0:
                            $status_homologacao = 'Não homologado';
                            break;

                        case 1:
                            $status_homologacao = 'Homologado';
                            break;

                        case 2:
                            $status_homologacao = 'Obsoleto';
                            break;

                        default:
                            $status_homologacao = NULL;
                            break;
                    }
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($status_homologacao);

                    $this->load->model('ncm_model');

                    $cell = 'AD' . $i;

                    $ncm_atual_impostos = NULL;

                    if (!empty($item->ncm_atual)) {
                        $ncm_atual_impostos = $this->ncm_model->get_impostos($item->ncm_atual);
                    }

                    $cell_data = '';

                    if (isset($ncm_atual_impostos)) {
                        if ($ncm_atual_impostos->pct_ii != NULL) {
                            $cell_data .= 'II: ' . $ncm_atual_impostos->pct_ii . '%';
                        }

                        if ($ncm_atual_impostos->pct_ipi != NULL) {
                            $cell_data .= ' IPI: ' . $ncm_atual_impostos->pct_ipi . '%';
                        }

                        if ($ncm_atual_impostos->pct_pis != NULL) {
                            $cell_data .= ' PIS: ' . $ncm_atual_impostos->pct_pis . '%';
                        }

                        if ($ncm_atual_impostos->pct_cofins != NULL) {
                            $cell_data .= ' COFINS: ' . $ncm_atual_impostos->pct_cofins . '%';
                        }
                    }

                    $this->excel->getActiveSheet()->getCell($cell)->setValue($cell_data);

                    $cell = 'AE' . $i;

                    $ncm_proposto_impostos = NULL;

                    if (!empty($item->ncm_proposto)) {
                        $ncm_proposto_impostos = $this->ncm_model->get_impostos($item->ncm_proposto);
                    }

                    $cell_data = '';

                    if (isset($ncm_proposto_impostos)) {
                        if ($ncm_proposto_impostos->pct_ii != NULL) {
                            $cell_data .= 'II: ' . $ncm_proposto_impostos->pct_ii . '%';
                        }

                        if ($ncm_proposto_impostos->pct_ipi != NULL) {
                            $cell_data .= ' IPI: ' . $ncm_proposto_impostos->pct_ipi . '%';
                        }

                        if ($ncm_proposto_impostos->pct_pis != NULL) {
                            $cell_data .= ' PIS: ' . $ncm_proposto_impostos->pct_pis . '%';
                        }

                        if ($ncm_proposto_impostos->pct_cofins != NULL) {
                            $cell_data .= ' COFINS: ' . $ncm_proposto_impostos->pct_cofins . '%';
                        }
                    }

                    $this->excel->getActiveSheet()->getCell($cell)->setValue($cell_data);

                    $cell     = 'AF' . $i;
                    $cod_cest = '';

                    if ($item->cod_cest !== null) {
                        if ($item->cod_cest == '-1') {
                            $cod_cest = 'N/D - Não atende';
                        } else {
                            $cod_cest = $item->cod_cest;
                        }
                    }

                    $this->excel->getActiveSheet()->setCellValueExplicit($cell, $cod_cest, PHPExcel_Cell_DataType::TYPE_STRING);

                    $cell = 'AG' . $i;
                    $cell_value = '';

                    if ((!empty($item->num_ex_ii) || !empty($item->num_ex_ipi)) && (!empty($item->num_ex_ii) > 0 || $item->num_ex_ipi > 0)) {
                        $cell_value = $this->addValueExToCell($item->id_item);
                    }
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($cell_value);

                    if ($customer_can_suframa) {
                        $this->excel->getActiveSheet()->getCell('AH' . $i)->setValue($item->suframa_codigo);
                        $this->excel->getActiveSheet()->getCell('AI' . $i)->setValue($item->suframa_destaque);
                        $this->excel->getActiveSheet()->getCell('AJ' . $i)->setValue($item->suframa_ppb);
                        $this->excel->getActiveSheet()->getCell('AK' . $i)->setValue($item->suframa_descricao);
                        $this->excel->getActiveSheet()->getCell('AL' . $i)->setValue($item->suframa_produto);
                        $cell = 'AL';
                    }

                    if (!empty($attrs[$key])) {
                        $initialNum = alpha2num($cell) + 1;
                        foreach($attrs[$key] as $attr) {    
                            $this->excel->getActiveSheet()->getCell($this->num2alpha($initialNum++) . $i)->setValue($attr['key']);
                            $this->excel->getActiveSheet()->getCell($this->num2alpha($initialNum++) . $i)->setValue($attr['value']);
                        }
                    }
                }

                foreach (range('A', 'Z') as $columnID) {
                    $this->excel->getActiveSheet()->getColumnDimension($columnID)->setAutoSize(true);
                }

                foreach (range('A', 'Z') as $innerID) {
                    $this->excel->getActiveSheet()->getColumnDimension('A' . $innerID)->setAutoSize(true);
                }

                $filename = 'booking_' . date('Y-m-d_H-i-s') . '.xlsx';
                $objWriter = PHPExcel_IOFactory::createWriter($this->excel, 'Excel2007');

                header('Content-Type: application/vnd.ms-excel');
                header('Content-Disposition: attachment;filename="' . $filename . '"');
                header('Cache-Control: max-age=0');

                $objWriter->save('php://output');
                exit();
            }
        }
    }

    private function num2alpha($n) {
        for ($r = ""; $n >= 0; $n = intval($n / 26) - 1) {
            $r = chr($n % 26 + 0x41) . $r;
        }

        return $r;
    }
}
