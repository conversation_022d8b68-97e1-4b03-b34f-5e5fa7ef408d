<?php

/**
 * Autenticação do cliente cadastrado no Portal de Clientes
 *
 * Redirect: Autentica usuário e vincula o mesmo a empresa setada no portal de clientes.
 *
 * @package     CodeIgniter
 * @category    Controller
 * <AUTHOR>
 * @release     07/08/2015
 * @update      07/08/2015
 **/

class Auth extends MY_Controller
{

    var $private_key                = 'afd9fde4362e3cf7435fdc6824ded329'; // @Portal de Clientes
    var $table_usuario              = 'usuario';
    var $table_empresa              = 'empresa';
    var $table_rel_usuario_empresa  = 'rel_usuario_empresa';

    /**
     * Método responsável por fazer a autenticação do usuário
     * @method(get)  [varchar]          ?q=     [Responsável por trazer todos dados do usuário encriptado]
     * @return       [redirect/view]            [Se a autenticação der certo, é feito login e o usuário é redirecionado. Caso dê problema, exibe uma tela de erro]
     */
    public function index()
    {
        if ($hash_url = $this->input->get('q')) {
            $this->load->library('encrypt');
            $dados_decriptados = $this->encrypt->decode($hash_url, $this->private_key);

            $dados_decompactados = gzinflate($dados_decriptados);
            $dados_integracao = unserialize($dados_decompactados);

            // Dados de acesso do usuário
            $email                  = strtolower($dados_integracao['email']);
            // $nome                   = $dados_integracao['nome'];
            // $empresas               = $dados_integracao['empresas'];
            // $produto_tipo           = $dados_integracao['produto_tipo'];
            // $produto_nome           = $dados_integracao['produto_nome'];
            // $url_portal_clientes    = $dados_integracao['url_portal_clientes'];
            // $time                   = $dados_integracao['time'];

            if (isset($email)) {
                $data = array();

                $id_usuario = $this->verifica_email($email);

                if (!$this->link_valido($dados_integracao)) $data['errors'][] = 'O link expirou. Para atualizar o link, é necessário atualizar a página onde o link foi gerado.';

                if ($id_usuario == FALSE)
                    $data['errors'][] = 'Não há nenhum usuário cadastrado com esse e-mail &nbsp;<<strong>' . $email . '</strong>>';

                $this->load->model('usuario_model');
                $result = $this->usuario_model->check_empresa_ativa($email);
                if ($result == false)
                {
                    $data['errors'][] = 'Não é possível acessar uma empresa inativa';
                }

                // $result_empresas = $this->verifica_empresa($empresas);

                // if ($result_empresas['status'] == 'error') {
                //     $data['errors'] = $result_empresas['msg'];
                // }

                // if (($id_usuario == FALSE) || $result_empresas['status'] == 'error') {

                if (!empty($data['errors'])) {
                    $id_usuario = false;
                    $data['has_user'] = $id_usuario;
                    $data['resultado'] = $dados_integracao;
                    $this->load->view('auth', $data);
                } else {
                    $usuario = $this->resgata_usuario($id_usuario);

                    $this->session->set_userdata('user_id', $id_usuario);
                    $this->session->set_userdata('user_nome', $usuario->nome);
                    $this->session->set_userdata('perguntas_pendentes', true);

                    set_logon();
                    redirect('/');
                }
            }
        }
    }

    private function link_valido($dados_integracao)
    {
        try {
            $time = $dados_integracao['time'];
            $date = $date = new DateTime();
            $interval = config_item('interval_time_login');
            $date->sub(new DateInterval($interval));

            if (new DateTime($time) <= $date) {
                return false;
            }

            return true;
        } catch (Exception $e) {
            show_error('O link expirou. Para atualizar o link, é necessário atualizar a página onde o link foi gerado.');
        }
    }

    /**
     * Método responsável por resgatar informações do usuário.
     * @param  [int]            $id_usuario [ID do usuário resgatado pelo metodo $this->verifica_email()]
     * @return [boolean/object]             [Se existir usuário com o $id_usuario passado, retorna os dados cadastrados, se não retorna FALSE]
     */
    private function resgata_usuario($id_usuario)
    {
        $this->db->where('id_usuario', $id_usuario);
        $query = $this->db->get($this->table_usuario);

        if ($query->num_rows() > 0) {
            return $row = $query->row();
        }

        return FALSE;
    }

    /**
     * Método responsável por verificar se o usuário existe.
     * @param  [varchar]            $email  [EMAIL do usuário resgatado pelo dado encriptado]
     * @return [boolean/int]                [Se existir usuário com o $email passado, retorna o id_usuario, se não retorna FALSE]
     */
    private function verifica_email($email)
    {
        $this->db->where('email', $email);
        $query = $this->db->get($this->table_usuario);

        if ($query->num_rows() > 0) {
            $row = $query->row();

            return $row->id_usuario;
        }

        return FALSE;
    }

    /**
     * Método responsável por verificar se a empresa existe.
     * @param  [Array]           $empresas [Array de CNPJ => Razão Social das empresas resgatado pelo dado encriptado]
     * @return [boolean/int]   [Se existir empresa com o $cnpj passado, retorna o id_empresa, se não retorna FALSE]
     */
    private function verifica_empresa($empresas)
    {
        $msg = array();
        $found = 0;

        foreach ($empresas as $cnpj  => $razao_social) {
            $this->db->where('cnpj', $cnpj);
            $query = $this->db->get($this->table_empresa);

            if ($query->num_rows() == 0) {
                $msg[] = 'Não há nenhuma empresa cadastrada com esse cnpj <<strong>' . $cnpj . '</strong>>';
            } else {
                $found++;

                foreach ($query->result() as $row) {
                    $id_empresas[] = $row->id_empresa;
                }
            }
        }

        if (!empty($msg) && $found == 0) {
            $data['status'] = 'error';
            $data['msg'] = $msg;
        } else {
            $data['status'] = 'success';
            $data['id_empresas'] = $id_empresas;
        }

        return $data;
    }

    /**
     * Método responsável por verificar e relacionar o usuário com a empresa.
     * @param  [int]        $id_usuario [ID do usuário resgatado pelo e-mail]
     * @param  [int]        $id_empresa [ID da empresa resgatado pelo cnpj  ]
     * @return [boolean]                [Retorna de acordo com o update/insert ou true caso não entre em nenhum if]
     */
    private function relaciona_usuario_empresa($id_usuario, $id_empresa)
    {
        // Verifica se existe a tabela de relacionamento entre empresa / usuario.
        if ($this->db->table_exists($this->table_rel_usuario_empresa)) {
            // Verifica se já está cadastrado, caso não esteja, faz insert.
            $this->db->where('id_empresa', $id_empresa);
            $this->db->where('id_usuario', $id_usuario);
            $query = $this->db->get($this->table_rel_usuario_empresa);

            if ($query->num_rows() == 0) {

                $insert = array(
                    'id_empresa' => $id_empresa,
                    'id_usuario' => $id_usuario
                );

                return $this->db->insert($this->table_rel_usuario_empresa, $insert);
            }
        }

        if ($this->db->field_exists('id_empresa', $this->table_usuario)) {
            $update = array(
                'id_empresa' => $id_empresa
            );

            $this->db->where('id_usuario', $id_usuario);
            return $this->db->update($this->table_usuario, $update);
        }

        return true;
    }
}
