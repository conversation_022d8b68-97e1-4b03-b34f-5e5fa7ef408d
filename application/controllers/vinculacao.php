<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

class Vinculacao extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }
        $this->load->model('ncm_model');

        $this->load->model('empresa_model');
        $empresa = $this->empresa_model->get_entry(sess_user_company());

        $allowed_pages = array('pre_selecao', 'ajax_save_pre_selecao', 'ajax_pagination_pre_selecao');

        if (( !customer_can('atr') && !customer_can('ii') && !customer_can('ipi') && !customer_can('nve') && !customer_can('cest')) && !in_array($this->uri->segment(2), $allowed_pages)) {
            show_permission();
        }
    }

    public function index()
    {
        $this->load->model(array(
            'cad_item_model',
            'empresa_model',
            'empresa_ex_tarifario_model',
            'catalogo/produto_model'
        ));
        
        $data = array();

        $id_empresa = sess_user_company();

        $empresa = $this->empresa_model->get_entry($id_empresa);

        if ((customer_can('atr') || customer_can('ii') || customer_can('ipi') || customer_can('nve') || customer_can('cest'))) {
            $pendencias = $this->cad_item_model->get_pendencias();

            // Total Geral
            $this->_total_geral_alertas = ($pendencias->pendencias_ex_ii + $pendencias->pendencias_ex_ipi + $pendencias->pendencias_nve + $pendencias->pendencias_attrs);

            // EX de II
            $data['ex_ii'] = array(
                'pendencias' => $pendencias->pendencias_ex_ii,
                'classificado' => $pendencias->itens_classif_ex_ii,
            );

            // EX de IPI
            $data['ex_ipi'] = array(
                'pendencias' => $pendencias->pendencias_ex_ipi,
                'classificado' => $pendencias->itens_classif_ex_ipi,
            );

            // NVE
            $data['nve'] = array(
                'pendencias' => $pendencias->pendencias_nve,
                'classificado' => $pendencias->itens_classif_nve,
            );

            // CEST
            $data['cest'] = array(
                'pendencias' => $pendencias->pendencias_cest,
                'classificado' => $pendencias->itens_classif_cest
            );

            $result = $this->produto_model->get_all_attr_ncm();
            
            //Atributos
            $data['attrs'] = array(
                'total' => $result['total'],
                'classificado' => $result['total_atribuidos']
            );
        }
        
        $data['part_number_search'] = null;
        $data['ncm_search'] = null;

        $empresa = $this->empresa_model->get_entry($id_empresa);
        $data['multi_estabelecimentos'] = $empresa->multi_estabelecimentos;

        $data['show_auto_filtro'] = $this->empresa_ex_tarifario_model->check_empresa_has_pre_selecao($id_empresa);

        if ($get = $this->input->get()) {
            if ($this->input->is_set('part_number')) {
                $data['part_number_search'] = $get['part_number'];
            }

            if ($this->input->is_set('ncm')) {
                $data['ncm_search'] = $get['ncm'];
            }
        }

        // PEGA TODOS OS EVENTOS DA EMPRESA
        $data['eventos'] = $this->cad_item_model->get_all_evento_in_item_by_empresa();

        $this->include_js(array('bootstrap-select/bootstrap-select.js'));
        $this->include_css('bootstrap-select/bootstrap-select.css');

        $this->render('vinculacao/index', $data);
    }

    public function ajax_get_cest_cod_segmento()
    {
        $this->load->model('cest_model');

        if ($post = $this->input->post()) {
            $data = array();

            if ($this->input->is_set('order')) {
                $order_by = 'descricao '.$post['order']['by'];
            }

            $ncm = isset($post['ncm']) ? $post['ncm'] : '';
            $auto_filtro = $post['auto_filtro'];

            $data = $this->cest_model->get_entries_by_ncm_cod_segmento($ncm);

            echo json_encode($data);

            return true;
        }
    }

    public function ajax_get_cest()
    {
        $this->load->model('cest_model');

        if ($post = $this->input->post()) {
            $data = array();

            if ($this->input->is_set('order')) {
                $order_by = 'descricao '.$post['order']['by'];
            }

            $ncm = isset($post['ncm']) ? $post['ncm'] : '';
            $cod_segmento = isset($post['cod_segmento']) ? $post['cod_segmento'] : '';
            $auto_filtro = $post['auto_filtro'];

            $data['cest'] = $this->cest_model->get_entries_by_ncm($ncm, $cod_segmento);

            echo json_encode($data);

            return true;
        }
    }

    public function deletar()
    {
        $this->render('vinculacao/deletar');
    }

    public function ajax_get_item()
    {
        $this->load->model('item_model');

        if ($post = $this->input->post()) {
            $data = array();

            $data['item'] = $this->item_model->get_entry($post['part_number'], sess_user_company());

            echo json_encode($data);

            return true;
        }
    }

    public function ajax_get_cad_itens()
    {
        $this->load->model('cad_item_model');
        $this->load->model('catalogo/produto_model');
 
        if ($post = $this->input->post()) {
            $this->cad_item_model->set_state_store_session(true);
            $this->cad_item_model->clear_states();

            $data = array();

            $order_by = $post['order']['order'].' '.$post['order']['by'];

            if ($this->input->is_set('item_input'))
            {
                $this->apply_search_rules($post['item_input']);
            }
            $bk = null;
            $bit = null;
            if (!empty($post['bk_bit']))
            {
                if (in_array('bk', $post['bk_bit']))
                {
                    $bk = 'S';
                }

                if (in_array('bit', $post['bk_bit']))
                {
                    $bit = 'S';
                }
            }

            $nacional = null;
            $importado = null;
            if (!empty($post['nacional_importado']))
            {
                if (in_array('nacional', $post['nacional_importado']))
                {
                    $nacional = 'S';
                }

                if (in_array('importado', $post['nacional_importado']))
                {
                    $importado = 'S';
                }
            }

            $eventos = !empty($post['eventos']) ? $post['eventos'] : NULL;

            $this->cad_item_model->set_state('filter.ncm_proposto', $post['ncm']);
            $this->cad_item_model->set_state('filter.id_empresa', sess_user_company());
            $this->cad_item_model->set_state('filter.order_by', $order_by);
            $this->cad_item_model->set_state('filter.auto_filtro', $post['auto_filtro']);
            $this->cad_item_model->set_state('filter.bk', $bk);
            $this->cad_item_model->set_state('filter.bit', $bit);
            $this->cad_item_model->set_state('filter.nacional', $nacional);
            $this->cad_item_model->set_state('filter.importado', $importado);
            $this->cad_item_model->set_state('filter.eventos', $eventos);
            $this->cad_item_model->set_state('filter.with_metrics', true);

            $data['itens'] = $this->cad_item_model->get_entries_by_pn_or_desc_mercado_local();

            echo json_encode($data);

            return true;
        }
    }

    public function ajax_get_ex_tarifarios()
    {
        $this->load->model('ex_tarifario_model');

        if ($post = $this->input->post()) {
            $data = array();

            if ($this->input->is_set('order')) {
                $order_by = 'descricao '.$post['order']['by'];
            }

            $ncm = isset($post['ncm']) ? $post['ncm'] : '';
            $auto_filtro = $post['auto_filtro'];

            $data['ex_ii'] = $this->ex_tarifario_model->get_all_ex_ii_by_ncm($ncm, true, $auto_filtro);
            $data['ex_ipi'] = $this->ex_tarifario_model->get_all_ex_ipi_by_ncm($ncm, true, $auto_filtro);

            echo json_encode($data);

            return true;
        }
    }

    public function ajax_get_ncm_info()
    {
        if ($post = $this->input->post()) {
            if (isset($post['ncm']) && !empty($post['ncm'])) {
                $ncm_entries = $this->ncm_model->get_entries_levels($post['ncm']);

                $data['ncm_entries'] = $ncm_entries;
                $data['ncm'] = $post['ncm'];

                $this->load->view('homologacao/ncm_table', $data);
            }
        }
    }

    private function apply_search_rules($search)
    {
        $this->load->model('cad_item_model');

        $part_numbers = $search;

        if (!is_array($part_numbers) && !empty($part_numbers))
        {
            $part_numbers = str_replace(array("\t", "\r\n", "\s", "\n"), ' ', $part_numbers);

            $matches = array();
            preg_match_all('/"([^"]*)"/',$part_numbers,$matches);
            $btw_quotes = $matches[1];

            $part_numbers = str_replace("*", "%", $part_numbers);
            $part_numbers = preg_replace('/"([^"]*)"/', "", $part_numbers);

            //Acha todos os partnumbers entre aspas simples
            $matches_simple = array();
            preg_match_all('~\'(.*?)\'~', $part_numbers, $matches_simple);
            $btw_simple_quotes = $matches_simple[1];

            //Retira da string todos os partnumbers entre aspas simples
            $part_numbers = preg_replace('~\'(.*?)\'~', "", $part_numbers);

            $part_numbers = explode(" ", addslashes($part_numbers));
            $part_numbers = array_filter($part_numbers);

            if (!empty($btw_quotes))
            {
                $addslashes_btw_quotes = implode(',', $btw_quotes);
                $btw_quotes = explode(",", addslashes($addslashes_btw_quotes));
            }

            $part_numbers = array_merge($part_numbers, $btw_quotes);
            $part_numbers = array_merge($part_numbers, $btw_simple_quotes);

            $generic_part_numbers = array();

            foreach ($part_numbers as $key => $part_number)
            {
                if (strpos($part_number, "%"))
                {
                    $generic_part_numbers[] = $part_number;
                    unset($part_numbers[$key]);
                }
            }

            if (!empty($part_numbers))
            {
                $this->cad_item_model->set_state('filter.part_numbers', $part_numbers);
            } else
            {
                $this->cad_item_model->unset_state('filter.part_numbers');
            }

            if (!empty($generic_part_numbers))
            {
                $this->cad_item_model->set_state('filter.generic_part_numbers', $generic_part_numbers);
            } else
            {
                $this->cad_item_model->unset_state('filter.generic_part_numbers', $generic_part_numbers);
            }
        } else
        {
            $this->cad_item_model->unset_state('filter.part_numbers');
            $this->cad_item_model->unset_state('filter.generic_part_numbers');
        }
    }

    public function salvar()
    {
        if ($post = $this->input->post()) {

            $this->load->model(array(
                'cad_item_model',
                'ex_tarifario_model',
                'nve_atributo_model',
                'cad_item_nve_model',
                'cad_item_attr_model',
                'item_log_model',
                'item_cest_model'
            ));

            $this->cad_item_model->set_state_store_session(true);

            $dbdata = array();

            $refresh_total_alertas = false;

            if ($this->input->is_set('ex_ipi') && !empty($post['ex_ipi'])) {
                $dbdata['num_ex_ipi'] = $post['ex_ipi'];
                $this->cad_item_model->unset_state('total_pendencias_ex_ipi');
                $refresh_total_alertas = true;
            }

            if ($this->input->is_set('ex_ii') && !empty($post['ex_ii'])) {
                $dbdata['num_ex_ii'] = $post['ex_ii'];
                $this->cad_item_model->unset_state('total_pendencias_ex_ii');
                $refresh_total_alertas = true;
            }

            if ($this->input->is_set('cest') && !empty($post['cest'])) {
                $dbdata['cod_cest'] = $post['cest'];
                $this->cad_item_model->unset_state('total_pendencias_cest');
                $refresh_total_alertas = true;
            }

            $nve_atributos = json_decode($post['nve'], true);

            if (!empty($nve_atributos)) {
                $this->cad_item_model->unset_state('total_pendencias_nve');
                $refresh_total_alertas = true;
            }

            if ($refresh_total_alertas === true) {
                $this->cad_item_model->unset_state('total_geral_alertas');
            }

            $dbdata_nve = array();
            $erro = true;
            $has_attr_item = false;

            foreach ($post['itens'] as $item) {
                $part_number = $item['part_number'];
                $estabelecimento = $item['estabelecimento'];

                $log_data['part_number'] = $part_number;
                $log_data['estabelecimento'] = $estabelecimento;
                $log_data['id_usuario'] = sess_user_id();
                $log_data['id_empresa'] = sess_user_company();
                $log_data['criado_em'] = date('Y-m-d H:i:s');

                $cad_item = $this->cad_item_model->get_entry_by_pn($part_number, sess_user_company(), $estabelecimento);
                $has_attr_item = $has_attr_item == false ? $this->cad_item_attr_model->has_attr_empty($cad_item->id_item) : $has_attr_item;

                $cad_item_array = \json_decode(\json_encode($cad_item), TRUE);
                try {
                    $this->cad_item_attr_model->set_state('filter.do_not_update', true);    
                    $this->cad_item_attr_model->set_state('filter.vinculacao', true);   
                    $this->cad_item_attr_model->save_attrs($post['raw_attrs'], $cad_item_array);
                    $erro = FALSE;

                } catch (\Exception $e) {
                    $erro = TRUE;
                }

                $dbdata_nve['id_item'] = $cad_item->id_item;

                if (!empty($dbdata)) {
                    if ($this->cad_item_model->save($dbdata, 'part_number = "'.$part_number.'" AND estabelecimento = "'.$estabelecimento.'" AND id_empresa = '.sess_user_company())) {
                        // exit('aqui');
                        if (!empty($dbdata['num_ex_ipi'])) {
                            // Descrição EX de IPI.
                            if ($post['ex_ipi'] == -1) {
                                $descricao_ipi = 'Item não atende EX.';
                            } else {
                                $ipi = $this->ex_tarifario_model->get_ex_ipi_by_ncm($post['ex_ipi'], $cad_item->ncm_proposto);
                                $descricao_ipi = "<strong>EX:</strong> {$ipi->num_ex}
                                <br /><strong>Descrição:</strong> {$ipi->descricao_linha1}";
                            }

                            $log_data['titulo'] = 'vinculacao_ex';
                            $log_data['motivo'] = '<strong>EX de IPI vinculado</strong><br />'.$descricao_ipi;

                            $this->item_log_model->save($log_data);
                            $erro = false;
                        }

                        if (!empty($dbdata['num_ex_ii'])) {
                            // Descrição EX de II.
                            if ($post['ex_ii'] == -1) {
                                $descricao_ii = 'Item não atende EX.';
                            } else {
                                $ii = $this->ex_tarifario_model->get_ex_ii_by_ncm($post['ex_ii'], $cad_item->ncm_proposto);
                                $descricao_ii = "<strong>EX:</strong> {$ii->num_ex}
                                <br /><strong>Descrição:</strong> {$ii->descricao_linha1}";
                            }

                            $log_data['titulo'] = 'vinculacao_ex';
                            $log_data['motivo'] = '<strong>EX de II vinculado</strong><br />'.$descricao_ii;

                            $this->item_log_model->save($log_data);
                            $erro = false;
                        }

                        if (!empty($dbdata['cod_cest']))
                        {
                            // Alteração de CEST.
                            if ($post['cest'] == -1) {
                                $descricao = 'Item não atende CEST.';
                            } else {
                                $descricao = "<strong>COD CEST:</strong> {$post['cest']}";
                            }

                            $log_data['titulo'] = 'vinculacao_cest_novo';
                            $log_data['motivo'] = '<strong>CEST Vinculado</strong><br />'.$descricao;

                            $this->item_log_model->save($log_data);

                            $motivo = $log_data['motivo'];

                            if (!$this->item_cest_model->insert_item_log($part_number, sess_user_company(), $estabelecimento, array(), $motivo)) {
                                $this->message_on_render('<strong>Oops!</strong> Ocorreu um erro ao gerar um log de CEST para o item selecionado.', 'error');
                                $erro = true;
                            }

                            $erro = false;
                        }
                    }
                }

                foreach ($nve_atributos as $key => $attr) {
                    $dbdata_nve['nve_atributo'] = $key;
                    $dbdata_nve['nve_valor'] = $attr;

                    if ($this->cad_item_nve_model->save($dbdata_nve)) {
                        $nve = $this->nve_atributo_model->get_valor($cad_item->ncm_proposto, $key, $attr);

                        $log_data['titulo'] = 'vinculacao_nve';
                        $log_data['motivo'] = '
                            <strong>Atributo:</strong> '.$key.'<br />
                            <strong>Código:</strong> '.$attr.'<br />
                            <strong>Descrição: </strong> '.$nve->nm_especif_ncm;

                        $this->item_log_model->save($log_data);
                        $erro = false;
                    }
                }
            }

            if ($erro === false) {
                $this->message_next_render('<strong>OK!</strong> Atribuição realizada com sucesso');
            } else {
                $this->message_next_render('<strong>Oops!</strong> Ocorreu um erro ao atribuir o grupo', 'error');
            }
        }

        echo json_encode(['has_attr_item' => $has_attr_item]);

        return ;
    }

    public function ajax_get_nve()
    {
        if ($post = $this->input->post()) {
            $this->load->model('nve_atributo_model');
            $this->load->model('cad_item_nve_model');

            $data = array();

            $data['ncm'] = $ncm = $post['ncm'];
            $data['atributos'] = $this->nve_atributo_model->get_atributos_by_ncm($ncm);

            $view_data['response'] = $this->load->view('homologacao/subview_nve', $data, true);
            $view_data['has_atributos'] = empty($data['atributos']) ? false : true;

            echo json_encode($view_data);

            return true;
        }
    }

    public function pre_selecao()
    {
        if (!has_role('ncm_pre_selecao')) {
            show_permission();
        }

        $data = array();

        $data['search_term'] = null;
        $data['ex_ii_entries'] = array();
        $data['ex_ipi_entries'] = array();

        $this->load->model('ex_tarifario_model');

        if ($this->input->post()) {

            if (!empty($this->input->post('reset_filter')))
            {
                $this->usuario_model->clear_states();
            }
            else
            {
                $this->load->library('form_validation');

                if (! $this->input->post('somente_marcados')) {
                    $this->form_validation->set_rules('search', 'Palavra-chave', 'trim|required');
                } else {
                    $this->form_validation->set_rules('search', 'Palavra-chave', 'trim');
                }

                $this->form_validation->set_rules('search_ncm', 'NCM', 'trim');
                $this->form_validation->set_rules('search_except', 'Termo a desconsiderar', 'trim');
                $this->form_validation->set_rules('somente_marcados', 'Mostrar somente os marcados', 'trim');

                if ($this->form_validation->run()) {
                    $search = $this->input->post('search');
                    $search_except = $this->input->post('search_except');
                    $search_ncm = $this->input->post('search_ncm');
                    $somente_marcados = $this->input->post('somente_marcados');

                    $this->ex_tarifario_model->set_state('filter.search', $search);
                    $this->ex_tarifario_model->set_state('filter.search_except', $search_except);
                    $this->ex_tarifario_model->set_state('filter.search_ncm', $search_ncm);
                    $this->ex_tarifario_model->set_state('filter.somente_marcados', $somente_marcados);

                    $limit = 50;

                    $data['search_term'] = $search;
                    $data['somente_marcados'] = $somente_marcados;
                    $data['ex_ii_entries'] = $this->ex_tarifario_model->get_empresa_ex_entries('ii', $limit);
                    $data['ex_ipi_entries'] = $this->ex_tarifario_model->get_empresa_ex_entries('ipi', $limit);

                    // Próxima página
                    $data['ex_ii_next_page'] = $this->ex_tarifario_model->get_empresa_ex_entries('ii', 1, ($limit*1));
                    $data['ex_ipi_next_page'] = $this->ex_tarifario_model->get_empresa_ex_entries('ipi', 1, ($limit*1));
                } else {
                    $err = '<h4>Ops... alguns erros aconteceram</h4><ul>'.validation_errors('<li>', '</li>').'</ul>';
                    $this->message_on_render($err, 'error');
                }
            }
        }

        $this->render('vinculacao/pre_selecao', $data);
    }

    public function ajax_save_pre_selecao()
    {
        if ($this->input->post()) {
            $this->load->model('empresa_ex_tarifario_model');

            $num_ex = $this->input->post('num_ex');
            $cod_ncm = $this->input->post('cod_ncm');
            $cod_tipo_ex = $this->input->post('cod_tipo_ex');
            $id_empresa = sess_user_company();

            $db_data = array(
                'num_ex' => $num_ex,
                'cod_ncm' => $cod_ncm,
                'id_empresa' => $id_empresa,
                'cod_tipo_ex' => $cod_tipo_ex
            );

            $err = false;

            if ($this->empresa_ex_tarifario_model->check_exists($num_ex, $cod_ncm, $cod_tipo_ex, $id_empresa)) {
                if ($this->empresa_ex_tarifario_model->remove($num_ex, $cod_ncm, $cod_tipo_ex, $id_empresa)) {
                    $response['msg'] = $this->message_config('<strong>OK! </strong> Pré-seleção de EX Tarifário removida com sucesso.', 'success');
                } else {
                    $err = true;
                }
            } else {
                if ($this->empresa_ex_tarifario_model->save($db_data)) {
                    $response['msg'] = $this->message_config('<strong>OK! </strong> Pré-seleção de EX Tarifário concluída com sucesso.', 'success');
                } else {
                    $err = true;
                }
            }

            if ($err === false) {
                $response['status'] = 1;
            } else {
                $response['status'] = 0;
                $response['msg'] = $this->message_config('<strong>Oops! </strong> Ocorreu um erro ao salvar a pré-seleção.', 'error');
            }

            echo json_encode($response);

            return true;
        }
    }

    public function ajax_pagination_pre_selecao()
    {
        if ($this->input->post()) {
            $this->load->model('ex_tarifario_model');

            $page = $this->input->post('page');
            $type = $this->input->post('type');

            $limit = 50;
            $offset = (($page > 0 ? $page : 1) * $limit);
            $next_page_offset = (($page+1) * $limit);

            $search = $this->input->post('search');
            $search_ncm = $this->input->post('search_ncm');
            $search_except = $this->input->post('search_except');
            $somente_marcados = $this->input->post('somente_marcados');

            $this->ex_tarifario_model->set_state('filter.search', $search);
            $this->ex_tarifario_model->set_state('filter.search_except', $search_except);
            $this->ex_tarifario_model->set_state('filter.search_ncm', $search_ncm);
            $this->ex_tarifario_model->set_state('filter.somente_marcados', $somente_marcados);

            $data = $this->ex_tarifario_model->get_empresa_ex_entries($type, $limit, $offset);
            $next_page = $this->ex_tarifario_model->get_empresa_ex_entries($type, 1, (($page+1) * $limit));

            $has_next_page = ($next_page ? true : false);

            $result = array(
                'next_page' => $has_next_page,
                'data' => $data
            );

            echo json_encode($result);
        }
    }

    // @TODO ajustar aqui o get atributos
    public function ajax_get_atributos()
    {
        if ($post = $this->input->post()) {
            $this->load->model(array(
                'catalogo/produto_model',
                'grupo_tarifario_attr_model'
            ));

            $ncm = trim($post['ncm']);
            $atributos = !empty($ncm) ? $this->produto_model->get_attr_ncm($ncm) : [];

            return response_json(array(
                'response' => $this->load->view('atribuir_grupo/modal_atribuir_grupo/aba_subview_atributos', array(
                    'items' => $atributos,
                    'ncm' => $ncm,
                    'idGrupoTarifario' => $post['idGrupoTarifario']
                ), true),
                'has_itens' => $this->grupo_tarifario_attr_model->has_attrs_empty_item_ncm($post['pns'])
            ));
        }

        return response_json(array());
    }
}
