<?php

class Monitor_ex extends MY_Controller
{
    private $_tabs_editing = array('dados-basicos', 'partnumbers', 'arquivos');

    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }

        $this->load->model('monitor_ex/ctrl_ex_tarifario_model');
        $this->load->model('monitor_ex/arquivo_model');
        $this->load->library('breadcrumbs');

        $this->ctrl_ex_tarifario_model->set_namespace('monitor_ex');

        $this->ctrl_ex_tarifario_model->set_state_store_session(TRUE);
        $this->ctrl_ex_tarifario_model->restore_state_from_session();

        if ($this->input->is_set('reset_filters')) {
            $this->ctrl_ex_tarifario_model->clear_states();
        }

        if (!customer_can('ii')) {
            show_error('MONITOR DE EX DESABILITADO PARA EMPRESA ATUAL (Entre em contato com os consultores Becomex).', 401);
        }

        if (!has_role('monitor_ex')) {
            show_permission();
        }
    }

    public function index($page = 0)
    {
        $page = $this->input->get('per_page');
        $limit = 10;

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Monitor de EX / FTA', '/monitor-ex/');
        $data = array();

        $this->load->library('pagination');

        $offset = ($page > 0 ? $page - 1 : 0) * $limit;

        if ($post = $this->input->post()) {
            if ($post['submit'] == 1) {
                $post['tipo'] = isset($post['tipo']) ? $post['tipo'] : array();
                $this->ctrl_ex_tarifario_model->set_state('filter.id_tipo', $post['tipo']);
                $post['evento'] = isset($post['evento']) ? $post['evento'] : array();
                $this->ctrl_ex_tarifario_model->set_state('filter.id_evento', $post['evento']);
                $post['pacote'] = isset($post['pacote']) ? $post['pacote'] : array();
                $this->ctrl_ex_tarifario_model->set_state('filter.id_pacote', $post['pacote']);
                $post['status'] = isset($post['status']) ? $post['status'] : array();
                $this->ctrl_ex_tarifario_model->set_state('filter.id_status', $post['status']);
                $post['search_type'] = isset($post['search_type']) ? $post['search_type'] : array();
                $this->ctrl_ex_tarifario_model->set_state('filter.search_type', $post['search_type']);
                $this->apply_default_filters();
            }
        }

        $total_entries = $this->ctrl_ex_tarifario_model->get_total_entries();
        $entries = $this->ctrl_ex_tarifario_model->get_entries($limit, $offset);

        $query_str = '';
        $config['base_url'] = base_url("/monitor-ex?{$query_str}");
        $config['use_page_numbers'] = TRUE;
        $config['total_rows'] = $total_entries;
        $config['per_page'] = $limit;
        $config['page_query_string'] = TRUE;

        $this->pagination->initialize($config);

        $this->load->model(array(
            'monitor_ex/tipo_ex_model',
            'monitor_ex/evento_model',
            'monitor_ex/pacote_model',
            'monitor_ex/status_model'
        ));

        $data['filters'] = array(
            'tipo' => $this->tipo_ex_model->get_entries(),
            'evento' => $this->evento_model->get_entries(),
            'pacote' => $this->pacote_model->get_entries(),
            'status' => $this->status_model->get_entries()
        );

        $this->include_js(
            array(
                'bootstrap-select/bootstrap-select.js'
            )
        );

        $this->include_css(
            array(
                'bootstrap-select/bootstrap-select.css',
                'monitor_ex.css'
            )
        );

        $data['total_entries'] = $total_entries;
        $data['entries']       = $entries;

        $data['pagination'] = $this->pagination->create_links();

        $this->render('monitor_ex/default', $data);
    }

    public function download($id_arquivo, $where_type = NULL)
    {
        if ($where_type == NULL) {
            $arquivo = $this->arquivo_model->get_entry($id_arquivo);
            $arquivo = $arquivo[0];

            if ($arquivo->ext == '.docx') {
                header("Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            } else {
                header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            }

            $string = str_replace(',', '', $arquivo->name);

            header("Content-Disposition: attachment; filename=" . $string);
            header("Pragma: no-cache");
            header("Expires: 0");

            if (PHP_OS == 'WINNT') {
                readfile(FCPATH . 'assets\uploads\monitor_ex\\' . $arquivo->filename);
            } else {
                readfile(FCPATH . 'assets/uploads/monitor_ex/' . $arquivo->filename);
            }
        }
    }

    protected function apply_default_filters()
    {
        if ($this->input->is_set('search')) {
            $part_numbers = $this->input->post('search');

            if (!is_array($part_numbers) && !empty($part_numbers)) {

                $this->ctrl_ex_tarifario_model->set_state('filter.search_view', $part_numbers);

                $separator = get_company_separator(sess_user_company());

                $part_numbers = str_replace(array("\t", "\r\n", "\s", "\n"), $separator, $this->input->post('search'));

                $matches = array();
                preg_match_all('/"([^"]*)"/', $part_numbers, $matches);
                $btw_quotes = $matches[1];

                $part_numbers = str_replace("*", "%", $part_numbers);
                $part_numbers = preg_replace('/"([^"]*)"/', "", $part_numbers);

                //Acha todos os partnumbers entre aspas simples
                $matches_simple = array();
                preg_match_all('~\'(.*?)\'~', $part_numbers, $matches_simple);
                $btw_simple_quotes = $matches_simple[1];

                //Retira da string todos os partnumbers entre aspas simples
                $part_numbers = preg_replace('~\'(.*?)\'~', "", $part_numbers);

                $part_numbers = explode($separator, addslashes($part_numbers));
                $part_numbers = array_filter($part_numbers);

                if (!empty($btw_quotes)) {
                    $addslashes_btw_quotes = implode(',', $btw_quotes);
                    $btw_quotes = explode(",", addslashes($addslashes_btw_quotes));
                }

                $part_numbers = array_merge($part_numbers, $btw_quotes);
                $part_numbers = array_merge($part_numbers, $btw_simple_quotes);

                if (!empty($part_numbers)) {
                    $this->ctrl_ex_tarifario_model->set_state('filter.search', $part_numbers);
                } else {
                    $this->ctrl_ex_tarifario_model->unset_state('filter.search');
                }
            } else {
                $this->ctrl_ex_tarifario_model->unset_state('filter.search');
                $this->ctrl_ex_tarifario_model->unset_state('filter.search_view');
            }
        }
    }

    protected function processa_homologacao($post, $id_mvto, $id)
    {
        if (array_key_exists('homologar', $post)) {
            $homologado = 1;
            $id_status = $post['id_status'] + 1;
        } else {
            $homologado = 0;
            $id_status = $post['id_status'] - 1;
        }

        $dbdata = array(
            'id_mvto' => $id_mvto,
            'id_ctrl_ex' => $id,
            'data_homologado' => date('Y-m-d h:m:s'),
            'homologado' => $homologado,
        );

        $id_arquivo = NULL;
        if (isset($_FILES) && !empty($_FILES['file']['name'])) {
            $id_arquivo = $this->upload_file(TRUE);
            if (array_key_exists('status', $id_arquivo)) {
                if ($id_arquivo['status'] == 'error') {
                    redirect('/monitor_ex/homologacao_cliente/' . $id . '/' . $id_mvto . '?insert=error&error=' . base64_encode($id_arquivo['error']));
                }
            }
        }

        $this->mvto_model->homologar($dbdata, array('id_mvto' => $id_mvto, 'id_ctrl_ex' => $id));

        $insertData = array(
            'id_ctrl_ex' => $id,
            'id_status' => $id_status,
            'id_user' => sess_user_id(),
            'data_ref' => date('Y-m-d h:m:s'),
            'id_arquivo' => is_numeric($id_arquivo) ? $id_arquivo : '',
            'descricao' => $post['descricao_status']
        );
        try {
            $id_mvto = $this->mvto_model->save($insertData);

            $this->ctrl_ex_tarifario_model->save(array('id_status' => $id_status), array('id_ctrl_ex' => $id));
        } catch (Exception $e) {
            throw new Exception('Ocorreu um erro inesperado.');
        }
        redirect('/monitor_ex/detalhes/' . $id . '/historico');
    }

    public function homologacao_cliente($id, $id_mvto = null)
    {
        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Monitor de EX / FTA', '/monitor-ex/');
        $this->breadcrumbs->push('Homologação Cliente', '/monitor-ex/homologacao_cliente/');

        $tab = 'homologacao-cliente';

        try {
            $entry = $this->ctrl_ex_tarifario_model->get_entry_details($id);
        } catch (Exception $e) {
            show_404();
        }

        $data['entry'] = $entry;

        $data['tab'] = $tab;

        $this->load->model('monitor_ex/status_model');
        $this->load->model('monitor_ex/mvto_model');
        $this->load->model('monitor_ex/tipo_ex_model');

        $pendencia = $this->mvto_model->get_pendencia_by_ctrl_ex($id);
        $data['pendencia'] = $pendencia;

        if (isset($pendencia) && $pendencia == NULL) {
            show_error('Não existe nenhuma pendência para homologação.');
        }

        $status = $this->status_model->get_all_entries();
        $data['status'] = $status;

        $historico = $this->mvto_model->get_entries_by_id_ctrl_ex($id);

        $count = 0;

        // SELECIONA QUAIS SÃO OS ITENS DA FASE ATUAL e MOSTRA QUAIS JÁ TEM NO HISTÓRICO
        foreach ($status as $key => $value) {
            if (strtolower($value->estagio) == strtolower($entry->status_estagio)) {
                $statusitens[$count] = $value;
                foreach ($historico as $hkey => $hvalue) {
                    if ($value->id_status == $hvalue->id_status) {
                        $statusitens[$count]->selected = true;
                    }
                }
                $count++;
            }
        }

        $mvto = NULL;
        if ($id_mvto != NULL) {
            $mvto = $this->mvto_model->get_entry($id_mvto);
        }

        $data['mvto'] = $mvto;

        $data['statusitens'] = $statusitens;

        if ($this->input->get('insert') == 'error') {
            $data['insert'] = $this->input->get('insert');
            $data['error'] = base64_decode($this->input->get('error'));
        }

        // USUÁRIO HOMOLOGA OU REPROVA A HOMOLOGAÇÃO
        if ($post = $this->input->post()) {
            $this->processa_homologacao($post, $id_mvto, $id);
        }

        $this->include_js(
            array(
                'bootstrap-select/bootstrap-select.js',
                'bootstrap-toggle.js',
                'sweetalert.min.js',
                'ckeditor/ckeditor.js'
            )
        );

        $this->include_css(
            array(
                'bootstrap-select/bootstrap-select.css',
                'bootstrap-toggle.css',
                'monitor_ex.css'
            )
        );

        $entry_tipo = $this->tipo_ex_model->get_entry($entry->id_tipo_ex);

        $fases = $this->status_model->get_enums($entry_tipo);

        $data['fases'] = $fases;

        $this->render('monitor_ex/detalhes', $data);
    }

    public function detalhes_novo_movimento($id, $tab = 'default')
    {
        $post = $this->input->post();

        $this->load->library('form_validation');

        $this->form_validation->set_rules('id_status', 'Título', 'trim|required');
        $this->form_validation->set_rules('descricao_status', 'Núm. EX', 'trim|required');

        if (!$this->form_validation->run())
            throw new Exception(validation_errors());

        $id_arquivo = null;

        if (isset($_FILES) && !empty($_FILES['file']['name'])) {
            $id_arquivo = $this->upload_file(TRUE);
            if (array_key_exists('status', $id_arquivo)) {
                if ($id_arquivo['status'] == 'error') {
                    redirect('/monitor_ex/detalhes/' . $id . '/novo-movimento?insert=error&error=' . $id_arquivo['error']);
                }
            }
        }

        $this->load->model(array(
            'monitor_ex/mvto_model',
            'monitor_ex/status_model',
            'monitor_ex/ex_tarif_pn_model'
        ));

        $entry_status = $this->status_model->get_entry($post['id_status']);

        $ctrl_ex = $this->ctrl_ex_tarifario_model->get_entry($id);

        $antigo_status = isset($ctrl_ex->id_status) && !empty($ctrl_ex->id_status) ? $this->status_model->get_entry($ctrl_ex->id_status) : null;

        $insertData = array(
            'id_ctrl_ex' => $id,
            'id_status' => $post['id_status'],
            'id_user' => sess_user_id(),
            'data_ref' => date('Y-m-d h:m:s'),
            'id_arquivo' => is_numeric($id_arquivo) ? $id_arquivo : '',
            'descricao' => $post['descricao_status']
        );

        try {
            if ($id_mvto = $this->mvto_model->save($insertData)) {
                $this->ctrl_ex_tarifario_model->save(array('id_status' => $post['id_status']), array('id_ctrl_ex' => $id));
                $this->ex_tarif_pn_model->save_log_by_ex_tarif(array(
                    'id_ctrl_ex' => $id,
                    'novo_status' => $entry_status,
                    'antigo_status' => $antigo_status,
                    'id_arquivo' => $insertData['id_arquivo'],
                    'descricao_movimento' => $post['descricao_status']
                ));
            }
        } catch (Exception $e) {
            throw new Exception('Ocorreu um erro inesperado.');
        }

        if ($entry_status->homologacao_cliente == TRUE) {
            $pendenciaData = array(
                'id_ctrl_ex' => $id,
                'data_ref' => date('Y-m-d'),
                'id_mvto' => $id_mvto,
                'homologado' => false
            );

            $this->mvto_model->salvar_pendencia($pendenciaData);

            $this->load->library('email');
            $this->load->model(array(
                'arquivo_model',
                'usuario_model'
            ));

            $this->email->initialize(array('charset' => 'UTF-8'));

            $ex_tarif = $this->ctrl_ex_tarifario_model->get_entry($id);

            $usuario = $this->usuario_model->get_entry($ex_tarif->id_responsavel_cliente);

            $this->email->to($usuario->email);
            $this->email->from($this->config->item('mail_from_addr'), $this->config->item('mail_from_name'));
            $this->email->subject('[Gestão Tarifária] - Homologação Cliente');

            $data['base_url'] = config_item('online_url');
            $data['html_message'] =
                '<h4>Notificação de atualização de status</h4>
                <p>' . $usuario->nome . ', o status foi alterado, e está pendente para homologação do cliente.<br>
                <strong>Atualização de status: </strong> ' . $post['descricao_status'] . '</p>
                <p>Em caso de dúvidas, contate o gerente de projetos.</p>
                <p><a href="' . config_item('online_url') . 'monitor_ex/homologacao_cliente/' . $id . '/' . $id_mvto . '">Clique aqui para confirmar a homologação.</a></p>';

            $html = $this->load->view('templates/basic_template', $data, TRUE);

            $this->email->message($html);

            if (!$this->email->send()) {
                $error = 'Não foi possível enviar email de comunicação para o responsável pelo cliente.';
                redirect('/monitor_ex/detalhes/' . $id . '/' . $tab . '?insert=error&error=' . $error);
            }
        }

        redirect('/monitor_ex/detalhes/' . $id . '/' . $tab . '?insert=success');
    }

    public function detalhes($id, $tab = 'default')
    {
        if (!has_role('monitor_ex_detalhes')) {
            show_permission();
        }

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Monitor de EX / FTA', '/monitor-ex/');

        $this->load->helper('formatador_helper');

        if ($tab == 'default') $crumbtext = 'Detalhes';
        if ($tab == 'novo-movimento') $crumbtext = 'Novo Movimento';
        if ($tab == 'historico') $crumbtext = 'Histórico';
        if ($tab == 'visualizacao-part-numbers') $crumbtext = 'Histórico';
        if ($tab == 'arquivos') $crumbtext = 'Arquivos';

        $this->breadcrumbs->push($crumbtext, $tab);

        $data = array();

        try {
            $entry = $this->ctrl_ex_tarifario_model->get_entry_details($id);
        } catch (Exception $e) {
            show_404();
        }

        $this->load->model(array(
            'monitor_ex/status_model',
            'ex_tarifario_model',
            'monitor_ex/mvto_model',
            'monitor_ex/tipo_ex_model',
            'monitor_ex/evento_model'
        ));

        $data['entry'] = $entry;

        $entry_tipo = $this->tipo_ex_model->get_entry($entry->id_tipo_ex);

        $data['entry_tipo'] = $entry_tipo;
        $data['tab'] = $tab;

        $pendencia = $this->mvto_model->get_pendencia_by_ctrl_ex($id);

        $data['pendencia'] = $pendencia;

        $args = NULL;

        if ($data['entry_tipo']->slug == 'automotivo') {
            $args = array('in_automotivo' => true);
        }

        $status = $this->status_model->get_all_entries($args);
        $historico = $this->mvto_model->get_entries_by_id_ctrl_ex($id);

        $statusitens = array();
        $count = 0;

        // SELECIONA QUAIS SÃO OS ITENS DA FASE ATUAL e MOSTRA QUAIS JÁ TEM NO HISTÓRICO
        foreach ($status as $key => $value) {
            if (strtolower($value->estagio) == strtolower($entry->status_estagio)) {
                $statusitens[$count] = $value;
                foreach ($historico as $hkey => $hvalue) {
                    if ($value->id_status == $hvalue->id_status) {
                        $statusitens[$count]->selected = true;
                    }
                }
                $count++;
            }
        }
        $data['statusitens'] = $statusitens;

        if ($tab == 'historico') {
            $datahistorico = $historico;
            $historico = array();
            foreach ($datahistorico as $key => $value) {
                if (($key + 1) < count($datahistorico)) {
                    $datahistorico[$key]->descricao_status = $datahistorico[$key + 1]->descricao_status . ' > ' . $value->descricao_status;
                }
            }

            $historico = $datahistorico;
            $data['historico'] = $historico;
        }

        if ($tab == 'novo-movimento') {
            $data['status'] = $status;
            if ($submit = $this->input->get('insert')) {
                $data['insert'] = $submit;
                if ($submit == 'error') {
                    $data['error'] = $this->input->get('error');
                }
            }
        }

        if ($tab == 'arquivos') {
            if ($arquivos = $this->arquivo_model->get_files_by_id_ctrl_ex($id)) {
                $data['arquivos'] = $arquivos;
            }
        }

        $this->load->model('monitor_ex/evento_model');
        $evento = $this->evento_model->get_entry_by_slug('ex-tarifario-existente');

        if (isset($entry->id_status))
            $status = $this->status_model->get_entry($entry->id_status);

        if ($evento->id_evento == $entry->id_evento || (isset($status) && $status->slug == 'deferimento'))
            $data['show_change_pn'] = $evento;

        $tec_ncm_ex_tarif = $this->ex_tarifario_model->get_entries_by_ncm_exnumber($entry->cod_ncm, $entry->num_ex);
        $data['tec_ncm_ex_tarif'] = $tec_ncm_ex_tarif;

        $this->include_js(
            array(
                'bootstrap-select/bootstrap-select.js',
                'bootstrap-toggle.js',
                'sweetalert.min.js',
                'ckeditor/ckeditor.js'
            )
        );

        $this->include_css(
            array(
                'bootstrap-select/bootstrap-select.css',
                'bootstrap-toggle.css',
                'monitor_ex.css'
            )
        );

        $fases = $this->status_model->get_enums($entry_tipo);

        $data['fases'] = $fases;

        $this->render('monitor_ex/detalhes', $data);
    }

    public function exporta_monitor_ex()
    {
        $this->load->model(array('empresa_model', "cad_item_model"));
        $this->load->model(array(
            'monitor_ex/status_model',
            'ex_tarifario_model',
            'monitor_ex/mvto_model',
            'monitor_ex/tipo_ex_model',
            'monitor_ex/evento_model'
        ));
        ini_set('memory_limit', -1);
    	set_time_limit(0);

        $entries = $this->ctrl_ex_tarifario_model->get_entries();
        $empresa = $this->empresa_model->get_entry(sess_user_company());

        $colunas = array(
            array(
                'label' => 'Empresa',
                'field' => 'empresa',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Responsável',
                'field' => 'responsavel',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'E-Mail',
                'field' => 'email',
                'col_format' => 'texto',
                'col_width' => 60
            ),
            array(
                'label' => 'Status',
                'field' => 'status',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Evento',
                'field' => 'evento',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Pacote',
                'field' => 'pacote',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Tipo',
                'field' => 'tipo',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Data hom. pelo cliente',
                'field' => 'dt_hom_cliente',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Data protocolo',
                'field' => 'dt_protocolo',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Data status atual',
                'field' => 'dt_status',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Texto status atual',
                'field' => 'txt_status',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Descrição resumida',
                'field' => 'desc_resumida',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Part numbers',
                'field' => 'part_numbers',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'NCM',
                'field' => 'ncm',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Inicio vigência',
                'field' => 'inicio_vigencia',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Fim vigência',
                'field' => 'fim_vigencia',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Codigo EX',
                'field' => 'codigo_ex',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Descrição EX',
                'field' => 'descricao_ex',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Consulta publica',
                'field' => 'consulta_publica',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'CAEX',
                'field' => 'caex',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'GECEX',
                'field' => 'gecex',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Ganho variavel ativo',
                'field' => 'ganho_variavel_ativo',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Inicio ganho variavel',
                'field' => 'inicio_ganho_variavel',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Fim ganho variavel',
                'field' => 'fim_ganho_variavel',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Valor da economia (R$)',
                'field' => 'valor_economia',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'Codigo SDCI',
                'field' => 'codigo_sdci',
                'col_format' => 'texto',
                'col_width' => 30
            )
        );
        
        $cabecalho = $this->extract_fields_from_cols($colunas);

        $config = array(
            'filename'      => 'Monitoramento_EX_FTA_' . date('Y-m-d_H-i-s'),
            'titulo'        => "Monitoramento EX FTA",
            'nome_planilha' => "Monitoramento EX FTA",
            'colunas'       => $colunas,
            'cabecalho'     => $cabecalho,
            'filter_suffix' => "Monitoramento_EX_FTA_"
        );

        require_once APPPATH . 'libraries/XlsxGenerator/XlsxGenerator.php';
        
        $relatorio = new XlsxGenerator($config['filename']);
        $writer = $relatorio->init($config['filename']);

        $relatorio->filename = $config['filename'];

        // Estrutura XLSX
        $widths = $relatorio->getWidth($config);
        $fields = array();
        
        $writer->addImage(FCPATH . 'assets/img/header/logo.png', 1, array('widths' => $widths));

        if (isset($config['colunas'])) {
            foreach ($config['colunas'] as $coluna) {
                if (isset($coluna['label']))
                    $fields[$coluna['label']] = isset($coluna['col_format']) ? $coluna['col_format'] : 'string';
            }
        }

        $headerStyle = array(
            'font' => 'Arial',
            'font-size' => 12,
            'font-style' => 'bold',
            'color' => '#000000',
            'fill' => '#F9FF00',
            'halign' => 'center',
            'valign' => 'center',
            'border' => 'bottom',
            'wrap_text' => 'true',
            'widths' => $widths
        );

        $writer->writeSheetHeader($config['nome_planilha'], $fields, $headerStyle);

        if (isset($config['colunas']) &&  (count($config['colunas']) <= 24) && (count($config['colunas']) >= 10)) {
            $writer->markMergedCell($config['nome_planilha'], 0, 0, 0, count($config['colunas']) -1);
        } else if (isset($config['colunas']) && count($config['colunas']) <= 24) {
            $writer->markMergedCell($config['nome_planilha'], 0, 0, 0, 24);
        } else {
            $writer->markMergedCell($config['nome_planilha'], 0, 0, 0, count($config['colunas']) -1);
        }

        $defaultStyle = array(
            'font' => 'Arial',
            'font-size' => 11
        );
        // Estrutura XLSX

        foreach ($entries as $v) {

            try {
                $responsavel = $this->usuario_model->get_entry($v->id_responsavel_cliente);
            } catch (Exception $e) {
                $responsavel = new stdClass();
                $responsavel->nome = 'Responsável não encontrado';
            }

            $pendencia   = $this->mvto_model->get_pendencia_by_ctrl_ex($v->id_ctrl_ex);
            $entry       = $this->ctrl_ex_tarifario_model->get_entry_details($v->id_ctrl_ex);
            $historicos  = $this->mvto_model->get_entries_by_id_ctrl_ex($v->id_ctrl_ex);

            $dt_consulta_publica = '';
            $dt_caex             = '';
            $dt_gecex            = '';
            $dt_protocolo        = '';

            foreach ($historicos as $historico)
            {
                $dt_consulta_publica = $historico->id_status == '9'  && empty($dt_consulta_publica) ? $historico->data_ref : $dt_consulta_publica;
                $dt_caex             = $historico->id_status == '12' && empty($dt_caex)             ? $historico->data_ref : $dt_caex;
                $dt_gecex            = $historico->id_status == '13' && empty($dt_gecex)            ? $historico->data_ref : $dt_gecex;
                $dt_protocolo        = $historico->id_status == '7'  && empty($dt_protocolo)        ? $historico->data_ref : $dt_protocolo;
            }

            $ultimo_status = reset($historicos); 

            $part_numbers   = '';
            $ganho_variavel = $v->ganho_variavel_ativo == 1 ? 'sim' : 'não';

            foreach ($entry->get_partnumbers() as $value)
            {
                $part_numbers .= empty($part_numbers) ? $value->part_number : '; '.$value->part_number;
            }

            $data_status             = isset($ultimo_status->data_ref) && !empty($ultimo_status->data_ref)  ? date('d/m/Y', strtotime($ultimo_status->data_ref)) : ''; 
            $data_inicio_v           = isset($v->inicio_vigencia)      && !empty($v->inicio_vigencia)       ? date('d/m/Y', strtotime($v->inicio_vigencia))      : ''; 
            $data_fim_v              = isset($v->fim_vigencia)         && !empty($v->fim_vigencia)          ? date('d/m/Y', strtotime($v->fim_vigencia))         : ''; 
            $data_homologado_cliente = isset($pendencia->data_ref)     && !empty($pendencia->data_ref)      ? date('d/m/Y', strtotime($pendencia->data_ref))     : ''; 
            $dt_protocolo            = isset($dt_protocolo)            && !empty($dt_protocolo)             ? date('d/m/Y', strtotime($dt_protocolo))            : ''; 
            $dt_consulta_publica     = isset($dt_consulta_publica)     && !empty($dt_consulta_publica)      ? date('d/m/Y', strtotime($dt_consulta_publica))     : ''; 
            $dt_caex                 = isset($dt_caex)                 && !empty($dt_caex)                  ? date('d/m/Y', strtotime($dt_caex))                 : ''; 
            $dt_gecex                = isset($dt_gecex)                && !empty($dt_gecex)                 ? date('d/m/Y', strtotime($dt_gecex))                : ''; 

            $valor_economia          = isset($v->economia_estimada)    && !empty($v->economia_estimada)     ? number_format($v->economia_estimada, 2, ',', '.')  : '';
            $txt_status              = rtrim(strip_tags(html_entity_decode($ultimo_status->descricao)));
            $descricao_detalhada     = rtrim(strip_tags(html_entity_decode($v->detalhes)));
            $row = array(
                'empresa'               => isset($empresa->razao_social)             && !empty($empresa->razao_social)             ?  $empresa->razao_social            : '',
                'responsavel'           => isset($responsavel->nome)                 && !empty($responsavel->nome)                 ? $responsavel->nome                 : '',
                'email'                 => isset($responsavel->email)                && !empty($responsavel->email)                ? $responsavel->email                : '',
                'status'                => isset($v->descricao_status)               && !empty($v->descricao_status)               ? $v->descricao_status               : '',
                'evento'                => isset($v->descricao_evento)               && !empty($v->descricao_evento)               ? $v->descricao_evento               : '',
                'pacote'                => isset($v->descricao_pacote)               && !empty($v->descricao_pacote)               ? $v->descricao_pacote               : '',
                'tipo'                  => isset($v->descricao_tipo)                 && !empty($v->descricao_tipo)                 ? $v->descricao_tipo                 : '',
                'dt_hom_cliente'        => isset($data_homologado_cliente)           && !empty($data_homologado_cliente)           ? $data_homologado_cliente           : '',
                'dt_protocolo'          => isset($dt_protocolo)                      && !empty($dt_protocolo)                      ? $dt_protocolo                      : '',
                'dt_status'             => isset($data_status)                       && !empty($data_status)                       ? $data_status                       : '',
                'txt_status'            => isset($txt_status)                        && !empty($txt_status)                        ? $txt_status                        : '',
                'desc_resumida'         => isset($entry->titulo_ex)                  && !empty($entry->titulo_ex)                  ? $entry->titulo_ex                  : '',
                'part_numbers'          => isset($part_numbers)                      && !empty($part_numbers)                      ? $part_numbers                      : '',
                'ncm'                   => isset($v->cod_ncm)                        && !empty($v->cod_ncm)                        ? $v->cod_ncm                        : '',
                'inicio_vigencia'       => isset($data_inicio_v)                     && !empty($data_inicio_v)                     ? $data_inicio_v                     : '',
                'fim_vigencia'          => isset($data_fim_v)                        && !empty($data_fim_v)                        ? $data_fim_v                        : '',
                'codigo_ex'             => isset($v->num_ex)                         && !empty($v->num_ex)                         ? $v->num_ex                         : '',
                'descricao_ex'          => isset($descricao_detalhada)               && !empty($descricao_detalhada)               ? $descricao_detalhada               : '',
                'consulta_publica'      => isset($dt_consulta_publica)               && !empty($dt_consulta_publica)               ? $dt_consulta_publica               : '',
                'caex'                  => isset($dt_caex)                           && !empty($dt_caex)                           ? $dt_caex                           : '',
                'gecex'                 => isset($dt_gecex)                          && !empty($dt_gecex)                          ? $dt_gecex                          : '',
                'ganho_variavel_ativo'  => isset($ganho_variavel)                    && !empty($ganho_variavel)                    ? $ganho_variavel                    : '',
                'inicio_ganho_variavel' => isset($v->inicio_apuracao_ganho_variavel) && !empty($v->inicio_apuracao_ganho_variavel) ? $v->inicio_apuracao_ganho_variavel : '',
                'fim_ganho_variavel'    => isset($v->fim_apuracao_ganho_variavel)    && !empty($v->fim_apuracao_ganho_variavel)    ? $v->fim_apuracao_ganho_variavel    : '',
                'valor_economia'        => isset($valor_economia)                    && !empty($valor_economia)                    ? $valor_economia                    : '',
                'codigo_sdci'           => isset($v->cod_sdci)                       && !empty($v->cod_sdci)                       ? $v->cod_sdci                       : '',                  
            );

            $writer->writeSheetRow($config['nome_planilha'], $row, $defaultStyle);

        }

        return $relatorio->download();
    }

    private function extract_fields_from_cols($cols, $line_break = ' ', $label_key_override = 'label_exportar') {
        if (empty($cols)) {
            return array();
        }

        $fields = array();

        foreach ($cols as $col) {
            $label_key = 'label';

            if (!empty($label_key_override) && array_key_exists($label_key_override, $col)) {
                $label_key = $label_key_override;
            }

            if (!empty($line_break)) {
                $col[$label_key] = str_replace('<br>', $line_break, $col[$label_key]);
            }

            $fields[$col['field']] = $col[$label_key];
        }

        return $fields;
    }

    public function remove()
    {
        if (!has_role('monitor_ex_detalhes')) {
            show_permission();
        }

        // tratar multiplo
        $id = $this->input->post('id_list');
        try {
            if (is_array($id)) {
                $post_item = array();
                foreach ($id as $value) {
                    $item = $this->ctrl_ex_tarifario_model->get_entry($value);
                    $this->ctrl_ex_tarifario_model->remove($item->id_ctrl_ex);
                }
            } else {
                $item = $this->ctrl_ex_tarifario_model->get_entry($id);
            }
        } catch (Exception $e) {
            show_404();
        }

        $this->ctrl_ex_tarifario_model->remove($item->id_ctrl_ex);

        return response_json(array('status' => true, 'msg' => 'O arquivo foi removido.'));
    }

    public function validar($data = NULL)
    {
        $this->load->library('form_validation');

        $this->form_validation->set_rules('titulo_ex', 'Título', 'trim|required');

        if (isset($data)) {
            if (array_key_exists('id_evento', $data)) {
                $this->load->model('monitor_ex/evento_model');
                $evento = $this->evento_model->get_entry($data['id_evento']);
                if ($evento->slug != 'novo-ex-tarifario') {
                    $this->form_validation->set_rules('num_ex', 'Núm. EX', 'trim|required');
                }
            }
        }

        $this->form_validation->set_rules('id_tipo_ex', 'Tipo EX', 'trim|required');
        $this->form_validation->set_rules('inicio_vigencia', 'Início Vigência', 'trim');
        $this->form_validation->set_rules('fim_vigencia', 'Fim Vigência', 'trim');
        $this->form_validation->set_rules('id_grupo_tarifario', 'Grupo Tarifário', 'trim|required');
        $this->form_validation->set_rules('id_evento', 'Evento', 'trim|required');
        $this->form_validation->set_rules('id_pacote', 'Pacote', 'trim|required');
        $this->form_validation->set_rules('id_responsavel_cliente', 'Responsável Cliente', 'trim|required');
        $this->form_validation->set_rules('economia_estimada', 'Economia Estimada', 'trim');
        $this->form_validation->set_rules('ganho_variavel', 'Ganho Variável', 'trim');
        $this->form_validation->set_rules('cod_ncm', 'NCM', 'trim');
        $this->form_validation->set_rules('cod_sdci', 'SDCI', 'trim');
        $this->form_validation->set_rules('inicio_apuracao_ganho_variavel', 'Início apuração ganho variável', 'trim');
        $this->form_validation->set_rules('fim_apuracao_ganho_variavel', 'Fim apuração ganho variável', 'trim');
        $this->form_validation->set_rules('detalhes', 'Detalhes', 'trim');

        if (!$this->form_validation->run())
            throw new Exception(validation_errors());
    }

    public function ajax_get_ex_by_ncm_exnumber()
    {
        if ($post = $this->input->post()) {
            $ncm = isset($post['cod_ncm']) ? $post['cod_ncm'] : null;
            $num_ex = isset($post['num_ex']) ? $post['num_ex'] : null;

            $this->load->model('ex_tarifario_model');

            if (!empty($ncm) && !empty($num_ex)) {
                $data = $this->ex_tarifario_model->get_entries_by_ncm_exnumber($ncm, $num_ex);
                if (!empty($data)) {
                    $resp = $data;
                    $resp->dat_vigencia_ini = (new DateTime($resp->dat_vigencia_ini))->format('d/m/Y');
                    $resp->dat_vigencia_fim = (new DateTime($resp->dat_vigencia_fim))->format('d/m/Y');

                    echo json_encode($resp);
                    return TRUE;
                }
            }
        }
        return FALSE;
    }

    private function format_date($date)
    {
        if (empty($date))
            return NULL;

        $d = DateTime::createFromFormat('d/m/Y', $date);

        return $d->format('Y-m-d');
    }

    private function get_post_data()
    {
        return array(
            'titulo_ex'                      => $this->input->post('titulo_ex'),
            'num_ex'                         => $this->input->post('num_ex'),
            'id_empresa'                     => sess_user_company(),
            'id_tipo_ex'                     => $this->input->post('id_tipo_ex'),
            'inicio_vigencia'                => $this->format_date($this->input->post('inicio_vigencia')),
            'fim_vigencia'                   => $this->format_date($this->input->post('fim_vigencia')),
            'id_grupo_tarifario'             => $this->input->post('id_grupo_tarifario'),
            'cod_ncm'                        => $this->input->post('cod_ncm'),
            'cod_sdci'                       => $this->input->post('cod_sdci'),
            'id_evento'                      => $this->input->post('id_evento'),
            'id_pacote'                      => $this->input->post('id_pacote'),
            'id_responsavel_cliente'         => $this->input->post('id_responsavel_cliente'),
            'economia_estimada'              => $this->input->post('economia_estimada'),
            'ganho_variavel_ativo'           => $this->input->post('ganho_variavel_ativo'),
            'inicio_apuracao_ganho_variavel' => $this->format_date($this->input->post('inicio_apuracao_ganho_variavel')),
            'fim_apuracao_ganho_variavel'    => $this->format_date($this->input->post('fim_apuracao_ganho_variavel')),
            'detalhes'                       => $this->input->post('detalhes')
        );
    }

    private function salvar($id = NULL)
    {
        $data = $this->get_post_data();

        $this->validar($data);

        $data['economia_estimada'] = str_replace(',', '.', str_replace('.', '', $data['economia_estimada']));

        $evento = $this->evento_model->get_entry_by_slug('ex-tarifario-existente');

        $this->load->model('monitor_ex/mvto_model');

        $data_status = isset($data['id_status']) ? $data['id_status'] : 1;
        $alter_status = false;

        if (!empty($id)) {
            $ex_tarif_atual = $this->ctrl_ex_tarifario_model->get_entry($id);
        }

        if ($evento->id_evento == $data['id_evento']) {
            $this->load->model('monitor_ex/status_model');
            $status = $this->status_model->get_entry_by_slug('deferimento');
            $data_status = $status->id_status;
            $alter_status = true;
        }

        if ($alter_status || empty($id)) {
            $data['id_status'] = $data_status;
        }
        
        if (empty($id)) {
            $id = $this->ctrl_ex_tarifario_model->save($data);
            // AO SER CRIADO UM PLEITO NOVO É SALVO UM LOG DE REGISTRO DO STATUS
            $insertData = array(
                'id_ctrl_ex' => $id,
                'id_status' => $data_status,
                'id_user' => sess_user_id(),
                'data_ref' => date('Y-m-d h:m:s'),
                'id_arquivo' => null,
                'descricao' => 'Criação do Pleito'
            );
            $this->mvto_model->save($insertData);
        } else {
            if ($ex_tarif_atual->id_evento != $data['id_evento']) {
                if ($evento->id_evento == $data['id_evento']) {
                    $this->load->model('monitor_ex/status_model');
                    $status = $this->status_model->get_entry_by_slug('deferimento');
                    $data_status = $status->id_status;
                } else {
                    $data_status = 1;
                }
                $alter_status = true;
            } else {
                $alter_status = false;
            }

            if ($alter_status == TRUE)
                $data['id_status'] = $data_status;

            $this->ctrl_ex_tarifario_model->save($data, array('id_ctrl_ex' => $id));

            // CASO SEJA ALTERADO O EVENTO DO PLEITO, OU TENHA O EVENTO ESPECIFICO SERÁ ALTERADO O STATUS PARA DEFERIMENTO DO PLEITO
            if ($alter_status == TRUE) {
                $insertData = array(
                    'id_ctrl_ex' => $id,
                    'id_status' => $data_status,
                    'id_user' => sess_user_id(),
                    'data_ref' => date('Y-m-d h:m:s'),
                    'id_arquivo' => null,
                    'descricao' => 'Alteração do Evento do Pleito, ocorrendo assim alteração do Status do Pleito.'
                );
                $this->mvto_model->save($insertData);
            }
        }
        
        if (empty($id)) {
            throw new Exception('Não foi possível salvar essas informações. Tente novamente');
        }

        return $id;
    }

    private function carrega_listas(&$data)
    {
        $this->load->model('monitor_ex/evento_model');
        $this->load->model('monitor_ex/pacote_model');
        $this->load->model('monitor_ex/tipo_ex_model');
        $this->load->model('grupo_tarifario_model');
        $this->load->model('usuario_model');

        $lista_eventos                                     = $this->evento_model->get_all_entries();
        $data['lista_eventos']                             = $lista_eventos;

        $lista_pacotes                                     = $this->pacote_model->get_all_entries();
        $data['lista_pacotes']                             = $lista_pacotes;

        $lista_tipos                                       = $this->tipo_ex_model->get_all_entries();
        $data['lista_tipos']                               = $lista_tipos;

        $by_empresa = false;
        $lista_grupos_tarif                                = $this->grupo_tarifario_model->get_entries_no_inner($by_empresa);
        $data['lista_grupos_tarif']                        = $lista_grupos_tarif;

        $this->usuario_model->set_state('filter.id_empresa', sess_user_company());

        // VERIFICAR ISSO, VER DE PEGAR DINAMICAMENTE O PERFIL DOS USUARIOS
        $this->usuario_model->set_state('filter.perfil', array(2, 3, 8, 9, 10));
        $lista_responsaveis_cliente                        = $this->usuario_model->get_entries(null, null, [$data['entry']->id_responsavel_cliente]);
        $data['lista_responsaveis_cliente_ativos'] = array_values(array_filter($lista_responsaveis_cliente, function($usuario) {
            return $usuario->status == 1;
        }));
        $data['lista_responsaveis_cliente_inativos'] = array_values(array_filter($lista_responsaveis_cliente, function($usuario) {
            return $usuario->status == 0;
        }));
    }

    public function xhr_get_lista_grupos_tarif()
    {
        $query = $this->input->get('query');

        $by_empresa = false;
        if ($query != NULL) {
            $data = array();

            $this->load->model('grupo_tarifario_model');
            $itens = $this->grupo_tarifario_model->get_entries_no_inner($by_empresa, $query);
            if (!empty($itens)) {
                foreach ($itens as $key => $value) {
                    $data[] = array(
                        'value' => $value->descricao . ' - NCM: ' . $value->ncm_recomendada,
                        'data' => $value->id_grupo_tarifario,
                        'ncm' => $value->ncm_recomendada,
                        'descricao' => $value->descricao
                    );
                }
            }

            return response_json(array('suggestions' => $data));
        }

        return response_json(array('query' => $query, 'response' => 'success', 'data' => $data, 'status' => 200));
    }

    public function addedit($id = NULL, $tab = 'dados-basicos')
    {
        if (!has_role('monitor_ex_detalhes')) {
            show_permission();
        }
        
        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Monitor de EX / FTA', '/monitor-ex/');
   
        if ($tab == 'dados-basicos') $crumbtext = 'Dados Básicos';
        if ($tab == 'partnumbers') $crumbtext = 'Part numbers';
        if ($tab == 'arquivos') $crumbtext = 'Arquivos';

        $this->breadcrumbs->push($crumbtext, $tab);
        $data = array();
        $data['title'] = !empty($id) ? 'Edição Pleito de EX Tarifário' : 'Novo Pleito de EX Tarifário';

        $this->load->helper('formatador');

        try {
            if (!in_array($tab, $this->_tabs_editing)) {
                throw new Exception('Modo de edição não encontrado. Utilize: ' . implode(', ', $this->_tabs_editing));
            }
            
            if (!empty($id)) {
                $this->load->model('monitor_ex/evento_model');

                $entry = $this->ctrl_ex_tarifario_model->get_entry_details($id);

                $data['entry'] = $entry;
                $data['entry_evento'] = $this->evento_model->get_entry($entry->id_evento);

                if ($arquivos = $this->arquivo_model->get_files_by_id_ctrl_ex($id)) {
                    $data['arquivos'] = $arquivos;
                }
            }
        } catch (Exception $e) {
            show_error($e->getMessage());
        }

        if (isset($entry) && !empty($entry)) {
            $this->load->model('monitor_ex/status_model');
            if (isset($entry->id_status)) {
                $status = $this->status_model->get_entry($entry->id_status);
                if (isset($status) && $status->slug == 'deferimento') {
                    $data['disabled_edit'] = TRUE;
                }
            }
        }

        try {
            $post = $this->input->post();
            if (!empty($post)) {
                $new = empty($id) ? true : false;
                $id = $this->salvar($id);

                $this->message_next_render('<h4>Sucesso!</h4> As informações foram gravadas.', 'success');

                redirect('monitor-ex/addedit/' . $id . '/' . ($new ? 'partnumbers' : ''), 'refresh');
            }
        } catch (Exception $e) {
            $data['error'] = $e->getMessage();
        }

        $this->carrega_listas($data);

        $this->include_js(
            array(
                'bootstrap-select/bootstrap-select.js',
                'bootstrap-toggle.js',
                'b3-datetimepicker.min.js?v=3.1.1',
                'jquery.maskmoney.min.js',
                'ckeditor/ckeditor.js',
                'dropzone/dropzone.js',
                'jquery.autocomplete.min.js',
                'sweetalert.min.js'
            )
        );
        $this->include_css(
            array(
                'bootstrap-select/bootstrap-select.css',
                'bootstrap-toggle.css',
                'b3-datetimepicker.min.css',
                'autocomplete.css',
                'dropzone/basic.css',
                'sweetalert.css',
                'monitor_ex.css'
            )
        );

        $data['tab'] = $tab;

        $this->render('monitor_ex/addedit', $data);
    }

    public function remove_rel_ex_tarif_partnumber($id_ctrl_ex = NULL)
    {
        if ($id_ctrl_ex != NULL && $part_number = $this->input->post('part_number')) {
            $this->load->model('monitor_ex/ex_tarif_pn_model');

            try {
                if ($this->ex_tarif_pn_model->remove_rel($id_ctrl_ex, $part_number)) {
                    $data = array(
                        'id_ctrl_ex' => $id_ctrl_ex,
                        'part_number' => $part_number
                    );
                    $this->ex_tarif_pn_model->save_log($data, 'remover');
                }

                redirect('monitor-ex/addedit/' . $id_ctrl_ex . '/partnumbers/?name=' . base64_encode($part_number) . '&re=1');
            } catch (Exception $e) {
                redirect('monitor-ex/addedit/' . $id_ctrl_ex . '/partnumbers/?re=0');
            }
        }
    }

    public function modal_pn_update_status($id = NULL)
    {
        if ($post = $this->input->post()) {
            $this->load->model('monitor_ex/ex_tarif_pn_model');

            $data = array(
                'id_ctrl_ex' => $post['id_ctrl_ex'],
                'part_number' => $post['part_number'],
                'descricao_status' => $post['descricao_status'],
                'status_part_number' => $post['status_part_number'],
                'id_usuario' => $post['id_usuario']
            );

            try {
                $id_arquivo = $this->upload_file(TRUE);
                if (!is_array($id_arquivo))
                    $data['id_arquivo'] = $id_arquivo;

                $this->ex_tarif_pn_model->update_status($data);
                $this->message_next_render('<h4>Sucesso!</h4> As informações foram gravadas.', 'success');
            } catch (Exception $e) {
                $this->message_next_render('<h4>Erro!</h4> Ocorreu um erro ao salvar as informações, tente novamente.', 'error');
            }

            redirect('/monitor_ex/detalhes/' . $post['id_ctrl_ex'] . '/visualizacao-part-numbers');
        }
    }

    public function xhr_modal_pn_get_content($id = NULL)
    {
        if ($post = $this->input->post()) {
            $this->load->model(array(
                'monitor_ex/ex_tarif_pn_model',
                'usuario_model'
            ));

            $status = $this->ex_tarif_pn_model->get_status_pn();
            $pn = $this->ex_tarif_pn_model->get_status_atual($post['id_ctrl_ex'], $post['part_number']);

            $status_obj = null;

            foreach ($status as $value) {
                if ($pn->status_part_number == $value->id_status)
                    $status_obj = $value;
            }

            $historico = $this->ex_tarif_pn_model->get_pn_status_log($pn->id_ctrl_ex, $pn->part_number);

            $this->usuario_model->set_state('filter.id_empresa', sess_user_company());
            $this->usuario_model->set_state('filter.ocultar_inativos', '1');
            $usuarios_select_ativos = $this->usuario_model->get_entries();

            $data = array(
                'id_ctrl_ex' => $pn->id_ctrl_ex,
                'part_number' => $pn->part_number,
                'status' => $status,
                'status_atual' => $pn->status_part_number,
                'status_obj' => $status_obj,
                'historico' => $historico,
                'usuarios_select_ativos' => $usuarios_select_ativos,
            );

            $this->include_js(array('ckeditor/ckeditor.js'));

            $this->load->view('monitor_ex/modal_pn_content', $data);
        }
    }

    public function xhr_ex_tarif_partnumber_status()
    {
        if ($post = $this->input->post()) {
            $this->load->model('monitor_ex/ex_tarif_pn_model');

            $id_ctrl_ex = isset($post['id_ctrl_ex']) && !empty($post['id_ctrl_ex']) ? $post['id_ctrl_ex'] : null;
            $part_number = isset($post['part_number']) && !empty($post['part_number']) ? $post['part_number'] : null;
            $status_part_number = isset($post['status_part_number']) && !empty($post['status_part_number']) ? $post['status_part_number'] : null;

            if ($id_ctrl_ex != NULL && $part_number != NULL && $status_part_number != NULL) {
                $data = array(
                    'id_ctrl_ex' => $id_ctrl_ex,
                    'part_number' => $part_number,
                    'status_part_number' => $status_part_number
                );
                $this->ex_tarif_pn_model->update_status($data);
            }
        }
    }

    public function xhr_relaciona_ex_tarif_partnumber()
    {
        $post = $this->input->post();

        if ($post['id_ctrl_ex'] && $post['part_number']) {
            $this->load->model('monitor_ex/ex_tarif_pn_model');

            $data = array(
                'id_ctrl_ex' => $post['id_ctrl_ex'],
                'part_number' => $post['part_number'],
                'status_part_number' => null
            );

            try {
                if ($this->ex_tarif_pn_model->save($data))
                    $this->ex_tarif_pn_model->save_log($data);

                return response_json(array('response' => 'success', 'success' => 'A relação foi feita.', 'status' => 200));
            } catch (Exception $e) {
                return response_json(array('response' => 'error', 'error' => $e, 'status' => 400));
            }
        }

        return false;
    }

    public function xhr_autocomplete_search($query = NULL)
    {
        $query = $this->input->get('query');

        if ($query != NULL) {
            $this->load->model('cad_item_model');
            $itens = $this->cad_item_model->get_entry_by_search($query, sess_user_company());

            $data = array();

            if (!empty($itens)) {
                foreach ($itens as $key => $value) {
                    $data[] = array(
                        'value' => $value->part_number . ' - ' . $value->descricao_item,
                        'data' => $value->part_number,
                        'ncm' => $value->ncm_proposto,
                        'descricao' => $value->descricao_item,
                        'descricao_grupo_tarifario' => $value->descricao,
                        'descricao_item' => $value->descricao_item
                    );
                }
            }

            return response_json(array('suggestions' => $data));
        }
    }

    public function xhr_delete_file($id = NULL, $filename = NULL)
    {
        if ($id != NULL) {
            try {
                $path = FCPATH . 'assets/uploads/monitor_ex/' . $filename;

                unlink($path);

                return $this->arquivo_model->delete_file_ctrl_ex($id);
            } catch (Exception $e) {
                return response_json(array('error' => $e, 400));
            }
        }
    }

    public function upload_file($return_id = null)
    {
        $path = FCPATH . 'assets/uploads/monitor_ex';

        if (!is_dir($path)) {
            if (@mkdir($path, 0777, true) === false) {
                return response_json(array('error' => 'Erro ao criar pastas'), 400);
            }
        }

        if (!is_writable($path)) {
            try {
                $iterator = new RecursiveIteratorIterator(
                    new RecursiveDirectoryIterator($path),
                    RecursiveIteratorIterator::SELF_FIRST
                );
                foreach ($iterator as $item) {
                    if (!chmod($item, 0777)) {
                        return response_json(array('error' => 'Erro ao alterar permissões da pasta'), 400);
                    }
                }
            } catch (Exception $e) {
                return response_json(array('error' => $e, 400));
            }
        }

        $config['upload_path'] = $path;
        $config['allowed_types'] = 'xlsx|xls|docx|pdf|rar|msg|png|jpg|jpeg|txt';
        // $config['allowed_types'] = '*';
        $config['max_size'] = '500000'; // 500mb
        $config['max_width'] = 0;
        $config['max_height'] = 0;
        $config['max_filename'] = 0;
        $config['encrypt_name'] = true;

        $this->load->library('upload', $config);

        try {
            if (!$this->upload->do_upload('file')) {
                if ($return_id) {
                    return array('status' => 'error', 'error' => $this->upload->display_errors());
                }
                return response_json(array('error' => $this->upload->display_errors('', '')), 400);
            } else {
                $upload_data = $this->upload->data();
                @chmod($upload_data['full_path'], 0777);
                $id = $this->input->post('id_pleito_ex');
                if ($id || $return_id) {
                    if (!empty($id) || $return_id) {
                        if ($return_id == true) {
                            $data = array(
                                'filename' => $upload_data['file_name'],
                                'name' => $upload_data['orig_name'],
                                'ext' => $upload_data['file_ext']
                            );
                            return $this->arquivo_model->insert_file_ctrl_ex($data);
                        } else {
                            $data = array(
                                'filename' => $upload_data['file_name'],
                                'name' => $upload_data['orig_name'],
                                'ext' => $upload_data['file_ext'],
                                'id_ctrl_ex' => $id
                            );
                            $data['id'] = $this->arquivo_model->insert_file_ctrl_ex($data);
                        }
                    }
                }

                return response_json(array(
                    'success' => 'Arquivo enviado com sucesso',
                    'file' => $data
                ));
            }
        } catch (Exception $e) {
            throw new Exception('Ocorreu um erro inesperado.');
        }
    }

    public function pacote_adicionar()
    {
        if (!has_role('sys_admin')) {
            show_permission();
        }
        $post = $this->input->post();

        $this->load->library('form_validation');

        $this->form_validation->set_rules('descricao_pacote', 'Descrição', 'trim|required');

        if ($post && $this->form_validation->run()) {
            $this->load->model('monitor_ex/pacote_model');
            $id = $post['id_pacote'];
            if (!empty($id)) {
                $this->pacote_model->save(array('descricao' => $post['descricao_pacote']), array('id_pacote' => $id, 'id_empresa' => sess_user_company()));
                $this->message_next_render('<h4>Sucesso!</h4> O pacote foi atualizado.', 'success');
            } else {
                $this->pacote_model->save(array('descricao' => $post['descricao_pacote'], 'id_empresa' => sess_user_company()));
                $this->message_next_render('<h4>Sucesso!</h4> As informações foram gravadas.', 'success');
            }
        } else {
            $this->message_next_render('<h4>Erro!</h4>' . validation_errors(), 'error');
        }

        redirect('/monitor_ex/pacotes');
    }

    public function pacote_remove()
    {
        if (!has_role('sys_admin')) {
            show_permission();
        }
        if ($id = $this->input->post('id_list')) {
            $this->load->model('monitor_ex/pacote_model');
            if (is_array($id)) {
                foreach ($id as $value) {
                    $this->pacote_model->remove($value);
                }
            } else {
                $this->pacote_model->remove($id);
            }
        }

        return response_json(array('status' => true));
    }

    public function xhr_pacote_get_entry($id = NULL)
    {
        if (!has_role('sys_admin')) {
            show_permission();
        }
        if ($id != NULL) {
            $this->load->model('monitor_ex/pacote_model');
            $entry = $this->pacote_model->get_entry($id);

            return response_json($entry);
        }
    }

    public function pacotes($page = 0)
    {
        $page = $this->input->get('per_page');
        if (!has_role('sys_admin') && !has_role('consultor')) {
            show_permission();
        }
        
        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Monitor de EX / FTA', '/monitor-ex/');
        $this->breadcrumbs->push('Pacotes', '/monitor-ex/pacotes');
        $data = array();

        $this->load->library('pagination');
        $this->load->model('monitor_ex/pacote_model');

        $limit  = 10;
        $offset = ($page > 0 ? $page - 1 : 0) * $limit;

        $total_entries = $this->pacote_model->get_total_entries();
        $entries = $this->pacote_model->get_entries($limit, $offset);

        $query_str = '';
        $config['base_url'] = base_url("/monitor-ex/pacotes?{$query_str}");
        $config['use_page_numbers'] = TRUE;
        $config['total_rows'] = $total_entries;
        $config['per_page'] = $limit;
        $config['page_query_string'] = TRUE;

        $this->pagination->initialize($config);

        $data['entries']       = $entries;

        $data['pagination'] = $this->pagination->create_links();

        $this->render('monitor_ex/pacotes', $data);
    }
}
