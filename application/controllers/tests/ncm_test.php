<?php

class NCM_Test extends CI_Controller 
{
    public function __construct() {
        parent::__construct();

        if (!has_role('sysadmin') && !$this->input->is_cli_request()) {
            show_permission();
        }
    }

    public function index() 
    {
        $this->testValorNormalImpostos();
    }

    public function testValorNormalImpostos() 
    {
        $this->load->model('ncm_model');

        $this->load->library('unit_test');

        $this->ncm_model->from_oracle_import_tec_ncm();

        $this->db->where('pct_ii_normal IS NOT NULL OR pct_ipi_normal IS NOT NULL', NULL, FALSE);
        $result = $this->db->count_all_results('ncm_impostos');

        $expected_result = true;

        $test_name = __method__;

        echo $this->unit->run($result, ($result > 0), $test_name);
    }

    public function test_atributos_ncm()
    {
        $this->load->model('catalogo/produto_model');

        $attrs = $this->produto_model->get_all_attr_ncm();
        
        echo '<pre>';
        var_dump($attrs);
    }
}