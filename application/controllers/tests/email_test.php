<?php

class Email_test extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        if (!has_role('sysadmin') && !$this->input->is_cli_request()) {
            show_permission();
        }
    }

    public function index() {
        $this->testSendEmail();
    }

    public function testSendEmail()
    {
        $this->load->library('unit_test');

        $this->load->library('email');

        $this->email->from($this->config->item('mail_from_addr'), $this->config->item('mail_from_name'));
        $this->email->subject('Teste');
        $this->email->to($this->config->item('mail_rcpt_general'));
        $this->email->cc($this->config->item('mail_rcpt_cc'));
        $this->email->message('Testando');
        $test = $this->email->send();

        $expected_result = true;

        $test_name = __method__;

        echo $this->unit->run($test, $expected_result, $test_name);
    }

}