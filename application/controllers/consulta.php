<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Consulta extends MY_Controller {

	public function __construct() {
		parent::__construct();

		if (!is_logged()) {
			redirect('/login');
        }
        
        if (!has_role('ncm_consulta_itens_grupo')) {
            show_permission();
        }

        $this->load->library('breadcrumbs');

        $this->load->model('empresa_model');
	}

    public function index() {
        redirect('consulta/itens-grupo-tarifario');
    }

	public function itens_grupo_tarifario()
	{
        $data = array();

        $limit = $offset = NULL;

        $this->load->model('grupo_tarifario_model');
        $this->load->model('cad_item_model');
        $this->load->model('ncm_model');
        $this->load->model('empresa_model');

        $this->cad_item_model->set_namespace('consulta');


        $this->cad_item_model->set_state_store_session(TRUE);
        $this->cad_item_model->restore_state_from_session();


        $this->load->library('pagination');

        $grupos = $this->grupo_tarifario_model->get_all_entries();

        if ($this->input->is_set('forma_lista'))
        {
            if ($this->input->get('forma_lista')) {
                $this->cad_item_model->set_state('filter.forma_lista', $this->input->get('forma_lista'));
            } else {
                $this->cad_item_model->unset_state('filter.forma_lista');
            }
        }

        $forma_lista = $this->cad_item_model->get_state('filter.forma_lista');

         $this->cad_item_model->set_state(
             'filter.id_empresa',
             sess_user_company()
         );

         $this->cad_item_model->set_state(
            'filter.atribuido_para',
            sess_user_id()
        );

        if ($id_grupo_tarifario = $this->input->get('id_grupo_tarifario'))
        {
            $this->cad_item_model->set_state(
                'filter.id_grupo_tarifario',
                $id_grupo_tarifario
            );
        } else {
            $this->cad_item_model->unset_state('filter.id_grupo_tarifario');
        }

        if ($this->input->is_set('item_homologado'))
        {
            $item_homologado = $this->input->get('item_homologado');

            if ($item_homologado == 1) {
                $this->cad_item_model->set_state('filter.list_opt', 'homologado');
            } else {
                $this->cad_item_model->unset_state('filter.list_opt');
            }
        }
        

        // só utiliza paginação quando não agrupa por similares ...
        if ($forma_lista == 2) {
            $this->cad_item_model->set_state('filter.group_similars', TRUE);
        } else {
            $this->cad_item_model->unset_state('filter.group_similars');

            $per_page = $this->input->get('per_page');

            $limit = 20;
            $offset = $per_page;

            $config['total_rows'] = $this->cad_item_model->get_total_entries();

            $config['per_page'] = $limit;
            $config['page_query_string'] = TRUE;
            $config['reuse_query_string'] = TRUE;
            $config['num_links'] = 5;

            $this->pagination->initialize($config);
        }


        $data['forma_lista'] = $forma_lista;
        $data['entries'] = $this->cad_item_model->get_entries($limit, $offset);
        $data['grupos'] = $grupos;

        if ($id_grupo_tarifario) {
            $grupo_tarifario = $this->grupo_tarifario_model->get_entry($id_grupo_tarifario);

            $data['grupo_tarifario'] = $this->grupo_tarifario_model->get_entry($id_grupo_tarifario);

            if ($impostos = $this->ncm_model->get_impostos($grupo_tarifario->ncm_recomendada))
            {
                $data['impostos'] = $impostos;
            }else if ($impostos_historico = $this->ncm_model->get_impostos($grupo_tarifario->ncm_recomendada, TRUE))
            {
                $data['impostos_historico'] = $impostos_historico;
            }

        }

        $this->include_css(
            array('thumbnail.grid.css', 'bootstrap-select/bootstrap-select.min.css')
        );

        $this->include_js(
            array(
                'modernizr.custom.js', 'jquery.expander.min.js',
                'bootstrap-select/bootstrap-select.min.js'
            )
        );

        $this->breadcrumbs->push('Home', '/');

        if (($capitulo = $this->input->get('capitulo'))) {
            $this->breadcrumbs->push('Capitulo ' . $capitulo, '/dashboard/view/'.$capitulo);
        }

        $this->breadcrumbs->push('Itens do grupo tarifário', '/consulta/');

        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $data['multi_estabelecimentos'] = $empresa->multi_estabelecimentos;

		$this->render('consulta/itens_grupo_tarifario', $data);
	}

    public function ncm_info_modal($ncm)
    {
        if (empty($ncm)) {
            return false;
        }

        $this->load->model('ncm_model');
        $this->load->model('ncm_capitulo_model');
        $this->load->model('ncm_capitulo_grupos_model');
        $this->load->model('ex_tarifario_model');
        $this->load->model('tec_log_alteracao_model');

        $this->estat_brasil = $this->load->database('estat_brasil', TRUE);
        $qquery = $this->estat_brasil->query("
            SELECT
                C.DAT_CONSULTA, C.EMENTA
                FROM CONSULTA_SRF C where
                C.COD_NCM = '{$ncm}'
                ORDER BY DAT_CONSULTA DESC
        ");

        $solucao_consulta = $qquery->result();

        $tec = new Tec_objeto_chave_ex_tarifario();

        $data['ncm'] = $ncm;
        $data['itens_ex_tarifario'] = $this->ex_tarifario_model->get_entries_by_ncm($ncm);
        $data['solucao_consulta'] = $solucao_consulta;
        $data['ex_tarifarios'] = $tec->desc_tipo_ex;

        $data['total_solucao_consulta'] = count($solucao_consulta);
        $data['total_itens_ex'] = count($data['itens_ex_tarifario']);

        $this->load->view('consulta/igt_ncm_info_modal', $data);
    }

    public function ajax_ncm_ex_tarifario()
    {
        $this->load->model('tec_log_alteracao_model');
        $tec = new Tec_objeto_chave_ex_tarifario();

        $ncm = $this->input->post('ncm');
        $cod_tipo_ex = $this->input->post('cod_tipo_ex');

        if ( empty($cod_tipo_ex) OR ! array_key_exists($cod_tipo_ex, $tec->desc_tipo_ex) ) {
            return false;
        }

        $query = $this->db->query("select * from tec_ncm_ex_tarif where cod_ncm = '{$ncm}' and cod_tipo_ex = '{$cod_tipo_ex}'");

        $data['cod_tipo_ex'] = $cod_tipo_ex;
        $data['itens'] = $query->result();

        $this->load->view('consulta/ajax_table_ex_tarif', $data);
    }

    public function itens_descricao()
    {
        if (!has_role('ncm_consulta_itens_descricao')) {
            show_permission();
        }

        $data = array();

        $limit = $offset = NULL;

        $this->load->model('grupo_tarifario_model');
        $this->load->model('cad_item_model');
        $this->load->model('ncm_model');
        $this->load->model('foto_model');

        $data['busca'] = $this->input->get('busca');

        if (!empty($data['busca'])) {

            $busca = $data['busca'];

            // Separa a string entre aspas duplas
            $matches = array();
            preg_match_all('/"([^"]*)"/',$busca,$matches);
            $btw_quotes = $matches[1];

            $busca = preg_replace('/"([^"]*)"/', "", $busca);

            // Separa a string entre aspas simples
            $matches_simple = array();
            preg_match_all('~\'(.*?)\'~', $busca, $matches_simple);
            $btw_simple_quotes = $matches_simple[1];

            $busca = preg_replace('~\'(.*?)\'~', "", $busca);

            $busca = explode(" ", addslashes($busca));
            $busca = array_filter($busca);

            if (!empty($btw_quotes))
            {
                $addslashes_btw_quotes = implode(',', $btw_quotes);
                $btw_quotes = explode(",", addslashes($addslashes_btw_quotes));
            }

            $busca = array_merge($busca, $btw_quotes);
            $busca = array_merge($busca, $btw_simple_quotes);

            $data['arr_busca'] = $busca;

            $this->grupo_tarifario_model->set_state('filter.busca', $busca);

            if (has_role('admin'))
            {
                if ($this->input->is_set('apenas_empresa_logada'))
                {
                    $data['apenas_empresa_logada'] = TRUE;
                    $this->grupo_tarifario_model->set_state('filter.apenas_empresa_logada', TRUE);
                }else
                {
                    $data['apenas_empresa_logada'] = FALSE;
                    $this->grupo_tarifario_model->unset_state('filter.apenas_empresa_logada');
                }
            }else
            {
                $this->grupo_tarifario_model->set_state('filter.apenas_empresa_logada', TRUE);
            }


            $data['grupos'] = $this->grupo_tarifario_model->get_grupos_by_itens();
        }

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Itens do grupo tarifário', '/consulta/');

        $this->render('consulta/itens_descricao', $data);
    }

    public function load_modal_detalhes($id_grupo_tarifario, $apenas_empresa_logada = FALSE)
    {
        $this->load->library('pagination');

        $per_page = $this->input->get('per_page');

        $limit = 10;
        $offset = $per_page;

        $this->load->model('grupo_tarifario_model');
        $this->load->model('cad_item_model');
        $this->load->model('ncm_model');
        $this->load->model('foto_model');
        $this->load->model('empresa_model');

        $data['grupo'] = $grupo = $this->grupo_tarifario_model->get_entry($id_grupo_tarifario);

        $data['impostos'] = $this->ncm_model->get_impostos($grupo->ncm_recomendada);

        $busca = $this->input->post('arr_busca');
        $arr_busca = json_decode($busca);

        $data['arr_busca'] = $arr_busca;

        if ($apenas_empresa_logada == TRUE)
        {
            $this->cad_item_model->set_state('filter.id_empresa', sess_user_company());
        }else
        {
            $this->cad_item_model->unset_state('filter.id_empresa');
        }

        $this->cad_item_model->set_state('filter.id_grupo_tarifario', $id_grupo_tarifario);
        $this->cad_item_model->set_state('filter.busca', $arr_busca);

        $data['itens'] = $this->cad_item_model->get_entries($limit, $offset);

        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $data['multi_estabelecimentos'] = $empresa->multi_estabelecimentos;

        $config['base_url'] = 'load_modal_detalhes/'.$id_grupo_tarifario.'/'.$apenas_empresa_logada;
        $config['total_rows'] = $this->cad_item_model->get_total_entries();
        $config['per_page'] = $limit;
        $config['page_query_string'] = TRUE;
        $config['reuse_query_string'] = TRUE;
        $config['num_links'] = 5;

        $this->pagination->initialize($config);

        $this->load->view('consulta/modal_itens_grupo_tarifario', $data);
    }
}
