<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

/**
 * GT - Criar importador de Itens sem De-para
 * #637 - Utilizar WS da Quirius para realizar importação/exportação de itens sem de para.
 * @requestor <PERSON><PERSON><PERSON> <<EMAIL>>
 */
class Depara extends MY_Controller {

    public function __construct() {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }

        if (has_role('becomex_pmo') && !has_role('sysadmin'))
        {
            show_permission();
        }

        $this->load->library('breadcrumbs');
        $this->load->library('receiverservicews');
    }

    public function index()
    {
        set_time_limit(0);

        if ($this->input->post('submit')) {

            $upload_path = config_item('upload_tmp_path');

            $config['upload_path'] = $upload_path;
            $config['allowed_types'] = 'xlsx';
            $config['max_size'] = 2147483648;

            $this->load->library('unzip');
            $this->load->library('upload', $config);

            if ( ! $this->upload->do_upload('arquivo'))
            {
                $data['errors'] = array('error' => $this->upload->display_errors('<p>', '</p>'));
            }
            else
            {
                $upload_data = $this->upload->data();

                $file_ext = strtolower($upload_data['file_ext']);

                // xlsx file
                if ($file_ext == '.xlsx') {

                    include APPPATH . 'libraries/xlsxreader.php';

                    $xlsx = new XLSXReader($upload_data['full_path']);
                    $sheetNames = $xlsx->getSheetNames();
                    $sheetActive = current($sheetNames);
                    $sheet = $xlsx->getSheet($sheetActive);

                    $item_sem_depara_arr = array();

                    $i = 0;

                    foreach($sheet->getData() as $row) {

                        if ($i > 0) {
                            $item = new ItemSemDePara();
                            $item->CodEmpresa    = (int) $row[0];
                            $item->CodFornecedor = (string) $row[1];
                            $item->CodItemFornec = (string) $row[2];
                            $item->CodItemInterno= (string) $row[3];
                            $item->CodUnidMedida = (string) $row[4];
                            $item->CodUnidMedidaFornec = (string) $row[5];
                            $item->DesItemInterno = (string) $row[6];
                            $item->TipoItem = (string) $row[7];

                            $item_sem_depara_arr[] = $item;
                        }

                        $i++;
                    }

                    $input = new AtualizaItemSemDePara();
                    $input->param = new AtualizaItemSemDeParaParam();
                    $input->param->Token = $this->receiverservicews->token;
                    $input->param->AtuSemDePara = $item_sem_depara_arr;

                    try {
                        $retorno = $this->receiverservicews->AtualizaItemSemDePara($input);

                        $message_on_render = '';

                        if ($retorno->AtualizaItemSemDeParaResult->WsError === NULL) {
                            // success
    						$message_on_render = $this->message_config('<h4>Sucesso</h4> Os itens informados foram processados.');
                        } else {

                            // error

                            $total_errors = count($retorno->AtualizaItemSemDeParaResult->WsError);

                            $errors = array();
                            $errors[] = '<ul>';

                            foreach ($retorno->AtualizaItemSemDeParaResult->WsError as $ws_error)
                            {
                                $errors[] = '<li>' . $ws_error->ErrorCode . ' - ' . $ws_error->ErrorMsg . '</li>';
                            }

                            $errors[] = '</ul>';

                            $error_title = $total_errors == 1 ? '<h4>Um erro aconteceu</h4>' : '<h4>Alguns erros aconteceram</h4>';
                            $message_on_render = $this->message_config($error_title . implode("", $errors), 'error');
                        }

						$this->message_next_render($message_on_render, NULL, TRUE);

                    } catch (Exception $e) {
                        //
                        $message_on_render = $this->message_config('<h4>Erro de Exceção do WS</h4>' . $e->getMessage(), 'error');
                        $this->message_next_render($message_on_render, NULL, TRUE);

                    }

                    redirect('depara');
                }
            }
        }

		$data = array();

        $data['show_alert_message'] = $this->show_alert_message();

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Itens sem De Para', '/depara/');

        $this->render('depara', $data);
    }

    public function download_modelo() {

        $this->load->model('empresa_model');
        $empresa_entry = $this->empresa_model->get_entry(sess_user_company());

        $input = new ConsultaItemSemDePara();
        $input->param = new ConsultaItemSemDeParaParam();
        $input->param->Token = $this->receiverservicews->token;
        $input->param->CNPJEmpresa = $empresa_entry->cnpj;

        try {
            $itemSemDeparaResult = $this->receiverservicews->ConsultaItemSemDePara($input);
            // var_dump($itemSemDeparaResult->ConsultaItemSemDeParaResult->ItemSemDePara->ItemSemDePara);

            $this->load->library('xlsxwriter');

            $this->xlsxwriter->setAuthor('Becomex Consulting');

            $header = array(
              'CodEmpresa'=>'string',
              'CodFornecedor'=>'string',
              'CodItemFornec'=>'string',
              'CodItemInterno'=>'string',
              'CodUnidMedida'=>'string',
              'CodUnidMedidaFornec'=>'string',
              'DesItemInterno'=>'string',
              'TipoItem'=>'string',
            );

            $data = array();

            if (!empty($itemSemDeparaResult->ConsultaItemSemDeParaResult->ItemSemDePara->ItemSemDePara))
            {
                foreach ($itemSemDeparaResult->ConsultaItemSemDeParaResult->ItemSemDePara->ItemSemDePara as $item)
                {
                    $data[] = array(
                        (string) $item->CodEmpresa, (string) $item->CodFornecedor,
                        (string) $item->CodItemFornec, (string) $item->CodItemInterno,
                        (string) $item->CodUnidMedida, (string) $item->CodUnidMedidaFornec,
                        (string) $item->DesItemInterno, (string) $item->TipoItem
                    );
                }
            }

            // var_dump($data);

            $writer = new XLSXWriter();
            $writer->writeSheet($data, 'ItensSemDePara', $header);

            // header('Content-Type: ')
            $filename = 'GT-ITENS-SEM-DEPARA-'.date('d-m-Y').'.xlsx';

            header('Content-disposition: attachment; filename='.$filename);
            header('Content-type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

            echo $writer->writeToStdOut();

            // [0]=>
            //   object(ItemSemDePara)#22 (8) {
            //     ["CodEmpresa"]=>
            //     string(1) "1"
            //     ["CodFornecedor"]=>
            //     string(14) "17465122000156"
            //     ["CodItemFornec"]=>
            //     string(8) "N0500486"
            //     ["CodItemInterno"]=>
            //     string(0) ""
            //     ["CodUnidMedida"]=>
            //     string(0) ""
            //     ["CodUnidMedidaFornec"]=>
            //     string(2) "PC"
            //     ["DesItemInterno"]=>
            //     string(0) ""
            //     ["TipoItem"]=>
            //     string(0) ""
            //   }

        } catch (Exception $e) {
            die('Impossível realizar leitura do WS');
            // except
        }
    }
}