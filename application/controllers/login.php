<?php if (! defined('BASEPATH')) exit('No direct script access allowed');

class Login extends MY_Controller
{

    var $table_usuario = 'usuario';

    public function __construct()
    {
        parent::__construct();
    }

    public function index()
    {

        // if (!is_logged()) {
        // 	redirect(config_item('login_bcx_pdc_url'));
        // }

        if (config_item('force_pdc_login')) {
            $product = config_item('product_slug') ? config_item('product_slug') : 'ges_tar';
            redirect(config_item('login_bcx_pdc_url') . '?product=' . $product);
        } elseif (config_item('force_oauth2_login')) {

            $oauth = new OauthValidate('config');
            try {
                $oauth->redirectToAuthentication();
            } catch (Exception $e) {
                exit($e->getMessage());
            }
            exit;
        }

        show_error('Página indisponível', 500, 'Erro');

        // $data = array();

        // if ($this->input->post()) {
        // 	$username	= $this->input->post('username');
        // 	$password	= $this->input->post('password');

        // 	$this->load->model('usuario_model');

        // 	if ($this->usuario_model->check_login($username, $password)) {

        // 		setcookie('homolog_bulk_selection', FALSE, time()-10, '/');

        // 		set_logon();
        // 		redirect('/consulta_diana');
        // 	} else {
        // 		$data['error'] = 'Dados <strong>inválidos</strong>! Tente novamente.';
        // 	}
        // }

        // $this->load->view('login', $data);
    }

    public function oauth()
    {
        $oauth = new OauthValidate('config');

        $response = $oauth->validateReturnData();

        // var_dump($response);
        // die;

        // dd($response);

        /*$response = [
                "status"=> "success",
                "message" => "",
                "data" => [
                    "accessToken" => "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICIzdEdaYmVwUU9qZlkzN00ybzZJVkJTbVVCaG5xMXlYY2dITV81VE4xeXA0In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.S39CSTHdcF9gCnmqy4n6BR8VzMgB6rqvKd4o_jiq7_w08DzEhk_vm_DImb4_lgU6AffXlPPUTb7jYFCL_bSn_ctFC7gqublKY7UpoB-kjcDGNvns0CvUVh5YpCQIXKmvzNkwq_rLgoLZ_ljHKs3gE1RfQc-GxQN3ShB41vBctR2YIeTHWWuAyURMYynA5lOOxNGZQRckk_wWd-GKWGv1XptDqfHLa_UCgDLBrNiA5xaGzyot2KnI8eUKBpWlQ2RW7CA69KQVM6EKr8VChzkcKYFSmU1megFpCwpVZWYW84U4dGr0zykOiFh8W2QpVgUv25HFreEgLi0rbtvzjSgI2g",
                    "refreshToken" => "eyJhbGciOiJIUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICIyYjU2OGEwOS0wODJhLTQ5OWMtOTQzZC0yZGJjMzZiYTQ3ZmEifQ.eyJleHAiOjE2NzM1MzExMjAsImlhdCI6MTY3MzUyMzkyMCwianRpIjoiMDZlNDMwZmEtNDVlOC00ODE2LWFlYmYtZjEzMmM3MWM3ZTE4IiwiaXNzIjoiaHR0cHM6Ly9sb2dpbi5iZWNvbWV4LmNvbS5ici9hdXRoL3JlYWxtcy9iZWNvbWV4IiwiYXVkIjoiaHR0cHM6Ly9sb2dpbi5iZWNvbWV4LmNvbS5ici9hdXRoL3JlYWxtcy9iZWNvbWV4Iiwic3ViIjoiZDkzZWIxMjEtNWYwNS00NGE1LWIwZmItNmNkZDYzODgxYWYwIiwidHlwIjoiUmVmcmVzaCIsImF6cCI6ImZhY2UtcG9ydGFsLWdlc3Rhby1kcmF3YmFjay1obWwiLCJzZXNzaW9uX3N0YXRlIjoiMWJlMTBkM2ItYzY3YS00ODA1LTk1NzEtZGE4ODRkYjgwYTI0Iiwic2NvcGUiOiJyb2xlcyBwcm9maWxlIGVtYWlsIiwic2lkIjoiMWJlMTBkM2ItYzY3YS00ODA1LTk1NzEtZGE4ODRkYjgwYTI0In0.EVncYyEn6uQX9FEDlnlIxSN2vqYI5L0o66q3iJ1QFCE",
                    "expires" => 1673527520,
                    0 => [
                        "sub" => "d93eb121-5f05-44a5-b0fb-6cdd63881af0",
                        "email_verified" => true,
                        "name" => "Giovan Dias",
                        "preferred_username" => "<EMAIL>",
                        "given_name" => "Giovan",
                        "family_name" => "Dias",
                        "email" => "<EMAIL>"
                    ]
                ]
            ];*/
        $erros = [];

        if ($response['status'] == 'success') {

            $this->load->model([
                'usuario_model',
                'perfil_model',
                'permissao_model',
            ]);

            $dados = $response['data'];
            $userOwner = $dados[0];
            $data['name'] = $userOwner['name'];
            if (
                !$userOwner['email_verified'] ||
                $userOwner['email_verified'] == false
            ) {
                $erros[] =
                    'Seu e-mail não está verificado no sistema de autenticação, por favor valide/verifique seu e-mail e tente novamente ou contate o administrador do sistema';
            }

            $id_usuario = $this->verifica_email($userOwner['email']);

            if ($id_usuario == false) {

                $dadosEmpresas = $oauth->getDadosEmpresas();

                if ($dadosEmpresas['status'] != 'success') {
                    $erros[] =
                        'Não há nenhum usuário cadastrado com esse e-mail&nbsp;<strong>' .
                        $userOwner['email'] .
                        '</strong> ou nenhuma empresa associada a ele, contate o administrador do sistema.<br><br>Dados adicionais: ' . $dadosEmpresas['message'];
                } else {
                    $empresasExistentes = [];
                    $permissionsSlugs = [
                        'homologacao',
                        'dashboard',
                        'homologacao_ficha',
                        'ncm_comparar',
                        'ncm_explorar',
                        'dados_tecnicos',
                        'gerenciar_logs',
                        'enviar_itens_revisao',
                    ];
                    foreach ($dadosEmpresas['data'] as $empresa) {
                        $sql = "SELECT e.id_empresa, e.cnpj, e.perfil_usuario_padrao_id
                            FROM empresa e
                            WHERE REPLACE(REPLACE(REPLACE(e.cnpj, '.', ''), '-', ''), '/', '') = ?
                            LIMIT 1";
                        $cnpjFormatado = str_pad($empresa['cnpj'], 14, '0', STR_PAD_LEFT);
                        $resultado = $this->db->query($sql, array(preg_replace('/[^0-9]/', '', $cnpjFormatado)));
                        if ($resultado->num_rows() > 0) {
                            $empresasExistentes[] = $resultado->row();
                        }
                    }

                    if (count($empresasExistentes) > 1) {
                        $erros[] =
                            'O usuário cadastrado com esse e-mail&nbsp;<strong>' .
                            $userOwner['email'] .
                            '</strong> possui mais de uma empresa vinculada ao seu cadastro, contate o administrador do sistema';
                    } elseif (count($empresasExistentes) < 1) {
                        $erros[] =
                            'O usuário cadastrado com esse e-mail&nbsp;<strong>' .
                            $userOwner['email'] .
                            '</strong> não possui empresa vinculada ao seu cadastro, contate o administrador do sistema';
                    } else {
                        $perfilDefaul = null;
                        if (!empty($empresasExistentes[0]->perfil_usuario_padrao_id)) {
                            $perfilDefaul = $this->perfil_model->get_entry($empresasExistentes[0]->perfil_usuario_padrao_id);
                        }

                        if (!$perfilDefaul) {
                            $perfilDefaul = $this->perfil_model->get_entry_default_for_keyclock($permissionsSlugs);

                            if (!$perfilDefaul) {
                                $data = array(
                                    'descricao' => 'Perfil padrão para novos usuários da API',
                                );

                                $id_perfil = $this->perfil_model->save($data);

                                $permisIds = [];

                                foreach ($permissionsSlugs as $slug) {
                                    $permission = $this->permissao_model->get_entry_by_slug($slug);
                                    $permisIds[] = $permission->id_permissao;
                                }

                                $this->perfil_model->save_perms($id_perfil, $permisIds);

                                $perfilDefaul = $this->perfil_model->get_entry($id_perfil);
                            }
                        }

                        $data = array(
                            'nome' => remove_special_char_str($userOwner['name']),
                            'email' => $userOwner['email'],
                            // 'senha' => $senha_hash,
                            'id_perfil'  => $perfilDefaul->id_perfil,
                            'recebe_email_pendencias' => 0,
                            'criado_em' => date('Y-m-d H:i:s'),
                            'id_empresa' => $empresasExistentes[0]->id_empresa
                        );
                        $id_usuario = $this->usuario_model->save($data);
                    }
                }
            }

            if (count($erros) < 1) {
                $usuario = $this->resgata_usuario($id_usuario);

                if ($usuario->status != '1') {
                    $erros[] = 'O usuário não está ativo, contate o administrador do sistema';
                } else {
                    $this->session->set_userdata('user_id', $id_usuario);
                    $this->session->set_userdata('user_nome', $usuario->nome);
                    $this->session->set_userdata('user_grupo', $usuario->id_grupo ?? null);
                    $this->session->set_userdata(
                        'user_perfil',
                        $usuario->id_perfil
                    );

                    set_logon();

                    if (($redirect_to = $this->input->get('redirect_to'))) {
                        redirect($redirect_to);
                    }

                    redirect('/');
                }
            }
        } else {
            $erros[] =
                'Ocorreu um problema que impediu a autenticação, contate o administrador do sistema passando a mensagem a seguir: <b>Falha ao autenticar, mensagem: ' .
                $response['message'] .
                '</b>';
        }
        $data['errors'] = $erros;
        $this->load->view('auth_oauth', $data);
    }

    private function resgata_usuario($id_usuario)
    {
        $this->db->where('id_usuario', $id_usuario);
        $query = $this->db->get($this->table_usuario);

        if ($query->num_rows() > 0) {
            return $row = $query->row();
        }

        return false;
    }

    private function verifica_email($email)
    {
        $this->db->where('email', $email);
        $query = $this->db->get($this->table_usuario);

        if ($query->num_rows() > 0) {
            $row = $query->row();

            return $row->id_usuario;
        }

        return false;
    }


    public function logout()
    {
        $sessCopy = $_SESSION;
        $this->session->destroy();

        if (config_item('force_pdc_login')) {
            redirect(config_item('login_bcx_pdc_url') . '?product=ges_drb&action=logout');
        } elseif (config_item('force_oauth2_login') && isset($sessCopy['idToken'])) {
            $oauth = new OauthValidate('config');
            $oauth->logout($sessCopy);
            //redirect('/');
            exit;
        } else {
            redirect(config_item('login_bcx_pdc_url') . '?product=ges_drb&action=logout');
        }
    }

    public function logoutOAuth()
    {
        $sessCopy = $_SESSION;
        $this->session->destroy();

        $oauth = new OauthValidate('config');
        $oauth->logout($sessCopy);
        exit;
    }
}
