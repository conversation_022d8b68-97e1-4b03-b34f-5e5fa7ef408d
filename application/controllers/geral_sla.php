<?php 

class Geral_sla extends MY_Controller
{

	public function __construct()
	{
	    parent::__construct();

	    if (!is_logged()) {
	        redirect('/login');
		}

		if (!has_role('dados_tecnicos_geral')) {
            show_permission();
	    }

		$this->load->library('breadcrumbs');

		$this->load->model(array(
            'geral_sla_model',
			'empresa_model',
			'empresa_prioridades_model',
			'item_model',
			'cad_item_model',
			'squad_model',
            'usuario_model'
        ));
	}

	public function index()
	{

		$post = $this->input->post();
        $per_page = $this->input->get('per_page');
        $parginate = $this->input->get('pag');
        

		$this->apply_default_filters($post, $parginate);

        if($this->input->post('filtered'))
        $this->load->library('pagination');

		$data = array();

		$data['lista_empresas'] = $this->empresa_model->get_all_entries();

        $data['id_empresa_sessao'] = sess_user_company();

		$empresa = get_filter_value('empresa', $this->geral_sla_model);
        $id_empresa = $empresa[0];

        if(empty($id_empresa) && $parginate == ''){
            $id_empresa = sess_user_company();
            $this->geral_sla_model->set_state('filter.empresa', $id_empresa);
        }

		if(!empty($id_empresa)){
			$data['estabelecimentos'] = $this->empresa_model->get_estabelecimentos($id_empresa);
			$data['empresa_prioridades'] =  $this->empresa_prioridades_model->get_entry($id_empresa);
            $data['eventos'] = $this->cad_item_model->getPacoteEventosByIdEmpresa($id_empresa);
		}
		$data['lista_status'] = $this->item_model->get_status();
		$data['lista_squads'] = $this->squad_model->get_entries();


        $search = $this->input->post('search') ? $this->input->post('search') : null;
        if (!is_null($search)) {
            $this->geral_sla_model->set_state("filter.search", $search);
        }else{
            //$this->geral_sla_model->unset_state("filter.search");
        }

        $userData = $this->usuario_model->get_entry(sess_user_id());

        $user = new stdClass();
        $user->id_usuario = sess_user_id();
        $user->id_empresa = $userData->id_empresa;
        $user->id_perfil = $userData->id_perfil;

        $data['user'] = $user;

		$total_entries = 0;
        $data['list'] = [];
        $data['pagination'] = [];

        $total_vermelho = 0;
        $total_amarelo = 0;
        $total_verde = 0;

        if($this->geral_sla_model->get_state('filter.filtered')){
            
            $this->load->library('pagination');
            $limit = 15;
            $offset = $per_page;
            $entries = $this->geral_sla_model->get_entries(NULL,NULL,TRUE);
            
            
            $total_entries = $entries['total'];
            $total_list = $entries['list'];
            
            $data['list'] = $this->geral_sla_model->get_entries($limit, $offset);
            $config['base_url'] = base_url("geral_sla?pag=1");
            $config['uri_segment'] = 3;
            //$config['use_page_numbers'] = TRUE;
            $config['total_rows'] = $total_entries;
            $data['total_rows'] = $total_entries;
            $config['per_page'] = $limit;
            $config['page_query_string'] = TRUE;
            $config['num_links'] = 5;

            $this->pagination->initialize($config);

            $data['pagination'] = $this->pagination->create_links();

            foreach($total_list as $item){

                $horas_sla_prioridade = $item->horas_sla_prioridade;
                $total_horas_consumidas = $item->total_horas_consumidas_becomex;
                
                // Tratar o Warning Division by zero
                if($horas_sla_prioridade == 0) {
                    $horas_sla_prioridade = 1;
                }
                // Tratar o Warning Division by zero
                if($total_horas_consumidas == 0) {
                    $total_horas_consumidas = 1;
                }

                $percentual_consumido = ($total_horas_consumidas / $horas_sla_prioridade) * 100;
                
                if ($percentual_consumido <= 75) {
                    $total_verde ++;
                } elseif (($percentual_consumido > 75) && ($percentual_consumido < 100)) {
                    $total_amarelo ++;
                } else {
                    $total_vermelho ++;
                }
            }

        } else {
            $data['itens'] = [];
            $data['query_homolog'] = '';
            $data['total_rows'] = 0;
            
        }

        $data['total_vermelho'] =  $total_vermelho;
        $data['total_amarelo'] = $total_amarelo;
        $data['total_verde'] = $total_verde;


		$this->title = "Dados Técnicos - Geral";

		$this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Dados Técnicos - Geral', '/geral_sla');

		$this->include_css(array('b3-datetimepicker.min.css', 'bootstrap-select/bootstrap-select.css', 'bootstrap-select/bootstrap-select.min.css', 'sweetalert.css'));
        $this->include_js(array('b3-datetimepicker.min.js', 'bootstrap-select/bootstrap-select.js', 'bootstrap-select/bootstrap-select.min.js', 'sweetalert.min.js', 'animate.js','jquery.mask.min.js'));

		 $this->render('geral_sla/default', $data);
	}

	private function apply_default_filters($post = NULL, $parginate = NULL)
    {
        $data = [];

        if ($this->input->is_set('reset_filters')) {
            $this->geral_sla_model->set_state_store_session(TRUE);
            $this->geral_sla_model->clear_states();
        } else {
            $this->geral_sla_model->set_state_store_session(TRUE);
            $this->geral_sla_model->restore_state_from_session('filter.', 'post');
        }

          //Filtered
        $filtred_post = isset($post['filtered']) ? $post['filtered'] : NULL;
        if (!empty($filtred_post) ) {
            $this->geral_sla_model->set_state('filter.filtered', $filtred_post);
        } else if(is_null($parginate) || $parginate == ''){
            $this->geral_sla_model->set_state_store_session(TRUE);
            $this->geral_sla_model->clear_states();
            return $data;
        }

		//Empresa
        $empresa = get_filter_value('empresa', $this->geral_sla_model);
        $empresa_post = isset($post['empresa']) ? $post['empresa'] : NULL;
		if(is_array($empresa) && !in_array(-1, $empresa)){
			$this->geral_sla_model->set_state('filter.empresa', $empresa);
		}else if(empty($empresa_post) || in_array(-1, $empresa_post)){
            $this->geral_sla_model->unset_state('filter.empresa');
            $this->geral_sla_model->unset_state('filter.estabelecimento');
            $this->geral_sla_model->unset_state('filter.prioridade');
            $this->geral_sla_model->unset_state('filter.evento');
            
		} 
        if(empty($empresa_post) &&  (is_null($parginate) || $parginate == '')){
            $this->geral_sla_model->unset_state('filter.empresa');
        }


		//Estabelecimento
		$estabelecimento = get_filter_value('estabelecimento', $this->geral_sla_model);
        $estabelecimento_post = isset($post['estabelecimento']) ? $post['estabelecimento'] : NULL;
		if(is_array($estabelecimento) && !in_array(-1, $estabelecimento)){
			$this->geral_sla_model->set_state('filter.estabelecimento', $estabelecimento);
		}else if(empty($estabelecimento_post) || in_array(-1, $estabelecimento_post)){
			$this->geral_sla_model->unset_state('filter.estabelecimento');
		} 
        
        if(empty($estabelecimento_post) &&  (is_null($parginate) || $parginate == '')){
            $this->geral_sla_model->unset_state('filter.estabelecimento');
        }

		 //Prioridade
        $empresa_prioridade_filter = get_filter_value('prioridade', $this->geral_sla_model);
        $empresa_prioridade_filter_post = isset($post['prioridade']) ? $post['prioridade'] : NULL;
        if (!empty($empresa_prioridade_filter) && !in_array(-1, $empresa_prioridade_filter)) {
            $this->geral_sla_model->set_state('filter.prioridade', $empresa_prioridade_filter);
        }else if(empty($empresa_prioridade_filter_post) || in_array(-1, $empresa_prioridade_filter_post) ){
            $this->geral_sla_model->unset_state('filter.prioridade');
        } 
        if(empty($empresa_prioridade_filter_post) &&  (is_null($parginate) || $parginate == '')){
            $this->geral_sla_model->unset_state('filter.prioridade');
        }

        //Pacote/Evento
        $evento = get_filter_value('evento', $this->geral_sla_model);
        $evento_post = isset($post['evento']) ? $post['evento'] : NULL;

        if((is_array($evento) && !in_array(-1, $evento))){

			$this->geral_sla_model->set_state('filter.evento', $evento);
		}else if(empty($evento_post) || in_array(-1, $evento_post)){
			$this->geral_sla_model->unset_state('filter.evento');
		}
        
        if(empty($evento_post) &&  (is_null($parginate) || $parginate == '')){
            $this->geral_sla_model->unset_state('filter.evento');
        }

		// Status do Item
        $status = get_filter_value('status', $this->geral_sla_model);
        $status_post = isset($post['status']) ? $post['status'] : NULL;
        if (!empty($status) && !in_array(-1, $status)) {
            $this->geral_sla_model->set_state('filter.status', $status);
        }else if(empty($status_post) || in_array(-1, $status_post) ){
            $this->geral_sla_model->unset_state('filter.status');
        } 

        if(empty($status_post) && (is_null($parginate) || $parginate == '')){
            $this->geral_sla_model->unset_state('filter.status');
        }

		// Squads
        $squads = get_filter_value('squads', $this->geral_sla_model);
        $squads_post = isset($post['squads']) ? $post['squads'] : NULL;
        if (!empty($squads) || !empty($squads_post)) {

            if(!empty($squads_post)){
                $squads = $squads_post;
            }

            $this->geral_sla_model->set_state('filter.squads', $squads);
        }

        if(empty($squads_post) && (is_null($parginate) || $parginate == '')){
            $this->geral_sla_model->unset_state('filter.squads');
        }

        //Data Inicial Criação
        $data_ini_str = get_filter_value('data_ini', $this->geral_sla_model);
        $data_ini_str_post = isset($post['data_ini']) ? $post['data_ini'] : NULL;

        if (!empty($data_ini_str) || !empty($data_ini_str_post)) {

            if(!empty($data_ini_str_post)){
                $data_ini_str= $data_ini_str_post;
            }

            $data_ini = str_replace('/', '-', $data_ini_str);
            $data_ini = date('Y-m-d', strtotime($data_ini));

            $this->geral_sla_model->set_state('filter.data_ini', $data_ini);
        } 
        
        if(empty($data_ini_str_post) && (is_null($parginate) || $parginate == '')){
            $this->geral_sla_model->unset_state('filter.data_ini');
        }

        //Data Final Criação
        $data_fim_str = get_filter_value('data_fim', $this->geral_sla_model);
        $data_fim_str_post = isset($post['data_fim']) ? $post['data_fim'] : NULL;
        if (!empty($data_fim_str) || !empty($data_fim_str_post)) {

            if(!empty($data_fim_str_post)){
                $data_fim_str = $data_fim_str_post;
            }

            $data_fim = str_replace('/', '-', $data_fim_str);
            $data_fim = date('Y-m-d', strtotime($data_fim));

            $this->geral_sla_model->set_state('filter.data_fim', $data_fim);
        }
        
        if(empty($data_fim_str_post) && (is_null($parginate) || $parginate == '')){
            $this->geral_sla_model->unset_state('filter.data_fim');
        }


        //Gestão Mensal
        $gestao_mensal = get_filter_value('gestao_mensal', $this->geral_sla_model);
        $gestao_mensal_post = isset($post['gestao_mensal']) ? $post['gestao_mensal'] : NULL;

        if(!empty($gestao_mensal) || !empty($gestao_mensal_post)){
            if(!empty($gestao_mensal_post)){
                $gestao_mensal = $gestao_mensal_post;
            }
            
            $this->geral_sla_model->set_state('filter.gestao_mensal', $gestao_mensal);
            
        }
        
        if(empty($gestao_mensal_post) && (is_null($parginate) || $parginate == '')){
            $this->geral_sla_model->unset_state('filter.gestao_mensal');
        }
     
        return $data;
    }

    public function get_ajax_estabelecimentos(){
        $this->load->model('empresa_model');
        $id_empresa_get    = $this->input->get('empresa_id');
        $estabelecimentos  = $this->empresa_model->get_estabelecimentos($id_empresa_get);
        $data['entry']     =  $estabelecimentos ;
        return response_json($data);  
    }

    public function get_ajax_prioridades(){
        $this->load->model('empresa_prioridades_model');
        $id_empresa_get    = $this->input->get('empresa_id');
        $prioridades  = $this->empresa_prioridades_model->get_entry($id_empresa_get);
        $data['entry']     =  $prioridades;
        return response_json($data);
    }

    public function get_ajax_eventos(){
        $this->load->model('cad_item_model');
        $id_empresa_get    = $this->input->get('empresa_id');
        $eventos = $this->cad_item_model->getPacoteEventosByIdEmpresa($id_empresa_get);
        $data['entry']     =  $eventos;
        return response_json($data);
    }

}