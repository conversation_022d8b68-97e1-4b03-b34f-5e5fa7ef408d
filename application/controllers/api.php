<?php

ini_set("soap.wsdl_cache_enabled", 0);

include APPPATH . 'third_party/nusoap/nusoap' . EXT;

if (file_exists(APPPATH . 'third_party/soap_types/api.classes' . EXT)) {
    include APPPATH . 'third_party/soap_types/api.classes'      . EXT;
}

class Api extends MY_Controller
{
	private $server;

    public function tests()
    {
        $consultaItemInput = new ConsultaItemInput();
        $consultaItemInput->cnpjFornecedor = "16716417000195";
        $consultaItemInput->cnpjCliente = "05876349000105";
        $consultaItemInput->partNumber = "10120693";
        $consultaItemInput->dataNF = "2014-07-10";

        $this->load->model('api_model');
        $test = $this->api_model->consultaItem($consultaItemInput);
        var_dump($test);
    }

    public function sandbox()
    {
        if (! has_role('admin')) {
            show_404();
        }

        $cnpj_fornecedor = $this->input->get('cnpj_fornecedor');
        $cnpj_cliente = $this->input->get('cnpj_cliente');
        $part_number = $this->input->get('part_number');
        $data_nf = $this->input->get('data_nf');

        $consultaItemInput = new ConsultaItemInput();
        $consultaItemInput->cnpjFornecedor = $cnpj_fornecedor;
        $consultaItemInput->cnpjCliente = $cnpj_cliente;
        $consultaItemInput->partNumber = $part_number;
        $consultaItemInput->dataNF = $data_nf;

        $this->load->model('api_model');
        $test = $this->api_model->consultaItem($consultaItemInput);

        ob_start();
        var_dump($test);
        $return = ob_get_clean();

        printf("%s%s%s", "<pre>", $return, "</pre>");
    }

    public function docs() {
		$content = file_get_contents('http://' . $_SERVER['HTTP_HOST'] . site_url("api?wsdl"));

		$xml = new DOMDocument('1.0', 'utf-8');
		$xml->loadXML($content);

		$xsl = new DOMDocument;

		$xsl->load(FCPATH . 'wsdl-viewer/wsdl-viewer.xsl');
		//$xsl->setParameter ("param", "ENABLE-LINK", "false()");
		$proc = new XSLTProcessor();
		$proc->setParameter("param", "BASE_URL", config_item("base_url"));
		$proc->importStyleSheet($xsl);

		echo $proc->transformToXML($xml);
    }

	public function __construct()
	{
		parent::__construct();

		$URL       = "gestaotarifaria.ws.becomex.com.br";
		$namespace = $URL . '?wsdl';

		$this->server = new soap_server;

		$this->server->debug_flag = false;
		$this->server->configureWSDL('GestaoTarifariaWS', NULL);
		$this->server->wsdl->soap_defencoding = "utf-8";

        $this->load->model('api_model');

        // Definição dos tipos complexos (arrays)
        $array_types = array(
//            ['AtoProcessado', 'Lista dos atos processados']
        );

        if (isset($array_types) && count($array_types) > 0)
        {
            foreach ($array_types as $array_type)
            {
                list($complexTypeName, $complexTypeDescription) = $array_type;
        		$this->server->wsdl->addComplexType(
        			'ArrayOf'.$complexTypeName,
        			'complexType',
        			'array',
        			'',
        			'SOAP-ENC:Array',
        			array(),
        			array(
        				array('ref'=>'SOAP-ENC:arrayType','wsdl:arrayType'=>'tns:'.$complexTypeName.'[]')
        			),
        			'tns:'.$complexTypeName,
        			$complexTypeDescription
        		);
            }
        }

        // Definição das Classes de Objetos
        $describe_classes = array(
            'ConsultaItemInput',
            'Item'
        );

        if (isset($describe_classes) && count($describe_classes) > 0) {
            foreach ($describe_classes as $_class) {
                call_user_func_array(array($this->server->wsdl, 'addComplexType'), describe_class($_class));
            }
        }

		# Processos de negócio
		 // , array('cnpjFornecedor' => 'xsd:string', 'cnpjCliente' => 'xsd:string',
//                                  'partNumber' => 'xsd:string',
//                                  'dataNF' => 'xsd:string')
		$this->server->register('consultaItem'
                         , array('consultaItemInput' => 'tns:ConsultaItemInput')
		                 , array('return'=>'tns:Item')
		                 , $namespace, false
		                 , 'rpc'
		                 , 'encoded'
		                 , 'Consulta um determinado item na base do GT');

		$this->server->wsdl->schemaTargetNamespace = 'urn:Api';
	}

	public function index()
	{
		if (!isset($HTTP_RAW_POST_DATA)) {
			$HTTP_RAW_POST_DATA = file_get_contents('php://input');
		}

		$HTTP_RAW_POST_DATA = isset ( $HTTP_RAW_POST_DATA ) ? $HTTP_RAW_POST_DATA : '';

		$this->server->service ( $HTTP_RAW_POST_DATA );
	}
}
