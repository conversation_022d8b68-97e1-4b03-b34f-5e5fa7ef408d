<?php

class Download extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        if (! is_logged()) {
            redirect('login');
        }

        $this->load->model('anexo_model');
    }

    public function index()
    {
        $nome_hash          = $this->input->get('arquivo');
        $id_empresa         = sess_user_company();

        $anexo = $this->anexo_model->get_entry_by_hash($nome_hash, $id_empresa);

        if (empty($anexo)) {
            show_404();
        }

        $this->load->helper('download');

        $nome_arquivo = sprintf('%s.%s', $anexo->nome_arquivo, $anexo->extensao);
        $arquivo = $this->anexo_model->get_full_path($anexo->nome_hash, $anexo->extensao);

        if (! file_exists($arquivo)) {
            show_404();
        }

        $data_arquivo = file_get_contents($arquivo);
        force_download($nome_arquivo, $data_arquivo);
    }
}
