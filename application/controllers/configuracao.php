<?php

class Configuracao extends MY_Controller {

	public function __construct() {
		parent::__construct();

		$this->load->model('usuario_model');

		if (!is_logged()) {
			redirect('/login');
		}

		$this->load->library('breadcrumbs');
	}

	public function editar_perfil()
    {
        $this->load->model(array('empresa_model', 'perfil_model'));
        $this->load->helper('common_helper');

		$id_usuario = sess_user_id();
		$id_empresa = sess_user_company();

        $data = array();

        try {
            $empresas_logado = $empresas_usuario = array();

            $entry = $this->usuario_model->get_entry($id_usuario);
            $data['entry'] = $entry;

            if (!has_role('sysadmin')) {
                $acesso_empresas_logado = $this->usuario_model->get_empresas_by_user(sess_user_id());
                $acesso_empresas_usuario = $this->usuario_model->get_empresas_by_user($entry->id_usuario);

                foreach ($acesso_empresas_logado as $acesso_empresa_logado) {
                    $empresas_logado[] = $acesso_empresa_logado->id_empresa;
                }

                foreach ($acesso_empresas_usuario as $acesso_empresa_usuario) {
                    $empresas_usuario[] = $acesso_empresa_usuario->id_empresa;
                }

                // if (!in_array($entry->id_perfil, array(2, 3)) || !array_intersect($empresas_logado, $empresas_usuario)) {
                //     $this->message_next_render('Você não possui permissões suficientes.', 'error');
                //     redirect('cadastros/usuario');
                // }
            }
        } catch (Exception $e) {
            show_error($e->getMessage());
        }

        if (has_role('sysadmin') || has_role("consultor")) {
            $data['empresas'] = $this->empresa_model->get_all_entries();
        } else {
            $data['empresas'] = $this->usuario_model->get_empresas_by_user(sess_user_id());
        }

        $data['perfis'] = $this->perfil_model->get_all_entries();

        $usuario = $this->usuario_model->get_entry($id_usuario);
        $empresa = $this->empresa_model->get_entry($usuario->id_empresa);

        if ($this->input->post('submit')) {
            $this->load->library('form_validation');

            $this->form_validation->set_rules('nome', 'Nome', 'trim|required');
            $this->form_validation->set_rules('id_empresa[]', 'Empresa', 'required');

            if (has_role("gerenciar_usuarios")) {
                $this->form_validation->set_rules('id_perfil', 'Perfil', 'trim');
            }

            if ($this->form_validation->run() == TRUE) {
                $id_empresa = $this->input->post('id_empresa') ? $this->input->post('id_empresa') : sess_user_company();

                $perfil = $this->input->post('id_perfil');

                $datadb = array(
                    'nome' => remove_special_char_str($this->input->post('nome')),
                    'id_perfil' => $usuario->id_perfil,
                    'recebe_email_pendencias' => $this->input->post('recebe_email_pendencias'),
                    'atualizado_por' => sess_user_id(),
                    'atualizado_em' => date('Y-m-d H:i:s')
                );

                if (has_role("gerenciar_usuarios")) {
                    $datadb['id_perfil'] = $perfil;
                }

                if (is_array($id_empresa)) {
                    $data['id_empresa'] = $id_empresa[0];
                } else {
                    $data['id_empresa'] = $id_empresa;
                }

                $this->usuario_model->save($datadb, array('id_usuario' => $id_usuario));
                $this->usuario_model->remove_usuario_empresas($id_usuario, $empresas_logado);

                if (customer_has_role('engenheiro', $id_usuario) || customer_has_role('fiscal', $id_usuario) || customer_has_role('consultor', $id_usuario)) {
                    $this->usuario_model->save_usuario_empresas($id_usuario, $id_empresa);
                }

                if ($this->input->is_set('gerente_de_projetos')) {
                    //Se perfil Cliente-PMO
                    if (customer_has_role('cliente_pmo', $id_usuario)) {
                        $this->empresa_model->save_gp($id_usuario, $id_empresa);
                    } else {
                        if ($empresa->id_gerente_de_projetos == $id_usuario) {
                            $this->empresa_model->save_gp(NULL, $id_empresa);
                        }
                    }
                } else if ($empresa->id_gerente_de_projetos == $id_usuario) {
                    $this->empresa_model->save_gp(NULL, $id_empresa);
                }

                $this->message_next_render('Sucesso! Usuário [<strong>' . $this->input->post('nome') . '</strong> atualizado]', 'success');
            } else {
                $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
                $this->message_on_render($err, 'error');
            }
        }

        $data['id_gerente_de_projetos'] = $empresa->id_gerente_de_projetos;

        if (!$data['usuario_empresas'] = $this->usuario_model->get_empresas_by_user($id_usuario, TRUE)) {
            $data['usuario_empresas'][] = $data['entry']->id_empresa;
        }

		$this->breadcrumbs->push('Home', '/');
		$this->breadcrumbs->push('Editar perfil', '/configuracao/editar_perfil');

        $this->include_js('bootstrap-select/bootstrap-select.js');
        $this->include_css('bootstrap-select/bootstrap-select.css');

        $this->render('configuracao/editar_perfil', $data);
	}
}
