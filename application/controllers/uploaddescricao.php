<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

require_once  APPPATH . 'libraries/Spout/Autoloader/autoload.php';
use Box\Spout\Reader\ReaderFactory;
use Box\Spout\Common\Type;

class UploadDescricao extends MY_Controller {

    public function __construct()
    {
        parent::__construct();

        if (!is_logged())
        {
            redirect('/login');
        }

        if (!has_role('arquivos_sugestao'))
        {
            show_permission();
        }

        $this->load->library('breadcrumbs');
    }

    public function index()
    {
        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Enviar Itens para Sugestão de Grupo Tarifário', '/uploaddescricao/');

        $data = array();

        $view = 'uploaddescricao';

        if ($this->input->post('submit'))
        {
            $this->load->library('upload');

            $upload_path = config_item('upload_tmp_path');

            $config['upload_path'] = $upload_path;
            $config['allowed_types'] = 'zip|xlsx';
            $config['max_size'] = 2147483648;

            $this->upload->initialize($config);

            if ( ! $this->upload->do_upload('arquivo') )
            {
                $data['errors'] = array('error' => $this->upload->display_errors('<p>', '</p>'));
            } else
            {
                $upload_data = $this->upload->data();
                $reader = ReaderFactory::create(Type::XLSX);
                $reader->open($upload_data['full_path']);

                $count = 0;
                foreach ($reader->getSheetIterator() as $sheet)
                {
                    if ($sheet->getIndex() === 0)
                    {
                        foreach($sheet->getRowIterator() as $row)
                        {
                            $count++;
                        }
                    }
                }

                $post = $this->input->post();

                if($count > 100)
                {
                    $upload_data = $this->upload->data();

                    $query_str  = 'uploadpath='.$upload_data['full_path'];
                    $query_str .= '&id_usuario='.sess_user_id();
                    $query_str .= '&id_empresa='.sess_user_company();

                    $query_str = rawurlencode(base64_encode($query_str));

                    if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN')
                    {
                        shell_exec('start /B php.exe index.php cron/save_xls/'.$query_str);
                    } else
                    {
                        shell_exec('/usr/bin/php index.php cron/save_xls/'.$query_str.' > /dev/null 2>/dev/null &');
                    }

                    $this->message_next_render('<h5><strong>OK!</strong> o arquivo será enviado por email após ser processado.</h5>');

                    redirect("uploaddescricao");

                } else
                {
                    $this->load->helper('text_helper');

                    $id_empresa = sess_user_company();

                    //Índices de cada coluna na tabela:
                    $idx = array(
                        'part_number'   => 0,
                        'descricao'     => 1
                    );

                    $this->load->library('gerador_xls_grupo_tarifario');

                    $items = $this->gerador_xls_grupo_tarifario->iteratorSheet($reader, $idx, array('id_empresa' => $id_empresa));

                    $this->breadcrumbs->push('Home', '/');
                    $this->breadcrumbs->push('Enviar Itens para Sugestão de Grupo Tarifário', '/uploaddescricao/');
                    $this->breadcrumbs->push('Resultados', '/uploaddescricao/resultados/');

                    if (count($items) > 100)
                    {
                        $data['xls'] = $this->gerador_xls_grupo_tarifario->generate_xls($items);
                        $view = 'uploaddescricao-download';
                    } else
                    {
                        $data['results'] = $items;
                        $view = 'uploaddescricao-list';
                    }
                }

            }
        }

        $this->render($view, $data);
    }

    public function download_xls()
    {
        $post = $this->input->post();

        if (count($post['item']) == 0)
        {
            redirect("uploaddescricao");
        }

        foreach ($post['item'] as $k => $item)
        {
            $items[$k] = array(
                'part_number' => $item['part_number'],
                'descricao' => $item['descricao']
            );

            if (isset($item['grupo']))
            {
                foreach ($item['grupo'] as $grupo)
                {
                    $items[$k]['grupos'][] = array(
                        'descricao' => $post['grupo'][$grupo]['grupo_tarifario'],
                        'ncm' => $post['grupo'][$grupo]['ncm'],
                        'descricao_sugerida' => $post['grupo'][$grupo]['descricao_sugerida']
                    );
                }
            }
        }

        $this->load->library('gerador_xls_grupo_tarifario');

        $this->gerador_xls_grupo_tarifario->generate_xls($items, true);
    }
}
