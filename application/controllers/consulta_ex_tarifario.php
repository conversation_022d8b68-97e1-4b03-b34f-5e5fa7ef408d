<?php 

class Consulta_ex_tarifario extends MY_Controller
{
	public function __construct()
	{
	    parent::__construct();

	    if (!is_logged()) {
	        redirect('/login');
        }

        if (!has_role('ncm_consulta_ex_tarif')) {
            show_permission();
        }

        $this->load->model('suframa_model');
	}

	public function index()
	{
		$this->load->library('breadcrumbs');

		$this->breadcrumbs->push('Home', '/');
		$this->breadcrumbs->push("Consulta Ex", '/consulta_ex_tarifario/');

		$this->include_js(array(
            'bootstrap-select/bootstrap-select.js',
            'bootstrap-notify.min.js',
			'jquery.cookie.js'
        ));

        $this->include_css(array(
            'bootstrap-select/bootstrap-select.css',
            'animate.css'
        ));
		
		$this->render('consulta_ex_tarifario/default');
    }
    
    public function ajax_get_suframa() 
    {
        try {
            $response = [
                'message' => 'sucesso!',
                'data'    => array()
            ];

            $status = 200;

            if ($post = $this->input->post()) {

                $ncm = $post['ncm'];

                $suframa_produtos = $this->suframa_model->get_suframa_produtos_ex_tarifario();

                $produtos_suframa = array();

                foreach ($suframa_produtos as $produto) {
                    $suframas = $this->suframa_model->get_suframa_produtos_lista_by_code_and_descricao($produto->CODIGO, $ncm);

                    if (!empty($suframas)) {
                        array_push($produtos_suframa, $produto);
                    }
                }

                $response['data'] = array(
                    'response' => $produtos_suframa,
                    'has_atributos' =>  empty($produtos_suframa) ? false : true
                );
            }
        } catch (Exception $e) {
            $response['message'] = $e->getMessage();
            $response['data'] = array('has_atributos' => false);
            $status = 406;
        }

        return response_json($response, $status);
    }

	public function xhr_get_suframa_produtos_by_code()
    {
        try {
            $response = [
                'message' => 'sucesso!',
                'data'    => array()
            ];
            $status = 200;

            $this->load->model('suframa_model');

            $code = $this->input->get('code');
            $ncm = $this->input->get('ncm');
            $descricao = $this->input->get('descricao');

            $response['data'] = $this->suframa_model->get_suframa_produtos_lista_by_code_and_descricao($code, $ncm, $descricao);
        } catch (Exception $e) {
            $reponse_data['message'] = $e->getMessage();
            $status = 406;
        }

        return response_json($response, $status);
    }

    public function ajax_get_nve()
    {
        try {
            $response = [
                'message' => 'sucesso!',
                'data'    => array('has_atributos' => false)
            ];
            $status = 200;

            if ($post = $this->input->post()) {
                $this->load->model(array(
                    'nve_atributo_model'
                ));

                $ncm = $post['ncm'];
                $atributos = $this->nve_atributo_model->get_atributos_by_ncm($ncm);

                if(empty($ncm)) {
                    $atributos = [];
                }

                $response['data'] = array(
                    'response' => $this->load->view('consulta_ex_tarifario/tabs/subview_nve', array(
                        'ncm' => $ncm,
                        'atributos' => $atributos
                    ), true),
                    'has_atributos' =>  empty($atributos) ? false : true
                );
            }
        } catch (Exception $e) {
            $response['message'] = $e->getMessage();
            $response['data'] = array('has_atributos' => false);
            $status = 406;
        }

        return response_json($response, $status);
    }

    public function ajax_get_li()
    {
        try {
            $response = [
                'message' => 'sucesso!',
                'data'    => array('has_atributos' => false)
            ];
            $status = 200;

            if ($post = $this->input->post()) {
                $this->load->model(array(
                    'ncm_model'
                ));

                $ncm = $post['ncm'];
                $lis = !empty($ncm) ? $this->ncm_model->get_li_oracle($ncm) : [];

                $response['data'] = array(
                    'response' => $this->load->view('consulta_ex_tarifario/tabs/subview_li', array(
                        'lis' => $lis,
                        'ncm' => $ncm
                    ), true),
                    'has_atributos' =>  empty($lis) ? false : true
                );
            }
        } catch (Exception $e) {
            $response['message'] = $e->getMessage();
            $response['data'] = array('has_atributos' => false);
            $status = 406;
        }

        return response_json($response, $status);
    }

    public function ajax_get_ex_ii()
    {
        try {
            $response = [
                'message' => 'sucesso!',
                'data'    => array('has_atributos' => false)
            ];
            $status = 200;

            if ($post = $this->input->post()) {
                $this->load->model(array(
                    'ex_tarifario_model'
                ));

                $ncm = $post['ncm'];
                $descricao = $post['descricao'];

                $ex_ii = $this->ex_tarifario_model->get_all_ex_ii_by_ncm_descricao($ncm, $descricao, true);

                if(empty($ncm)) {
                    $ex_ii = [];
                }

                $response['data'] = array(
                    'response' => $this->load->view('consulta_ex_tarifario/tabs/subview_ex_ii', array(
                        'ex_iis' => $ex_ii,
                        'ncm' => $ncm
                    ), true),
                    'has_atributos' =>  empty($ex_ii) ? false : true,
                    'descricao' => $descricao
                );
            }
        } catch (Exception $e) {
            $response['message'] = $e->getMessage();
            $response['data'] = array('has_atributos' => false);
            $status = 406;
        }

        return response_json($response, $status);
    }

    public function ajax_get_ex_ipi()
    {
        try {
            $response = [
                'message' => 'sucesso!',
                'data'    => array('has_atributos' => false)
            ];
            $status = 200;

            if ($post = $this->input->post()) {
                $this->load->model(array(
                    'ex_tarifario_model'
                ));

                $ncm = $post['ncm'];

                $ex_ipi = $this->ex_tarifario_model->get_all_ex_ipi_by_ncm($ncm, true);

                if(empty($ncm)) {
                    $ex_ipi = [];
                }

                $response['data'] = array(
                    'response' => $this->load->view('consulta_ex_tarifario/tabs/subview_ex_ipi', array(
                        'ex_ipis' => $ex_ipi,
                        'ncm' => $ncm
                    ), true),
                    'has_atributos' =>  empty($ex_ipi) ? false : true
                );
            }
        } catch (Exception $e) {
            $response['message'] = $e->getMessage();
            $response['data'] = array('has_atributos' => false);
            $status = 406;
        }

        return response_json($response, $status);
    }

    public function ajax_get_classificacao_energetica()
    {
        try {
            $response = [
                'message' => 'sucesso!',
                'data'    => array('has_atributos' => false)
            ];
            $status = 200;

            if ($post = $this->input->post()) {
                $this->load->model(array(
                    'ncm_model'
                ));

                $ncm = $post['ncm'];
                $items = !empty($ncm) ? $this->ncm_model->get_classificacao_energetica($ncm) : [];

                $response['data'] = array(
                    'response' => $this->load->view('consulta_ex_tarifario/tabs/subview_classificacao_energetica', array(
                        'items' => $items,
                        'ncm' => $ncm
                    ), true),
                    'has_atributos' =>  empty($items) ? false : true
                );
            }
        } catch (Exception $e) {
            $response['message'] = $e->getMessage();
            $response['data'] = array('has_atributos' => false);
            $status = 406;
        }

        return response_json($response, $status);
    }

    public function ajax_get_antidumping()
    {
        try {
            $response = [
                'message' => 'sucesso!',
                'data'    => array('has_atributos' => false)
            ];
            $status = 200;

            if ($post = $this->input->post()) {
                $this->load->model(array(
                    'ncm_model'
                ));

                $ncm = $post['ncm'];
                $antidumpings = !empty($ncm) ? $this->ncm_model->get_antidumping_oracle($ncm) : [];

                $response['data'] = array(
                    'response' => $this->load->view('consulta_ex_tarifario/tabs/subview_antidumping', array(
                        'antidumpings' => $antidumpings,
                        'ncm' => $ncm
                    ), true),
                    'has_atributos' =>  empty($antidumpings) ? false : true
                );
            }
        } catch (Exception $e) {
            $response['message'] = $e->getMessage();
            $response['data'] = array('has_atributos' => false);
            $status = 406;
        }

        return response_json($response, $status);
    }

    public function ajax_get_atributos()
    {
        try {
            $response = [
                'message' => 'sucesso!',
                'data'    => array('has_atributos' => false)
            ];
            $status = 200;

            if ($post = $this->input->post()) {
                $this->load->model(array(
                    'catalogo/produto_model'
                ));

                $ncm = trim($post['ncm']);
                $atributos = !empty($ncm) ? $this->produto_model->get_attr_ncm($ncm) : [];

                $response['data'] = array(
                    'response' => $this->load->view('consulta_ex_tarifario/tabs/subview_atributos', array(
                        'atributos' => $atributos,
                        'ncm' => $ncm
                    ), true),
                    'has_atributos' => empty($atributos) ? false : true
                );
            }
        } catch (Exception $e) {
            $response['message'] = $e->getMessage();
            $response['data'] = array('has_atributos' => false);
            $status = 406;
        }

        return response_json($response, $status);
    }
}