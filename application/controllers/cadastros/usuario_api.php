<?php

class Usuario_api extends MY_Controller
{

    public function __construct()
    {
        parent::__construct();

        $this->load->model('api/usuario_api_model');

        if (!is_logged()) {
            redirect('/login');
        }

        if (!has_role('gerenciar_usuarios')) {
            show_permission();
        }
    }

    public function novo()
    {
        $usuario = $this->usuario_api_model->generateNewUser();

        return response_json($usuario);
    }

    public function todos()
    {
        $usuarios = $this->usuario_api_model->all();

        return response_json($usuarios);
    }

    public function remover()
    {
        $success = $this->usuario_api_model->remove(get_axios_post());

        return response_json(array(
            'success' => $success
        ));
    }
}
