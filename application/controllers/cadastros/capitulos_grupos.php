<?php

class Capitulos_grupos extends MY_Controller {

    public function __construct() {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }

        if (!has_role('gerenciar_secao')) {
            show_permission();
        }

        $this->load->library('breadcrumbs');

        $this->load->model('ncm_capitulo_grupos_model');

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Capítulos NCM', '/cadastros/capitulos/');
        $this->breadcrumbs->push('Seções', '/cadastros/capitulos/grupos');
    }

    public function index()
    {
        $data = array();

        $this->ncm_capitulo_grupos_model->set_state_store_session(TRUE);
        $this->ncm_capitulo_grupos_model->restore_state_from_session();

        $query_str = '';

        $this->load->library('pagination');

        $page = $this->input->get('per_page');
        $limit = 50;

        $total_entries = $this->ncm_capitulo_grupos_model->get_total_entries();
        $data['list'] = $this->ncm_capitulo_grupos_model->get_entries($limit, ($page>0?$page-1:0)*$limit);

        $config['base_url'] = base_url("cadastros/capitulos/grupos?{$query_str}");
        $config['use_page_numbers'] = TRUE;
        $config['total_rows'] = $total_entries;
        $config['per_page'] = $limit;
        $config['page_query_string'] = TRUE;

        $this->pagination->initialize($config);

        $data['pagination'] = $this->pagination->create_links();

        $this->load->model('ncm_capitulo_model');
        $data['capitulos'] = $this->ncm_capitulo_model->get_entries();

        $this->include_js('jquery.multi-select.js');
        $this->include_css('multi-select.css');

        $this->title = "Grupos";

        $this->render('cadastros/capitulos/grupos/default', $data);
    }

    public function excluir()
    {

        $id_list = $this->input->post('id_list');
        $return = array();
        $return['status'] = FALSE;

        $msg = '';
        $id_used = $this->ncm_capitulo_grupos_model->check_has_capitulos($id_list);

        if (!empty($id_used))
        {
            $msg .= $this->message_config('Algumas seções não puderam ser excluídas pois possuem capítulos vinculados.', 'warning');
        }

        if ($this->ncm_capitulo_grupos_model->remove($id_list, $id_used)) {
            $return['status'] = TRUE;
            $msg .= $this->message_config('Exclusão realizada com sucesso!', 'success');
        }

        $this->message_next_render($msg, NULL, TRUE);

        echo json_encode($return);
    }

    public function novo()
    {
        $this->title = "Grupos &gt; Novo";

        $this->include_js('bootstrap-colorpicker.js');
        $this->include_css('bootstrap-colorpicker.min.css');

        $data = array();

        if ($this->input->post('submit'))
        {
            $this->load->library('form_validation');

            $this->form_validation->set_rules('descricao', 'Descrição', 'trim|required');
            $this->form_validation->set_rules('cor_normal', 'Cor Habilitada', 'trim');
            $this->form_validation->set_rules('cor_desabilitado', 'Cor Desabilitado', 'trim');


            if ($this->form_validation->run() == TRUE)
            {
                $data = array(
                    'codigo_secao' => $this->input->post('codigo_secao'),
                    'descricao' => $this->input->post('descricao'),
                    'cor_normal' => $this->input->post('cor_normal'),
                    'cor_desabilitado' => $this->input->post('cor_desabilitado'),
                );

                $add = $this->ncm_capitulo_grupos_model->save($data);

                $this->message_next_render('Sucesso! Grupo <strong>'.$add.'</strong> adicionado');

                redirect('cadastros/capitulos/grupos');
            }
            else
            {
                $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
                $this->message_on_render($err, 'error');
            }
        }

        $this->breadcrumbs->push('Nova seção', '/cadastros/capitulos/grupos/novo/');

        $this->render('cadastros/capitulos/grupos/novo', $data);
    }

    public function editar($id_agrupamento)
    {
        $data = array();

        $this->title = "Grupos &gt; Editar";

        $this->include_js('bootstrap-colorpicker.js');
        $this->include_css('bootstrap-colorpicker.min.css');

        try {
            $entry = $this->ncm_capitulo_grupos_model->get_entry($id_agrupamento);
            $data['entry'] = $entry;
        } catch (Exception $e) {
            show_error($e->getMessage());
        }

        if ($this->input->post('submit'))
        {
            $this->load->library('form_validation');

            $this->form_validation->set_rules('descricao', 'Descrição', 'trim|required');
            $this->form_validation->set_rules('cor_normal', 'Cor Habilitada', 'trim');
            $this->form_validation->set_rules('cor_desabilitado', 'Cor Desabilitado', 'trim');


            if ($this->form_validation->run() == TRUE)
            {
                $data = array(
                    'codigo_secao' => $this->input->post('codigo_secao'),
                    'descricao' => $this->input->post('descricao'),
                    'cor_normal' => $this->input->post('cor_normal'),
                    'cor_desabilitado' => $this->input->post('cor_desabilitado'),
                );

                $this->ncm_capitulo_grupos_model->save($data, array('id_agrupamento' => $id_agrupamento));

                $this->message_next_render('Sucesso! Grupo <strong>'.$this->input->post('capitulo').'</strong> atualizado');

                redirect('cadastros/capitulos/grupos');
            }
            else
            {
                $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
                $this->message_on_render($err, 'error');
            }
        }

        $this->breadcrumbs->push('Editar seção', '/cadastros/capitulos/grupos/editar/'.$id_agrupamento);

        $this->render('cadastros/capitulos/grupos/editar', $data);
    }

    public function ajax_get_capitulos_por_secao()
    {
        if ($post = $this->input->post())
        {
            $this->load->model('ncm_capitulo_model');
            $data['capitulos'] = $this->ncm_capitulo_model->get_entries_by_agrupamento($post['id_agrupamento']);

            echo json_encode($data);
            return TRUE;
        }
    }

    public function relacionar_capitulos()
    {
        if ($post = $this->input->post())
        {
            $this->load->model('ncm_capitulo_model');

            $this->ncm_capitulo_model->save(array('id_agrupamento' => NULL), array('id_agrupamento' => $post['id_agrupamento']));

            $err = FALSE;
            foreach ($post['capitulos'] as $cap)
            {
                if (!$this->ncm_capitulo_model->save(array('id_agrupamento' => $post['id_agrupamento']), array('capitulo' => $cap)))
                {
                    $err = TRUE;
                }
            }

            if ($err === FALSE)
            {
                $this->message_next_render('<strong>OK!</strong> Capítulos relacionados com sucesso.', 'success');
            }else
            {
                $this->message_next_render('<strong>Oops!</strong> Não foi possível concluir o relacionamento de capítulos a seção.', 'error');
            }

            redirect('cadastros/capitulos_grupos/');
        }
    }
}
