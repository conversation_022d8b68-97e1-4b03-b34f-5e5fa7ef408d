<?php

class Cest extends MY_Controller {

	public function __construct() {
		parent::__construct();

		$this->load->model('item_cest_model');

		if (!is_logged()) {
			redirect('/login');
		}

		if (!has_role('sysadmin')) {
            show_permission();
		}

		$this->load->library('breadcrumbs');
	}

	public function index()
	{
		$this->load->library('pagination');

        $id_empresa = sess_user_company();

        $query_str = '';

        $search = $this->input->get('search');
        $page = $this->input->get('per_page');
		$limit = 15;

		$data = array();

        $busca = $this->input->get('search');
        $estabelecimento = $this->input->get('estabelecimento');
        $ncm = $this->input->get('ncm');

        $order = $this->input->get('order');
        $order_by = $this->input->get('order_by');

        $allowed_order = array('asc', 'desc');
        $allowed_order_by = array(
            'part_number', 'estabelecimento', 
            'descricao', 'valor', 'ncm_atual', 
            'status', 'codigo_cest_atual', 
            'codigo_cest_alterado'
        );

        if ( ! in_array($order, $allowed_order) ) {
            $order = 'asc';
        }

        if ( ! in_array($order_by, $allowed_order_by) ) {
            $order_by = 'part_number';
        }

        $this->item_cest_model->set_state('filter.id_empresa', sess_user_company());
        $this->item_cest_model->set_state('filter.estabelecimento', $estabelecimento);
        $this->item_cest_model->set_state('filter.ncm', $ncm);
        $this->item_cest_model->set_state('filter.search', $busca);
        $this->item_cest_model->set_state('filter.porta_a_porta', 1);

        $data['filtro'] = array(
            'busca' => $busca,
            'estabelecimento' => $estabelecimento,
            'ncm' => $ncm
        );

        $data['ordem'] = array(
            'order' => $order,
            'by' => $order_by,
            'next' => ($order == 'asc' ? 'desc' : 'asc')
        );

        $this->item_cest_model->set_state('filter.order', array('order' => $order, 'by' => $order_by));

        $data['estabelecimentos'] = $this->item_cest_model->get_all_estabelecimentos();

        $this->item_cest_model->set_state('filter.id_empresa', $id_empresa);

        $total_entries = $this->item_cest_model->get_total_entries();
		$data['list'] = $this->item_cest_model->get_entries($limit, ($page>0?$page-1:0)*$limit);

		$config['base_url'] = base_url("cadastros/cest");
        $config['total_rows'] = $total_entries;
        $config['per_page'] = $limit;
        $config['page_query_string'] = TRUE;
        $config['reuse_query_string'] = TRUE;
        $config['use_page_numbers'] = TRUE;

		$this->pagination->initialize($config);

        $data['pagination'] = $this->pagination->create_links();

        $this->load->model('empresa_model');
        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $data['multi_estabelecimentos'] = $empresa->multi_estabelecimentos;

		$this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('CEST', '/cest');
        $this->breadcrumbs->push('Gerenciar', '/cadastros/cest');

        $this->include_js('bootstrap-select/bootstrap-select.js');
        $this->include_css('bootstrap-select/bootstrap-select.css');

		$this->render('cadastros/cest/default', $data);
	}

	public function excluir()
	{
		$id_list = $this->input->post('id_list');

        $return = array();
        $return['status'] = FALSE;

        $err = array();

        foreach ($id_list as $item)
        {
            if (!$this->item_cest_model->remove($item['part_number'], sess_user_company(), $item['estabelecimento']))
            {
                $err[] = $item['part_number'];
            }
        }

        if (empty($err)) {
            $return['status'] = TRUE;

            $this->message_next_render('<strong>OK!</strong> Os itens selecionados foram excluídos com sucesso.', 'success');
        }else
        {
            $return['status'] = FALSE;

            $this->message_next_render('<strong>Oops!</strong> Ocorreu um erro ao tentar excluir os itens selecionados.', 'error');
        }

        echo json_encode($return);
	}

	public function novo()
    {
		$data = array();

        $id_empresa = sess_user_company();

        if ($this->input->post())
        {
            $this->load->library('form_validation');

            $this->form_validation->set_rules('part_number', 'Part number', 'trim|required');
            $this->form_validation->set_rules('estabelecimento', 'Estabelecimento', 'trim|required');
            $this->form_validation->set_rules('descricao', 'Descrição', 'trim');
            $this->form_validation->set_rules('valor', 'Valor', 'trim');
            $this->form_validation->set_rules('ncm_atual', 'NCM Atual', 'trim|required|is_numeric');
            $this->form_validation->set_rules('codigo_cest_atual', 'Código CEST Atual', 'trim|is_numeric');

            if ($this->form_validation->run())
            {
                $part_number = $this->input->post('part_number');
                $estabelecimento = $this->input->post('estabelecimento');

                if ( ! $this->item_cest_model->check_item_exists($part_number, $id_empresa, $estabelecimento)) 
                {
                    $valor = null;

                    if ($_valor = $this->input->post('valor')) {
                        $valor_num = str_replace(array('.', ','), array('', '.'), $_valor);
                        $valor = number_format($valor_num, 2, '.', '');
                    }

                    $dbdata = array(
                        'part_number'          => $part_number,
                        'estabelecimento'      => $estabelecimento,
                        'id_empresa'           => $id_empresa,
                        'descricao'            => $this->input->post('descricao'),
                        'valor'                => $valor,
                        'ncm_atual'            => $this->input->post('ncm_atual'),
                        'status'               => 0,
                        'codigo_cest_atual'    => $this->input->post('codigo_cest_atual'),
                        'codigo_cest_alterado' => NULL
                    );

                    if (!$this->item_cest_model->save($dbdata))
                    {
                        $this->message_on_render('<strong>Oops!</strong> Ocorreu um erro ao tentar editar o item selecionado.', 'error');
                    } else {
                        $this->load->model('empresa_model');
                        $empresa = $this->empresa_model->get_entry($id_empresa);

                        if ($empresa->multi_estabelecimentos == 1)
                        {
                            $this->message_next_render('<strong>OK!</strong> O item ['.$part_number.']['.$estabelecimento.'] foi adicionado com sucesso.');
                        } else {
                            $this->message_next_render('<strong>OK!</strong> O item ['.$part_number.'] foi adicionado com sucesso.');
                        }
                    }

                    redirect('cadastros/cest');
                } else {
                    $this->message_on_render('<strong>Oops!</strong> Já existe um item com o part number [<strong>'.$part_number.'</strong>] para o mesmo estabelecimento.', 'error');
                }

            }else
            {
                $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
                $this->message_on_render($err, 'error');
            }
        }

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('CEST', '/cest');
        $this->breadcrumbs->push('Gerenciar', '/cadastros/cest/');
        $this->breadcrumbs->push('Novo item', '/cadastros/cest/novo');

        $this->include_js('jquery.maskmoney.min.js');

        $this->render('cadastros/cest/novo', $data);
	}

	public function editar($part_number = NULL, $estabelecimento = NULL)
    {
        $data = array();

        $id_empresa = sess_user_company();

        try {
            $entry = $this->item_cest_model->get_entry($part_number, $id_empresa, $estabelecimento);
        }catch(Exception $e)
        {
            $this->message_next_render('<strong>Oops!</strong> O item selecionado para editar não existe.', 'error');
            redirect('cadastros/cest');
        }

        if ($this->input->post())
        {
            $this->load->library('form_validation');

            $this->form_validation->set_rules('part_number', 'Part number', 'trim|required');
            $this->form_validation->set_rules('estabelecimento', 'Estabelecimento', 'trim|required');
            $this->form_validation->set_rules('descricao', 'Descrição', 'trim');
            $this->form_validation->set_rules('ncm_atual', 'NCM Atual', 'trim|required|is_numeric');
            $this->form_validation->set_rules('codigo_cest_atual', 'Código CEST Atual', 'trim|is_numeric');

            if ($this->form_validation->run())
            {
                $part_number_post = $this->input->post('part_number');
                $estabelecimento_post = $this->input->post('estabelecimento');

                if (
                    ($estabelecimento !== $estabelecimento_post ||
                    $part_number !== $part_number_post) &&
                    $this->item_cest_model->check_item_exists($part_number_post, $id_empresa, $estabelecimento_post)
                ) 
                {
                    $this->message_on_render('<strong>Oops!</strong> Já existe um item com o part number [<strong>'.$part_number_post.'</strong>] para o mesmo estabelecimento.', 'error');
                } else 
                {
                    $log_item_pendente = FALSE;

                    $codigo_cest_alterado = $entry->codigo_cest_alterado;
                    $status = $entry->status;

                    if ($entry->status == 1 && ($entry->ncm_atual !== $this->input->post('ncm_atual')))
                    {
                        $log_item_pendente = TRUE;

                        $codigo_cest_alterado = null;
                        $status = 0;
                    }

                    $valor = null;
                    if ($_valor = $this->input->post('valor')) {
                        $valor_num = str_replace(array('.', ','), array('', '.'), $_valor);
                        $valor = number_format($valor_num, 2, '.', '');
                    }

                    $dbdata = array(
                        'part_number'          => $part_number,
                        'estabelecimento'      => $estabelecimento_post,
                        'id_empresa'           => $id_empresa,
                        'descricao'            => $this->input->post('descricao'),
                        'valor'                => $valor,
                        'ncm_atual'            => $this->input->post('ncm_atual'),
                        'status'               => $status,
                        'codigo_cest_atual'    => $this->input->post('codigo_cest_atual'),
                        'codigo_cest_alterado' => $codigo_cest_alterado
                    );

                    $arr_label = array(
                        'estabelecimento' => 'Estabelecimento',
                        'ncm_atual' => 'NCM Atual',
                        'codigo_cest_atual' => 'Código CEST Atual',
                        'descricao' => 'Descrição'
                    );
    
                    $motivo_html = NULL;

                    foreach ($this->input->post() as $key => $val)
                    {
                        if (isset($entry->{$key}))
                        {
                            if ($val != $entry->{$key})
                            {
                                $motivo_html .= '<strong>'.$arr_label[$key].' de: </strong>'.$entry->{$key}.' <strong> para: </strong>'.$val.'<br>';
                            }
                        }
                    }

                    if ($log_item_pendente == TRUE) {
                        $motivo_html .= "<strong>Obs.:</strong> Atribuição de CEST desfeita devido a alteração no NCM do item.";
                    }

                    $motivo = ($motivo_html) ? "Atualização dos dados do item<br />{$motivo_html}" : FALSE;

                    if (!$this->item_cest_model->update_item($part_number, $id_empresa, $estabelecimento, $dbdata, $motivo))
                    {
                        $this->message_on_render('<strong>Oops!</strong> Ocorreu um erro ao tentar editar o item selecionado.', 'error');
                    } else
                    {
                        $this->load->model('empresa_model');
                        $empresa = $this->empresa_model->get_entry($id_empresa);

                        if ($empresa->multi_estabelecimentos == 1)
                        {
                            $this->message_next_render('<strong>OK!</strong> O item ['.$part_number.']['.$estabelecimento.'] foi editado com sucesso.');
                        } else
                        {
                            $this->message_next_render('<strong>OK!</strong> O item ['.$part_number.'] foi editado com sucesso.');
                        }

                        redirect('cadastros/cest');
                    }
                }
            } else
            {
                $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
                $this->message_on_render($err, 'error');
            }
        }

        $data['entry'] = $entry;

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('CEST', '/cest');
        $this->breadcrumbs->push('Gerenciar', '/cadastros/cest/');
        $this->breadcrumbs->push('Editar item', '/cadastros/cest/editar');

        $this->include_js('jquery.maskmoney.min.js');

		$this->render('cadastros/cest/editar', $data);
	}
}
