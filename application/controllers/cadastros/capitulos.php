<?php

class Capitulos extends MY_Controller {

	public function __construct() {
		parent::__construct();

		if (!is_logged()) {
			redirect('/login');
		}

		if (!has_role('gerenciar_capitulos')) {
			show_permission();
		}

        $this->load->library('breadcrumbs');

        $this->load->model('ncm_capitulo_model');

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Capítulos NCM', '/cadastros/capitulos/');
	}

	public function index()
	{
		$data = array();

        $this->ncm_capitulo_model->set_state_store_session(TRUE);
        $this->ncm_capitulo_model->restore_state_from_session();

        $query_str = '';

		$this->load->library('pagination');

		$page = $this->input->get('per_page');
		$limit = 50;

        $total_entries = $this->ncm_capitulo_model->get_total_entries();
		$data['list'] = $this->ncm_capitulo_model->get_entries($limit, ($page>0?$page-1:0)*$limit);

		$config['base_url'] = base_url("cadastros/capitulos?{$query_str}");
		$config['use_page_numbers'] = TRUE;
		$config['total_rows'] = $total_entries;
		$config['per_page'] = $limit;
		$config['page_query_string'] = TRUE;

		$this->pagination->initialize($config);

        $data['pagination'] = $this->pagination->create_links();

		$this->title = "Capítulos";

		$this->render('cadastros/capitulos/default', $data);
	}

	public function excluir()
    {
        //Função retirada a pedidos da Becomex
        show_404();


		$id_list = $this->input->post('id_list');
		$return = array();
		$return['status'] = FALSE;

		if ($this->ncm_capitulo_model->remove($id_list)) {
			$return['status'] = TRUE;
			$this->message_next_render('Exclusão realizada com sucesso!');
		}

		echo json_encode($return);
	}

	public function novo()
    {
        //Função retirada a pedidos da Becomex
        show_404();

		$this->title = "Capítulos NCM &gt; Novo";

		$data = array();
		$data['grupos'] = $this->ncm_capitulo_model->get_lista_grupos();

		if ($this->input->post('submit'))
		{
			$this->load->library('form_validation');

			$this->form_validation->set_rules('id_agrupamento', 'Agrupamento', 'trim|required');
            $this->form_validation->set_rules('resumo_capitulo', 'Resumo Capítulo', 'trim|required');
            $this->form_validation->set_rules('descricao', 'Descrição', 'trim|required');
			$this->form_validation->set_rules('capitulo', 'Capitulo', 'trim|required|is_unique[ncm_capitulo.capitulo]');


			if ($this->form_validation->run() == TRUE)
			{
				$data = array(
					'capitulo' => $this->input->post('capitulo'),
					'resumo_capitulo' => $this->input->post('resumo_capitulo'),
					'descricao' => $this->input->post('descricao'),
					'id_agrupamento'  => $this->input->post('id_agrupamento'),
				);

				$capitulo = $this->ncm_capitulo_model->save($data);

				$this->message_next_render('Sucesso! Capítulo <strong>'.$capitulo.'</strong> adicionado');

				redirect('cadastros/capitulos');
			}
			else
			{
				$err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
				$this->message_on_render($err, 'error');
			}
		}

        $this->breadcrumbs->push('Novo Capítulo', '/cadastros/capitulos/novo/');

		$this->render('cadastros/capitulos/novo', $data);
	}

	public function editar($capitulo)
    {
		$data = array();

        $this->title = "Capítulos NCM &gt; Editar";

        try {
            $entry = $this->ncm_capitulo_model->get_entry($capitulo);
            $data['entry'] = $entry;
        } catch (Exception $e) {
            show_error($e->getMessage());
        }

        $data['grupos'] = $this->ncm_capitulo_model->get_lista_grupos();

		if ($this->input->post('submit'))
		{
			$this->load->library('form_validation');

            $unique = '';

            if ($entry->capitulo != $this->input->post('capitulo')) {
                $unique = "|is_unique[ncm_capitulo.capitulo]";
            }

            $this->form_validation->set_rules('id_agrupamento', 'Agrupamento', 'trim|required');
            $this->form_validation->set_rules('resumo_capitulo', 'Resumo Capítulo', 'trim|required');
            $this->form_validation->set_rules('descricao', 'Descrição', 'trim|required');
            $this->form_validation->set_rules('capitulo', 'Capitulo', 'trim|required'.$unique);


			if ($this->form_validation->run() == TRUE)
			{
				$data = array(
                    'resumo_capitulo' => $this->input->post('resumo_capitulo'),
                    'descricao' => $this->input->post('descricao'),
                    'id_agrupamento'  => $this->input->post('id_agrupamento'),
                );

				$this->ncm_capitulo_model->save($data, array('capitulo' => $capitulo));

				$this->message_next_render('Sucesso! Capítulo <strong>'.$this->input->post('capitulo').'</strong> atualizado');

				redirect('cadastros/capitulos');
			}
			else
			{
				$err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
				$this->message_on_render($err, 'error');
			}
		}

        $this->breadcrumbs->push('Editar capítulo', '/cadastros/capitulos/editar/'.$capitulo);

		$this->render('cadastros/capitulos/editar', $data);
	}
}
