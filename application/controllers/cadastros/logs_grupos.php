<?php

class Logs_grupos extends MY_Controller {

	public function __construct() {
		parent::__construct();

		$this->load->model('cad_item_model');
		$this->load->model('item_log_model');

		if (!is_logged()) {
			redirect('/login');
		}

        if (!has_role('gerenciar_grupos_logs'))
        {
            show_permission();
        }
	}

	public function index()
	{
		$this->load->library('pagination');

		$page = $this->input->get('per_page');
		$limit = 15;

        $this->load->model('grupo_tarifario_log_model');

		if ($this->input->is_set('busca'))
		{
            $busca_str = $this->input->get('busca');
			$this->grupo_tarifario_log_model->set_state('filter.busca', $busca_str);
        } else {
            $this->grupo_tarifario_log_model->unset_state('filter.busca');
        }

        if ($this->input->is_set('reset_filter'))
        {
            $this->grupo_tarifario_log_model->clear_states();
        }

		$data = array();
        $total_entries = $this->grupo_tarifario_log_model->get_total_entries();
		$data['list'] = $this->grupo_tarifario_log_model->get_all_entries($limit, ($page>0?$page-1:0)*$limit);

		$config['base_url'] = base_url("cadastros/logs_grupos");
		$config['use_page_numbers'] = TRUE;
		$config['total_rows'] = $total_entries;
		$config['per_page'] = $limit;
		$config['page_query_string'] = TRUE;
        $config['reuse_query_string'] = TRUE;

		$this->pagination->initialize($config);

        $data['pagination'] = $this->pagination->create_links();

		$this->title = "Logs de Grupos Tarifários";

		$this->render('cadastros/logs_grupos/default', $data);
	}

	public function detalhe()
	{
        if ($get = $this->input->get())
        {
            $id_grupo_tarifario = $get['id_grupo_tarifario'];

            $this->load->model('grupo_tarifario_log_model');
            $this->load->model('grupo_tarifario_model');

            $data = array();

            try {
                $entries = $this->grupo_tarifario_log_model->get_entries($id_grupo_tarifario);
                $entry = $this->grupo_tarifario_model->get_entry($id_grupo_tarifario);

                $data['entries'] = $entries;

                $data['descricao_grupo'] = $entry->descricao;
            } catch (Exception $e) {
                show_error($e->getMessage());
            }

            $this->render('cadastros/logs_grupos/detalhe', $data);
        }
	}

}



