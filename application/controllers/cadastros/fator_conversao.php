<?php

class Fator_conversao extends MY_Controller {

	public function __construct() {
		parent::__construct();

		$this->load->model('fator_conversao_model');

		if (!is_logged()) {
			redirect('/login');
		}

		if (!has_role('gerenciar_auditoria_fator'))
        {
			show_permission();
        }

		if (has_role('becomex_pmo') && !has_role('sysadmin'))
        {
			show_permission();
        }

        $this->load->library('breadcrumbs');
        $this->load->helper('formatador_helper');
	}

	public function index()
	{
        $query_str = '';

		$this->load->library('pagination');

		$page = $this->input->get('per_page');
		$limit = 15;


		$data = array();
        $total_entries = $this->fator_conversao_model->get_total_entries();
		$data['list'] = $this->fator_conversao_model->get_entries($limit, ($page>0?$page-1:0)*$limit);

		$config['base_url'] = base_url("cadastros/fator_conversao?{$query_str}");
		$config['use_page_numbers'] = TRUE;
		$config['total_rows'] = $total_entries;
		$config['per_page'] = $limit;
		$config['page_query_string'] = TRUE;

		$this->pagination->initialize($config);

        $data['pagination'] = $this->pagination->create_links();

		$this->title = "Fator de Conversão";

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Fator de Conversão', '/cadastros/fator_conversao/');

		$this->render('cadastros/fator_conversao/default', $data);
	}

	public function excluir() {

		$id_list = $this->input->post('id_list');
		$return = array();
		$return['status'] = FALSE;

		if ($this->fator_conversao_model->remove($id_list)) {
			$return['status'] = TRUE;
			$this->message_next_render('Exclusão realizada com sucesso!');
		}

		echo json_encode($return);
	}

	public function novo() {
		$this->load->model('fator_conversao_model');

		$this->title = "Fator de Conversão &gt; Novo";

		$data = array();

		if ($this->input->post('submit'))
		{
			$this->load->library('form_validation');

			$this->form_validation->set_rules('cnpj_fornecedor', 'CNPJ Fornecedor', 'trim|required');
			$this->form_validation->set_rules('um_nf', 'Unidade Medida da Nota Fiscal', 'trim|required');
			$this->form_validation->set_rules('um_tab_pre', 'Unidade Medida Tabela de Preços', 'trim|required');
            $this->form_validation->set_rules('fator_conversao', 'Fator de conversão', 'trim|required');

			if ($this->form_validation->run() == TRUE)
			{
				$data = array(
                    'id_empresa' => sess_user_company(),
					'cnpj_fornecedor' => $this->input->post('cnpj_fornecedor'),
					'um_nf' => $this->input->post('um_nf'),
					'um_tab_pre' => $this->input->post('um_tab_pre'),
                    'fator_conversao' => $this->input->post('fator_conversao'),
                    'atualizado_em' => date('Y-m-d H:i:s')
				);

				$this->fator_conversao_model->save($data);

				$this->message_next_render('Sucesso! Novo registro para o CNPJ [<strong>'.$this->input->post('cnpj_fornecedor').'</strong>] adicionado');

				redirect('cadastros/fator_conversao');
			} else
            {
				$err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
				$this->message_on_render($err, 'error');
			}
		}

        $this->include_js('jquery-fileupload.js');
        $this->include_css('jquery-fileupload.css');

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Fator de Conversão', '/cadastros/fator_conversao/');
        $this->breadcrumbs->push('Novo fator', '/cadastros/fator_conversao/novo/');

		$this->render('cadastros/fator_conversao/novo', $data);
	}

	public function editar($id) {
		$this->load->model('fator_conversao_model');

		$this->title = "Fator de Conversão &gt; Editar";

		$data = array();

        try {
            $entry = $this->fator_conversao_model->get_entry($id);
            $data['entry'] = $entry;
        } catch (Exception $e) {
            show_error($e->getMessage());
        }

		if ($this->input->post('submit'))
		{
			$this->load->library('form_validation');

			$this->form_validation->set_rules('cnpj_fornecedor', 'CNPJ Fornecedor', 'trim|required');
			$this->form_validation->set_rules('um_nf', 'Unidade Medida da Nota Fiscal', 'trim|required');
			$this->form_validation->set_rules('um_tab_pre', 'Unidade Medida Tabela de Preços', 'trim|required');
            $this->form_validation->set_rules('fator_conversao', 'Fator de conversão', 'trim|required');

			if ($this->form_validation->run() == TRUE)
			{

				$data = array(
					'cnpj_fornecedor' => $this->input->post('cnpj_fornecedor'),
					'um_nf' => $this->input->post('um_nf'),
					'um_tab_pre' => $this->input->post('um_tab_pre'),
                    'fator_conversao' => $this->input->post('fator_conversao'),
                    'atualizado_em' => date('Y-m-d H:i:s'),
				);

				$this->fator_conversao_model->save($data, array('id_fator' => $entry->id_fator, 'id_empresa' => sess_user_company()));

				$this->message_next_render('Sucesso! Fator de conversão [<strong>'.$id.'</strong>] atualizado');

				redirect('cadastros/fator_conversao');
			} else
			{
				$err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
				$this->message_on_render($err, 'error');
			}
		}

        $this->include_js('jquery-fileupload.js');
        $this->include_css('jquery-fileupload.css');

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Fator de Conversão', '/cadastros/fator_conversao/');
        $this->breadcrumbs->push('Editar fator', '/cadastros/fator_conversao/editar/'.$id);

		$this->render('cadastros/fator_conversao/editar', $data);
	}

}



