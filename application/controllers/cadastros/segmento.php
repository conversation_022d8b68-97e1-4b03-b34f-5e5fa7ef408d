<?php

class Segmento extends MY_Controller {

	public function __construct() {
		parent::__construct();

		$this->load->model('segmento_model');

		if (!is_logged()) {
			redirect('/login');
		}

		if (!has_role('gerenciar_segmentos')) {
			show_permission();
		}

		$this->load->library('breadcrumbs');
	}

	public function index()
	{
        $query_str = '';

		$this->load->library('pagination');

		$page = $this->input->get('per_page');
		$limit = 15;


		$data = array();
        $total_entries = $this->segmento_model->get_total_entries();
		$data['list'] = $this->segmento_model->get_entries($limit, ($page>0?$page-1:0)*$limit);

		$config['base_url'] = base_url("cadastros/segmento?{$query_str}");
		$config['use_page_numbers'] = TRUE;
		$config['total_rows'] = $total_entries;
		$config['per_page'] = $limit;
		$config['page_query_string'] = TRUE;

		$this->pagination->initialize($config);

        $data['pagination'] = $this->pagination->create_links();

		$this->title = "Segmentos";

		$this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Segmentos', '/cadastros/segmento/');

		$this->render('cadastros/segmento/default', $data);
	}

	public function excluir() {

		$id_list = $this->input->post('id_list');
		$return = array();
		$return['status'] = FALSE;

		if ($this->segmento_model->remove($id_list)) {
			$return['status'] = TRUE;
			$this->message_next_render('Exclusão realizada com sucesso!');
		}

		echo json_encode($return);
	}

	public function novo()
    {
		$this->title = "Segmento &gt; Novo";

		$data = array();

		if ($this->input->post('submit'))
		{
			$this->load->library('form_validation');

			$this->form_validation->set_rules('descricao', 'Descrição do segmento', 'trim|required|is_unique[segmento.descricao]');


			if ($this->form_validation->run() == TRUE)
			{
				$data = array(
					'descricao' => $this->input->post('descricao'),
				);

				$this->segmento_model->save($data);

				$this->message_next_render('Sucesso! Segmento [<strong>'.$this->input->post('descricao').'</strong>] adicionado');

				redirect('cadastros/segmento');
			}
			else
			{
				$err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
				$this->message_on_render($err, 'error');
			}
		}

        $this->include_js('jquery-fileupload.js');
        $this->include_css('jquery-fileupload.css');

		$this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Segmentos', '/cadastros/segmento/');
        $this->breadcrumbs->push('Novo segmento', '/cadastros/segmento/novo/');

		$this->render('cadastros/segmento/novo', $data);
	}

	public function editar($id_segmento)
    {
		$this->title = "Segmento &gt; Editar";

		$data = array();

        try {
            $entry = $this->segmento_model->get_entry($id_segmento);
            $data['entry'] = $entry;
        } catch (Exception $e) {
            show_error($e->getMessage());
        }

		if ($this->input->post('submit'))
		{
			$this->load->library('form_validation');

			$this->form_validation->set_rules('descricao', 'Descrição do segmento', 'trim|required' . (($entry->descricao != $this->input->post('descricao') ? '|is_unique[segmento.descricao]' : '' )));

            $valid_form = $this->form_validation->run();

			if ($valid_form == TRUE)
			{
				$data = array(
					'descricao' => $this->input->post('descricao'),
				);

				$this->segmento_model->save($data, array('id_segmento' => $entry->id_segmento));

				$this->message_next_render('Sucesso! Segmento [<strong>'.$this->input->post('descricao').'</strong>] atualizado');

				redirect('cadastros/segmento');

			} else
			{
				$err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
				$this->message_on_render($err, 'error');
			}
		}

		$this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Segmentos', '/cadastros/segmento/');
        $this->breadcrumbs->push('Editar segmento', '/cadastros/segmento/editar/'.$id_segmento);

		$this->render('cadastros/segmento/editar', $data);
	}

}



