<?php

class Prioridades extends MY_Controller {

	public function __construct()
    {
        parent::__construct();

        $this->load->model('empresa_model');
        $this->load->model('empresa_prioridades_model');
		
        if (!is_logged()) {
            redirect('/login');
        }

        if (!has_role('gerenciar_empresas') && !has_role('consultor')) {
            show_permission();
        }

        $this->load->library('breadcrumbs');
    }

    public function index()
    {
		$id_empresa = sess_user_company();
        $this->load->model('segmento_model');
        $query_str = '';
        if (!empty($this->input->get())) {
            $query_str = http_build_query($this->input->get());
        }
        $this->load->library('pagination');

        $page = $this->input->get('per_page');
        $limit = 15;

        if ($this->input->is_set('reset_filter')) {
            $this->empresa_prioridades_model->clear_states();
        } else {
            if ($this->input->is_set('prioridade') && $this->input->get('prioridade') != '') {
                $this->empresa_prioridades_model->set_state('filter.prioridade', $this->input->get('prioridade'));
            } else {
                $this->empresa_prioridades_model->unset_state('filter.prioridade');
            }
        }

        $data = array();
        $total_entries = $this->empresa_prioridades_model->get_total_entries($id_empresa);
        $data['list'] = $this->empresa_prioridades_model->get_entries($id_empresa,$limit, ($page > 0 ? $page - 1 : 0) * $limit);
 
        $config['base_url'] = base_url("cadastros/prioridades?{$query_str}");
        $config['use_page_numbers'] = TRUE;
        $config['total_rows'] = $total_entries;
        $config['per_page'] = $limit;
        $config['page_query_string'] = TRUE;

        $this->pagination->initialize($config);

        $data['pagination'] = $this->pagination->create_links();

        $this->title = "Prioridades";

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Prioridades', '/cadastros/empresa/');

        $this->render('cadastros/prioridades/default', $data);
    }


    public function format_decimal_for_db($value_from_post) {
        if ($value_from_post === null || $value_from_post === '') {
            return '0.00'; 
        }
        $value_with_dot = str_replace(',', '.', $value_from_post);
        $cleaned_value = preg_replace('/[^\d\.]/', '', $value_with_dot);
        
        if (is_numeric($cleaned_value)) {
            return (float) $cleaned_value; 
        }
        return null; 
    }

	public function excluir() {

		$id_list = $this->input->post('id_list');
		$return = array();
		$return['status'] = FALSE;

		if ($this->empresa_prioridades_model->remove($id_list)) {
			$return['status'] = TRUE;
			$this->message_next_render('Exclusão realizada com sucesso!');
		}

		echo json_encode($return);
	}

	public function editar($id_prioridades)
    {
		$this->title = "prioridades &gt; Editar";

		$data = array();
		$id_empresa = sess_user_company();
        try {
            $entry = $this->empresa_prioridades_model->get_entry($id_empresa, null, $id_prioridades);
            $data['entry'] = reset($entry);
        } catch (Exception $e) {
            show_error($e->getMessage());
        }

		$this->load->model('mascara_model');

 
		if ($this->input->post('submit'))
		{
 
			$this->load->library('form_validation');

			$this->form_validation->set_rules('nome', 'Nome', 'trim|required');
			$this->form_validation->set_rules('ordem', 'Ordem', 'trim|required');
			$this->form_validation->set_rules('qdt_horas_uteis', 'Horas', 'trim|required');


			if ($this->form_validation->run() == TRUE)
			{
				$valor_padrao_normal = $this->input->post('valor_padrao');
				$valor_quimico_normal = $this->input->post('valor_quimico');

				$data = array(
					'id_empresa' => $id_empresa,
					'nome' => $this->input->post('nome'),
					'ordem' => $this->input->post('ordem'),
					'qdt_horas_uteis' => $this->input->post('qdt_horas_uteis'),
					'valor_padrao' =>  $this->format_decimal_for_db($valor_padrao_normal),
					'valor_quimico' => $this->format_decimal_for_db($valor_quimico_normal)
				);
				$this->empresa_prioridades_model->update($id_prioridades, $data);
				$this->message_next_render('Sucesso! Prioridade [<strong>'.$this->input->post('nome').'</strong>] atualizado');

				redirect('cadastros/prioridades');
			}
			else
			{
				$err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
				$this->message_on_render($err, 'error');
			}
		}

		$this->include_js(array(
			'jquery-fileupload.js',
			'jquery.mask.min.js'
		));
		
		$this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Prioridade', '/cadastros/prioridades/');
        $this->breadcrumbs->push('Editar prioridade', '/cadastros/prioridades/editar/'.$id_prioridades);

		$this->render('cadastros/prioridades/editar', $data);
	}


	public function novo() {

		$this->title = "Prioridade &gt; Novo";
		$id_empresa = sess_user_company();
		$data = array();

		if ($this->input->post('submit'))
		{
			$this->load->library('form_validation');

			$this->form_validation->set_rules('nome', 'Nome', 'trim|required');
			$this->form_validation->set_rules('ordem', 'Ordem', 'trim|required');
			$this->form_validation->set_rules('qdt_horas_uteis', 'Horas', 'trim|required');

			if ($this->form_validation->run() == TRUE)
			{
				$valor_padrao_normal = $this->input->post('valor_padrao');
				$valor_quimico_normal = $this->input->post('valor_quimico');

				$data = array(
					'id_empresa' => $id_empresa,
					'nome' => $this->input->post('nome'),
					'ordem' => $this->input->post('ordem'),
					'qdt_horas_uteis' => $this->input->post('qdt_horas_uteis'),
					'valor_padrao' =>  $this->format_decimal_for_db($valor_padrao_normal),
					'valor_quimico' => $this->format_decimal_for_db($valor_quimico_normal)
				);

				$this->empresa_prioridades_model->save($data);

				$this->message_next_render('Sucesso! Prioridade [<strong>'.$this->input->post('nome').'</strong>] adicionado');

				redirect('cadastros/prioridades');
			}
			else
			{
				$err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
				$this->message_on_render($err, 'error');
			}
		}

		$this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Prioridade', '/cadastros/prioridades/');
        $this->breadcrumbs->push('Nova prioridade', '/cadastros/prioridades/novo/');

		$this->render('cadastros/prioridades/novo', $data);
	}

}



