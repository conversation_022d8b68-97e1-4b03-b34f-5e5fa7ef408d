<?php

class Logs extends MY_Controller {

	public function __construct() {
		parent::__construct();

		$this->load->model('cad_item_model');
		$this->load->model('item_log_model');

		if (!is_logged()) {
			redirect('/login');
        }
        
        if (!has_role('gerenciar_logs')) {
            show_permission();
		}

        $this->load->library('breadcrumbs');
	}

	public function index()
	{
        $data = array();

		$this->load->library('pagination');

		$offset = $this->input->get('per_page');
		$limit = 15;

        $this->load->model('item_log_model');

        $this->item_log_model->set_state_store_session(TRUE);
        $this->item_log_model->restore_state_from_session();

        $data['checked_search'] = 'part_numbers';
        $data['data_ini'] = NULL;
        $data['data_fim'] = NULL;
        $data['busca'] = NULL;

        if ($this->input->post())
        {
            $this->item_log_model->clear_states();

            $data['busca'] = $this->input->post('busca');

            $this->item_log_model->set_state('filter.busca', $this->input->post('busca'));
            
            $data['data_ini'] = $this->input->post('data_ini');
            $data['data_fim'] = $this->input->post('data_fim');
        } else if ($this->input->get('restore_search'))
        {
            if (!empty($this->item_log_model->get_state('filter.part_numbers')))
            {
                $data['busca'] = $this->item_log_model->get_state('filter.part_numbers');
            }else if (!empty($this->item_log_model->get_state('filter.descricao')))
            {
                $data['busca'] = $this->item_log_model->get_state('filter.descricao');
            }

            $data_ini = $this->item_log_model->get_state('filter.data_ini');
            if (!empty($data_ini))
            {
                $data_ini = date('d/m/Y', strtotime($data_ini));
            }

            $data_fim = $this->item_log_model->get_state('filter.data_fim');
            if (!empty($data_fim))
            {
                $data_fim = date('d/m/Y', strtotime($data_fim));
            }

            $data['data_ini'] = $data_ini;
            $data['data_fim'] = $data_fim;
            $data['checked_search'] = $this->item_log_model->get_state('filter.checked_search');
        }

        $id_empresa = sess_user_company();
        $this->item_log_model->set_state('filter.id_empresa', $id_empresa);

        if ($field_search = $this->input->post('field_search'))
        {
            $search = $data['busca'];

            if (!empty($search))
            {
                if ($field_search == 'part_numbers')
                {
                    $this->item_log_model->unset_state('filter.descricao');

                    $data['checked_search'] = 'part_numbers';
                    $this->item_log_model->set_state('filter.part_numbers', $search);
                }else
                {
                    $this->item_log_model->unset_state('filter.part_numbers');

                    $data['checked_search'] = 'descricao';
                    $this->item_log_model->set_state('filter.descricao', $search);
                }

                $this->item_log_model->set_state('filter.checked_search', $data['checked_search']);
            }
        }

        if (!empty($data['data_ini']))
        {
            $data_ini = str_replace('/', '-', $data['data_ini']);
            $data_ini = date('Y-m-d 00:00:00', strtotime($data_ini));

            $this->item_log_model->set_state('filter.data_ini', $data_ini);
        }

        if (!empty($data['data_fim']))
        {
            $data_fim = str_replace('/', '-', $data['data_fim']);
            $data_fim = date('Y-m-d 23:59:59', strtotime($data_fim));

            $this->item_log_model->set_state('filter.data_fim', $data_fim);
        }

        if ($this->input->post() || $this->input->get('restore_search'))
        {
            $total_entries = $this->item_log_model->get_total_entries();
            $data['list'] = $this->item_log_model->get_all_entries($limit, $offset);

            $config['base_url'] = base_url("cadastros/logs");
            $config['total_rows'] = $total_entries;
            $config['per_page'] = $limit;
            $config['page_query_string'] = TRUE;
            $config['reuse_query_string'] = TRUE;

            $this->pagination->initialize($config);
        }

        if ($this->input->is_set('reset_filter'))
        {
            $this->item_log_model->clear_states();
        }

        $this->load->model('empresa_model');
        $empresa = $this->empresa_model->get_entry($id_empresa);

        $data['multi_estabelecimentos'] = $empresa->multi_estabelecimentos;

        $data['pagination'] = $this->pagination->create_links();

		$this->title = "Logs de Aprovação";

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Logs de Aprovação', '/cadastros/logs');

        $this->include_css('b3-datetimepicker.min.css');
        $this->include_js('b3-datetimepicker.min.js');

		$this->render('cadastros/logs/default', $data);
	}

	public function detalhe()
	{
        if ($get = $this->input->get())
        {
            $part_number = $get['part_number'];

            $this->load->model(array('usuario_model', 'cad_item_model', 'item_log_model'));

            $this->title = "Logs de Aprovação > Detalhes";

            $data = array();

            $id_empresa = $get['id_empresa'];
            $estabelecimento = $get['estabelecimento'];

            $this->load->model('empresa_model');
            $empresa = $this->empresa_model->get_entry($id_empresa);

            if ($empresa->multi_estabelecimentos == 1)
            {
                $data['estabelecimento'] = $estabelecimento;
            }

            $this->item_log_model->set_state('filter.id_empresa', $id_empresa);
            $this->item_log_model->set_state('filter.estabelecimento', $estabelecimento);

            try {
                $entry = $this->item_log_model->get_entries($part_number);

                $data['entries'] = $entry;
                $data['part_number'] = $part_number;
            } catch (Exception $e) {
                show_error($e->getMessage());
            }

            $this->breadcrumbs->push('Home', '/');
            $this->breadcrumbs->push('Logs de Aprovação', '/cadastros/logs');
            $this->breadcrumbs->push('Detalhes do log', '/cadastros/logs/detalhe?part_number='.$part_number);

            $this->render('cadastros/logs/detalhe', $data);
        }
	}

}



