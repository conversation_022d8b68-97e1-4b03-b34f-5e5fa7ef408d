<?php

class Grupotarifario extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $load_models = array(
            'grupo_tarifario_model',
            'ex_tarifario_model',
            'nve_atributo_model',
            'permissao_atribuir_grupo_model',
            'empresa_model'
        );

        $this->load->model($load_models);

        if (!is_logged()) {
            redirect('/login');
        }

        $segment = $this->uri->segment(3);

        if (!has_role('gerenciar_grupos_cadastro')) {
            show_permission();
        }

        if ((!has_role('sysadmin') && !has_role('consultor')) && $segment !== false) {
            show_permission();
        }

        $this->load->library('breadcrumbs');
        $this->load->helper('formatador_helper');
    }

    public function index()
    {
        $this->load->library('pagination');

        $arr_query_str = array();

        $search = $this->input->get('search');
        if (!empty($search)) {
            $arr_query_str['search'] = $search;
        }

        $offset = $this->input->get('per_page');
        $reset_filter = $this->input->get('reset_filter');

        $limit = 20;

        if ($reset_filter) {
            $this->grupo_tarifario_model->clear_states();
        } else {
            $this->grupo_tarifario_model->set_state_store_session(TRUE);
            $this->grupo_tarifario_model->restore_state_from_session();
        }

        if ($this->input->is_set('tag')) {
            if (!empty($this->input->get('tag'))) {
                $this->grupo_tarifario_model->set_state('filter.tag', $this->input->get('tag'));
                $arr_query_str['tag'] = $this->input->get('tag');
            } else {
                $this->grupo_tarifario_model->unset_state('filter.tag');
            }
        } else {
            $this->grupo_tarifario_model->unset_state('filter.tag');
        }

        if ($this->input->is_set('tag_letra_inicial')) {
            if ($this->input->get('tag_letra_inicial')) {
                $this->grupo_tarifario_model->set_state('filter.tag_letra_inicial', $this->input->get('tag_letra_inicial'));
                $arr_query_str['tag_letra_inicial'] = $this->input->get('tag_letra_inicial');
            } else {
                $this->grupo_tarifario_model->unset_state('filter.tag_letra_inicial');
            }
        }

        if ($this->input->is_set('tag_name') && $this->input->get('tag_name') != '') {
            $this->grupo_tarifario_model->unset_state('filter.tag_letra_inicial');
            $this->grupo_tarifario_model->set_state('filter.tag_name', $this->input->get('tag_name'));
        } else {
            $this->grupo_tarifario_model->unset_state('filter.tag_name');
        }

        $data = array();

        if (!$data['tag_name'] = $this->grupo_tarifario_model->get_state('filter.tag_name')) {
            $data['tag_name'] = NULL;
        }

        if ($this->input->is_set('search_descricao') == 'on' && !empty($search)) {
            $data['search_descricao'] = TRUE;
            $this->grupo_tarifario_model->set_state('filter.descricao', $search);
            $arr_query_str['search_descricao'] = $this->input->get('search_descricao');
        } else {
            $this->grupo_tarifario_model->unset_state('filter.descricao');
        }

        if ($this->input->get('search_ncm') == 'on' && !empty($search)) {
            $data['search_ncm'] = TRUE;
            $this->grupo_tarifario_model->set_state('filter.ncm_recomendada', $search);
            $arr_query_str['search_ncm'] = $this->input->get('search_ncm');
        } else {
            $this->grupo_tarifario_model->unset_state('filter.ncm_recomendada');
        }

        if (!isset($data['search_ncm']) && !isset($data['search_descricao'])) {
            $data['search_ncm'] = TRUE;
            $data['search_descricao'] = TRUE;
        }

        $order = '';

        // if ($this->input->is_set('by')) {
        //     $order .= $this->input->get('by');
        // }

        // if ($this->input->is_set('order')) {
        //     $order .= ' ' . $this->input->get('order');
        //     if ($this->input->get('order') == 'desc') {
        //         $arr_query_str['order'] = 'asc';
        //     } else if ($this->input->get('order') == 'asc') {
        //         $arr_query_str['order'] = 'desc';
        //     }
        // } else {
        //     $arr_query_str['order'] = 'asc';
        // }

        // if (empty($order)) {
        //     $order = 'id_grupo_tarifario ASC';
        // }

        $order_by = $this->input->get('by');
        $order_direction = $this->input->get('order');

        if (!empty($order_by)) {
            $order = $order_by . ' ' . (!empty($order_direction) ? $order_direction : 'asc');
            if ($order_direction == 'desc') {
                $arr_query_str['order'] = 'asc';
            } else if ($order_direction == 'asc') {
                $arr_query_str['order'] = 'desc';
            }
        } else {
            $order = 'id_grupo_tarifario ASC';
            $arr_query_str['order'] = 'asc';
        }


        $data['order_url'] = site_url('cadastros/grupotarifario?' . http_build_query($arr_query_str));

        $total_entries = $this->grupo_tarifario_model->get_total_entries();
        $data['list'] = $this->grupo_tarifario_model->get_entries($limit, $offset, $order);

        $tags_available = array();

        // $config['base_url'] = base_url("cadastros/grupotarifario");
        $config['base_url'] = base_url("cadastros/grupotarifario") . '?' . http_build_query($arr_query_str);

        $config['total_rows'] = $total_entries;
        $config['per_page'] = $limit;
        $config['page_query_string'] = TRUE;
        $config['num_links'] = 5;

        $this->pagination->initialize($config);

        $data['pagination'] = $this->pagination->create_links();

        $this->set_back_link();

        $this->title = "Grupos Tarifários";

        $data['tags_disp'] = $tags_disp = $this->grupo_tarifario_model->get_all_tags($tags_available);
        $data['tags_letras_iniciais'] = $this->grupo_tarifario_model->get_all_tags_letras_iniciais($tags_available);

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Grupos Tarifários', '/cadastros/grupotarifario/');

        $this->render('cadastros/grupotarifario/default', $data);
    }

    public function excluir()
    {
        $id_list = $this->input->post('id_list');
        $return = array();
        $return['status'] = FALSE;

        $message_on_render = $log_error = $log_success = '';

        if (($return_array = $this->grupo_tarifario_model->remove($id_list))) {
            $return['status'] = TRUE;

            if (!empty($return_array)) {
                foreach ($return_array as $id_grupo_tarifario => $success) {
                    if ($success) {
                        $log_success .= "<li>Excluindo grupo tarifário #{$id_grupo_tarifario}</li>";
                    } else {
                        $log_error .= "<li>O Grupo tarifário #{$id_grupo_tarifario} está relacionado ao cadastro de itens.</li>";
                    }
                }

                if (!empty($log_success)) {
                    $message_on_render .= $this->message_config("<h4>Sucesso</h4><ul>{$log_success}</ul>", "success");
                }

                if (!empty($log_error)) {
                    $message_on_render .= $this->message_config("<h4>Erro</h4><ul>{$log_error}</ul>", "error");
                }

                $this->message_next_render($message_on_render, NULL, TRUE);
            }
        }

        echo json_encode($return);
    }

    public function novo()
    {
        $this->title = "Grupo Tarifário &gt; Novo";

        $this->load->model('segmento_model');

        $data = array();

        $data['duplicate'] = FALSE;
        $data['segmentos'] = $this->segmento_model->get_entries();

        if ($this->input->post('submitform')) {
            $this->load->library('form_validation');

            $this->form_validation->set_rules('descricao', 'Descrição do grupo', 'trim|required|is_unique_binary[grupo_tarifario.descricao]');
            $this->form_validation->set_rules('palavras_chaves', 'Palavras chaves', 'trim');
            $this->form_validation->set_rules('ncm_recomendada', 'Código NCM', 'trim|numeric');

            if ($this->form_validation->run() == TRUE) {
                $data = array(
                    'id_segmento' => $this->input->post('id_segmento'),
                    'descricao' => $this->input->post('descricao'),
                    'ncm_recomendada' => $this->input->post('ncm_recomendada'),
                    'subsidio' => $this->input->post('subsidio'),
                    'caracteristica' => $this->input->post('caracteristica'),
                    'memoria_classificacao' => $this->input->post('memoria_classificacao'),
                    'tipo_descricao_resumida' => (int) $this->input->post('tipo_descricao_sugerida'),
                    'descricao_resumida' => $this->input->post('descricao_resumida'),
                    'utilizacao_geral' => (int) $this->input->post('utilizacao_geral'),
                    'habilita_regras' => (int) $this->input->post('habilita_regras'),
                    'num_ex_ii' => $this->input->post('num_ex_ii'),
                    'num_ex_ipi' => $this->input->post('num_ex_ipi'),
                    'observacao' => $this->input->post('observacao'),
                    'dispositivo_legal' => $this->input->post('dispositivo_legal'),
                    'solucao_consulta' => $this->input->post('solucao_consulta'),
                    'ativo'            => (int) $this->input->post('ativo')
                );

                $log_data = array(
                    'id_usuario' => sess_user_id(),
                    'motivo' => 'Novo grupo cadastrado: ' . $this->input->post('descricao'),
                    'criado_em' => date('Y-m-d H:i:s'),
                    'slug' => 'insert'
                );

                $id_grupo_tarifario = $this->grupo_tarifario_model->save($data, NULL, $log_data);

                $num_ex_data = array();

                if ($this->input->is_set('num_ex_ii')) {
                    $num_ex_data['num_ex_ii'] = $this->input->post('num_ex_ii');
                }

                if ($this->input->is_set('num_ex_ipi')) {
                    $num_ex_data['num_ex_ipi'] = $this->input->post('num_ex_ipi');
                }

                if ($this->input->is_set('ncm_recomendada')) {
                    $this->load->model('cad_item_model');
                    $this->cad_item_model->update_ex_tarifarios_from_grupo($num_ex_data, $id_grupo_tarifario, $this->input->post('ncm_recomendada'));
                }

                $this->load->model('permissao_atribuir_grupo_model');

                $rel_empresas = $this->input->post('empresas');

                $this->permissao_atribuir_grupo_model->delete($id_grupo_tarifario);

                foreach ($rel_empresas as $empresa) {
                    $this->permissao_atribuir_grupo_model->save(array('id_grupo_tarifario' => $id_grupo_tarifario, 'id_empresa' => $empresa));
                }

                if ($this->input->post('palavras_chaves')) {
                    $palavras_chaves = explode(",", $this->input->post('palavras_chaves'));
                    $this->grupo_tarifario_model->save_tags($id_grupo_tarifario, $palavras_chaves);
                }

                $nve = $this->input->post('nve');

                if (!empty($nve)) {
                    foreach ($nve as $nve_k => $nve_v) {
                        $cd_atributo = $nve_k;
                        $cd_especif  = $nve_v;

                        $this->grupo_tarifario_model->save_nve($id_grupo_tarifario, $this->input->post('ncm_recomendada'), $cd_atributo, $cd_especif);
                    }
                }

                $this->message_next_render('Sucesso! Grupo Tarifário [<strong>' . $this->input->post('descricao') . '</strong>] adicionado');

                if ($this->input->post('commit') == 2) {
                    redirect('cadastros/grupotarifario/editar/' . $id_grupo_tarifario . '?catalogo=1');
                } else if ($this->input->post('habilita_regras') == 1) {
                    redirect('cadastros/grupotarifario/editar/' . $id_grupo_tarifario);
                } else {
                    $this->redirect_url('cadastros/grupotarifario');
                }
            } else {
                $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";

                if ($this->input->is_set('duplicate')) {
                    $this->message_next_render($err, 'error');
                    redirect('cadastros/grupotarifario/duplicar/' . $this->input->post('duplicate'));
                }

                $this->message_on_render($err, 'error');
            }
        }

        $data['tipo_descricao_resumida_arr'] = $this->grupo_tarifario_model->_tipo_descricao_sugerida;

        $this->load->model('empresa_model');
        $this->empresa_model->set_state('filter.order_by', 'razao_social asc');
        $empresas = $this->empresa_model->get_entries();

        $data['empresas'] = $empresas;

        $this->include_js(
            array(
                'ckeditor/ckeditor.js',
                'bootstrap-select/bootstrap-select.min.js',
                'bootstrap-select/i18n/defaults-pt_BR.min.js',
                'bootstrap-tagsinput.min.js',
                'sweetalert.min.js'
            )
        );
        $this->include_css(
            array(
                'bootstrap-select/bootstrap-select.min.css',
                'bootstrap-tagsinput.css',
                'sweetalert.css'
            )
        );

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Grupos Tarifários', '/cadastros/grupotarifario');
        $this->breadcrumbs->push('Novo Grupo Tarifário', '/cadastros/grupotarifario/novo');

        $this->render('cadastros/grupotarifario/novo', $data);
    }

    public function duplicar($id_grupo_tarifario)
    {
        $this->load->model('ex_tarifario_model');
        $this->load->model('segmento_model');
        $this->load->model('empresa_model');
        $this->load->model('permissao_atribuir_grupo_model');

        $data = array();

        $data['duplicate'] = TRUE;

        try {
            $entry = $this->grupo_tarifario_model->get_entry($id_grupo_tarifario);
            $data['entry'] = $entry;
        } catch (Exception $e) {
            show_error($e->getMessage());
        }

        $data['empresas'] = $this->empresa_model->get_entries();
        $data['itens_ex_ii'] = $this->ex_tarifario_model->get_all_ex_ii_by_ncm($entry->ncm_recomendada);
        $data['itens_ex_ipi'] = $this->ex_tarifario_model->get_all_ex_ipi_by_ncm($entry->ncm_recomendada);
        $data['palavras_chaves'] = $this->grupo_tarifario_model->get_tags($entry->id_grupo_tarifario);
        $data['segmentos'] = $this->segmento_model->get_entries();
        $data['tipo_descricao_resumida_arr'] = $this->grupo_tarifario_model->_tipo_descricao_sugerida;
        $data['arr_permissoes'] = $this->permissao_atribuir_grupo_model->get_permissoes_as_array($entry->id_grupo_tarifario);

        $this->include_js(
            array(
                'ckeditor/ckeditor.js',
                'bootstrap-select/bootstrap-select.min.js',
                'bootstrap-select/i18n/defaults-pt_BR.min.js',
                'bootstrap-tagsinput.min.js',
                'sweetalert.min.js'
            )
        );
        $this->include_css(
            array(
                'bootstrap-select/bootstrap-select.min.css',
                'bootstrap-tagsinput.css',
                'sweetalert.css'
            )
        );

        $this->render('cadastros/grupotarifario/novo', $data);
    }

    public function editar($id_segmento)
    {
        $this->title = "Grupo Tarifário &gt; Editar";

        $this->load->model(array(
            'segmento_model',
            'permissao_atribuir_grupo_model',
            'empresa_model',
            'grupo_tarifario_excecao_model',
            'grupo_tarifario_regra_model',
            'grupo_tarifario_attr_model'
        ));

        $this->load->library('form_validation');

        $data = array();

        $data['segmentos'] = $this->segmento_model->get_entries();

        try {
            $entry = $this->grupo_tarifario_model->get_entry($id_segmento);
            $data['entry'] = $entry;
        } catch (Exception $e) {
            show_error($e->getMessage());
        }

        if ($this->input->post('submitform')) {
            $this->form_validation->set_rules('descricao', 'Descrição do grupo', 'trim|required' . (($entry->descricao != $this->input->post('descricao') ? '|is_unique_binary[grupo_tarifario.descricao]' : '')));
            $this->form_validation->set_rules('palavras_chaves', 'Palavras chaves', 'trim');
            $this->form_validation->set_rules('ncm_recomendada', 'Código NCM', 'trim|numeric');

            $valid_form = $this->form_validation->run();

            if ($valid_form == TRUE) {
                $data = array(
                    'id_segmento' => (int) $this->input->post('id_segmento'),
                    'descricao' => $this->input->post('descricao'),
                    'ncm_recomendada' => $this->input->post('ncm_recomendada'),
                    'subsidio' => $this->input->post('subsidio'),
                    'caracteristica' => $this->input->post('caracteristica'),
                    'memoria_classificacao' => $this->input->post('memoria_classificacao'),
                    'descricao_resumida' => $this->input->post('descricao_resumida'),
                    'utilizacao_geral' => (int) $this->input->post('utilizacao_geral'),
                    'habilita_regras' => (int) $this->input->post('habilita_regras'),
                    'num_ex_ii' => $this->input->post('num_ex_ii'),
                    'num_ex_ipi' => $this->input->post('num_ex_ipi'),
                    'observacao' => $this->input->post('observacao'),
                    'dispositivo_legal' => $this->input->post('dispositivo_legal'),
                    'solucao_consulta' => $this->input->post('solucao_consulta'),
                    'ativo'            => (int) $this->input->post('ativo')
                );

                if ($tipo_descricao_resumida = (int) $this->input->post('tipo_descricao_sugerida')) {
                    $data['tipo_descricao_resumida'] = $tipo_descricao_resumida;
                }

                $this->save_log($this->input->post(), $entry->id_grupo_tarifario);
                $this->grupo_tarifario_model->save($data, array('id_grupo_tarifario' => $entry->id_grupo_tarifario));

                $rel_empresas = $this->input->post('empresas');

                $this->permissao_atribuir_grupo_model->delete($entry->id_grupo_tarifario);

                foreach ($rel_empresas as $empresa) {
                    $this->permissao_atribuir_grupo_model->save(array('id_grupo_tarifario' => $entry->id_grupo_tarifario, 'id_empresa' => $empresa));
                }

                $this->grupo_tarifario_model->save($data, array('id_grupo_tarifario' => $entry->id_grupo_tarifario));

                $msg = '';

                $num_ex_data = array();

                if ($this->input->is_set('num_ex_ii')) {
                    $num_ex_data['num_ex_ii'] = $this->input->post('num_ex_ii');
                }

                if ($this->input->is_set('num_ex_ipi')) {
                    $num_ex_data['num_ex_ipi'] = $this->input->post('num_ex_ipi');
                }

                if ($this->input->is_set('ncm_recomendada')) {
                    $this->load->model('cad_item_model');
                    $this->cad_item_model->update_ex_tarifarios_from_grupo($num_ex_data, $entry->id_grupo_tarifario, $entry->ncm_recomendada);
                }

                if ($this->input->post('palavras_chaves')) {
                    $palavras_chaves = explode(",", $this->input->post('palavras_chaves'));

                    if ($this->grupo_tarifario_model->check_has_used_tags($entry->id_grupo_tarifario, $palavras_chaves)) {
                        $msg = $this->message_config('Algumas TAGs estão sendo utilizadas e não puderam ser apagadas.', 'warning');
                    }
                    $this->grupo_tarifario_model->save_tags($entry->id_grupo_tarifario, $palavras_chaves);
                } else {
                    if ($this->grupo_tarifario_model->check_has_used_tags($entry->id_grupo_tarifario, array())) {
                        $msg = $this->message_config('Algumas TAGs estão sendo utilizadas e não puderam ser apagadas.', 'warning');
                    }

                    $this->grupo_tarifario_model->clear_tags($entry->id_grupo_tarifario);
                }

                $nve = $this->input->post('nve');

                if (!empty($nve)) {
                    foreach ($nve as $nve_k => $nve_v) {
                        $cd_atributo = $nve_k;
                        $cd_especif  = $nve_v;

                        $this->grupo_tarifario_model->save_nve($entry->id_grupo_tarifario, $this->input->post('ncm_recomendada'), $cd_atributo, $cd_especif);
                    }
                }

                if (!empty($this->input->post('raw_attr')))
                {
                    $raw_attr = $this->input->post('raw_attr'); // O conjunto de atributos enviados por post em RAW.
                    $this->grupo_tarifario_attr_model->save_attrs($raw_attr, $entry);
                }
                $msg_success = $this->message_config('Sucesso! Grupo Tarifário [<strong>' . $this->input->post('descricao') . '</strong>] atualizado');

                if (!empty($msg)) 
                {
                    $msg_success = $msg . $msg_success;
                }

                $this->message_next_render($msg_success, NULL, TRUE);

                $this->redirect_url('cadastros/grupotarifario');
            } else {
                $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
                $this->message_on_render($err, 'error');
            }
        }

        /** Dados **/
        $data['itens_ex_ii'] = $this->ex_tarifario_model->get_all_ex_ii_by_ncm($entry->ncm_recomendada, false);
        $data['itens_ex_ipi'] = $this->ex_tarifario_model->get_all_ex_ipi_by_ncm($entry->ncm_recomendada, false);

        $data['palavras_chaves'] = $this->grupo_tarifario_model->get_tags($entry->id_grupo_tarifario);
        $data['tipo_descricao_resumida_arr'] = $this->grupo_tarifario_model->_tipo_descricao_sugerida;

        $this->empresa_model->set_state('filter.order_by', 'razao_social asc');
        $empresas = $this->empresa_model->get_entries();

        $data['excecoes_empresa'] = array();

        foreach ($empresas as $empresa) {
            if ($this->grupo_tarifario_excecao_model->get_entries($entry->id_grupo_tarifario, $empresa->id_empresa) != NULL) {
                $data['excecoes_empresa'][$empresa->id_empresa] = $this->grupo_tarifario_excecao_model->get_entries($entry->id_grupo_tarifario, $empresa->id_empresa);
            }
        }

        $data['empresas'] = $empresas;
        $data['arr_permissoes'] = $this->permissao_atribuir_grupo_model->get_permissoes_as_array($entry->id_grupo_tarifario);

        /** Aba Regras **/
        $this->grupo_tarifario_regra_model->set_id_empresa(sess_user_company());

        $this->grupo_tarifario_regra_model->set_state('filter.id_grupo_tarifario', $id_segmento);
        $data['regras'] = $this->grupo_tarifario_regra_model->get_entries();

        $data['id_grupo_tarifario'] = $id_segmento;

        $this->include_js(
            array(
                'ckeditor/ckeditor.js',
                'bootstrap-select/bootstrap-select.min.js',
                'bootstrap-select/i18n/defaults-pt_BR.min.js',
                'bootstrap-tagsinput.min.js',
                'sweetalert.min.js',
                'summernote/summernote.js',
                'summernote/lang/summernote-pt-BR.min.js'
            )
        );

        $this->include_css(
            array(
                'bootstrap-select/bootstrap-select.min.css',
                'bootstrap-tagsinput.css',
                'sweetalert.css',
                'summernote/summernote.css'
            )
        );

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Grupos Tarifários', '/cadastros/grupotarifario');
        $this->breadcrumbs->push('Editar Grupo Tarifário', '/cadastros/grupotarifario/editar/' . $id_segmento);

        $this->render('cadastros/grupotarifario/editar', $data);
    }

    public function save_log($post, $id_grupo_tarifario)
    {
        $this->load->model('grupo_tarifario_model');
        $this->load->model('grupo_tarifario_log_model');

        $entry = $this->grupo_tarifario_model->get_entry_as_array($id_grupo_tarifario);
        $entry = $entry[0];

        $log_data = array(
            'id_grupo_tarifario' => $id_grupo_tarifario,
            'id_usuario' => sess_user_id(),
            'motivo' => '',
            'criado_em' => date('Y-m-d H:i:s'),
            'slug' => 'update'
        );

        $labels = array(
            'descricao' => 'Descrição',
            'ncm_recomendada' => 'NCM Recomendada',
            'observacao' => 'Observação',
            'dispositivo_legal' => 'Dispositivo legal',
            'num_ex_tarifario' => 'EX Tarifários',
            'id_segmento' => 'Segmento',
            'tipo_descricao_resumida' => 'Tipo de descrição resumida',
            'descricao_resumida' => 'Descrição resumida',
            'caracteristica' => 'Características',
            'subsidio' => 'Subsídio',
            'memoria_classificacao' => 'Memória de Classificação',
            'solucao_consulta' => 'Solução de consulta',
            'utilizacao_geral' => 'Utilização geral',
            'habilita_regras'    => 'Regras',
            'ativo'            => 'Ativo'
        );

        foreach ($post as $key => $val) {
            if (isset($entry[$key])) {
                if (strip_tags($entry[$key]) != strip_tags($val)) {

                    if ($entry[$key] == 0 && $val == '') {
                        continue;
                    }

                    switch ($key) {
                        case 'id_segmento':

                            $this->load->model('segmento_model');
                            $segmentos = $this->segmento_model->get_entries();

                            $new_val = 'Não informado';
                            $old_val = 'Não informado';

                            foreach ($segmentos as $segmento) {
                                if ($segmento->id_segmento == $val) {
                                    $new_val = $segmento->descricao;
                                }

                                if ($segmento->id_segmento == $entry[$key]) {
                                    $old_val = $segmento->descricao;
                                }
                            }

                            break;

                        case 'utilizacao_geral':
                        case 'ativo':
                        case 'habilita_regras':
                            $new_val = $val == 1 ? 'Sim' : 'Não';
                            $old_val = $entry[$key] == 1 ? 'Sim' : 'Não';
                            break;

                        default:
                            $old_val = empty($entry[$key]) ? 'Não informado' : $entry[$key];
                            $new_val = trim(strip_tags($val));
                            $new_val = empty($new_val) ? 'Não informado' : $new_val;
                            break;
                    }

                    $log_data['motivo'] .= '<strong>' . $labels[$key] . ' de: </strong>' . trim(strip_tags($old_val));
                    $log_data['motivo'] .= ' -> <strong>Para: </strong>' . $new_val . ' <br>';
                }
            }
        }

        if (isset($post['empresas'])) {
            $this->load->model('permissao_atribuir_grupo_model');
            $permissoes = $this->permissao_atribuir_grupo_model->get_permissoes_as_array($id_grupo_tarifario);

            $diff_n_empresas = array_diff($post['empresas'], $permissoes);

            $empresas = array();
            if (!empty($post['empresas'])) {
                $empresas = $post['empresas'];
            }

            $diff_o_empresas = array_diff($permissoes, $empresas);

            if (!empty($diff_n_empresas) || !empty($diff_o_empresas)) {
                $log_data['motivo'] .= 'Alteração de permissões de empresa em atribuir grupos <br>';
            }
        }

        if (isset($post['palavras_chaves'])) {
            $tags = $this->grupo_tarifario_model->get_tags($id_grupo_tarifario);

            $new_tags = array();
            if (!empty($post['palavras_chaves'])) {
                $new_tags = explode(',', $post['palavras_chaves']);
            }

            $diff_n_tags = array_diff($new_tags, $tags);
            $diff_o_tags = array_diff($tags, $new_tags);

            if (!empty($diff_n_tags)) {
                $log_data['motivo'] .= 'Palavras chave (TAGs) adicionadas ao grupo: <strong>' . implode(', ', $diff_n_tags) . '</strong><br>';
            }

            if (!empty($diff_o_tags)) {
                $log_data['motivo'] .= 'Palavras chave (TAGs) removidas do grupo: <strong>' . implode(', ', $diff_o_tags) . '</strong><br>';
            }
        }

        if (isset($post['nve'])) {
            $old_nve = $this->grupo_tarifario_model->get_atributos_nve_as_array($id_grupo_tarifario);

            $diff_nve = array_diff_assoc($post['nve'], $old_nve);
            if (!empty($diff_nve)) {
                $log_data['motivo'] .= 'Alteração de atributos de NVE do grupo <br>';
            }
        }

        if (!empty($log_data['motivo'])) {
            $this->grupo_tarifario_log_model->save($log_data);
        }
    }

    public function atualizar_itens($id_grupo_tarifario)
    {
        $this->load->model('cad_item_model');
        $can_formatar_texto = company_can("formatar_texto");

        if ($this->input->post('commit')) {
            $errors = $success = array();

            $itens              = $this->input->post('item');
            $itens_descricao   = $this->input->post('item_descricao');

            // Tipo de descrição resumida
            $gt = $this->grupo_tarifario_model->get_entry($id_grupo_tarifario);
            $tipo_descricao_resumida_id = $gt->tipo_descricao_resumida_id;

            foreach ($itens as $k => $item) {
                $id_item = $item;
                $nova_descricao_sugerida = formatar_texto($can_formatar_texto, $itens_descricao[$k]);

                $item = $this->cad_item_model->get_entry($id_item);

                $motivo = "Alteração da descrição proposta resumida: <em>{$item->descricao_mercado_local}</em> &rarr; <strong>{$nova_descricao_sugerida}</strong>";

                if ($this->cad_item_model->update_item($item->part_number, $item->id_empresa, array(
                    'descricao_mercado_local' => $nova_descricao_sugerida,
                    'forma_descricao_sugerida' => $tipo_descricao_resumida_id
                ), $motivo)) {
                    $success[] = $item->part_number;
                } else {
                    $errors[] = $item->part_number;
                }
            }

            if (count($errors) > 0) {
                $error_message = '<strong>Erro!</strong> Não foi possível atualizar os seguintes itens: ' . implode(', ', $errors) . '';
                $this->message_next_render($error_message, 'error');
            }

            if (count($success) > 0) {
                $success_message = '<strong>Sucesso!</strong> ' . count($success) . ' item(ns) atualizado(s) com sucesso: ' . implode(', ', $success) . '';
                $this->message_next_render($success_message, 'success');
            }

            redirect('cadastros/grupotarifario/atualizar_itens/' . $id_grupo_tarifario);
        }

        $data = array();

        $grupo_tarifario = $this->grupo_tarifario_model->get_entry($id_grupo_tarifario);

        if (!$grupo_tarifario) {
            show_error('Grupo tarifário não encontrado', 500);
        }

        $data['id_grupo_tarifario'] = $id_grupo_tarifario;

        if ($grupo_tarifario->tipo_descricao_resumida_id == 1 || $grupo_tarifario->tipo_descricao_resumida_id == 0) {
            $data['gt_error'] = TRUE;
        } else {
            $id_empresa = sess_user_company();

            $this->cad_item_model->set_state('filter.id_empresa', $id_empresa);
            $this->cad_item_model->set_state('filter.id_grupo_tarifario', $id_grupo_tarifario);
            $this->cad_item_model->set_state('filter.list_opt', 'nao_homologado');

            $itens_reprovados = $this->cad_item_model->get_entries();

            $this->cad_item_model->set_state('filter.id_empresa', $id_empresa);
            $this->cad_item_model->set_state('filter.id_grupo_tarifario', $id_grupo_tarifario);
            $this->cad_item_model->set_state('filter.list_opt', 'homologar');

            $itens_a_homologar = $this->cad_item_model->get_entries();

            $itens = array_merge($itens_reprovados, $itens_a_homologar);

            $data['list'] = $itens;
        }

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Grupos Tarifários', '/cadastros/grupotarifario');
        $this->breadcrumbs->push('Atualizar descrições sugeridas', '/cadastros/grupotarifario/atualizar_itens/' . $id_grupo_tarifario);

        $this->render('cadastros/grupotarifario/atualizar_itens', $data);
    }

    public function ajax_ex_tarifario_by_ncm()
    {
        $ncm = $this->input->get('ncm');

        $itens_ex_tarifario = $this->ex_tarifario_model->get_entries_by_ncm($ncm);
?>
        <option value="">[Selecione]</option>
        <?php

        if (!empty($itens_ex_tarifario)) { ?>
            <?php
            foreach ($itens_ex_tarifario as $ex_tarifario) {
                if ($ex_tarifario->vigente == 0) continue;

                $data_ini = new DateTime($ex_tarifario->dat_vigencia_ini);
                $data_ini = $data_ini->format('d/m/Y');

                $data_fim = new DateTime($ex_tarifario->dat_vigencia_fim);
                $data_fim = $data_fim->format('d/m/Y');
            ?>
                <option data-vigencia-ini="<?php echo $data_ini ?>" data-vigencia-fim="<?php echo $data_fim ?>" value="<?php echo $ex_tarifario->num_ex ?>"><?php echo $ex_tarifario->descricao_linha1 ?></option>
            <?php } ?>
        <?php
        }
    }

    public function ajax_ex_ii_by_ncm()
    {
        $ncm = $this->input->get('ncm');

        $itens_ex_tarifario = $this->ex_tarifario_model->get_all_ex_ii_by_ncm($ncm);

        if (!empty($itens_ex_tarifario)) { ?>
            <option value="">[Selecione]</option>
            <?php
            foreach ($itens_ex_tarifario as $ex_tarifario) {
                if ($ex_tarifario->vigente == 0) continue;

                $data_ini = new DateTime($ex_tarifario->dat_vigencia_ini);
                $data_ini = $data_ini->format('d/m/Y');

                $data_fim = new DateTime($ex_tarifario->dat_vigencia_fim);
                $data_fim = $data_fim->format('d/m/Y');
            ?>
                <option data-vigencia-ini="<?php echo $data_ini ?>" data-vigencia-fim="<?php echo $data_fim ?>" value="<?php echo $ex_tarifario->num_ex ?>"><?php echo $ex_tarifario->descricao_linha1 ?></option>
            <?php } ?>
        <?php
        } else {
        ?>
            <option value="">Nenhum EX de II disponível para a NCM</option>
        <?php
        }
    }

    public function ajax_ex_ipi_by_ncm()
    {
        $ncm = $this->input->get('ncm');

        $itens_ex_tarifario = $this->ex_tarifario_model->get_all_ex_ipi_by_ncm($ncm);

        if (!empty($itens_ex_tarifario)) { ?>
            <option value="">[Selecione]</option>
            <?php
            foreach ($itens_ex_tarifario as $ex_tarifario) {
                if ($ex_tarifario->vigente == 0) continue;

                $data_ini = new DateTime($ex_tarifario->dat_vigencia_ini);
                $data_ini = $data_ini->format('d/m/Y');

                $data_fim = new DateTime($ex_tarifario->dat_vigencia_fim);
                $data_fim = $data_fim->format('d/m/Y');
            ?>
                <option data-vigencia-ini="<?php echo $data_ini ?>" data-vigencia-fim="<?php echo $data_fim ?>" value="<?php echo $ex_tarifario->num_ex ?>"><?php echo $ex_tarifario->descricao_linha1 ?></option>
            <?php } ?>
        <?php
        } else {
        ?>
            <option value="">Nenhum EX de IPI disponível para a NCM</option>
<?php
        }
    }

    public function ajax_atributos_nve()
    {
        $ncm = $this->input->get('ncm');
        $id_grupo_tarifario = $this->input->get('id_grupo_tarifario');

        $data = array();

        $data['id_grupo_tarifario'] = $id_grupo_tarifario;
        $data['ncm'] = $ncm;
        $data['atributos'] = $this->nve_atributo_model->get_atributos_by_ncm($ncm);

        $this->load->view('cadastros/grupotarifario/subview_nve', $data);
    }

    /** Cadastro de Regras **/
    public function ajax_cr_list_regra()
    {
        $this->load->model('grupo_tarifario_regra_model');

        $post = $this->input->post();

        if (!isset($post['id_grupo_tarifario']) || empty($post['id_grupo_tarifario'])) {
            show_error('', '500');
        }

        $id_grupo_tarifario = $post['id_grupo_tarifario'];

        $this->grupo_tarifario_regra_model->set_state('filter.id_grupo_tarifario', $id_grupo_tarifario);
        $regras = $this->grupo_tarifario_regra_model->get_entries();

        $data = array();

        foreach ($regras as $regra) {
            $data[] = array(
                'id_regra' => $regra->id_regra,
                'nome' => $regra->nome
            );
        }

        echo json_encode($data);
    }

    public function ajax_cr_load_regra()
    {
        $this->load->model('grupo_tarifario_regra_model');
        $this->load->model('empresa_model');

        $post = $this->input->post();

        if (!isset($post['id_grupo_tarifario']) || empty($post['id_grupo_tarifario'])) {
            show_error("Não foi possivel realizar a requisição. Argumentos não enviados o vazios");
        }

        $id_grupo_tarifario = $post['id_grupo_tarifario'];

        if (isset($post['id_regra']) && !empty($post['id_regra'])) {
            $this->grupo_tarifario_regra_model->set_id_empresa(sess_user_company());

            $regra = $this->grupo_tarifario_regra_model->get_entry($post['id_regra']);
            $condicoes = $this->grupo_tarifario_regra_model->get_condicoes($regra->id_regra);
            $regra_descricao = $this->grupo_tarifario_regra_model->get_regra_descricao($regra->id_regra);

            $condicao_arr_v = array();

            foreach ($condicoes as $condicao) {
                switch ($condicao->tipo_condicao) {
                    case 'Definição':
                        $condicao_key = 'definicao';
                        break;

                    case 'Material':
                        $condicao_key = 'material';
                        break;

                    case 'Localização':
                        $condicao_key = 'localizacao';
                        break;

                    case 'Aplicação':
                        $condicao_key = 'aplicacao';
                        break;

                    case 'Outros':
                        $condicao_key = 'outros';
                        break;

                    case 'Desconsiderar':
                        $condicao_key = 'desconsiderar';
                        break;
                }

                $condicao_arr_v[$condicao_key][] = $condicao->valor;
            }

            $data['regra'] = $regra;
            $data['condicoes'] = $condicao_arr_v;
            $data['regra_descricao'] = $regra_descricao;
        }

        $data['id_grupo_tarifario'] = $id_grupo_tarifario;

        $this->empresa_model->set_state('filter.order_by', 'razao_social asc');
        $empresas = $this->empresa_model->get_entries();
        $data['empresas'] = $empresas;

        echo $this->load->view('cadastros/grupotarifario/subview_regra', $data);
    }

    public function ajax_cr_save_regra()
    {
        // Libraries
        $this->load->library('form_validation');

        // Models
        $this->load->model('grupo_tarifario_regra_model');

        // Helpers
        $this->load->helper('text_helper');

        $post = $this->input->post();

        $id_regra = $post['id_regra'];
        $condicao = $post['condicao'];

        $id_grupo_tarifario = $post['id_grupo_tarifario'];
        $id_empresa = $post['id_empresa'];

        $this->form_validation->set_rules('nome_regra', 'Nome da Regra', 'trim|required');
        $this->form_validation->set_rules('descricao_regra', 'Descrição da Regra', 'trim');
        $this->form_validation->set_rules('empresa', 'Empresa', 'trim');

        $this->form_validation->set_rules('condicao[definicao][tags]', 'Definição', 'trim|callback_check_condicoes');
        $this->form_validation->set_rules('condicao[material][tags]', 'Material', 'trim|callback_check_condicoes');
        $this->form_validation->set_rules('condicao[localizacao][tags]', 'Localização', 'trim|callback_check_condicoes');
        $this->form_validation->set_rules('condicao[aplicacao][tags]', 'Aplicação', 'trim|callback_check_condicoes');

        if (!$this->form_validation->run()) {
            $fields_err = array();
            $validation_errors = array();

            $errors_arr = $this->form_validation->error_array();

            foreach ($errors_arr as $error_key => $error_message) {
                $fields_err[] = array('field' => $error_key, 'message' => $error_message);

                $tmp_key = $error_key;

                if (preg_match('/^condicao\[\w+\]\[\w+\]$/', $error_key)) {
                    $tmp_key = 'condicao';
                }

                $validation_errors[$tmp_key] = '&#8594; ' . $error_message;
            }

            $ret = array(
                'status' => 'error',
                'message' => 'Por favor, verifique os campos! <br /> ' . implode("<br />", $validation_errors),
                'fields' => $fields_err
            );
        } else {
            $modelo_descricao = trim(
                html_entity_decode($post['modelo_descricao'])
            );

            // Regra
            $regra_db_data = array(
                // Dados básicos
                'id_empresa'                => $id_empresa,
                'id_grupo_tarifario'        => $id_grupo_tarifario,

                'nome'                      => $post['nome_regra'],
                'descricao'                 => $post['descricao_regra'],
                'modelo_descricao'          => $modelo_descricao,
                // Definição
                'definicao_obrigatorio'     => (isset($condicao['definicao']['obrigatorio']) ? 1 : 0),
                'definicao_pergunta'        => (isset($condicao['definicao']['pergunta']) ? 1 : 0),
                'definicao_inicio'          => (isset($condicao['definicao']['inicio']) ? 1 : 0),
                // Material
                'material_obrigatorio'      => (isset($condicao['material']['obrigatorio']) ? 1 : 0),
                'material_pergunta'         => (isset($condicao['material']['pergunta']) ? 1 : 0),
                // Localização
                'localizacao_obrigatorio'   => (isset($condicao['localizacao']['obrigatorio']) ? 1 : 0),
                'localizacao_pergunta'      => (isset($condicao['localizacao']['pergunta']) ? 1 : 0),
                // Aplicação
                'aplicacao_obrigatorio'     => (isset($condicao['aplicacao']['obrigatorio']) ? 1 : 0),
                'aplicacao_pergunta'        => (isset($condicao['aplicacao']['pergunta']) ? 1 : 0)
            );

            if (!empty($id_regra)) {
                $this->grupo_tarifario_regra_model->save($regra_db_data, $id_regra);
            } else {
                $id_regra = $this->grupo_tarifario_regra_model->save($regra_db_data);
            }

            // Definição (tags)
            if (isset($condicao['definicao']['tags']) && !empty($condicao['definicao']['tags'])) {
                $definicao_arr = explode(",", $condicao['definicao']['tags']);

                foreach ($definicao_arr as $definicao_v) {
                    $definicao_v = strtoupper(
                        convert_accented_characters($definicao_v)
                    );

                    $condicao_db_data[] = array(
                        'id_regra' => $id_regra,
                        'tipo_condicao' => 'Definição',
                        'valor' => $definicao_v
                    );
                }
            }

            // Material (tags)
            if (isset($condicao['material']['tags']) && !empty($condicao['material']['tags'])) {
                $material_arr = explode(",", $condicao['material']['tags']);

                foreach ($material_arr as $material_v) {
                    $material_v = strtoupper(
                        convert_accented_characters($material_v)
                    );

                    $condicao_db_data[] = array(
                        'id_regra' => $id_regra,
                        'tipo_condicao' => 'Material',
                        'valor' => $material_v
                    );
                }
            }

            // Localização (tags)
            if (isset($condicao['localizacao']['tags']) && !empty($condicao['localizacao']['tags'])) {
                $localizacao_arr = explode(",", $condicao['localizacao']['tags']);

                foreach ($localizacao_arr as $localizacao_v) {
                    $localizacao_v = strtoupper(
                        convert_accented_characters($localizacao_v)
                    );

                    $condicao_db_data[] = array(
                        'id_regra' => $id_regra,
                        'tipo_condicao' => 'Localização',
                        'valor' => $localizacao_v
                    );
                }
            }

            // Aplicação (tags)
            if (isset($condicao['aplicacao']['tags']) && !empty($condicao['aplicacao']['tags'])) {
                $aplicacao_arr = explode(",", $condicao['aplicacao']['tags']);

                foreach ($aplicacao_arr as $aplicacao_v) {
                    $aplicacao_v = strtoupper(
                        convert_accented_characters($aplicacao_v)
                    );

                    $condicao_db_data[] = array(
                        'id_regra' => $id_regra,
                        'tipo_condicao' => 'Aplicação',
                        'valor' => $aplicacao_v
                    );
                }
            }

            // Outros (tags)
            if (isset($condicao['outros']['tags']) && !empty($condicao['outros']['tags'])) {
                $outros_arr = explode(",", $condicao['outros']['tags']);

                foreach ($outros_arr as $outros_v) {
                    $outros_v = strtoupper(
                        convert_accented_characters($outros_v)
                    );

                    $condicao_db_data[] = array(
                        'id_regra' => $id_regra,
                        'tipo_condicao' => 'Outros',
                        'valor' => $outros_v
                    );
                }
            }

            // Desconsiderar (tags)
            if (isset($condicao['desconsiderar']['tags']) && !empty($condicao['desconsiderar']['tags'])) {
                $desconsiderar_arr = explode(",", $condicao['desconsiderar']['tags']);

                foreach ($desconsiderar_arr as $desconsiderar_v) {
                    $desconsiderar_v = strtoupper(
                        convert_accented_characters($desconsiderar_v)
                    );

                    $condicao_db_data[] = array(
                        'id_regra' => $id_regra,
                        'tipo_condicao' => 'Desconsiderar',
                        'valor' => $desconsiderar_v
                    );
                }
            }

            $this->grupo_tarifario_regra_model->save_condicao($condicao_db_data, $id_regra);

            // Regras de Descrição
            if (isset($post['regra_descricao'])) {
                $regras_descricao = $post['regra_descricao'];
                $regra_desc_data = array();

                foreach ($regras_descricao as $regra_desc) {
                    $regra_desc_tags = explode(",", $regra_desc['tags']);

                    foreach ($regra_desc_tags as $regra_desc_tag) {
                        $regra_desc_tag = convert_accented_characters($regra_desc_tag);

                        $regra_desc_data[] = array(
                            'id_regra' => $id_regra,
                            'nome' => $regra_desc['nome'],
                            'tipo' => $regra_desc['tipo'],
                            'valor' => strtoupper($regra_desc_tag)
                        );
                    }
                }

                $this->grupo_tarifario_regra_model->save_regras_descricao($regra_desc_data, $id_regra);
            } else {
                $this->grupo_tarifario_regra_model->limpar_regra_descricao($id_regra);
            }

            $ret = array(
                'status' => 'success',
                'message' => 'Regra salva com sucesso!'
            );
        }

        echo json_encode($ret);
    }

    public function ajax_cr_del_regra()
    {
        $this->load->model('grupo_tarifario_regra_model');
        $post = $this->input->post();

        $id_regra = $post['id_regra'];

        $this->grupo_tarifario_regra_model->remove_entry($id_regra);

        echo json_encode(array('status' => 'success'));
    }

    public function check_condicoes()
    {
        if (
            (
                (!empty($_POST['condicao']['definicao']['tags']) &&
                    !empty($_POST['condicao']['definicao']['obrigatorio'])) or (!empty($_POST['condicao']['material']['tags']) &&
                    !empty($_POST['condicao']['material']['obrigatorio'])) or (!empty($_POST['condicao']['localizacao']['tags']) &&
                    !empty($_POST['condicao']['localizacao']['obrigatorio'])) or (!empty($_POST['condicao']['aplicacao']['tags']) &&
                    !empty($_POST['condicao']['localizacao']['obrigatorio']))) or !empty($_POST['condicao']['outros']['tags'])
        ) {
            return true;
        }

        $this->form_validation->set_message('check_condicoes', 'É necessário, no mínimo, uma condição obrigatória para a regra.');

        return false;
    }

    /** Exceção **/
    public function salvar_excecao()
    {
        if ($post = $this->input->post()) {
            $this->load->model('grupo_tarifario_excecao_model');
            if (isset($post['delete'])) {
                $this->grupo_tarifario_excecao_model->delete($post['id_excecao']);
            } else {
                $db_data = array(
                    'campo' => $post['campo'],
                    'id_empresa' => $post['empresa'],
                    'id_grupo_tarifario' => $post['grupo_tarifario'],
                    'descricao' => $post['descricao']
                );

                if (isset($post['id_excecao'])) {
                    $this->grupo_tarifario_excecao_model->save($db_data, array('id_excecao' => $post['id_excecao']));
                } else {
                    $this->grupo_tarifario_excecao_model->save($db_data);
                }
            }
        }
    }

    public function show_modal()
    {
        $this->load->model(array(
            'grupo_tarifario_excecao_model',
            'empresa_model'
        ));

        if ($get = $this->input->get()) {
            if (!empty($get['grupo'])) {
                $data['id_grupo_tarifario'] = $get['grupo'];
            }

            if (!empty($get['excecao'])) {
                $excecao = $this->grupo_tarifario_excecao_model->get_entry($get['excecao']);
                $data['id_excecao'] = $excecao->id_excecao;
                $data['descricao'] = $excecao->descricao;
                $data['id_empresa'] = $excecao->id_empresa;
                $data['campo'] = $excecao->campo;
                $data['id_grupo_tarifario'] = $excecao->id_grupo_tarifario;
            }
        }

        $data['empresas'] = $this->empresa_model->get_entries();

        $this->load->view('cadastros/modal/add_excecao', $data);
    }

    public function check_ncm_exists()
    {
        $ncm = $this->input->post('ncm_recomendada');

        if (empty($ncm)) {
            echo 'Digite uma NCM antes de continuar.';
            return TRUE;
        } else if (!is_numeric($ncm)) {
            echo 'Apenas números são permitidos na NCM.';
            return FALSE;
        }

        $this->load->model('ncm_model');

        if ($this->ncm_model->check_ncm_exists($ncm)) {
            return TRUE;
        } else {
            $err = '';

            if ($this->ncm_model->check_ncm_exists($ncm, TRUE)) {
                $err = "A NCM selecionada não está presente na TEC atual (2017). Tem certeza que deseja utilizá-la?";
            } else {
                $err = "Código de NCM não encontrado na TEC. Tem certeza que deseja utilizá-la?";
            }

            echo $err;
        }
    }

    public function populate_companys()
    {
        if(isset($_GET['populateinit'])){
            $allGt = $this->grupo_tarifario_model->get_entries_no_inner();
            $allCompanies = $this->empresa_model->get_all_entries();
            
            foreach($allGt as $entry){
                $this->permissao_atribuir_grupo_model->delete($entry->id_grupo_tarifario);
                foreach ($allCompanies as $empresa) {
                    $this->permissao_atribuir_grupo_model->save(array('id_grupo_tarifario' => $entry->id_grupo_tarifario, 'id_empresa' => $empresa->id_empresa));
                }
            }
        }
    }
}
