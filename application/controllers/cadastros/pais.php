<?php

class Pais extends MY_Controller {

	public function __construct() {
		parent::__construct();

		$this->load->model('pais_model');

		if (!is_logged()) {
			redirect('/login');
		}

		if (!has_role('gerenciar_paises')) {
			show_permission();
		}

		$this->load->library('breadcrumbs');
		$this->load->helper('formatador_helper');
	}

	public function index()
	{
        $query_str = '';

		$this->load->library('pagination');

		$this->load->model('mascara_model');

		$data = array();

		$data['mascaras'] = $this->mascara_model->get_entries();

		$page = $this->input->get('per_page');
		$limit = 15;


        if ($this->input->is_set('reset_filter')) {
            $this->pais_model->clear_states();
        } else {
            if ($this->input->is_set('nome_pais') && $this->input->get('nome_pais') != '') {
                $this->pais_model->set_state('filter.nome_pais', $this->input->get('nome_pais'));
            } else {
                $this->pais_model->unset_state('filter.nome_pais');
            }

            if ($this->input->is_set('id_mascara') && $this->input->get('id_mascara') != '') {
                $this->pais_model->set_state('filter.id_mascara', $this->input->get('id_mascara'));
            } else {
                $this->pais_model->unset_state('filter.id_mascara');
            }
        }

        $total_entries = $this->pais_model->get_total_entries();
		$data['list'] = $this->pais_model->get_entries($limit, ($page>0?$page-1:0)*$limit);

		$config['base_url'] = base_url("cadastros/pais?{$query_str}");
		$config['use_page_numbers'] = TRUE;
		$config['total_rows'] = $total_entries;
		$config['per_page'] = $limit;
		$config['page_query_string'] = TRUE;

		$this->pagination->initialize($config);

        $data['pagination'] = $this->pagination->create_links();

		$this->title = "Países";

		$this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Países', '/cadastros/pais/');

		$this->render('cadastros/pais/default', $data);
	}

	public function excluir() {
		
		$this->load->model('empresa_pais_model');

		$id_list = $this->input->post('id_list');	
		$return = array();
		$return['status'] = FALSE;

		foreach ($id_list as $id){
			$item = $this->empresa_pais_model->get_entry_pais($id);

			if(!empty($item)){
				
				$err = "Ops... Erro ao excluir, é necessário desvincular o país de todas as empresas vinculadas.";
				$return['msg'] = $err;
				echo json_encode($return);
				die();
			}
		}

		if ($this->pais_model->remove($id_list)) {
			$return['status'] = TRUE;
			$this->message_next_render('Exclusão realizada com sucesso!');
		}

		echo json_encode($return);
	}

	public function novo()
    {
		$this->title = "Pais &gt; Novo";

		$this->load->model('mascara_model');

		$data = array();

		$data['mascaras'] = $this->mascara_model->get_entries();


		if ($this->input->post('submit'))
		{
			$this->load->library('form_validation');

			$this->form_validation->set_rules('nome', 'Nome', 'trim|required|is_unique[pais.nome]');
			$this->form_validation->set_rules('codigo', 'Codigo', 'trim|required|is_unique[pais.codigo]');
			$this->form_validation->set_rules('sigla', 'Sigla', 'trim|required|is_unique[pais.sigla]');
			$this->form_validation->set_rules('id_mascara', 'Máscara', 'trim|required');


			if ($this->form_validation->run() == TRUE)
			{
				$data = array(
					'nome' => $this->input->post('nome'),
					'sigla' => strtoupper($this->input->post('sigla')),
					'nome_upper' => strtoupper(remove_acentos($this->input->post('nome'))),
					'slug' => to_slug($this->input->post('nome')),
					'codigo' => $this->input->post('codigo'),
					'id_mascara' => $this->input->post('id_mascara'),
				);

				$this->pais_model->save($data);

				$this->message_next_render('Sucesso! pais [<strong>'.$this->input->post('nome').'</strong>] adicionado');

				

				redirect('cadastros/pais');
			}
			else
			{
				$err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
				$this->message_on_render($err, 'error');
			}
		}

		$this->include_js(array(
			'jquery-fileupload.js',
			'jquery.mask.min.js'
		));
				

        $this->include_css('jquery-fileupload.css');

		$this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Países', '/cadastros/pais/');
        $this->breadcrumbs->push('Novo pais', '/cadastros/pais/novo/');

		$this->render('cadastros/pais/novo', $data);
	}

	public function editar($id_pais)
    {
		$this->title = "pais &gt; Editar";

		$data = array();

        try {
            $entry = $this->pais_model->get_entry($id_pais);
            $data['entry'] = $entry;
        } catch (Exception $e) {
            show_error($e->getMessage());
        }

		$this->load->model('mascara_model');

		$data['mascaras'] = $this->mascara_model->get_entries();

		if ($this->input->post('submit'))
		{
			$this->load->library('form_validation');

			$this->form_validation->set_rules('nome', 'Nome', 'trim|required');
			$this->form_validation->set_rules('codigo', 'Codigo', 'trim|required');
			$this->form_validation->set_rules('sigla', 'Sigla', 'trim|required');
			$this->form_validation->set_rules('id_mascara', 'Máscara', 'trim|required');


			if ($this->form_validation->run() == TRUE)
			{
				$data = array(
					'nome' => $this->input->post('nome'),
					'sigla' => strtoupper($this->input->post('sigla')),
					'nome_upper' => strtoupper(remove_acentos($this->input->post('nome'))),
					'slug' => to_slug($this->input->post('nome')),
					'codigo' => $this->input->post('codigo'),
					'id_mascara' => $this->input->post('id_mascara'),
				);

				$this->pais_model->save($data, array('id_pais' => $entry->id_pais));

				$this->message_next_render('Sucesso! País [<strong>'.$this->input->post('nome').'</strong>] atualizado');

				redirect('cadastros/pais');
			}
			else
			{
				$err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
				$this->message_on_render($err, 'error');
			}
		}

		$this->include_js(array(
			'jquery-fileupload.js',
			'jquery.mask.min.js'
		));
		
		$this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Países', '/cadastros/pais/');
        $this->breadcrumbs->push('Editar país', '/cadastros/pais/editar/'.$id_pais);

		$this->render('cadastros/pais/editar', $data);
	}

}