<?php

class Perfil extends MY_Controller {

	public function __construct() {
		parent::__construct();

		$this->load->model('perfil_model');

		if (!is_logged()) {
			redirect('/login');
		}

		if (!has_role('gerenciar_perfil')) {
			show_permission();
		}

		$this->load->library('breadcrumbs');
	}

	public function index()
	{
		$data = array();
		$data['list'] = $this->perfil_model->get_entries();

		$this->title = "Perfil de Usuário";

		$this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Perfil de usuário', '/cadastros/perfil/');

		$this->render('cadastros/perfil/default', $data);
	}

	public function excluir() {

		$id_list = $this->input->post('id_list');
		$return = array();
		$return['status'] = FALSE;

		if ($this->perfil_model->remove($id_list)) {
			$return['status'] = TRUE;
			$this->message_next_render('Exclusão realizada com sucesso!');
		}

		echo json_encode($return);
	}

	public function editar($id_perfil) {
		$this->load->model('permissao_model');
		$this->load->model('empresa_model');

        $data = array();

        try {
            $entry = $this->perfil_model->get_entry($id_perfil);
		} catch (Exception $e) {
            show_error($e->getMessage());
		}

		$this->title = "Perfil de Usuário &gt; Editar Perfil";

		$data['permissoes'] = $this->permissao_model->get_all_entries();
		$data['entry']		= $entry;
		$data['related_perms'] = $this->perfil_model->get_related_perms($id_perfil, TRUE);

		$empresa = $this->empresa_model->get_entry(sess_user_company());
        
        $campos_adicionais = explode("|", $empresa->campos_adicionais);
		$hasOwner = in_array('owner', $campos_adicionais);
		$data['hasOwner'] = $hasOwner;

		if ($this->input->post('submit'))
		{
			$this->load->library('form_validation');

			$this->form_validation->set_rules('descricao', 'Descrição', 'trim|required');

			if ($this->form_validation->run() == TRUE)
			{
				$data = array(
					'descricao' => $this->input->post('descricao'),
				);

				$this->perfil_model->save($data, array('id_perfil' => $id_perfil));

				$permissao_lst = $this->input->post('permissao');

				if ($permissao_lst && count($permissao_lst) > 0) {
					$this->perfil_model->save_perms($id_perfil, $permissao_lst);
				}

				$this->message_next_render('Sucesso! Perfil [<strong>'.$this->input->post('descricao').'</strong>] editado');

				redirect('cadastros/perfil');
			}
			else
			{
				$err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
				$this->message_on_render($err, 'error');
			}
		}

		$this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Perfil de usuário', '/cadastros/perfil/');
        $this->breadcrumbs->push('Editar perfil', '/cadastros/perfil/editar/'.$id_perfil);

		$this->render('cadastros/perfil/editar', $data);
	}

	public function novo() {

		$this->load->model('permissao_model');

		$this->title = "Perfil de Usuário &gt; Novo";

		$data = array();

		$data['permissoes'] = $this->permissao_model->get_all_entries();

		if ($this->input->post('submit'))
		{
			$this->load->library('form_validation');

			$this->form_validation->set_rules('descricao', 'Descrição', 'trim|required');

			if ($this->form_validation->run() == TRUE)
			{
				$data = array(
					'descricao' => $this->input->post('descricao'),
				);

				$id_perfil = $this->perfil_model->save($data);

				$permissao_lst = $this->input->post('permissao');

				if ($permissao_lst && count($permissao_lst) > 0) {
					$this->perfil_model->save_perms($id_perfil, $permissao_lst);
				}

				$this->message_next_render('Sucesso! Perfil [<strong>'.$this->input->post('descricao').'</strong>] adicionado');

				redirect('cadastros/perfil');
			}
			else
			{
				$err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
				$this->message_on_render($err, 'error');
			}
		}

		$this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Perfil de usuário', '/cadastros/perfil/');
        $this->breadcrumbs->push('Novo perfil', '/cadastros/perfil/novo/');

		$this->render('cadastros/perfil/novo', $data);
	}

}



