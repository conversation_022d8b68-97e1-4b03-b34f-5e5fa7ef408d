<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Implementacao extends MY_Controller {

    public function __construct() {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }

        if (!has_role('arquivos_implementacao')) {
            show_permission();
        }

        $this->load->library('breadcrumbs');
    }

    public function upload()
    {
        $data = array();

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Enviar itens para Implementação', '/implementacao/upload/');

        if ($this->input->post('submit'))
        {
            $upload_path = config_item('upload_tmp_path');

            $this->load->library('form_validation');

            $config['upload_path'] = $upload_path;
            $config['allowed_types'] = 'xlsx';
            $config['max_size'] = 2147483648;

            $this->load->library('upload', $config);

            if (!$this->upload->do_upload('arquivo'))
            {
                $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . $this->upload->display_errors('<li>', '</li>') . "</ul>";
                $this->message_on_render($err, 'error');
            }
            else
            {
                $this->load->model('cad_item_model');
                $this->load->helper('text');

                $upload_data = $this->upload->data();

                include APPPATH . 'libraries/xlsxreader.php';

                $xlsx = new XLSXReader($upload_data['full_path']);
                $sheetNames = $xlsx->getSheetNames();
                $sheetActive = current($sheetNames);
                $sheet = $xlsx->getSheet($sheetActive);

                $i = 0;

                $log_erros     = array();
                $log_sucesso   = array();
                $total_erros   = 0;
                $total_sucesso = 0;

                $idx = array(
                    'cnpj'                 => 0,
                    'part_number'          => 1,
                    'estabelecimento'      => 2,
                    'status_implementacao' => 3,
                    'motivo_implementacao' => 4
                );

                foreach ($sheet->getData() as $key => $row)
                {
                    //Primeira linha é o cabeçalho da planilha
                    if ($i > 0)
                    {
                        $dbdata = array();

                        $part_number = clean_str($row[$idx['part_number']], true);

                        // Limpeza de Caracteres Especiais
                        array_walk($row, function(&$v) {
                            $v = clean_str($v);
                        });

                        $cnpj = $row[$idx['cnpj']];
                        $estabelecimento = convert_accented_characters($row[$idx['estabelecimento']]);
                        $status_implementacao = $row[$idx['status_implementacao']];
                        $motivo_implementacao = $row[$idx['motivo_implementacao']];

                        if (empty($part_number))
                        {
                            $log_erros[] = 'Part number <strong>não informado</strong> na Linha <strong>'.$i.'.</strong>';
                            $total_erros++;
                            continue;
                        }

                        if (empty($cnpj))
                        {
                            $log_erros[] = '<strong>Part number '.$part_number.'</strong>: Nenhum CNPJ informado para o item (Linha '.$i.').';
                            $total_erros++;
                            continue;
                        }else
                        {
                            $this->load->model('empresa_model');

                            if ($empresa = $this->empresa_model->get_entry_by_cnpj($cnpj))
                            {
                                $id_empresa = $empresa->id_empresa;
                            }else
                            {
                                $log_erros[] = 'CNPJ informado (<strong>'.$cnpj.'</strong>) não confere com as empresas cadastradas.';
                                $total_erros++;
                                continue;
                            }
                        }

                        if (empty($estabelecimento))
                        {
                            $log_erros[] = '<strong>Part number '.$part_number.'</strong>: Estabelecimento não informado para o item.';
                            $total_erros++;
                            continue;
                        }

                        if (empty($status_implementacao))
                        {
                            $log_erros[] = '<strong>Part number '.$part_number.'</strong>: Status de implementação não informado para o item.';
                            $total_erros++;
                            continue;
                        }else if ($status_implementacao != 'I' && $status_implementacao != 'R' && $status_implementacao != 'N')
                        {
                            $log_erros[] = '<strong>Part number '.$part_number.'</strong>: Status de implementação "'.$status_implementacao.'" não existe.';
                            $total_erros++;
                            continue;
                        }else
                        {
                            $dbdata['status_implementacao'] = $status_implementacao;

                            switch ($status_implementacao) {
                                case 'I':
                                    $label_status = 'Implementado';
                                    break;

                                case 'N':
                                    $label_status = 'Não implementado';
                                    break;

                                case 'R':
                                    $label_status = 'Revisão';
                                    break;
                            }
                        }

                        if (empty($motivo_implementacao))
                        {
                            $log_erros[] = '<strong>Part number '.$part_number.'</strong>: Motivo da implementação não informado para o item.';
                            $total_erros++;
                            continue;
                        }else
                        {
                            $dbdata['motivo_implementacao'] = $motivo_implementacao;
                        }

                        $this->load->model('cad_item_model');

                        try {

                            $item = $this->cad_item_model->get_entry_by_pn($part_number, $id_empresa, $estabelecimento);

                            switch ($item->status_implementacao) {
                                case 'I':
                                    $label_old_status = 'Implementado';
                                    break;

                                case 'N':
                                    $label_old_status = 'Não implementado';
                                    break;

                                case 'R':
                                    $label_old_status = 'Revisão';
                                    break;
                            }

                            //- Itens com status R ou N podem passar para o status
                            //- Itens com status I podem passar para o status R
                            //- Itens não homologados não podem ser do status I

                            if ( (
                                    ($item->status_implementacao == 'R' || $item->status_implementacao == 'N')
                                    && ($status_implementacao == 'I')
                                ) || ($item->status_implementacao == 'I' && $status_implementacao == 'R') )
                            {
                                if ($status_implementacao == 'I')
                                {
                                    // Verifica status de homologação do Item informado na planilha.
                                    $check_item_homolog = $this->cad_item_model->get_homolog_info($item->id_item);

                                    $homologado = array();

                                    foreach ($check_item_homolog as $item_homolog) {
                                        if ($item_homolog->homologado == 1) {
                                            $homologado[] = $item_homolog->tipo_homologacao;
                                        }
                                    }

                                    // Verificação para validar se foi homologado pelos dois perfis.
                                    if (count($homologado) !== 2) {
                                        $log_erros[] = '<strong>Part number '.$part_number.'</strong>: Não foi possível atualizar o status de "'.$label_old_status.'" para "'.$label_status.'. O item informado não está homologado.';
                                        $total_erros++;
                                        continue;
                                    }
                                }

                                if ($this->cad_item_model->save($dbdata, array('part_number' => $part_number, 'id_empresa' => $id_empresa, 'estabelecimento' => $estabelecimento)))
                                {
                                    $log_sucesso[] = '<strong>Part number '.$part_number.'</strong>: Atualizado de "'.$label_old_status.'" para "'.$label_status.'".';
                                    $total_sucesso++;
                                }
                            } else {
                                $log_erros[] = '<strong>Part number '.$part_number.'</strong>: Não foi possível atualizar o status de "'.$label_old_status.'" para "'.$label_status.'".';
                                $total_erros++;
                                continue;
                            }

                        } catch (Exception $e) {
                            $log_erros[] = 'Nenhum item foi encontrado para o <strong>Part number:</strong> '.$part_number.', <strong>CNPJ:</strong> '.$cnpj.' e <strong>Estabelecimento:</strong> '.$estabelecimento;
                            $total_erros++;
                            continue;
                        }
                    }

                    $i++;
                }

                $log_final = '';

                if ($total_sucesso > 0)
                {
                    $msg = '<h4>Implementações realizadas com sucesso:</h4><ul>';

                    foreach ($log_sucesso as $log_msg)
                    {
                        $msg .= '<li>'.$log_msg.'</li>';
                    }
                    $msg .= '</ul>';

                    $log_final .= $this->message_config($msg, 'success');
                }

                if ($total_erros > 0)
                {
                    $msg = '<h4>Implementações realizadas com erro:</h4><ul>';

                    foreach ($log_erros as $log_msg)
                    {
                        $msg .= '<li>'.$log_msg.'</li>';
                    }
                    $msg .= '</ul>';

                    $log_final .= $this->message_config($msg, 'error');
                }

                if (!empty($log_final))
                {
                    $this->message_next_render($log_final, NULL, TRUE);
                    redirect('implementacao/upload');
                }
            }
        }

        $this->load->model('empresa_model');
        $data['empresas'] = $this->empresa_model->get_all_entries();

        $this->render('implementacao/upload', $data);
    }
}