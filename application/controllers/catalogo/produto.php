<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

/* vim: set expandtab tabstop=4 shiftwidth=4 softtabstop=4: */

/** 
 *    ___  _____________  __  ________  __
 *   / _ )/ __/ ___/ __ \/  |/  / __/ |/_/
 *  / _  / _// /__/ /_/ / /|_/ / _/_>  <  
 * /____/___/\___/\____/_/  /_/___/_/|_|
 * 
 * Controller do produto.
 * 
 * ...
 *  
 * PHP version 5
 *
 * LICENSE: This source file is subject to version 3.01 of the PHP license
 * that is available through the world-wide-web at the following URI:
 * http://www.php.net/license/3_01.txt.  If you did not receive a copy of
 * the PHP License and are unable to obtain it through the web, please
 * send a <NAME_EMAIL> so we can mail you a copy immediately.
 * 
 * @category  Gestão Tarifaria
 * @package   CodeIgniter
 * 
 * <AUTHOR>    <<EMAIL>>
 * 
 * @copyright 1997-2005 The PHP Group
 * @license   http://www.php.net/license/3_01.txt  PHP License 3.01
 * @see       Produto, Produto::Produto()
 */
class Produto extends MY_Controller
{
    // {{{ Propriedades

    // }}
    // {{ __construct()

    /**
     * Metodo construtor.
     * 
     * <AUTHOR> Santana <<EMAIL>>
     * 
     * @return void          Sem retorno.
     */
    public function __construct()
    {
        parent::__construct();

        $this->load->library([
            'breadcrumbs'
        ]);

        $this->load->model([
            'catalogo/produto_model',
        ]);

        if (!is_logged()) {
            redirect('/login');
        }
    }

    // }}
    // {{ index()

    /**
     * Metodo principal.
     * 
     * <AUTHOR> Santana <<EMAIL>>
     * 
     * @return void          Sem retorno.
     */
    public function index()
    {
        $data = [];

        $this->breadcrumbs->push('Home', '/');

        $this->render('dashboard/default', $data);
    }
}
