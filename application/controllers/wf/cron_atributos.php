<?php
require_once  APPPATH . 'libraries/Spout/Autoloader/autoload.php';

use Box\Spout\Reader\ReaderFactory;
use Box\Spout\Common\Type;

class Cron_Atributos extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
    }

    public function debug($message)
    {

        if (!has_role('sysadmin') && !$this->input->is_cli_request()) {
            show_permission();
        }

        if ($this->input->is_cli_request()) {
            echo $message . PHP_EOL;
        } else {
            echo $message . '<br />';
        }
    }

    public function index()
    {   
        if (!has_role('sysadmin') && !$this->input->is_cli_request()) {
            show_permission();
        }

        $this->load->model('cad_item_wf_atributo_model');

        set_time_limit(0);
        @ob_implicit_flush(TRUE);
        @ob_end_flush();

        $this->db->query('SET NAMES binary');

        // Resolve o problema de memory leak
        $this->db->save_queries = FALSE;
        $this->db->db_debug     = FALSE;

        $this->load->library('benchmark');

        $this->benchmark->mark('code_start');

        $this->debug('Integração iniciada - ' . date('Y-m-d H:i:s'));

 
        $result_itens_enviados = $this->cron_criar_versao_e_enviar_pucomex();
        $this->debug('OK - Cadastro e envio dos produtos para o catalogo, OK');

        $this->benchmark->mark('code_end');

        $elapsed_time = $this->benchmark->elapsed_time('code_start', 'code_end');

        $this->debug('Tempo gasto: ' . $elapsed_time . ' segundos');

        $filename = APPPATH . '/logs/log-cronjob-atrubutos.txt';
 
        if ((file_exists($filename) && is_writable($filename)) || !file_exists($filename)) {
            $message = '--------------------------------------------------------------' . PHP_EOL;
            $message .= '[' . date('Y-m-d H:i:s') . '] - Integração com Atributos WF' . PHP_EOL;
              $message .= ' - Integração Catálogo - Enviados: ' .  $result_itens_enviados['enviados'] . PHP_EOL;
            if($result_itens_enviados['mensagem_erro_log'] != ""){
                $message .=  $result_itens_enviados['mensagem_erro_log'];
            }  
            file_put_contents($filename, $message, FILE_APPEND);
        }
    }

 

    private function send_mail_alteracao_status_item_wf_atributos()
    {
        $this->load->model(array('cad_item_wf_atributo_model', 'usuario_model'));

        $usuarios = $this->cad_item_wf_atributo_model->get_usuarios_emails_cron();
        if (empty($usuarios)) {
            return;
        };

        $itens = $this->cad_item_wf_atributo_model->get_itens_cron_email();

        $this->load->library("Item/Atributo");
        $desc_status = $this->atributo->get_status('2');


        $url = config_item('online_url') . '/wf/atributos';
        $count_emails = 0;
        $count_itens = 0;
        foreach($usuarios as $usuario){

            $tabela_itens = '<table>';
            $tabela_itens .= '<tr>';
            $tabela_itens .= '<th><strong>Part-number</strong><th>';
            $tabela_itens .= '<th><strong>Estabelecimento</strong><th>';
            $tabela_itens .= '<th><strong>Descrição</strong><th>';
            $tabela_itens .= '<th><strong>NCM Proposto</strong><th>';
            $tabela_itens .= '</tr>';
            $qtd_itens = 0;
            foreach($itens as $item){
                if($item->id_resp_engenharia ==  $usuario->id_resp_engenharia){
                    $count_itens++;
                    $tabela_itens .= '<tr>';
                    $tabela_itens .= '<td>'.$item->part_number.'<td>';
                    $tabela_itens .= '<td>'.$item->estabelecimento.'<td>';
                    $tabela_itens .= '<td>'.$item->descricao.'<td>';
                    $tabela_itens .= '<td>'.$item->ncm_proposto.'<td>';
                    $tabela_itens .= '</tr>';
                    $qtd_itens++;
                }
            }
            $tabela_itens .= '</table>';

            $html_message = '
                <h3>Itens com alteração de status para '.$desc_status.':</h3>

                <br>

                <p>Olá, ' . $usuario->nome . '!</p>
                <p>Os seguintes itens foram alterados no portal, verifique abaixo as informações: </p>

                <div class="panel panel-default">
                    <div class="panel-body" style="padding: 20px;border-left: 3px solid #ccc;background: #FBFBFB;">
                        <p><strong>Quantidade de itens: </strong> ' . $qtd_itens . '</p>
                        '.$tabela_itens.'
                    </div>
                </div>

                <p style="text-align: right;"><a href="' . $url . '">Clique aqui para acompanhar no portal</a></p>
            ';
            
            $temp_data = array(
                'base_url' => config_item('online_url'),
                'html_message' => $html_message
            );

            $body = $this->load->view('templates/basic_template', $temp_data, TRUE);
            
            $this->load->library('email');

            $this->email->from(config_item('mail_from_addr'), config_item('mail_from_name'));
            $this->email->to($usuario->email);
            $this->email->subject('[Gestão Tarifária] - Alterações de Status dos atributos nos itens');
            $this->email->message($body);

            $this->email->send();

            $count_emails ++;
        }

        return array('emails' => $count_emails, 'itens_com_responsaveis' => $count_itens);
    }

    public function cron_criar_versao_e_enviar_pucomex() {
        
        $this->load->model('cad_item_wf_atributo_model');
    
        // Obtenha todos os itens prontos para envio
        $itens = $this->cad_item_wf_atributo_model->cron_get_itens_prontos_para_envio();

        $totalItens = count($itens);
        $this->debug("Total de itens prontos para envio: $totalItens");

        // Enviar itens em lotes de 100
        $loteSize = 100;
        $enviados = 0;
        $mensagem_erro_log = "";
        $count = 0; 
        for ($i = 0; $i < $totalItens; $i += $loteSize) {
            $loteItens = array_slice($itens, $i, $loteSize);
            $retorno = $this->enviar_lote_para_api($loteItens);

            $enviados = $enviados + $retorno['enviados'];
            if($retorno['mensagem_erro_log'] != ""){
                $mensagem_erro_log .= ' --- Ciclo: '. $count.' - Mensagem de erro: '.  $retorno['mensagem_erro_log'] . PHP_EOL;
            }
            $count ++;
        }

        return array('enviados' => $enviados, 'mensagem_erro_log' => $mensagem_erro_log);
    }
    
    private function enviar_lote_para_api($loteItens) {

        $this->load->model('cad_item_wf_atributo_model');
        $mensagem_erro_log = "";
        $urlCreate = 'https://hml-catalogo-produtos-api.becomex.com.br/api/app/produto-versao';
        $urlIntegrar = 'https://hml-catalogo-produtos-api.becomex.com.br/api/app/produto-versao/integrar';
        $token = $this->obter_token();
    
        $data = [];
        foreach ($loteItens as $item) {
            $data[] = [
                "cpfCnpjRaiz" => str_replace(['.', '/', '-'], '', $item->cpfCnpjRaizRaiz),
                "descricao" => $item->descricao,
                "denominacao" => $item->denominacao,
                "modalidade" => $item->modalidade,
                "ncm" => $item->ncm,
                "atributos" => $item->atributos,
                "atributosCompostos" => $item->atributosCompostos,
                "atributosMultivalorados" => $item->atributosMultivalorados,
                "codigosInterno" => $item->codigosInterno
            ];
        }

        // Enviar para o primeiro endpoint (createProdutoVersao)
        $ch = curl_init($urlCreate);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'Authorization: Bearer ' . $token
        ));
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            $error_msg = curl_error($ch);
            $this->debug("Erro ao enviar lote para API: $error_msg");
            $mensagem_erro_log = "Erro ao enviar lote para API: $error_msg";
            curl_close($ch);
            return;
        } else {
            $this->debug("Resposta da API: $response");
            $result = json_decode($response, true);
        }
        curl_close($ch);

        // Verificar o retorno e enviar para o segundo endpoint (integrarProdutoVersao)
        if (isset($result) && is_array($result) && !empty($result)) {
            $idsVersoes = array_column($result, 'id');  
    
            $dataIntegrar = array(
                "produtoVersoes" => $idsVersoes
            );
    
            $ch = curl_init($urlIntegrar);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/json',
                'Authorization: Bearer ' . $token
            ));
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($dataIntegrar));
    
            $responseIntegrar = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            if (curl_errno($ch)) {
                $error_msg = curl_error($ch);
                $this->debug("Erro ao enviar lote para API de integração: $error_msg");
                $mensagem_erro_log = "Erro ao enviar lote para API de integração: $error_msg";
            } else {
                $this->debug("Resposta da API de integração: $httpCode");
                
                // Verificar se a resposta de integração foi bem-sucedida
                $enviados = [];
                if ($httpCode == 202) {
                    $enviados = $this->cad_item_wf_atributo_model->cron_atualizacao_status_itens_enviados_integracao($loteItens);
                } else {
                    $this->debug("Erro: Integração não foi bem-sucedida");
                    $mensagem_erro_log = " Erro: Integração não foi bem-sucedida";
                }
            }
            curl_close($ch);
        } else {
            $this->debug("Erro: Retorno do primeiro endpoint está vazio ou não é válido");
            $mensagem_erro_log = "Erro: Retorno do primeiro endpoint está vazio ou não é válido";
        }

        return array('enviados' => $enviados, 'mensagem_erro_log' => $mensagem_erro_log);
    }
    
    private function obter_token() {
        $url = 'https://login.becomex.com.br/auth/realms/becomex/protocol/openid-connect/token';
        $data = array(
            'client_id' => config_item('wf_client_id'),
            'client_secret' => config_item('wf_client_secret'),
            'grant_type' => 'client_credentials',
            'scope' => 'openid'
        );
    
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/x-www-form-urlencoded'));
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            $error_msg = curl_error($ch);
            $this->debug("Erro ao obter token: $error_msg");
            return null;
        } else {
            $result = json_decode($response, true);
            return $result['access_token'];
        }
        curl_close($ch);
    }
}