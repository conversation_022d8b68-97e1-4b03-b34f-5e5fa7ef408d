<?php

class Apuracao_Ganhos extends MY_Controller
{
    private $sort_fields;

    public function __construct()
    {
        parent::__construct();

        $this->load->model('apuracao_ganhos_model');

        $this->sort_fields =  array('cnpj', 'descricao', 'dat_inicio', 'dat_fim', 'dat_criacao');
    }

    public function index()
    {
        $this->load->helper('formatador_helper');
        $this->load->library('pagination');

        $sort = $this->input->get('sort');

        if ((!empty($sort['order']) && !empty($sort['order'])) &&
            in_array($sort['field'], $this->sort_fields) &&
            in_array($sort['order'], array('asc', 'desc'))
        ) {
            $this->apuracao_ganhos_model->set_state('filter.sort', $sort);
        } else {
            $sort = array(
                'field' => 'dat_criacao',
                'order' => 'desc'
            );
        }

        $page = $this->input->get('per_page');

        $limit    = 10;
        $offset   = ($page > 0 ? $page-1 : 0);

        $config['base_url']          = base_url("beneficios/apuracao-de-ganhos");
        $config['per_page']          = $limit;
        $config['total_rows']        = $this->apuracao_ganhos_model->get_total_entries();
        $config['num_links']         = 5;
        $config['page_query_string'] = TRUE;
        $config['use_page_numbers']  = TRUE;

        $this->pagination->initialize($config);

        $data['entries'] = $this->apuracao_ganhos_model->get_entries(
            $limit,
            $offset
        );

        $data['sort']         = $sort;
        $data['valid_fields'] = $this->sort_fields;

        $this->render('beneficios/apuracao-de-ganhos', $data);
    }

    public function download($id_fechamento)
    {
        if (!($fechamento = $this->apuracao_ganhos_model->get_entry($id_fechamento))) {
            show_404();
        }

        $arquivo = config_item('upload_ganhos_path') . $fechamento->nome_hash;

        if (!file_exists($arquivo)) {
            show_404();
        }

        $this->load->helper('download');

        $data = file_get_contents($arquivo);
        $nome = $fechamento->nome_arquivo;

        force_download($nome, $data);
    }
}
