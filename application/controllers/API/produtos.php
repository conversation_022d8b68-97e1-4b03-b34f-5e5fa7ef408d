<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

require APPPATH . '/libraries/REST_Controller.php';

class Produtos extends REST_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model(array(
            'produtos_model',
            'empresa_model'
        ));
    }

    private function apply_default_filter($idEmpresa = '')
    {
        if ($this->input->get('codigoInterno')) {
            $this->produtos_model->set_state('filter.part_number', $this->input->get('codigoInterno'));
        }

        if ($this->input->get('descricao')) {
            $this->produtos_model->set_state('filter.descricao', $this->input->get('descricao'));
        }

        if ($this->input->get('ncm')) {
            $this->produtos_model->set_state('filter.ncm', $this->input->get('ncm'));
        }

        if ($this->input->get('ultimaAlteracaoInicio')) {
            $this->produtos_model->set_state('filter.ultimaAlteracaoInicio', $this->input->get('ultimaAlteracaoInicio'));
        }

        if ($this->input->get('ultimaAlteracaoFim')) {
            $this->produtos_model->set_state('filter.ultimaAlteracaoFim', $this->input->get('ultimaAlteracaoFim'));
        }

        if ($this->input->get('modalidade')) {
            $this->produtos_model->set_state('filter.modalidade', $this->input->get('modalidade'));
        }

        if (!empty($idEmpresa)) {
            if (is_array($idEmpresa)) {
                $this->produtos_model->set_state('filter.idEmpresas', $idEmpresa);
            } else {
                $this->produtos_model->set_state('filter.idEmpresa', $idEmpresa);
            }
        }
    }

    public function buscar_get()
    {
        try {
            $arrIdEmpresa = array();

            if (!empty($this->input->get('cpfCnpjRaiz'))) {
                $empresas = $this->empresa_model->get_entry_by_cnpj_raiz($this->input->get('cpfCnpjRaiz'));

                foreach($empresas as $empresa) {
                    $arrIdEmpresa[] = $empresa->id_empresa;
                }
            }

            if (empty($arrIdEmpresa) || empty($this->input->get('cpfCnpjRaiz'))) {
                return response_json(array(
                    'error' => true,
                    'msg' => 'É obrigatório informar o campo de CPF/CNPJ',
                    'data' => array()
                ), 400);
            }

            $this->apply_default_filter($arrIdEmpresa);

            $entries = $this->produtos_model->pesquisar();

            return response_json(array(
                "msg" => "Lista de produtos",
                "data" => $this->preencher_json($entries)
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                "msg" => "Não foi possível realizar a ação solicitada",
                "error" => true,
                "data" => array()
            ), 400);
        }
    }

    public function preencher_json($items = null) 
    {
        $data = array();
        $atributos = array();

        foreach ($items as $row) {

            if ($row->Muda == 'N') {
                array_push($atributos, array(
                    'atributo' => $row->atributo,
                    'valor' => $row->codigo,
                    'descricao' => $row->desc_attr,
                    'apresentacao' => $row->apresentacao,
                    'atualizado_em' => $row->atualizado_em
                ));
            } else {
                array_push($atributos, array(
                    'atributo' => $row->atributo,
                    'valor' => $row->codigo,
                    'descricao' => $row->desc_attr,
                    'apresentacao' => $row->apresentacao,
                    'atualizado_em' => $row->atualizado_em
                ));

                array_push($data, array(
                    'denominacao' => !empty($row->descricao) ? substr($row->descricao, 0, 100) : '',
                    'descricao' => $row->descricao,
                    'cpfCnpjRaizRaiz' => $row->cnpj,
                    'situacao' => "Rascunho",
                    'modalidade' => "AMBOS",
                    'ncm' => $row->ncm,
                    'atributos' => $atributos,
                    'codigosInterno' => array($row->part_number)
                )); 
                
                $atributos = array();
            }             
        }

        return $data;
    }
}
