<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

if (!function_exists('get_brightness'))
{
    function get_brightness($hex) {
        $hex = str_replace('#', '', $hex);

        $c_r = hexdec(substr($hex, 0, 2));
        $c_g = hexdec(substr($hex, 2, 2));
        $c_b = hexdec(substr($hex, 4, 2));

        return (($c_r * 299) + ($c_g * 587) + ($c_b * 114)) / 1000;
    }
}

class Dashboard extends MY_Controller
{
    public function __construct() {
        parent::__construct();

        $this->load->library('breadcrumbs');

        if (!is_logged()) {
            redirect('/login');
        }

        if(!has_role('dashboard')) {
            show_permission();
        }
    }

    public function index()
    {
        $data = array();

        $id_empresa = sess_user_company();

        $this->load->model('dashboard_model');
        $this->load->model('ncm_capitulo_grupos_model');

        $capitulos = $this->dashboard_model->get_lista_capitulos();
        $data['capitulos'] = $capitulos;

        $grupos = $this->ncm_capitulo_grupos_model->get_entries();

        $data['grupos'] = $grupos;

        $totais_por_capitulo = $this->dashboard_model->get_totais_por_capitulo($id_empresa);

        $resumo = array();

        foreach ($totais_por_capitulo as $result) {
            $capitulo = sprintf("%02d", $result->capitulo);
            $resumo[$capitulo] = array();
            $resumo[$capitulo]['total_itens'] = $result->total_itens;
            $resumo[$capitulo]['total_grupos'] = $result->total_grupos;
        }

        $data['resumo'] = $resumo;

        $this->breadcrumbs->push('Home', '/');

        $this->render('dashboard/default', $data);
    }

    public function view($capitulo)
    {
        $data = array();

        $id_empresa = sess_user_company();

        $this->load->model('dashboard_model');
        $this->load->model('ncm_capitulo_grupos_model');

        $data['capitulo'] = $capitulo;

        $capitulo_info = $this->dashboard_model->get_capitulo_by_id($capitulo);
        $data['capitulo_info'] = $capitulo_info;

        $data['secao'] = $this->ncm_capitulo_grupos_model->get_entry($capitulo_info->id_agrupamento);

        $grupos = $this->dashboard_model->get_grupos_por_capitulo($id_empresa, $capitulo);
        $data['grupos'] = $grupos;

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Capítulo '.$capitulo, '/view/'.$capitulo);

        $this->render('dashboard/view', $data);
    }
}

