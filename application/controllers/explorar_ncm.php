<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Explorar_ncm extends MY_Controller {

	public function __construct() {
		parent::__construct();

		if (!is_logged()) {
			redirect('/login');
		}

        if (!has_role('ncm_explorar')) {
            show_permission();
        }

        $this->load->library('breadcrumbs');
	}

	public function index()
	{
        $data = array();

        $this->load->model('ncm_model');
        $this->load->model('ncm_capitulo_model');


        if (!empty($this->input->post('reset_filter')))
        {
            $this->ncm_model->clear_states();
        }
        else
        {
            if ($this->input->post('submit')) {

    			$this->load->library('form_validation');

    			$this->form_validation->set_rules('ncm', 'NCM', 'trim|required|min_length[4]|max_length[6]');

                if ($this->form_validation->run() !== FALSE) {
                    $itens = $this->ncm_model->get_ncm_treeview($this->input->post('ncm'));
                    $data['itens'] = $itens;
                    $data['itens_historico_2012'] = $this->ncm_model->get_ncm_treeview($this->input->post('ncm'), '2012');
                    $data['itens_historico_2017'] = $this->ncm_model->get_ncm_treeview($this->input->post('ncm'), '2017');

                } else {
    				$err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
    				$this->message_on_render($err, 'error');
                }
            }
        }

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push("Explorar NCM's", '/explorar_ncm/');

		$this->render('explorar_ncm', $data);
	}

    public function ajax_get_cest_by_ncm()
    {
        if ($post = $this->input->post())
        {
            $ncm = $post['ncm'];

            $this->load->model('cest_model');

            $cest_itens = $this->cest_model->get_entries_by_ncm($ncm);

            echo json_encode($cest_itens);
            return TRUE;
        }
    }
}

