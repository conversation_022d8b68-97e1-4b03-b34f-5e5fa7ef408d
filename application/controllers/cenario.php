<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class GraphInfo {
    public $capitulo;
    public $descricao;
    public $total_atual;
    public $total_proposto;

    public function __construct($capitulo, $descricao, $total_atual, $total_proposto)
    {
        $this->capitulo         = $capitulo;
        $this->descricao        = $descricao;
        $this->total_atual      = $total_atual;
        $this->total_proposto   = $total_proposto;
    }
}

class Cenario extends MY_Controller {

	public function __construct() {
		parent::__construct();

		if (!is_logged()) {
			redirect('/login');
		}

        $this->load->library('breadcrumbs');

        if (!has_role('ncm_cenario')) {
            show_permission();
        }
	}

    // Este método irá apresentar os grupos associados a este capítulo
    public function view($cap, $tipo = 'original')
    {
        $data = array();

        $this->load->model(array('ncm_model', 'grupo_tarifario_model'));

        try {
            $data['ncm'] = $this->ncm_model->get_entry($cap);
        } catch (Exception $e) {
            show_error($e->getMessage());
        }

        $data['entries'] = $this->grupo_tarifario_model->get_entries_by_cap_ncm($cap, $tipo);

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Cenário', '/cenario/');
        $this->breadcrumbs->push('Detalhes do capítulo', '/cenario/view/');

        $this->render('cenario/view', $data);
    }

	public function index()
	{
        $data = array();

        $this->load->helper('text');
        $this->load->model('ncm_model');
        $this->load->model('cad_item_model');

        $q_lista_atual      = $this->ncm_model->get_capitulos_base_itens('atual');
        $q_lista_proposto   = $this->ncm_model->get_capitulos_base_itens('proposto');

        $lista = $q_lista = array();

        // array merge - mantendo chaves ;)
        foreach (array($q_lista_atual, $q_lista_proposto) as $arg) {
            foreach ((array) $arg as $k => $v) {
                $q_lista[$v->capitulo] = $v;
            }
        }

        foreach ($q_lista as $k => $item)
        {
            $descricao      = $item->capitulo . ' - ' . addslashes(word_limiter($item->descricao, 3, '...'));

            $total_atual    = $this->cad_item_model->get_total_by_ncm($item->capitulo, 'atual');
            $total_proposto = $this->cad_item_model->get_total_by_ncm($item->capitulo, 'proposto');

            $lista[$k] = new GraphInfo($item->capitulo, $descricao, $total_atual, $total_proposto);
        }

        ksort($lista);

        $data['lista'] = $lista;

        $this->include_js('highcharts/highcharts.js');
        $this->include_js('highcharts/highcharts-3d.js');
        $this->include_js('highcharts/modules/exporting.js');

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Cenário', '/cenario/');

		$this->render('cenario/default', $data);
	}

}

