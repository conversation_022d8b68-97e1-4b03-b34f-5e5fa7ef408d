<?php defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * API Integração SimplusTEC
 *
 * NCM Restful API Server
 *
 * @package     CodeIgniter
 * @subpackage  Rest Server
 * @category    Controller
 * <AUTHOR> <PERSON>
*/

// This can be removed if you use __autoload() in config.php OR use Modular Extensions
require APPPATH.'/libraries/REST_Controller.php';

class Simplus extends REST_Controller
{
    /**
    * @api {post} /simplus/novo_item Insere novo item
    * @apiVersion 0.0.1
    * @apiName novo_item
    * @apiGroup item
    *
    * @apiParam {String} cnpj CNPJ da Empresa (sem pontuação)
    * @apiParam {String} gtin GTIN
    * @apiParam {String} descricao Descrição do Item
    * @apiParam {String} ncm NCM do Item
    *
    * @apiSuccess {String} status Status
    */
    public function novo_post()
    {

    }

    /**
    * @api {post} /simplus/homologacao Sincroniza homologação
    * @apiVersion 0.0.1
    * @apiName homologacao
    * @apiGroup item
    *
    * @apiParam {String} cnpj CNPJ da Empresa (sem pontuação)
    * @apiParam {String} gtin GTIN
    * @apiParam {String} nome_usuario Nome do usuário
    * @apiParam {String} status_homologacao Status da homologação (reprovado, homologado, inativo)
    * @apiParam {Sotivo} motivo Motivo
    *
    * @apiSuccess {String} status Status
    */
    public function homologacao_post()
    {

    }
}