<?php defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * API NCM
 *
 * NCM Restful API Server
 *
 * @package     CodeIgniter
 * @subpackage  Rest Server
 * @category    Controller
 * <AUTHOR> Decks
*/

// This can be removed if you use __autoload() in config.php OR use Modular Extensions
require APPPATH.'/libraries/REST_Controller.php';

class Ncm extends REST_Controller
{
    function index_post()
    {
        $ncm = $_POST['ncm'];

        $this->load->model('ncm_model');
        $this->load->model('ncm_capitulo_model');
        $this->load->model('ncm_capitulo_grupos_model');

        $results = $this->ncm_model->get_entries_levels($ncm);

        try {

            $codigo_capitulo = ltrim($results[0]->cod_ncm, 0);
            $capitulo = $this->ncm_capitulo_model->get_entry($codigo_capitulo);

            $arr_result['secao'] = $this->ncm_capitulo_grupos_model->get_entry($capitulo->id_agrupamento);

        } catch (Exception $e) {
            $capitulo = 'Capitulo Inexistente';
            $secao = '';
        }

        $arr_result['ncm_entries'] = $results;
        $filter_result = array_filter($arr_result);

        if (!empty($filter_result))
        {
            $this->response(array('status' => 'success', 'return' => $arr_result), 200);
        } else
        {
            $this->response(array('status' => 'error', 'message' => 'Nenhuma NCM encontrada!'), 200);
        }
    }
}