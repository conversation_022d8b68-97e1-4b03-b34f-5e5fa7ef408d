<?php defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * API Integração SimplusTEC
 *
 * NCM Restful API Server
 *
 * @package     CodeIgniter
 * @subpackage  Rest Server
 * @category    Controller
 * <AUTHOR>
*/

// This can be removed if you use __autoload() in config.php OR use Modular Extensions
require APPPATH.'/libraries/REST_Controller.php';

class Item extends REST_Controller
{
    /**
    * @api {post} /ws/simplus/item Insere/atualiza um item
    * @apiVersion 0.0.1
    * @apiName index
    * @apiGroup item
    *
    * @apiParam {String} cnpj CNPJ da Empresa (sem pontuação)
    * @apiParam {String} companyName Razão Social da Empresa
    * @apiParam {String} gtin GTIN
    * @apiParam {String} description Descrição do Item
    * @apiParam {String} ncm NCM do Item
    * @apiParam {String} url URL de visualização do Item
    * @apiParam {Integer} requestReason 1, 2, 3
    *
    * @apiSuccessExample Success-Response:
    *     HTTP/1.1 200 OK
    *
    * @apiError ItemAlreadyExists Item já cadastrado para o CNPJ.
    * @apiError ItemNotFound Item não encontrado para o CNPJ.
    * @apiError EmptyCNPJ CNPJ não informado.
    * @apiError InvalidCNPJ CNPJ inválido.
    * @apiError EmptyGTIN GTIN não informado.
    * @apiError EmptyDescription Descrição do item não informado.
    * @apiError EmptyNCM NCM do item não informado.
    * @apiError InvalidRequestReason Ação inválida.
    * @apiError EmptyRequest Request sem dados.
    *
    * @apiErrorExample Error-Response:
    *     HTTP/1.1 400 Bad Request
    *     {
    *       "error": "ItemAlreadyExists"
    *       "message": "Item já cadastrado para o CNPJ."
    *     }
    */
    private $requestReasonList = array(
        1 => 'Produto Novo',
        2 => 'Produto Alterado',
        3 => 'Nova Versão',
        4 => 'Manter informações'
    );

    private $statusList = array(
        1 => 'Homologado',
        2 => 'Reprovado',
        3 => 'Inativado',
        4 => 'Reativado',
        5 => 'Reprovado'
    );

    public function index_post()
    {

        $this->load->helper('formatador');
        
        // Post data
        if ($this->input->post() === false)
        {
            $this->response(array('error' => 'EmptyRequest', 'message' => ''), 400);
            return false;
        }

        $post = array_map('trim', $this->input->post());

        // Empresa Simplus
        $this->db->where('integracao_simplus', 1);
        $query = $this->db->get('empresa', 1);

        $empresa = $query->row();

        $cnpj          = null;
        $gtin          = null;
        $description   = null;
        $requestReason = null;
        $ncm           = null;
        $brand         = null;
        $url           = null;
        $category = null;
        $companyName = null;
        $countryOfOrigin = null;
        $netContent = null;
        $netContentUnitMeasure = null;
        $ingredients = null;
        $useMode = null;
        $alcoholContent = null;
        $composition = null;
        $totalFatPercent = null;
        $productMaterial = null;
        $integralProduct = null;
        $type = null;
        $midias = null;

        extract($post, EXTR_IF_EXISTS);

        // Validações
        if (empty($cnpj)) {
            $this->response(array('error' => 'EmptyCNPJ', 'message' => 'CNPJ não informado.'), 400);
            return false;
        }

        if (!empty($empresa->estabelecimento_regex) && !preg_match($empresa->estabelecimento_regex, $cnpj)) {
            $this->response(array('error' => 'InvalidCNPJ', 'message' => 'CNPJ inválido.'), 400);
            return false;
        }

        if (empty($gtin)) {
            $this->response(array('error' => 'EmptyGTIN', 'message' => 'GTIN não informado.'), 400);
            return false;
        }

        if (empty($description)) {
            $this->response(array('error' => 'EmptyDescription', 'message' => 'Descrição não informada.'), 400);
            return false;
        }

        if (empty($requestReason) || ! array_key_exists($requestReason, $this->requestReasonList)) {
            $this->response(array('error' => 'InvalidRequestReason', 'message' => 'Ação inválida.'), 400);
            return false;
        }

        // Inserir novo item
        if ($requestReason == 1)
        {
            $this->db->where('id_empresa', $empresa->id_empresa);
            $this->db->where('part_number', $gtin);
            $this->db->where('estabelecimento', $cnpj);
            $query = $this->db->get('item', 1);

            if ($query->num_rows() == 1) {
                $this->response(array('error' => 'ItemAlreadyExists', 'message' => 'Item já existente.'), 400);
                return false;
            }

            // Usuário Fiscal
            $this->db->where('id_empresa', $empresa->id_empresa);
            $this->db->where('id_perfil', 2);
            $query = $this->db->get('usuario', 1);

            if ($query->num_rows() == 0)
            {
                $this->response(array('error' => 'UserNotFound', 'message' => 'Usuário fiscal não encontrado.'), 400);
                return false;
            }

            $fiscal = $query->row();

            // Usuário Técnico
            $this->db->where('id_empresa', $empresa->id_empresa);
            $this->db->where('id_perfil', 3);
            $query = $this->db->get('usuario', 1);

            if ($query->num_rows() == 0)
            {
                $this->response(array('error' => 'UserNotFound', 'message' => 'Usuário técnico não encontrado.'), 400);
                return false;
            }

            $tecnico = $query->row();

            $dbdata = array(
                'id_empresa'         => $empresa->id_empresa,
                'part_number'        => $gtin,
                'estabelecimento'    => $cnpj,
                'descricao'          => $description,
                'ncm'                => $ncm,
                'id_resp_fiscal'     => $fiscal->id_usuario,
                'id_resp_engenharia' => $tecnico->id_usuario,
                'tag'                => strtoupper(remove_acentos($category))
            );

            $this->db->insert('item', $dbdata);

            $dbdata2 = array(
                'part_number' => $gtin,
                'estabelecimento' => $cnpj,
                'categoria_produto' => $category,
                'empresa_nome' => $companyName,
                'pais_origem' => $countryOfOrigin,
                'conteudo_liquido' => $netContent,
                'unid_med_conteudo_liquido' => $netContentUnitMeasure,
                'ingredientes' => $ingredients,
                'modo_de_uso' => $useMode,
                'teor_alcoolico' => $alcoholContent,
                'composicao' => $composition,
                'gorduras_totais' => $totalFatPercent,
                'material_do_produto' => $productMaterial,
                'produto_integral' => $integralProduct,
                'tipo' => $type,
                'midias' => $midias
            );

            $this->db->insert('item_extra_simplus', $dbdata2);

            // Log
            $log  = 'Item cadastrado.';
            $log .= '<br><strong>Descrição:</strong> '  . $description;
            $log .= '<br><strong>NCM:</strong> '        . ($ncm   ? $ncm   : '<em>Não informado</em>');
            $log .= '<br><strong>Marca:</strong> '      . ($brand ? $brand : '<em>Não informado</em>');
            $log .= '<br><strong>Categoria do Produto:</strong>' . ($category ? $category : '<em>Não informado</em>') ;
            $log .= '<br><strong>Nome da Empresa:</strong>' . ($companyName ? $companyName : '<em>Não informado</em>') ;
            $log .= '<br><strong>País de Origem:</strong>' . ($countryOfOrigin ? $countryOfOrigin : '<em>Não informado</em>') ;
            $log .= '<br><strong>Conteúdo Líquido:</strong>' . ($netContent ? $netContent : '<em>Não informado</em>') ;
            $log .= '<br><strong>Unidade de Medida do Conteúdo Líquido:</strong>' . ($netContentUnitMeasure ? $netContentUnitMeasure : '<em>Não informado</em>') ;
            $log .= '<br><strong>Ingredientes</strong>' . ($ingredients ? $ingredients : '<em>Não informado</em>') ;
            $log .= '<br><strong>Modo de Uso:</strong>' . ($useMode ? $useMode : '<em>Não informado</em>') ;
            $log .= '<br><strong>Teor Alcoólico (%):</strong>' . ($alcoholContent ? $alcoholContent : '<em>Não informado</em>') ;
            $log .= '<br><strong>Composição:</strong>' . ($composition ? $composition : '<em>Não informado</em>') ;
            $log .= '<br><strong>Gorduras Totais (%):</strong>' . ($totalFatPercent ? $totalFatPercent : '<em>Não informado</em>') ;
            $log .= '<br><strong>Material do Produto:</strong>' . ($productMaterial ? $productMaterial : '<em>Não informado</em>') ;
            $log .= '<br><strong>Produto Integral:</strong>' . ($integralProduct ? $integralProduct : '<em>Não informado</em>') ;
            $log .= '<br><strong>Tipo:</strong>' . ($type ? $type : '<em>Não informado</em>') ;
            $log .= '<br><strong>Mídias:</strong>' . ($midias ? $midias : '<em>Não informado</em>') ;
            if (!empty($url)) {
                $log .= '<br><strong>URL:</strong> <a href="' . $url . '" target="_blank">' . $url . "</a>";
            }
        } else // Atualizar item
        {
            $this->db->where('id_empresa', $empresa->id_empresa);
            $this->db->where('part_number', $gtin);
            $this->db->where('estabelecimento', $cnpj);

            $query = $this->db->get('item', 1);

            if ($query->num_rows() == 0) {
                $this->response(array('error' => 'ItemNotFound', 'message' => 'Item não encontrado.'), 400);
                return false;
            }

            $__where = array(
                'id_empresa' => $empresa->id_empresa,
                'part_number' => $gtin,
                'estabelecimento'   => $cnpj
            );

            // cad_item
            $cad_item_dbdata = array('status_simplus' => 0);
            $this->db->update('cad_item', $cad_item_dbdata, $__where);

            // item
            $item_dbdata = array(
                'descricao'         => $description,
                'ncm'               => $ncm
            );

            $this->db->update('item', $item_dbdata, $__where);

            $log  = 'Item alterado.';
            $log .= '<br><strong>Descrição:</strong> '  . $description;
            $log .= '<br><strong>NCM:</strong> ' . $ncm;
        }

        $data = array(
            'id_empresa'        => $empresa->id_empresa,
            'part_number'       => $gtin,
            'estabelecimento'   => $cnpj,
            'titulo'            => 'simplus',
            'motivo'            => $log,
            'criado_em'         => date("Y-m-d H:i:s")
        );

        $this->db->insert('item_log', $data);

        $this->response(null, 200);
    }

    /**
    * @api {post} /ws/simplus/item/homologacao Sincroniza homologação
    * @apiVersion 0.0.1
    * @apiName homologacao
    * @apiGroup item
    *
    * @apiParam {String} cnpj CNPJ da Empresa (sem pontuação)
    * @apiParam {String} gtin GTIN
    * @apiParam {Integer} status Status da homologação (reprovado, homologado, inativado, reativado)
    * @apiParam {String} user Nome do usuário
    * @apiParam {String} reason Motivo
    *
    * @apiSuccessExample Success-Response:
    *     HTTP/1.1 200 OK
    *
    * @apiError EmptyCNPJ CNPJ não informado.
    * @apiError InvalidCNPJ CNPJ inválido.
    * @apiError EmptyGTIN GTIN não informado.
    * @apiError EmptyUser Usuário não informado.
    * @apiError EmptyReason Motivo não informado.
    * @apiError InvalidStatus Status inválido.
    * @apiError ItemNotFound Item não encontrado para o CNPJ.
    * @apiError EmptyRequest Request sem dados.
    *
    * @apiErrorExample Error-Response:
    *     HTTP/1.1 400 Bad Request
    *     {
    *       "error": "ItemNotFound",
    *       "message": "Item não encontrado para o CNPJ"
    *     }
    */
    public function homologacao_post()
    {
        // Post data
        if ($this->input->post() === false)
        {
            $this->response(array('error' => 'EmptyRequest', 'message' => ''), 400);
            return false;
        }

        $post = array_map('trim', $this->input->post());

        // Empresa Simplus
        $this->db->where('integracao_simplus', 1);
        $query = $this->db->get('empresa', 1);

        $empresa = $query->row();

        $cnpj   = null;
        $gtin   = null;
        $status = null;
        $user   = null;
        $reason = null;

        extract($post, EXTR_IF_EXISTS);

        // Validações
        if (empty($cnpj)) {
            $this->response(array('error' => 'EmptyCNPJ', 'message' => 'CNPJ não informado.'), 400);
            return false;
        }

        if (!empty($empresa->estabelecimento_regex) && !preg_match($empresa->estabelecimento_regex, $cnpj)) {
            $this->response(array('error' => 'InvalidCNPJ', 'message' => 'CNPJ inválido.'), 400);
            return false;
        }

        if (empty($gtin)) {
            $this->response(array('error' => 'EmptyGTIN', 'message' => 'GTIN não informado.'), 400);
            return false;
        }

        if (empty($status) || ! array_key_exists($status, $this->statusList)) {
            $this->response(array('error' => 'InvalidStatus', 'message' => 'Status inválido.'), 400);
            return false;
        }

        if (empty($user)) {
            $this->response(array('error' => 'EmptyUser', 'message' => 'Usuário não informado.'), 400);
            return false;
        }

        if (empty($reason) && $status == 2) {
            $this->response(array('error' => 'EmptyReason', 'message' => 'Motivo não informado.'), 400);
            return false;
        }

        // Cad_item
        $this->db->where('id_empresa', $empresa->id_empresa);
        $this->db->where('part_number', $gtin);
        $this->db->where('estabelecimento', $cnpj);

        $query = $this->db->get('cad_item', 1);

        if ($query->num_rows() == 0)
        {
            $this->response(array('error' => 'ItemNotFound', 'message' => 'Item não encontrado.'), 400);
            return false;
        }

        $cad_item = $query->row();

        // Usuário Fiscal
        $this->db->where('id_empresa', $empresa->id_empresa);
        $this->db->where('id_perfil', 2);
        $query = $this->db->get('usuario', 1);

        if ($query->num_rows() == 0)
        {
            $this->response(array('error' => 'UserNotFound', 'message' => 'Usuário fiscal não encontrado.'), 400);
            return false;
        }

        $fiscal = $query->row();

        // Usuário Técnico
        $this->db->where('id_empresa', $empresa->id_empresa);
        $this->db->where('id_perfil', 3);
        $query = $this->db->get('usuario', 1);

        if ($query->num_rows() == 0)
        {
            $this->response(array('error' => 'UserNotFound', 'message' => 'Usuário técnico não encontrado.'), 400);
            return false;
        }

        $tecnico = $query->row();

        $this->db->where('id_item', $cad_item->id_item);
        $this->db->delete('cad_item_homologacao');

        if ($status == 4) {
            $log_mensagem = "Reativado por ";
        } else
        {
            switch ($status)
            {
                case 1:
                    $status_fixed = 1;
                    $log_mensagem = "Homologado por ";
                break;

                case 2:
                    $status_fixed = 0;
                    $log_mensagem = "Reprovado por ";
                break;

                case 3:
                    $status_fixed = 2;
                    $log_mensagem = "Inativado por ";
                break;

                case 5:
                    $status_fixed = 4;
                    $log_mensagem = "Reprovado por ";
                    $reason = 'Fornecedor aderiu as informações atuais de sua ficha. Não é necessário ação da Becomex.';
                break;
            }

            // Cad_item_homologacao
            $dbdata = array(
                array(
                    'id_item'           => $cad_item->id_item,
                    'id_usuario'        => $fiscal->id_usuario,
                    'tipo_homologacao'  => 'Fiscal',
                    'homologado'        => $status_fixed,
                    'motivo'            => $reason,
                    'criado_em'         => date("Y-m-d H:i:s")
                ),

                array(
                    'id_item'           => $cad_item->id_item,
                    'id_usuario'        => $tecnico->id_usuario,
                    'tipo_homologacao'  => 'Engenharia',
                    'homologado'        => $status_fixed,
                    'motivo'            => $reason,
                    'criado_em'         => date("Y-m-d H:i:s")
                )
            );

            $this->db->insert_batch('cad_item_homologacao', $dbdata);
        }

        // Log
        $log_mensagem .= $user . '<br>';

        if ($status == 5)
            $reason = "Fornecedor aderiu as informações atuais de sua ficha. Não é necessário ação da Becomex.";
            
        $log_mensagem .= "<strong>Motivo:</strong>&nbsp;" . (!empty($reason) ? $reason : '<em>Não informado</em>');

        $dbdata = array(
            'part_number'       => $gtin,
            'estabelecimento'   => $cnpj,
            'id_empresa'        => $empresa->id_empresa,
            'titulo'            => 'simplus',
            'motivo'            => $log_mensagem,
            'criado_em'         => date("Y-m-d H:i:s")
        );

        $this->db->insert('item_log', $dbdata);

        $this->response(null, 200);
    }

    public function response($data = null, $http_code = null, $continue = false)
    {
        // WS Log
        $this->load->model('ws_log_model');

        $json = json_encode($this->_args);
        $log_data = array(
            'origem'        => "SIMPLUS",
            'tipo'          => "INPUT",
            'dados'         => $json,
            'mensagem'      => $data['message'],
            'codigo_http'   => $http_code
        );

        $this->ws_log_model->save_entry($log_data);

        parent::response($data, $http_code, $continue);
    }
}