<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Hierarquia extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }

        if (!has_role('visualizar_hierarquia') && !has_role('gerenciar_hierarquia')) {
            show_permission();
        }

        $this->load->model('empresa_model');
        $this->load->model('hierarquia_model');
        $this->load->model('grupo_tarifario_model');

        $this->load->library('breadcrumbs');

        // ONLY-AJAX Protection
        if (
            preg_match("/^(ajax_).+/i", $this->router->fetch_method())
            && ! $this->input->is_ajax_request()
        ) {
            show_error('Erro.', 500);
        }
    }

    public function index()
    {
        // $categorias        = $this->hierarquia_model->get_entries();
        $categorias = '';

        $this->grupo_tarifario_model->set_state('filter.ativo', 1);
        // $grupos_tarifarios = $this->grupo_tarifario_model->get_entries(null, null, 'descricao ASC');
        $grupos_tarifarios = '';

        $data['categorias'] = $categorias;
        $data['gerenciar']  = $this->hierarquia_model->perm_gerenciar();

        if ($this->hierarquia_model->perm_gerenciar()) {
            $data['grupos_tarifarios'] = $grupos_tarifarios;
        }

        $this->include_js(array(
            'jstree/jstree.min.js',
            'jstree/jstreegrid.js',
            'jquery.slimscroll.min.js',
            'sweetalert.min.js',
            'bootstrap-select/bootstrap-select.min.js',
            'hierarquia/main.js',
        ));

        $this->include_css(array(
            'hierarquia.css',
            'jstree/proton/style.min.css',
            'bootstrap-select/bootstrap-select.min.css',
            'sweetalert.css'
        ));

        $this->render('hierarquia', $data);
    }

    public function ajax_list_categoria()
    {
        $get = $this->input->get();
        if (isset($get) && isset($get['search_category']) && !empty($get['search_category']))
        {
            $data['categorias'] = $this->hierarquia_model->get_entries_search($get['search_category']);
        } else
        {
            $data['categorias'] = $this->hierarquia_model->get_entries();
        }
        $this->load->view('hierarquia/panel-categoria', $data);
    }

    public function ajax_add_categoria()
    {
        if (! $this->hierarquia_model->perm_gerenciar())
            show_404();

        $this->load->view('hierarquia/modal-addedit-categoria');
    }

    public function ajax_copy_categoria()
    {
        if (! $this->hierarquia_model->perm_gerenciar())
            show_404();

        $this->load->model('empresa_model');

        $id_categoria = $this->input->post('id_categoria');

        $data['categoria'] = $this->hierarquia_model->get_entry($id_categoria);
        $data['empresas']  = $this->empresa_model->get_all_entries();

        $this->load->view('hierarquia/modal-copy-categoria', $data);
    }

    public function ajax_edit_categoria()
    {
        if (! $this->hierarquia_model->perm_gerenciar())
            show_404();

        $id_categoria = $this->input->post('id_categoria');

        $data['categoria'] = $this->hierarquia_model->get_entry($id_categoria);
        $this->load->view('hierarquia/modal-addedit-categoria', $data);
    }

    public function ajax_save_categoria()
    {
        if (! $this->hierarquia_model->perm_gerenciar())
            show_404();

        header('Content-Type: application/json');

        $tipo         = null;
        $nome         = null;
        $descricao    = null;
        $ncm          = null;
        $id_categoria = null;

        $post = $this->input->post();

        extract($post, EXTR_IF_EXISTS);

        $dbdata = array(
            'nome'      => $nome,
            'descricao' => $descricao,
            'ncm'       => $ncm
        );

        if (! $id_categoria) $dbdata['bom'] = (int) $tipo;

        try {
            $this->hierarquia_model->save_categoria($dbdata, (int) $id_categoria);
            echo json_encode(array(
                'status'       => 1,
                'message'      => 'Sucesso!',
                'id_categoria' => $id_categoria
            ));
        } catch (Exception $e) {
            echo json_encode(array('status' => 0, 'message' => $e->getMessage()));
        }
    }

    public function ajax_check_ncm_exists()
    {
        if (! $this->hierarquia_model->perm_gerenciar())
            show_404();

        header('Content-Type: application/json');

        $this->load->model('ncm_model');
        $ncm = $this->input->post('ncm');

        $check = (int) $this->ncm_model->check_ncm_exists($ncm);

        echo json_encode(array('status' => $check));
    }

    public function ajax_save_copy_categoria()
    {
        if (! $this->hierarquia_model->perm_gerenciar())
            show_404();

        header('Content-Type: application/json');

        $post = $this->input->post();

        $id_categoria = null;
        $id_empresa   = null;

        extract($post, EXTR_IF_EXISTS);

        // Resgata informações da empresa-alvo
        $empresa           = $this->empresa_model->get_entry($id_empresa);

        // Resgata as categorias existentes para a empresa.
        $categorias_atuais = $this->hierarquia_model->resgatar_categorias_atuais($id_empresa);

        // Gera a árvore no formato original linear
        $arvore_linear     = $this->hierarquia_model->gerar_hierarquia_linear($id_categoria, $id_empresa, $categorias_atuais);

        // Mapeia os novos IDs
        $remapeamento      = $this->hierarquia_model->remapeamento_hierarquia($arvore_linear, $id_empresa);

        // Resultado da Cópia
        $resultados        = $this->hierarquia_model->resgatar_resultado_copia();

        $alert = 'success';
        $message = '<p><b>Sucesso!</b> Todas as categorias e suas relações foram importadas para a empresa <b>' . $empresa->nome_fantasia . '</b>.</p>';

        if (count($resultados))
        {
            $alert = 'warning';

            $message .= '<p>O sistema encontrou ' . count($resultados) . ' conflitos nas categorias e as descrições foram alteradas automaticamente. Veja a lista abaixo das alterações:</p>';
            $message .= '<table class="table table-condensed table-alert table-hover"><tr><th>Descrição</th><th>Nova Descrição</th></tr>';

            foreach ($resultados as $resultado)
            {
                $message .= '
                <tr>
                    <td>' . $resultado['nome_original'] . '</td>
                    <td>' . $resultado['nome_novo'] . '</td>
                </tr>';
            }

            $message .= '</table>';
        }

        $this->message_next_render($message, $alert);

        echo json_encode(array('status' => 1));
    }

    public function ajax_del_categoria()
    {
        if (! $this->hierarquia_model->perm_gerenciar())
            show_404();

        header('Content-Type: application/json');

        $id_categoria = $this->input->post('id_categoria');
        $ret = $this->hierarquia_model->del_categoria($id_categoria);

        echo json_encode(array('status' => 1, 'message' => 'Sucesso!'));
    }

    public function ajax_get_hierarquia()
    {
        header('Content-Type: application/json');

        $id_categoria = $this->input->post('id_categoria');
        $opcoes = $this->input->post('opcoes');

        if (! ($categoria = $this->hierarquia_model->get_entry($id_categoria) )) {
            echo json_encode(array('status' => 0, 'message' => 'Erro interno.'));
            return false;
        }

        if (isset($opcoes['show_ncm'])) {
            $show_ncm = ($opcoes['show_ncm'] === 'true' ? true : false);
            $this->hierarquia_model->set_state('filter.show_ncm', $show_ncm);
        }

        if (isset($opcoes['show_desc'])) {
            $show_desc = ($opcoes['show_desc'] === 'true' ? true : false);
            $this->hierarquia_model->set_state('filter.show_desc', $show_desc);
        }

        $arvore     = $this->hierarquia_model->generate_tree($id_categoria);
        $restricoes = $this->hierarquia_model->get_relacoes($id_categoria);
        $flag_bom   = $categoria->bom;

        echo json_encode(array(
            'status'     => 1,
            'arvore'     => $arvore,
            'restricoes' => $restricoes,
            'flag_bom'   => $flag_bom
        ));
    }

    public function ajax_save_hierarquia()
    {
        if (! $this->hierarquia_model->perm_gerenciar())
            show_404();

        header('Content-Type: application/json');

        $dbdata = array();
        $hierarquia = $this->input->post('hierarquia');

        foreach ($hierarquia as $node)
        {
            if ($node["parent"] == "#")
            {
                $categoria_raiz = $this->hierarquia_model->get_entry($node["data"]["id_categoria"]);
                $id_categoria_raiz = ($categoria_raiz->bom == 1 ? $categoria_raiz->id_categoria : 0);
            } else
            {
                if ($categoria_raiz->bom == 1 && $node["data"]["id_categoria_raiz"] == 0) {
                    continue;
                }

                $_key = array_search(
                    $node["parent"],
                    array_column($hierarquia, 'id')
                );

                $parent = $hierarquia[$_key];

                $item = array();

                $item["id_categoria"]      = $node["data"]["id_categoria"];
                $item["id_categoria_pai"]  = $parent["data"]["id_categoria"];
                $item["id_categoria_raiz"] = $id_categoria_raiz;

                $dbdata[] = $item;
            }
        }

        // Remover duplicados
        $dbdata = array_map("unserialize", array_unique(
            array_map("serialize", $dbdata)
        ));

        $this->hierarquia_model->save_tree($dbdata);

        echo json_encode(array('status' => 1, 'root_id_categoria' => $id_categoria_raiz));
    }

    public function ajax_del_hierarquia()
    {
        if (! $this->hierarquia_model->perm_gerenciar())
            show_404();

        header('Content-Type: application/json');

        $id_categoria_raiz  = null;
        $nodes              = null;

        $post = $this->input->post();

        extract($post, EXTR_IF_EXISTS);

        $this->hierarquia_model->del_node_tree($id_categoria_raiz, $nodes);

        echo json_encode(array('status' => 1));
    }

    public function ajax_list_grupo_tarifario()
    {
        $get = $this->input->get();

        if (isset($get) && isset($get['search_grouptariff']) && !empty($get['search_grouptariff']))
        {
            $data['grupos_tarifarios'] = $this->grupo_tarifario_model->get_entries_search($get['search_grouptariff'], 'descricao ASC');
        } else
        {
            $data['grupos_tarifarios'] = $this->grupo_tarifario_model->get_entries(null, null, 'descricao ASC');
        }

        $this->load->view('hierarquia/panel-grupo-tarifario', $data);
    }

    public function ajax_save_grupo_tarifario()
    {
        if (! $this->hierarquia_model->perm_gerenciar())
            show_404();

        header('Content-Type: application/json');

        $id_categoria_raiz    = null;
        $id_categoria         = null;
        $id_grupo_tarifario   = null;

        $post = $this->input->post();

        extract($post, EXTR_IF_EXISTS);

        $this->hierarquia_model->save_grupo_tarifario($id_categoria, $id_categoria_raiz, $id_grupo_tarifario);

        echo json_encode(array('status' => 1));
    }

    public function ajax_update_grupo_tarifario()
    {
        if (! $this->hierarquia_model->perm_gerenciar())
            show_404();

        header('Content-Type: application/json');

        $id_categoria_raiz    = null;
        $id_categoria         = null;
        $id_grupo_tarifario   = null;

        $post = $this->input->post();

        extract($post, EXTR_IF_EXISTS);

        $this->hierarquia_model->update_grupo_tarifario($id_categoria, $id_categoria_raiz, $id_grupo_tarifario);

        echo json_encode(array('status' => 1));
    }

    public function ajax_del_grupo_tarifario()
    {
        if (! $this->hierarquia_model->perm_gerenciar())
            show_404();

        header('Content-Type: application/json');

        $id_categoria_raiz    = null;
        $id_categoria         = null;
        $id_grupo_tarifario   = null;

        $post = $this->input->post();

        extract($post, EXTR_IF_EXISTS);

        $this->hierarquia_model->del_grupo_tarifario($id_categoria, $id_categoria_raiz, $id_grupo_tarifario);

        echo json_encode(array('status' => 1));
    }
}