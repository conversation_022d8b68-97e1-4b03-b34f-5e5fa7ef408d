<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Comparar_ncm extends MY_Controller {

	public function __construct() {
		parent::__construct();

		if (!is_logged()) {
			redirect('/login');
		}

        if (!has_role('ncm_comparar')) {
            show_permission();
        }
        
        $this->load->library('breadcrumbs');
	}

	public function index()
	{
        $this->load->model('ncm_model');
        $this->load->model('ncm_capitulo_model');
        $this->load->model('ncm_capitulo_grupos_model');
        $this->load->model('detalhamento_aliquota_model');

        $data = array();

		if ($this->input->post('submit')) {

			$ncms = str_replace(array("\t", "\r\n", "\s", "\n", "."), array(' ', ' ', ' ', ' ', ''), $this->input->post('ncms'));
			$ncms = explode(" ", $ncms);

            $this->ncm_model->set_state('filter.num_versao', 0);
            $this->ncm_model->set_state('filter.vigente', 'S');

            $data['itens'] = $this->ncm_model->get_entries($ncms, 100);
            $data['itens_historico_2012'] = $this->ncm_model->get_entries($ncms, 100, '2012');
            $data['itens_historico_2017'] = $this->ncm_model->get_entries($ncms, 100, '2017');
            
            $data['detalhamento_aliquota'] = $this->detalhamento_aliquota_model->get_entries($ncms);
        }

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push("Comparar NCM's", '/comparar_ncm/');

		$this->render('comparar_ncm', $data);
	}
}

