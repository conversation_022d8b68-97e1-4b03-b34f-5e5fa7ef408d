<?php defined('BASEPATH') || exit('No direct script access allowed');
/**
 * Script para atualizar o campo 'observacoes' da tabela 'item' com base nas respostas da tabela 'ctr_resposta'
 *
 * Uso via CLI:
 * php index.php tools/update_observacoes_item/index [id_empresa] [dry_run]
 *
 * Exemplos:
 * php index.php tools/update_observacoes_item/index 201         # Executa atualizações para empresa 201
 * php index.php tools/update_observacoes_item/index 201 true    # Executa em modo de teste (sem atualizar)
 * php index.php tools/update_observacoes_item/index all         # Executa para todas as empresas
 */
class Update_observacoes_item extends CI_Controller {
    
    private $dry_run = false;
    private $total_itens = 0;
    private $total_atualizados = 0;
    private $total_erros = 0;
    
    public function __construct() {
        parent::__construct();
        $this->load->database();
        
        // Verifica se está sendo executado via CLI
        if (!$this->input->is_cli_request()) {
            show_error("Este script deve ser executado via linha de comando.", 403);
        }
    }
    
    public function index($id_empresa = null, $dry_run = false) {
        $this->dry_run = ($dry_run === 'true');
        
        echo "=== INÍCIO DO PROCESSAMENTO ===\n";
        echo "Data/Hora: " . date('Y-m-d H:i:s') . "\n";
        echo "Modo de teste (dry run): " . ($this->dry_run ? "SIM" : "NÃO") . "\n\n";
        
        try {
            if ($id_empresa === 'all') {
                $this->processar_todas_empresas();
            } else {
                $id_empresa = intval($id_empresa);
                if ($id_empresa <= 0) {
                    throw new Exception("ID de empresa inválido. Use um número válido ou 'all' para todas as empresas.");
                }
                $this->processar_empresa($id_empresa);
            }
            
            $this->exibir_resumo();
        } catch (Exception $e) {
            echo "ERRO CRÍTICO: " . $e->getMessage() . "\n";
            log_message('error', 'Erro no script update_observacoes_item: ' . $e->getMessage());
            exit(1);
        }
    }
    
    /**
     * Processa todas as empresas que possuem itens com observações vazias
     */
    private function processar_todas_empresas() {
        echo "Buscando empresas com itens para atualizar...\n";
        
        $query = $this->db->query("
            SELECT DISTINCT cpp.id_empresa
            FROM ctr_pendencias_pergunta cpp
            INNER JOIN item i ON i.part_number = cpp.part_number
                AND i.id_empresa = cpp.id_empresa
                AND i.estabelecimento = cpp.estabelecimento
            INNER JOIN ctr_resposta cr ON cr.id_pergunta = cpp.id
            WHERE cpp.pergunta = 'Informe as observações do item.'
                AND (i.observacoes = '' OR i.observacoes IS NULL)
        ");
        
        if ($query->num_rows() == 0) {
            echo "Nenhuma empresa encontrada com itens para atualizar.\n";
            return;
        }
        
        echo "Total de empresas encontradas: " . $query->num_rows() . "\n\n";
        
        foreach ($query->result() as $row) {
            $this->processar_empresa($row->id_empresa);
        }
    }
    
    /**
     * Processa os itens de uma empresa específica
     *
     * @param int $id_empresa ID da empresa a ser processada
     */
    private function processar_empresa($id_empresa) {
        echo "=== PROCESSANDO EMPRESA ID: {$id_empresa} ===\n";
        
        try {
            $itens = $this->obter_itens_empresa($id_empresa);
            
            if (empty($itens)) {
                echo "  - Nenhum item encontrado para esta empresa\n";
                return;
            }
            
            echo "  - Total de itens encontrados: " . count($itens) . "\n";
            $this->processar_itens($itens);
            
        } catch (Exception $e) {
            echo "  ERRO ao processar empresa ID {$id_empresa}: " . $e->getMessage() . "\n";
            log_message('error', "Erro ao processar empresa ID {$id_empresa}: " . $e->getMessage());
            $this->total_erros++;
        }
        
        echo "\n";
    }
    
    /**
     * Obtém os itens de uma empresa que precisam ter as observações atualizadas
     *
     * @param int $id_empresa ID da empresa
     * @return array Lista de itens para atualizar
     */
    private function obter_itens_empresa($id_empresa) {
        $query = $this->db->query("
            SELECT
                cpp.part_number,
                cpp.id_empresa,
                cpp.estabelecimento,
                cr.resposta,
                i.observacoes
            FROM ctr_pendencias_pergunta cpp
            INNER JOIN item i ON i.part_number = cpp.part_number
                AND i.id_empresa = cpp.id_empresa
                AND i.estabelecimento = cpp.estabelecimento
            INNER JOIN ctr_resposta cr ON cr.id_pergunta = cpp.id
            WHERE cpp.id_empresa = ?
                AND cpp.pergunta = 'Informe as observações do item.'
                AND (i.observacoes = '' OR i.observacoes IS NULL)
                AND cr.resposta IS NOT NULL
                AND cr.resposta != ''
        ", [$id_empresa]);
        
        $this->total_itens += $query->num_rows();
        return $query->result();
    }
    
    /**
     * Processa a lista de itens para atualização
     *
     * @param array $itens Lista de itens para atualizar
     */
    private function processar_itens($itens) {
        $this->db->trans_begin();
        
        try {
            foreach ($itens as $item) {
                $this->atualizar_item($item);
            }
            
            if ($this->dry_run) {
                $this->db->trans_rollback();
                echo "  - Modo de teste: rollback realizado, nenhuma alteração foi salva\n";
            } else {
                $this->db->trans_commit();
                echo "  - Transação concluída com sucesso\n";
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();
            echo "  - ERRO durante o processamento: " . $e->getMessage() . "\n";
            echo "  - Rollback realizado, nenhuma alteração foi salva\n";
            log_message('error', "Erro durante o processamento de itens: " . $e->getMessage());
            $this->total_erros++;
        }
    }
    
    /**
     * Atualiza um item específico
     *
     * @param object $item Item a ser atualizado
     */
    private function atualizar_item($item) {
        try {
            $data = [
                'observacoes' => $item->resposta
            ];
            
            $where = [
                'part_number' => $item->part_number,
                'estabelecimento' => $item->estabelecimento,
                'id_empresa' => $item->id_empresa
            ];
            
            if (!$this->dry_run) {
                $this->db->update('item', $data, $where);
                
                // Registrar no log de alterações
                $motivo = "Atualização automática do campo Observações";
                $this->registrar_log_alteracao($item, $motivo);
            }
            
            echo "  - " . ($this->dry_run ? "[SIMULAÇÃO] " : "") . "Item atualizado: {$item->part_number} (Estabelecimento: {$item->estabelecimento}, Empresa: {$item->id_empresa})\n";
            echo "    Observação: " . substr($item->resposta, 0, 50) . (strlen($item->resposta) > 50 ? "..." : "") . "\n";
            
            $this->total_atualizados++;
        } catch (Exception $e) {
            echo "  - ERRO ao atualizar item {$item->part_number}: " . $e->getMessage() . "\n";
            log_message('error', "Erro ao atualizar item {$item->part_number}: " . $e->getMessage());
            $this->total_erros++;
            throw $e;
        }
    }
    
    /**
     * Registra a alteração no log do sistema
     *
     * @param object $item Item atualizado
     * @param string $motivo Motivo da alteração
     */
    private function registrar_log_alteracao($item, $motivo) {
        $log_data = [
            'id_item' => 0,
            'titulo' => 'atualizacao',
            'motivo' => $motivo,
            'part_number' => $item->part_number,
            'estabelecimento' => $item->estabelecimento,
            'id_empresa' => $item->id_empresa,
            'id_usuario' => 1, // Usuário admin sistema
            'criado_em' => date('Y-m-d H:i:s')
        ];
        
        $this->db->insert('item_log', $log_data);
    }
    
    /**
     * Exibe o resumo do processamento
     */
    private function exibir_resumo() {
        echo "\n=== RESUMO DO PROCESSAMENTO ===\n";
        echo "Total de itens processados: {$this->total_itens}\n";
        echo "Total de itens atualizados: {$this->total_atualizados}\n";
        echo "Total de erros: {$this->total_erros}\n";
        echo "Data/Hora de conclusão: " . date('Y-m-d H:i:s') . "\n";
        echo "Modo de teste (dry run): " . ($this->dry_run ? "SIM" : "NÃO") . "\n";
        echo "=== FIM DO PROCESSAMENTO ===\n";
    }
}
