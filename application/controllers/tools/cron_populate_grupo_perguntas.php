<?php

// Crontab line to beta
// 0 8 * * 1-5 php /var/www/gestaotarifaria/current/index.php tools/cron_items_without_owner_mail >/dev/null 2>&
//

class Cron_Populate_Grupo_Perguntas extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();

        $this->load->model('ctr_pendencias_pergunta_model');
        $this->load->model('ctr_grupo_pergunta_model');
        $this->load->model("ctr_resposta_model");

    }

    public function index()
    {
        $this->populate_table();
    }

    private function populate_table()
    {
		$CI = &get_instance();
        
        $CI->db->where('id_pergunta > 0', NULL, FALSE);
        $CI->db->select('part_number, estabelecimento');

        $CI->db->group_by(['part_number', 'estabelecimento', 'id_empresa']);

        $query = $CI->db->get('ctr_pendencias_pergunta');

        $result = $query->result();        

        foreach ($result as $partnumber) {
            $this->ctr_resposta_model->set_state('filter.partnumber', $partnumber->part_number);
            $this->ctr_resposta_model->set_state('filter.estabelecimento', $partnumber->estabelecimento);

            $historicoItem = $this->ctr_resposta_model->getHistoricoItemPopulate();

            if (is_array($historicoItem) && count($historicoItem) > 0) {

            } else {
                continue;
            }

            $ids_grupos = array();
            $ids_grupos_perguntas = array();

            foreach ($historicoItem as $item) {

                if ($item->group_id == null || empty($item->group_id) || $item->group_id == 0 || $item->id_pergunta == null || empty($item->id_pergunta) || $item->id_pergunta == 0) {
                    continue;
                }

                if (isset($ids_grupos[$item->group_id])) {
                    $ids_grupos[$item->group_id]++;
                    $ids_grupos_perguntas[$item->group_id][$item->id_pergunta_ctr] = $item->id_pergunta;
                } else {
                    $ids_grupos[$item->group_id] = 1;
                    $ids_grupos_perguntas[$item->group_id][$item->id_pergunta_ctr] = $item->id_pergunta;
                }
            }

            if (count($ids_grupos) == 0) {
                continue;
            }


            $grupo_mais_perguntas = null;
            foreach ($ids_grupos as $id_grupo => $quantidade) {
                if ($grupo_mais_perguntas == null) {
                    $grupo_mais_perguntas = $id_grupo;
                } else {
                    if ($ids_grupos[$grupo_mais_perguntas] <= $quantidade) {
                        $grupo_mais_perguntas = $id_grupo;
                    }
                }
            }


            foreach ($ids_grupos as $id_grupo => $quantidade) {
                if ($id_grupo == $grupo_mais_perguntas) {
                    continue;
                }

                foreach ($ids_grupos_perguntas[$id_grupo] as $id_ctr_pergunta => $id_pergunta) {
                    $CI->db->where('id_pergunta', $id_pergunta);

                    $consulta = $CI->db->get('ctr_grupo_pergunta');


                    $resultado = $consulta->result();
                    
                    if (count($resultado) > 1) {
                        foreach ($resultado as $item) {
                            if ($item->id_grupo == $grupo_mais_perguntas) {
                                $ids_grupos[$grupo_mais_perguntas]++;
                                $ids_grupos_perguntas[$grupo_mais_perguntas][$id_ctr_pergunta] = $id_pergunta;

                                $ids_grupos[$id_grupo]--;
                                unset($ids_grupos_perguntas[$id_grupo][$id_ctr_pergunta]);
                                break;
                            }
                        }
                    }
                }

            }

            foreach ($ids_grupos_perguntas as $id_grupo => $perguntas) {
                if (is_array($perguntas) && count($perguntas) > 0) {
                    
                    foreach ($perguntas as $id_ctr_pergunta => $id_pergunta) {
                        
                        $CI->db->where('id', $id_ctr_pergunta);
                        $CI->db->update('ctr_pendencias_pergunta', ['id_ctr_grupo' => $id_grupo]);
    
    
                    }
    
                }
            }

        }  


    }
}
