<?php

// Crontab line to beta
// 1 8 * * 1-5 php /var/www/gestaotarifaria/current/index.php tools/cron_items_revisar_informacoes_erp_mail >/dev/null 2>&
//

class Cron_Items_Revisar_Informacoes_Erp_Mail extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('item_model');
        $this->load->model('unidade_negocio_model');
    }

    public function index()
    {
        $this->notificar_unidade_negocio_itens_revisar_informacoes_erp();
    }

    private function notificar_unidade_negocio_itens_revisar_informacoes_erp()
    {
        $lista_itens_revisar_informacoes_erp = $this->item_model->get_itens_revisar_informacoes_erp();

        $item_by_estabelecimento = [];

        if (is_array($lista_itens_revisar_informacoes_erp) && count($lista_itens_revisar_informacoes_erp) > 0) {
            foreach ($lista_itens_revisar_informacoes_erp as $item) {
                if (!isset($item_by_estabelecimento[$item->estabelecimento])) {
                    $item_by_estabelecimento[$item->estabelecimento] = [
                        'lista' => []
                    ];
                }

                $item_by_estabelecimento[$item->estabelecimento]['lista'][] = [
                    'part_number' => $item->part_number,
                    'descricao' => $item->descricao,
                    'criacao' => $item->dat_criacao,
                    'prioridade' => $item->prioridade,
                    'estabelecimento' => $item->estabelecimento,
                    'id_empresa' => $item->id_empresa,
                    'nome_empresa' => $item->nome_empresa,
                ];
            }

            $lista_envio = [];

            foreach ($item_by_estabelecimento as $estabelecimento => $item) {
                $usuarios_unidade_negocio = $this->unidade_negocio_model->get_usuarios_email_revisar_descricao_curta($estabelecimento);

                if (is_array($usuarios_unidade_negocio) && count($usuarios_unidade_negocio) > 0) {
                    foreach ($usuarios_unidade_negocio as $usuario) {
                        if (!isset($lista_envio[$usuario->email])) {
                            $lista_envio[$usuario->email] = [
                                'nome' => $usuario->nome,
                                'email' => $usuario->email,
                                'estabelecimento' => $estabelecimento,
                                'id_empresa' => $item_by_estabelecimento[$estabelecimento]['lista'][0]['id_empresa'],
                                'nome_empresa' => $item_by_estabelecimento[$estabelecimento]['lista'][0]['nome_empresa'],
                                'lista' => []
                            ];
                        }
                    }

                    foreach ($lista_itens_revisar_informacoes_erp as $item) {
                        if ($item->estabelecimento == $estabelecimento) {
                            foreach ($usuarios_unidade_negocio as $usuario) {
                                $lista_envio[$usuario->email]['lista'][] = [
                                    'part_number' => $item->part_number,
                                    'descricao' => $item->descricao ? $item->descricao : $item->descricao_global,
                                    'criacao' => $item->dat_criacao,
                                    'prioridade' => $item->prioridade,
                                    'estabelecimento' => $item->estabelecimento,
                                    'id_empresa' => $item->id_empresa,
                                    'nome_empresa' => $item->nome_empresa,
                                    'motivos' => $item->motivos,
                                    'owner' => !empty($item->cod_owner) ? $item->owner_codigo . ' - ' .      $item->owner_descricao . ' - ' . $item->responsaveis_gestores_nomes : '',
                                ];
                            }
                        }
                    }
                }
            }

            $lista_agrupada = [];
            if (count($lista_envio) > 0) {

                foreach ($lista_envio as $email => $dados) {
                    $estabelecimentos = array_unique(array_column($dados['lista'], 'estabelecimento'));
                    
                    foreach ($estabelecimentos as $estabelecimento) {
                        $lista_agrupada[] = [
                            $email => [
                                'nome' => $dados['nome'],
                                'email' => $dados['email'],
                                'estabelecimento' => $estabelecimento,
                                'id_empresa' => $dados['id_empresa'],
                                'nome_empresa' => $dados['nome_empresa'],
                                'lista' => array_filter($dados['lista'], function ($item) use ($estabelecimento) {
                                    return $item['estabelecimento'] == $estabelecimento;
                                })
                            ]
                        ];
                    }
                }
                
                foreach ($lista_agrupada as $list) {

                    foreach ($list as $email => $data) {

                        $unidade_negocio = $this->unidade_negocio_model
                                                ->get_unidade_negocio_by_estabelecimento(
                                                    $data['estabelecimento'],
                                                    $data['id_empresa']
                                                );

                        if (empty($unidade_negocio)) {
                            $unidade_negocio = new stdClass();
                            $unidade_negocio->codigo = $data['estabelecimento'];
                            $unidade_negocio->descricao = ' ';
                        }

                        $data['assunto'] =
                            '[GT] Relação de Itens em "Revisão de Informações ERP" - ' .
                            ' - ' . 'Unidade de Negócio: ' . $data['estabelecimento'] . ' - ' .
                            $unidade_negocio->descricao . ' - ' . 'Data: ' . date('d/m/Y');

                        $data['unidade_negocio_codigo'] = $unidade_negocio->codigo;
                        $data['unidade_negocio_descricao'] = $unidade_negocio->descricao;
                        $data['estabelecimento'] = $data['estabelecimento'];
                        $data['base_url'] = config_item('online_url');

                        $body = $this->load->view('templates/notificacao_unidade_negocio_itens_revisar_infos_erp', $data, true);

                        $this->load->library('email');

                        $this->email->from(config_item('mail_from_addr'), config_item('mail_from_name'));
                        $this->email->to($data['email']);
                        $this->email->subject($data['assunto']);
                        $this->email->message($body);

                        $this->email->send();
                    }
                }
            }
        }
    }
}
