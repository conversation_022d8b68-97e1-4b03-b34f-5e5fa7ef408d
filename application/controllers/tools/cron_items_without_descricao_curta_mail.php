<?php

// Crontab line to beta
// 1 8 * * 1-5 php /var/www/gestaotarifaria/current/index.php tools/cron_items_without_descricao_curta_mail >/dev/null 2>&
//

class Cron_Items_Without_Descricao_Curta_Mail extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('item_model');
        $this->load->model('unidade_negocio_model');
    }

    public function index()
    {
        $this->notificar_unidade_negocio_itens_sem_descricao_curta();
    }

    private function notificar_unidade_negocio_itens_sem_descricao_curta()
    {
        $lista_itens_sem_descricao_curta = $this->item_model->get_itens_sem_descricao_curta_dia_anterior();

        $item_by_estabelecimento = [];

        if (is_array($lista_itens_sem_descricao_curta) && count($lista_itens_sem_descricao_curta) > 0) {
            foreach ($lista_itens_sem_descricao_curta as $item) {
                if (!isset($item_by_estabelecimento[$item->estabelecimento])) {
                    $item_by_estabelecimento[$item->estabelecimento] = [
                        'lista' => []
                    ];
                }

                $item_by_estabelecimento[$item->estabelecimento]['lista'][] = [
                    'part_number' => $item->part_number,
                    'descricao' => $item->descricao,
                    'criacao' => $item->dat_criacao,
                    'prioridade' => $item->prioridade,
                    'estabelecimento' => $item->estabelecimento,
                    'id_empresa' => $item->id_empresa,
                    'nome_empresa' => $item->nome_empresa,
                ];
            }

            $lista_envio = [];

            foreach ($item_by_estabelecimento as $estabelecimento => $item) {
                $usuarios_unidade_negocio = $this->unidade_negocio_model->get_usuarios_email_revisar_descricao_curta($estabelecimento);

                if (is_array($usuarios_unidade_negocio) && count($usuarios_unidade_negocio) > 0) {
                    foreach ($usuarios_unidade_negocio as $usuario) {
                        if (!isset($lista_envio[$usuario->email])) {
                            $lista_envio[$usuario->email] = [
                                'nome' => $usuario->nome,
                                'email' => $usuario->email,
                                'estabelecimento' => $estabelecimento,
                                'id_empresa' => $item_by_estabelecimento[$estabelecimento]['lista'][0]['id_empresa'],
                                'nome_empresa' => $item_by_estabelecimento[$estabelecimento]['lista'][0]['nome_empresa'],
                                'lista' => []
                            ];
                        }
                    }

                    foreach ($lista_itens_sem_descricao_curta as $item) {
                        if ($item->estabelecimento == $estabelecimento) {
                            foreach ($usuarios_unidade_negocio as $usuario) {
                                $lista_envio[$usuario->email]['lista'][] = [
                                    'part_number' => $item->part_number,
                                    'descricao_global' => $item->descricao_global,
                                    'criacao' => $item->dat_criacao,
                                    'prioridade' => $item->prioridade,
                                    'estabelecimento' => $item->estabelecimento,
                                    'id_empresa' => $item->id_empresa,
                                    'nome_empresa' => $item->nome_empresa,
                                ];
                            }
                        }
                    }
                }
            }

            if (count($lista_envio) > 0) {
                foreach ($lista_envio as $email => $data) {
                    
                    $data['assunto'] = '[GT] Relação de Itens Aguardando Descrição - ' . date('d/m/Y') . ' - ' . 'Unidade de Negócio: ' . $estabelecimento . ' - ' . $usuario->descricao;

                    // $data['assunto'] = 'Teste';

                    $data['base_url'] = config_item('online_url');

                    $body = $this->load->view('templates/notificacao_unidade_negocio_itens_sem_descricao', $data, TRUE);

                    $this->load->library('email');

                    $this->email->from(config_item('mail_from_addr'), config_item('mail_from_name'));
                    $this->email->to($data['email']);
                    $this->email->subject($data['assunto']);
                    $this->email->message($body);

                    $this->email->send();
                }
            }
        }
    }
}
