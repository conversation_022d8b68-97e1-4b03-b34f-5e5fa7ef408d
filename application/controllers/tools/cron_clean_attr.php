<?php

// Crontab line to beta
// 0 8 * * 1-5 php /var/www/gestaotarifaria/current/index.php tools/cron_items_without_owner_mail >/dev/null 2>&
//

class Cron_Clean_Attr extends CI_Controller
{
    public $_attrs = [];
    public function __construct()
    {
        parent::__construct();

        $this->load->model('ctr_pendencias_pergunta_model');
        $this->load->model('ctr_grupo_pergunta_model');
        $this->load->model("ctr_resposta_model");

 
        $this->load->model('ncm_atributo_model');
        $this->load->model('cad_item_model');
        $this->load->model([
            "catalogo/produto_model",
        ]);

        set_time_limit(0);
        ini_set('memory_limit', '2048M');

    }

    public $_virtual_table = [];

    public function get_attrs_id_item()
    {
        $this->db->select('gr.ncm_recomendada,attr.id_item');
        $this->db->join('grupo_tarifario gr', 'gr.id_grupo_tarifario = attr.id_grupo_tarifario', 'inner');

        $this->db->group_by('gr.ncm_recomendada');
        $this->db->group_by('attr.id_item');
        return $query = $this->db->get("cad_item_attr attr");

        if ($query->num_rows()) {
            return $query->result();
        }

        return NULL;
    }

    private function filterUniqueAttributes($inputArray) {
        $attributeMap = [];
    
        foreach ($inputArray as $item) {
            $attribute = $item->atributo;
            $attributeMap[$attribute] = $item;
        }
    
        $resultArray = array_values($attributeMap);
    
        usort($resultArray, function($a, $b) {
            return strtotime($b->atualizado_em) - strtotime($a->atualizado_em);
        });
    
        return $resultArray;
    }
    
    public function remove_attrs($data)
    {
        if (!empty($data['attrs']) && !empty($data['id_item']) && !empty($data['ncm']))
        {
            $this->db->set('attr.ativo', 0);
            $this->db->where('attr.ativo', 1);
            $this->db->where_not_in('attr.atributo', $data['attrs']);
            $this->db->where('attr.id_grupo_tarifario IN (SELECT id_grupo_tarifario FROM grupo_tarifario WHERE ncm_recomendada = ' . $this->db->escape($data['ncm']) . ')');
            $this->db->update('cad_item_attr attr');

 
        }
    }

    public function update_mandatory()
    {

        $this->db->select("id_empresa");
        $this->db->where("ativo", 1);
        $query = $this->db->get('empresa');

        $empresas =  $query->result();

        echo 'Desativando registros que não existem na tabela cad_item'.PHP_EOL;
        $this->db->query("UPDATE cad_item_attr attr
        SET attr.ativo = '0'
        WHERE attr.ativo = '1'
        AND NOT EXISTS (
            SELECT 1
            FROM cad_item ci
            WHERE ci.id_item = attr.id_item
        ); ");
      
        foreach ($empresas as $empresa)
        {
            echo 'Atualizando atributos obrigatorios processando da empresa '.$empresa->id_empresa.PHP_EOL;
            $this->db->query(" UPDATE cad_item_attr attr
            INNER JOIN ncm_atributo n ON n.codigo = attr.atributo
            INNER JOIN cad_item ci ON ci.id_item = attr.id_item
            SET attr.obrigatorio = n.obrigatorio
            WHERE attr.ativo = '1'
            and attr.atributo = n.codigo
            and attr.obrigatorio <> n.obrigatorio
            and ci.id_empresa = {$empresa->id_empresa}");
        }
    }
        
    public function get_cad_item_attr()
    {
        $this->db->select("atributo");
        $this->db->where("ativo", 0);
        $this->db->group_by('atributo');
        $query = $this->db->get('cad_item_attr');

        return $query->result();
    }

    public function get_attr($id_item, $empty = FALSE)
    {
        if (empty($id_item)) {
            return FALSE;
        }

        if ($empty) {
            $this->db->where("codigo", "");
        }

        $this->db->where("id_item", $id_item);

        $query = $this->db->get('cad_item_attr');

        if (!$query->num_rows()) {
            return FALSE;
        }

        return $query->result();
    }

    public function index()
    {
        echo 'Atualizando campos ativo e obrigatorio'.PHP_EOL;
        $this->update_mandatory();
    
        $count = 0;
        $this->_virtual_table = [];
 
        $table_cad_attr = $this->get_attrs_id_item();
  
        while ($table = $table_cad_attr->unbuffered_row()) {
   
            $this->_attrs = [];
            if (!empty($table->ncm_recomendada))
            {
                $ncm_item = $this->get_required_attrs($this->produto_model->get_attr_ncm($table->ncm_recomendada));
                if (empty($ncm_item))
                {
                    continue;
                }
                $array = $this->get_attr($table->id_item);  
           
                 
                $ncm_item["defaultAttrs"] = $this->filterUniqueAttributes($array);
                $ncm_item["assocAttrs"] = $this->create_assoc_attrs_structure($ncm_item);
            }
            $atributos_ncm = $this->_attrs;

            if (isset($atributos_ncm['sim']))
            {
                unset($atributos_ncm['sim']);
            }
            if (!empty($atributos_ncm))
            {
                echo 'Desativando atributos removidos após a ultima atualização '.$table->id_item.PHP_EOL;
                $this->remove_attrs(['attrs' => $atributos_ncm, 'id_item' => $table->id_item, 'ncm' => $table->ncm_recomendada]);
        
            }

            
        }
    }

    private function get_required_attrs($listaAtributos)
    {
        if (empty($listaAtributos)) {
            return;
        }

        $listaAtributos = \json_decode(\json_encode($listaAtributos), TRUE); // Força a conversão para array.

        $ncm_item = [];
        $ncm_item['listaAtributos'] = $listaAtributos;


        \array_multisort($ncm_item["listaAtributos"]);

        return $ncm_item;
    }


    private function create_assoc_attrs_structure($ncm_item)
    {
        if (empty($ncm_item)) {
            return;
        }

        $ncm_item = \json_decode(\json_encode($ncm_item), TRUE);  

        $arr_dbdata = $ncm_item["defaultAttrs"];  
        $arr_attr   = $ncm_item["listaAtributos"]; 

        $this->assoc_recursively($arr_dbdata, $arr_attr);

        return $arr_attr;
    }

    public function has_attr_cond($attr, $cond)
    {
        $cond_op  = $cond["condicao"]["operador"]; // O operador para comparação.
        $cond_val = $cond["condicao"]["valor"];    // O valor para ser comparado.
        $cond_res = FALSE;                         // O resultado final da comparação.

        if (!empty($attr['multivalorado'])) {
            $attr_vals = $attr["dbdata"]["codigo"]; // Multivalorado.

            if (!\is_array($attr_vals)) {
                $attr_vals = explode(",", $attr_vals);
            }

            foreach($attr_vals as $attr_val) {

                $cond_res = eval("return \"$attr_val\" $cond_op \"$cond_val\";");

                if ($cond_res) {
                    break;
                }
            }

        } else {
            $attr_val = $attr["dbdata"]["codigo"]; // Valor simples.

            if (strtoupper($attr["formaPreenchimento"]) == 'BOOLEANO' && $attr_val > 0) {
                $attr_val = "true";
            }

            $cond_res = eval("return \"$attr_val\" $cond_op \"$cond_val\";");

        }

        return $cond_res;
    }

    private function assoc_recursively($arr_dbdata, &$arr_attr, $parent_attr = NULL)
    {
        if (empty($arr_attr)) {
            return;
        }

        if (empty($arr_dbdata)) {
            $arr_dbdata = [];
        }

        foreach($arr_attr as &$attr) {
            $attr_template = !empty($attr["atributo"])? $attr["atributo"] : $attr; 

            $attr_template["dbdata"] = [ "codigo" => "" ]; 

            foreach($arr_dbdata as $dbdata) {
                if (
                    !empty($parent_attr)
                    && !empty($dbdata["atributo_pai"])
                    && $dbdata["atributo_pai"] == $parent_attr["codigo"]
                    && $dbdata["atributo"] == $attr_template["codigo"]
                ) {
                    $attr_template["dbdata"] = $dbdata;

                } else if($dbdata["atributo"] == $attr_template["codigo"]) {
                    $attr_template["dbdata"] = $dbdata;
                }
            }

            if ($attr_template["atributoCondicionante"] && !empty($attr_template["condicionados"])) {
                    foreach ($attr_template["condicionados"] as &$cond) {
                        if ($this->has_attr_cond($attr_template, $cond)) 
                        {
                            if (!empty($attr_template["dbdata"]['atributo']))
                            {
                                $this->_attrs['sim'][] = $attr_template["dbdata"]['atributo'];
                            }
                        }
                    }

                    if (!empty($this->_attrs['sim']) && in_array($attr_template['codigo'], $this->_attrs['sim']))
                    {
                        $this->assoc_recursively($arr_dbdata, $attr_template["condicionados"], $attr_template);
                    }

            } else if (\strtoupper($attr_template["formaPreenchimento"]) == "COMPOSTO" && !empty($attr_template["listaSubatributos"])) {
                $this->assoc_recursively($arr_dbdata, $attr_template["listaSubatributos"], $attr_template);
            }
            if (!empty($attr_template["dbdata"]["id_item"]))
            {
                if (!empty($attr_template["dbdata"]['atributo']))
                {
                    $this->_attrs[] = $attr_template["dbdata"]['atributo'];
                }
            }

            if (!empty($attr["atributo"])) {
                $attr["atributo"] = $attr_template;

            } else {
                $attr = $attr_template;
            }
        }
    }
}
