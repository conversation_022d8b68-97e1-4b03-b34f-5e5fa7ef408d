<?php

// Crontab line to beta
// */5 * * * * php /var/www/gestaotarifaria/current/index.php tools/cron_mail >/dev/null 2>&
//
class Cron_Owner_Mail extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();

        $this->load->model('item_model');
        $this->load->model('owner_model');
    }

    public function index()
    {
        $this->notificar_owner_produto_revisao();
        
    }

    private function cria_lista_envio($lista_empresas_itens){

        $lista_envio = [];
        foreach($lista_empresas_itens as $dd){
            $empresa = $dd->id_empresa;
            $owner = $dd->codigo;
            
            if(!isset($lista_envio[$empresa][$owner])){
                $dadosOwner = $this->owner_model->get_owner_and_responsaveis($dd->cod_owner);

                $lista_envio[$empresa][$owner] = [
                    'id_empresa' => $dd->id_empresa,
                    'desc_empresa' => $dd->razao_social,
                    "id_owner" => $dadosOwner->id_owner,
                    "codigo" => $dadosOwner->codigo,
                    "desc_owner" => $dadosOwner->descricao,
                    "gestor_owner" => ($dadosOwner->responsaveis != '') ? $dadosOwner->responsaveis : '',
                    "data_email" => date('d/m/Y'),
                    "lista" => []
                ];
            }
            $dtCriacao = Datetime::createFromFormat('Y-m-d H:i:s', $dd->dat_criacao);
            $lista_envio[$empresa][$owner]['lista'][] = [
                'estabelecimento' => $dd->estabelecimento,
                'part_number' => $dd->part_number,
                'descricao' => $dd->descricao,
                'criacao' => $dtCriacao->format('d/m/Y H:i'),
                'prioridade' => $dd->prioridade,
            ];
            
        }

        return $lista_envio;
    }
    
    private function notificar_owner_produto_revisao(){
        $lista_empresas_itens = $this->item_model->lista_itens_por_empresa_e_owner_e_status();
            
        if(is_array($lista_empresas_itens) && count($lista_empresas_itens) > 0){
      
            $lista_envio = $this->cria_lista_envio($lista_empresas_itens);
                
            if(count($lista_envio) > 0){
                foreach($lista_envio as $empresa_group){
                    foreach($empresa_group as $id_owner => $data){
                    
                        $data['assunto'] = '[GT] Relação de itens Em Análise - '.  $data['data_email'] .' - Owner: '. $data['codigo'].' - '. $data['desc_owner'].' - '. $data['gestor_owner'];
                        $data['base_url'] = config_item('online_url');
                        $id_owner_cadastrado = $data['id_owner'];
                        $toList = $this->owner_model->get_owners_emails_by_responsavel($id_owner_cadastrado);

                        if(!is_array($toList) || count($toList) < 1)
                            continue;
                            
                        $body = $this->load->view('templates/notificacao_owner_itens_analise', $data, TRUE);

                        $this->load->library('email');

                        $this->email->from(config_item('mail_from_addr'), config_item('mail_from_name'));
                        $this->email->to($toList);
                        $this->email->subject($data['assunto']);
                        $this->email->message($body);

                        $this->email->send();
                    }
                }
            }
        }
        
    }
}
