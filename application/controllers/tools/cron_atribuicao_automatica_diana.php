<?php
require_once  APPPATH . 'libraries/Spout/Autoloader/autoload.php';

use Box\Spout\Reader\ReaderFactory;
use Box\Spout\Common\Type;

class Cron_Atribuicao_Automatica_Diana extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();

    }

    public function debug($message)
    {

        if (!has_role('sysadmin') && !$this->input->is_cli_request()) {
            show_permission();
        }

        if ($this->input->is_cli_request()) {
            echo $message . PHP_EOL;
        } else {
            echo $message . '<br />';
        }
    }

    public function index()
    {
        if (!has_role('sysadmin') && !$this->input->is_cli_request()) {
            show_permission();
        }

        set_time_limit(0);
        @ob_implicit_flush(TRUE);
        @ob_end_flush();

        $this->db->query('SET NAMES binary');

        // Resolve o problema de memory leak
        $this->db->save_queries = FALSE;
        $this->db->db_debug     = FALSE;

        $this->load->library('benchmark');

        $this->benchmark->mark('code_start');

        $this->debug('Integração iniciada - ' . date('Y-m-d H:i:s'));

        $result_atribuicao = $this->atribuicao_automatica();

        $this->debug('OK - atribuicao-automatica');

        $this->debug('Integração finalizada - ' . date('Y-m-d H:i:s'));

        $this->benchmark->mark('code_end');

        $elapsed_time = $this->benchmark->elapsed_time('code_start', 'code_end');

        $this->debug('Tempo gasto: ' . $elapsed_time . ' segundos');

        $filename = APPPATH . '/logs/log-cron-atrib-auto-diana.txt';

        if ((file_exists($filename) && is_writable($filename)) || !file_exists($filename)) {
            $message = '--------------------------------------------------------------' . PHP_EOL;
            $message .= '[' . date('Y-m-d H:i:s') . '] - Integração com o Diana' . PHP_EOL;;
            $message .= ' - Atrubuição Automática ->  Total Itens: ' .  $result_atribuicao['total_itens'] . ' - Atribuídos:' . $result_atribuicao['atribuidos'] . PHP_EOL;

            file_put_contents($filename, $message, FILE_APPEND);
        }
    }


    public function get_termo_pesquisa($part_number = '', $descricao = '', $id_empresa = '')
    {

        $this->db->where('id_empresa', $id_empresa);

        if (!empty($part_number)) {
            $this->db->where('part_number', $part_number);
        }

        if (!empty($descricao)) {
            $this->db->where('descricao', $descricao);
        }

        $query = $this->db->get('item');

        $item = $query->row();

        if (empty($item)) {
            return '';
        }

        $termoPesquisa = '';

        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('checked', 1);
        $this->db->order_by('index', 'ASC');

        $query = $this->db->get('empresa_diana_inf');

        foreach ($query->result() as $attr) {
            $termoPesquisa .= $item->{$attr->slug} . ' ';
        }

        return trim($termoPesquisa);
    }


    private function atribuicao_automatica()
    {

        $this->load->library('diana');

        //Buscar ites que se enquadram nas condições
        $this->load->model(array(
			'app_ia_segmento_model',
			'app_ia_segmento_modelo_model',
			'grupo_tarifario_model',
			'cad_item_model',
            'empresa_model',
            'item_model'
		));

        $itens = $this->item_model->get_items_atribuicao_automatica();

        $intens_save = []; 
        $atribuidos = 0;
        $total_itens = count($itens);

        if(!empty($itens)){
            foreach ($itens as $key => $item) {
                
                //Atribuiçao do grupo tarifário pela diana(Maior probabilidade)
                $segmento_id = 0;

                $empresa = $this->empresa_model->get_full_entry($item->id_empresa);
                $part_number = $item->part_number;
                $descricao = '';

                $termoPesquisa = $this->get_termo_pesquisa($part_number, $descricao,$item->id_empresa);
     
                $search_data['num_predicoes'] = 3;
                $search_data['termo_pesquisa'] = $termoPesquisa;
                $app = 'a';
                $modelos = null;
                if ($app == 'a') {
                    $segmento_id = $empresa->app_a_seguimento;
                } else if ($app == 'b') {
                    $segmento_id = $empresa->app_b_seguimento;
                }

                if (!empty($segmento_id)) {
                    $modelos = $this->app_ia_segmento_modelo_model->get_entry($segmento_id);
                }

                $grupo_id = '';
                $result = $this->diana->get_entries_by_app($search_data, $modelos, $app);
                $grupo_id = $result['result']->data[0]->predictions[0]->grupo_id;
       
               // $grupo_tarifario = $this->grupo_tarifario_model->get_entry_by_desc_no_exception($result['result']->data[0]->predictions[0]->pred);
            
                $ncm_proposta = $result['result']->data[0]->predictions[0]->pred;
                $acuracia = $result['result']->data[0]->predictions[0]->acc;
             
                //Atribuir grupo tarifario
                $retorno = $this->atribuir_grupo_tarifario($result,$grupo_id,$item, $empresa, $ncm_proposta, $acuracia);
              
                if ($retorno) {
                    $atribuidos++;
                }

            }
        }else{
            $this->debug('Nenhum item encontrado para atribuição');
        }

        return array('total_itens' => $total_itens, 'atribuidos' => $atribuidos);
    }

    private function atribuir_grupo_tarifario($result,$grupo_id,$item, $empresa, $ncm_proposta, $acuracia)
    {
        $return = false;

        $this->load->helper('formatador_helper');

        $this->load->model(array(
            'cad_item_model',
            'grupo_tarifario_model',
            'item_log_model',
            'item_model',
            'app_ia_segmento_model',
            'app_ia_segmento_modelo_model',
            'empresa_model'
        ));

        $id_empresa = $empresa->id_empresa;
        $funcoes_adicionais = explode('|', $empresa->funcoes_adicionais);
        $can_formatar_texto = company_can("formatar_texto", $funcoes_adicionais);
        $campos_adicionais = explode('|', $empresa->campos_adicionais);

        if(!empty($grupo_id)){

        //Simular e salvar o preenchimento da proposta de descrição resumida
            if (!$this->cad_item_model->check_item_exists($item->part_number, $id_empresa , $item->estabelecimento)) {
                $grupo = $this->grupo_tarifario_model->get_entry($grupo_id);
                
                $subsidio =  $grupo->subsidio;
                $caracteristica = $grupo->caracteristica;
                
                $item_row = $this->item_model->get_entry($item->part_number, $id_empresa , $item->estabelecimento);
                
                $descricao = formatar_texto($can_formatar_texto, $item_row->descricao);
                $descricao_mercado_local = strtoupper(remove_acentos($descricao));
                
                $cad_item = array(
                    'part_number'                 => $item->part_number,
                    'id_grupo_tarifario'          => $grupo->id_grupo_tarifario,
                    'ncm_proposto'                => $grupo->ncm_recomendada,
                    'id_empresa'                  => $id_empresa,
                    'origem'                      => 'atribuicao_automatica',
                    'estabelecimento'             => $item->estabelecimento,
                    'id_resp_fiscal'              => $item_row->id_resp_fiscal,
                    'id_resp_engenharia'          => $item_row->id_resp_engenharia,
                    'caracteristicas'             => (!empty($caracteristica)     ? $caracteristica    : null),
                    'subsidio'                    => (!empty($subsidio)           ? $subsidio          : null),
                    'dispositivo_legal'           => (!empty($grupo->dispositivo_legal)  ? $grupo->dispositivo_legal : null),
                    'solucao_consulta'            => (!empty($grupo->solucao_consulta)   ? $grupo->solucao_consulta  : null),
                    'memoria_classificacao'       => (!empty($memoria_classificacao)   ? $memoria_classificacao  : null),
                    'descricao_mercado_local'     => (!empty($descricao_mercado_local) ? formatar_texto($can_formatar_texto, $descricao_mercado_local) : null),
                    'has_predicao'                => false,
                    'historico_reanalise'         => '',

                );

                if (!empty($item_row->funcao)) {
                    $cad_item['funcao'] = formatar_texto($can_formatar_texto, $item_row->funcao);
                }

                if (empty($post['inf_adicionais']) && !empty($item_row->inf_adicionais)) {
                    $cad_item['inf_adicionais'] = formatar_texto($can_formatar_texto, $item_row->inf_adicionais);
                }

                if (!empty($item_row->aplicacao)) {
                    $cad_item['aplicacao'] = formatar_texto($can_formatar_texto, $item_row->aplicacao);
                }

                if (!empty($item_row->material_constitutivo)) {
                    $cad_item['material_constitutivo'] = formatar_texto($can_formatar_texto, $item_row->material_constitutivo);
                }

                if (!empty($item_row->marca)) {
                    $cad_item['marca'] = formatar_texto($can_formatar_texto, $item_row->marca);
                }
             
                if ($this->db->insert('cad_item', $cad_item)) {

                    $observacoes_mestre = "Atribuicao Automática";

                    $id_status = 1; //Homologar

                    $this->item_model->update_item($item->part_number, $id_empresa, array(
                        'observacoes' => $observacoes_mestre,
                        'id_status' => $id_status
                    ), 'Atualizando valor do campo Observações e ID Status através da atribuição do Item', $item->estabelecimento, 1);

                    $motivo = '<strong>Part Number:</strong> ' . $item->part_number . '<br><strong>Estabelecimento: </strong>' . $item->estabelecimento . '<br> <strong>Grupo atribuído:</strong> ' . $grupo->descricao . ' <br><small><strong>NCM Recomendada: </strong>' . $grupo->ncm_recomendada . '</small><br><strong>Observações: </strong>' . $observacoes_mestre;


                    if (!empty($subsidio)) {
                        $motivo .= '<br><strong>Subsidio:</strong> ' . $subsidio;
                    }

                    if (!empty($caracteristica)) {
                        $motivo .= '<br><strong>Característica:</strong> ' . $caracteristica;
                    }

                    if (!empty($memoria_classificacao)) {
                        $motivo .= '<br><strong>Memória de Classificação:</strong> ' . $memoria_classificacao;
                    }

                    if (!empty($descricao_mercado_local)) {
                        $motivo .= '<br><strong>Descrição proposta resumida:</strong> ' . formatar_texto($can_formatar_texto, $descricao_mercado_local);
                    }

                    if (!empty($descricao)) {
                        $motivo .= '<br><strong>Descrição:</strong> ' . formatar_texto($can_formatar_texto, $descricao);
                    }
                    $grupo_id = '';

                   
                    $this->db->where('part_number', $item->part_number);
                    $this->db->where('id_empresa', $id_empresa);
                    $this->db->where('estabelecimento', $item->estabelecimento);
                    $query = $this->db->get('cad_item');
                    $cad_item = $query->row();

                    //Registro de LOG
                    $sql = "INSERT INTO item_log (
                        id_item, 
                        tipo_homologacao, 
                        id_usuario, 
                        id_empresa, 
                        titulo, 
                        motivo, 
                        part_number, 
                        estabelecimento,
                        criado_em
                    ) VALUES (
                        $cad_item->id_item, 
                        'Engenharia', 
                        1, 
                        $id_empresa, 
                        'atribuicaogrupo', 
                        '$motivo', 
                        '$item->part_number', 
                        '$item->estabelecimento',
                        NOW()
                    )";
                
                    $sql2 = "INSERT INTO item_log (
                        id_item, 
                        tipo_homologacao, 
                        id_usuario, 
                        id_empresa, 
                        titulo, 
                        motivo, 
                        part_number, 
                        estabelecimento,
                        criado_em
                    ) VALUES (
                        $cad_item->id_item, 
                        'Engenharia', 
                        1, 
                        $id_empresa, 
                        'atualizacao', 
                        'Alteração do Status: Perguntas Respondidas → Pendente de Homologação', 
                        '$item->part_number', 
                        '$item->estabelecimento',
                        NOW()
                    )";

                    $this->db->query($sql);

                    $this->db->query($sql2);

                    $return = true;

                }
            }
        }

        return $return;

    }

}
