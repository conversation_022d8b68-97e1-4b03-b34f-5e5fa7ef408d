<?php
defined('BASEPATH') || exit('No direct script access allowed');

/**
 * Script para atualizar status de atributos e registrar logs
 *
 * Este script pode ser executado via CLI ou através de um controller
 * Para executar via CLI: php index.php tools update_wf_status_atributos
 */

class Update_wf_status_atributos extends CI_Controller {
    
    private $estatisticas_empresas = [];
    
    public function __construct() {
        parent::__construct();
        $this->load->database();
        $this->load->helper('date');
    }
    
    /**
     * Método principal para executar a atualização
     */
    public function index() {
        // Verificar se está sendo executado via CLI para maior segurança
        if (!$this->input->is_cli_request() && ENVIRONMENT !== 'development') {
            show_error('Este script só pode ser executado via linha de comando.', 403);
            return;
        }
        
        echo "=== INÍCIO DO PROCESSAMENTO ===\n";
        echo "Data/Hora: " . date('Y-m-d H:i:s') . "\n\n";
        
        try {
            // Primeiro, buscar estatísticas por empresa
            $estatisticas_empresas = $this->buscar_estatisticas_empresas();
            
            if (empty($estatisticas_empresas)) {
                echo "Nenhuma empresa encontrada para processamento.\n";
                return;
            }
            
            echo "=== ESTATÍSTICAS POR EMPRESA ===\n";
            $total_itens_todas_empresas = 0;
            foreach ($estatisticas_empresas as $empresa) {
                echo sprintf(
                    "Empresa: %s (ID: %d) - %d itens para processar\n",
                    $empresa->nome_empresa,
                    $empresa->id_empresa,
                    $empresa->qtd_registros_atributo_ATT545
                );
                $total_itens_todas_empresas += $empresa->qtd_registros_atributo_ATT545;
            }
            echo "Total geral de itens: {$total_itens_todas_empresas}\n\n";
            
            // Agora buscar todos os itens individuais para processamento
            $itens_para_processar = $this->buscar_itens_para_processar();
            
            if (empty($itens_para_processar)) {
                echo "Nenhum item individual encontrado para processamento.\n";
                return;
            }
            
            // Inicializar estatísticas por empresa
            foreach ($estatisticas_empresas as $empresa) {
                $this->estatisticas_empresas[$empresa->id_empresa] = array(
                    'nome_empresa' => $empresa->nome_empresa,
                    'total_esperado' => $empresa->qtd_registros_atributo_ATT545,
                    'processados' => 0,
                    'erros' => 0
                );
            }
            
            echo "=== PROCESSAMENTO INDIVIDUAL ===\n";
            
            // Processar cada item
            $total_processados = 0;
            $total_erros = 0;
            
            foreach ($itens_para_processar as $item) {
                if ($this->processar_item($item)) {
                    $total_processados++;
                    $this->estatisticas_empresas[$item->id_empresa]['processados']++;
                    echo "✓ Processado: {$item->part_number} - Empresa: {$item->nome_empresa}\n";
                } else {
                    $total_erros++;
                    $this->estatisticas_empresas[$item->id_empresa]['erros']++;
                    echo "✗ Erro ao processar: {$item->part_number} - Empresa: {$item->nome_empresa}\n";
                }
            }
            
            $this->exibir_resumo_final($total_processados, $total_erros);
            
        } catch (Exception $e) {
            echo "ERRO CRÍTICO: " . $e->getMessage() . "\n";
            log_message('error', 'Erro no script update_wf_status_atributos: ' . $e->getMessage());
        }
    }
    
    /**
     * Busca estatísticas agrupadas por empresa
     */
    private function buscar_estatisticas_empresas() {
        $sql = "
            SELECT
                i.id_empresa,
                e.nome_fantasia AS nome_empresa,
                COUNT(DISTINCT i.part_number, i.estabelecimento) AS qtd_registros_atributo_ATT545
            FROM item i
            INNER JOIN cad_item ci ON i.part_number = ci.part_number
                AND i.id_empresa = ci.id_empresa
                AND i.estabelecimento = ci.estabelecimento
            INNER JOIN cad_item_attr cia ON ci.id_item = cia.id_item
            INNER JOIN empresa e ON i.id_empresa = e.id_empresa
            INNER JOIN comex c ON i.part_number = c.part_number_original
                AND i.estabelecimento = c.unidade_negocio
                AND i.id_empresa = c.id_empresa
            WHERE i.wf_status_atributos = 7
                AND c.ind_ecomex = 'EI'
                AND e.ativo = 1
                AND cia.atributo = 'ATT_14545'
                AND (cia.codigo = '' OR cia.codigo IS NULL)
            GROUP BY i.id_empresa, e.nome_fantasia
            ORDER BY qtd_registros_atributo_ATT545 DESC
        ";
        
        $query = $this->db->query($sql);
        return $query->result();
    }
    
    /**
     * Busca todos os itens individuais que atendem às condições especificadas
     */
    private function buscar_itens_para_processar() {
        $sql = "
            SELECT
                i.part_number,
                i.estabelecimento,
                i.id_empresa,
                e.nome_fantasia AS nome_empresa
            FROM item i
            INNER JOIN cad_item ci ON i.part_number = ci.part_number
                AND i.id_empresa = ci.id_empresa
                AND i.estabelecimento = ci.estabelecimento
            INNER JOIN cad_item_attr cia ON ci.id_item = cia.id_item
            INNER JOIN empresa e ON i.id_empresa = e.id_empresa
            INNER JOIN comex c ON i.part_number = c.part_number_original
                AND i.estabelecimento = c.unidade_negocio
                AND i.id_empresa = c.id_empresa
            WHERE i.wf_status_atributos = 7
                AND c.ind_ecomex = 'EI'
                AND e.ativo = 1
                AND cia.atributo = 'ATT_14545'
                AND (cia.codigo = '' OR cia.codigo IS NULL)
            ORDER BY i.id_empresa, i.part_number
        ";
        
        $query = $this->db->query($sql);
        return $query->result();
    }
    
    /**
     * Processa um item individual
     */
    private function processar_item($item) {
        // Iniciar transação
        $this->db->trans_start();
        
        try {
            echo "Processando item: {$item->part_number} - Empresa: {$item->nome_empresa}\n";
            
            // 1. Atualizar o status do item
            $update_data = array(
                'wf_status_atributos' => 6
            );
            
            $this->db->where('part_number', $item->part_number);
            $this->db->where('estabelecimento', $item->estabelecimento);
            $this->db->where('id_empresa', $item->id_empresa);
            $this->db->where('wf_status_atributos', 7); // Confirmar que ainda está em 7
            
            $this->db->update('item', $update_data);
            
            // Verificar se a atualização afetou alguma linha
            if ($this->db->affected_rows() == 0) {
                echo "  - Nenhuma linha foi atualizada para o item: {$item->part_number}\n";
                throw new Exception("Nenhuma linha foi atualizada para o item: {$item->part_number}");
            }
            
            // 2. Inserir registro no log
            $log_data = array(
                'part_number' => $item->part_number,
                'estabelecimento' => $item->estabelecimento,
                'id_empresa' => $item->id_empresa,
                'status_atual' => 'Em revisão por alteração no PUCOMEX',
                'justificativa' => 'Em revisão por alteração no PUCOMEX',
                'informacoes_integracao' => null,
                'detalhes' => 'Atributos criados: <b>ATT_14545</b>',
                'id_usuario' => 1,
                'tipo' => 'Atualização da NCM',
                'criado_em' => date('Y-m-d H:i:s')
            );
            
            $this->db->insert('log_wf_atributos', $log_data);
            
            // Finalizar transação
            $this->db->trans_complete();
            
            if ($this->db->trans_status() === FALSE) {
                echo "ERRO: Transação falhou para o item: {$item->part_number}\n";
                throw new Exception("Erro na transação para o item: {$item->part_number}");
            }
            
            return true;
            
        } catch (Exception $e) {
            // Rollback em caso de erro
            $this->db->trans_rollback();
            echo "ERRO: " . $e->getMessage() . "\n";
            log_message('error', "Erro ao processar item {$item->part_number}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Exibe resumo final detalhado por empresa
     */
    private function exibir_resumo_final($total_processados, $total_erros) {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "RESUMO DETALHADO POR EMPRESA\n";
        echo str_repeat("=", 60) . "\n";
        
        foreach ($this->estatisticas_empresas as $id_empresa => $stats) {
            $percentual_sucesso = $stats['total_esperado'] > 0 ?
                round(($stats['processados'] / $stats['total_esperado']) * 100, 2) : 0;
                
            echo sprintf(
                "Empresa: %s (ID: %d)\n" .
                "  - Esperado: %d itens\n" .
                "  - Processados: %d itens (%.2f%%)\n" .
                "  - Erros: %d itens\n" .
                "  - Status: %s\n\n",
                $stats['nome_empresa'],
                $id_empresa,
                $stats['total_esperado'],
                $stats['processados'],
                $percentual_sucesso,
                $stats['erros'],
                ($stats['processados'] == $stats['total_esperado']) ? '✅ COMPLETO' : '⚠️  PARCIAL'
            );
        }
        
        echo str_repeat("=", 60) . "\n";
        echo "RESUMO GERAL\n";
        echo str_repeat("=", 60) . "\n";
        echo "Total processados com sucesso: {$total_processados}\n";
        echo "Total com erro: {$total_erros}\n";
        echo "Total de empresas processadas: " . count($this->estatisticas_empresas) . "\n";
        echo "Data/Hora fim: " . date('Y-m-d H:i:s') . "\n";
    }
    
    /**
     * Método para executar um teste sem fazer alterações no banco
     */
    public function dry_run() {
        echo "=== MODO TESTE (DRY RUN) ===\n";
        echo "Nenhuma alteração será feita no banco de dados.\n\n";
        
        // Mostrar estatísticas por empresa
        $estatisticas = $this->buscar_estatisticas_empresas();
        
        if (empty($estatisticas)) {
            echo "Nenhuma empresa encontrada para processamento.\n";
            return;
        }
        
        echo "=== ESTATÍSTICAS POR EMPRESA ===\n";
        echo str_repeat("-", 80) . "\n";
        
        $total_geral = 0;
        foreach ($estatisticas as $empresa) {
            echo sprintf(
                "Empresa: %-40s | ID: %5d | Itens: %5d\n",
                substr($empresa->nome_empresa, 0, 40),
                $empresa->id_empresa,
                $empresa->qtd_registros_atributo_ATT545
            );
            $total_geral += $empresa->qtd_registros_atributo_ATT545;
        }
        
        echo str_repeat("-", 80) . "\n";
        echo "Total de empresas: " . count($estatisticas) . "\n";
        echo "Total de itens que seriam processados: {$total_geral}\n\n";
        
        // Mostrar alguns itens individuais como exemplo
        echo "=== EXEMPLO DE ITENS INDIVIDUAIS (primeiros 10) ===\n";
        $itens = $this->buscar_itens_para_processar();
        
        $contador = 0;
        foreach ($itens as $item) {
            if ($contador >= 10) {
                break;
            }
            
            echo sprintf(
                "Part Number: %-20s | Estabelecimento: %-10s | Empresa: %s\n",
                $item->part_number,
                $item->estabelecimento,
                $item->nome_empresa
            );
            $contador++;
        }
        
        if (count($itens) > 10) {
            echo "... e mais " . (count($itens) - 10) . " itens\n";
        }
    }
    
    /**
     * Método para verificar o status atual de um item específico
     */
    public function verificar_item($part_number = null, $estabelecimento = null, $id_empresa = null) {
        if (!$part_number || !$estabelecimento || !$id_empresa) {
            echo "Uso: php index.php tools update_wf_status_atributos verificar_item part_number estabelecimento id_empresa\n";
            return;
        }
        
        $this->db->select('part_number, estabelecimento, id_empresa, wf_status_atributos');
        $this->db->where('part_number', $part_number);
        $this->db->where('estabelecimento', $estabelecimento);
        $this->db->where('id_empresa', $id_empresa);
        
        $query = $this->db->get('item');
        $item = $query->row();
        
        if ($item) {
            echo "Item encontrado:\n";
            echo "Part Number: {$item->part_number}\n";
            echo "Estabelecimento: {$item->estabelecimento}\n";
            echo "ID Empresa: {$item->id_empresa}\n";
            echo "Status Atual: {$item->wf_status_atributos}\n";
        } else {
            echo "Item não encontrado.\n";
        }
    }
    
    /**
     * Método para verificar uma empresa específica
     */
    public function verificar_empresa($id_empresa = null) {
        if (!$id_empresa) {
            echo "Uso: php index.php tools update_wf_status_atributos verificar_empresa id_empresa\n";
            return;
        }
        
        echo "=== VERIFICAÇÃO DA EMPRESA {$id_empresa} ===\n";
        
        // Buscar estatísticas da empresa
        $sql = "
            SELECT
                i.id_empresa,
                e.nome_fantasia AS nome_empresa,
                COUNT(DISTINCT i.part_number, i.estabelecimento) AS qtd_registros_atributo_ATT545
            FROM item i
            INNER JOIN cad_item ci ON i.part_number = ci.part_number
                AND i.id_empresa = ci.id_empresa
                AND i.estabelecimento = ci.estabelecimento
            INNER JOIN cad_item_attr cia ON ci.id_item = cia.id_item
            INNER JOIN empresa e ON i.id_empresa = e.id_empresa
            INNER JOIN comex c ON i.part_number = c.part_number_original
                AND i.estabelecimento = c.unidade_negocio
                AND i.id_empresa = c.id_empresa
            WHERE i.wf_status_atributos = 7
                AND c.ind_ecomex = 'EI'
                AND e.ativo = 1
                AND cia.atributo = 'ATT_14545'
                AND (cia.codigo = '' OR cia.codigo IS NULL)
                AND i.id_empresa = ?
            GROUP BY i.id_empresa, e.nome_fantasia
        ";
        
        $query = $this->db->query($sql, array($id_empresa));
        $empresa = $query->row();
        
        if ($empresa) {
            echo "Empresa: {$empresa->nome_empresa}\n";
            echo "ID: {$empresa->id_empresa}\n";
            echo "Itens para processar: {$empresa->qtd_registros_atributo_ATT545}\n";
        } else {
            echo "Empresa não encontrada ou não possui itens para processar.\n";
        }
    }
}

/**
 * Para usar este script:
 *
 * 1. Via CLI (linha de comando):
 *    php index.php tools update_wf_status_atributos
 *    php index.php tools update_wf_status_atributos dry_run
 *    php index.php tools update_wf_status_atributos verificar_item PART123 EST01 1
 *    php index.php tools update_wf_status_atributos verificar_empresa 353
 *
 * 2. Via controller (apenas em desenvolvimento):
 *    http://localhost/tools/update_wf_status_atributos
 *    http://localhost/tools/update_wf_status_atributos/dry_run
 *    http://localhost/tools/update_wf_status_atributos/verificar_item/PART123/EST01/1
 *    http://localhost/tools/update_wf_status_atributos/verificar_empresa/353
 *
 * IMPORTANTE:
 * - Sempre execute o dry_run primeiro para verificar quais itens seriam processados
 * - Faça backup do banco antes de executar em produção
 * - Execute em horários de menor movimento
 * - Monitore os logs do CodeIgniter para verificar erros
 */
