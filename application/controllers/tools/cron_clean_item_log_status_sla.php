<?php

// Crontab line to beta
// 0 8 * * 1-5 php /var/www/gestaotarifaria/current/index.php tools/cron_items_without_owner_mail >/dev/null 2>&
//

class Cron_Clean_Item_Log_Status_Sla extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
    }

    public function index()
    {
        $CI = &get_instance();
        
        $CI->db->query("SET SESSION sql_mode = ''");

        $CI->db->query("INSERT INTO `item_log_status_sla` (
                  `id_empresa`,
                  `part_number`,
                  `descricao_curta`,
                  `estabelecimento`,
                  `id_usuario_responsavel`,
                  `status_anterior_descricao`,
                  `novo_status_descricao`,
                  `data_status_anterior`,
                  `data_novo_status`,
                  `objeto_alterado`,
                  `valor_anterior`,
                  `novo_valor`,
                  `data_objeto_alterado`
              )
          SELECT t1.id_empresa,
              t1.part_number,
              COALESCE(c.descricao_mercado_local, i.descricao) AS descricao_curta,
              t1.estabelecimento,
              t1.id_usuario AS id_usuario_responsavel,
              REPLACE(
                  SUBSTR(
                      t1.motivo,
                      POSITION('<em>' IN t1.motivo),
                      POSITION('</em>' IN t1.motivo) - POSITION('<em>' IN t1.motivo)
                  ),
                  '<em>',
                  ''
              ) AS status_anterior_descricao,
              REPLACE(
                  SUBSTR(
                      t1.motivo,
                      POSITION('<strong>' IN t1.motivo),
                      POSITION('</strong>' IN t1.motivo) - POSITION('<strong>' IN t1.motivo)
                  ),
                  '<strong>',
                  ''
              ) AS novo_status_descricao,
              COALESCE(
                  (
                      SELECT temp1.criado_em
                      FROM item_log temp1
                      WHERE temp1.id_item_log <> t1.id_item_log
                          AND temp1.titulo = 'atualizacao'
                          AND temp1.motivo LIKE 'Alteração do Status: <em>%'
                          AND temp1.part_number = t1.part_number
                          AND temp1.id_empresa = t1.id_empresa
                          AND temp1.estabelecimento = t1.estabelecimento
                          AND temp1.criado_em < t1.criado_em
                      ORDER BY temp1.id_item_log DESC
                      LIMIT 1
                  ), i.dat_criacao
              ) AS data_status_anterior,
              t1.criado_em AS data_novo_status,
              NULL as objeto_alterado,
              NULL as valor_anterior,
              NULL as novo_valor,
              NULL as data_objeto_alterado
          FROM item_log t1
              JOIN empresa e ON e.id_empresa = t1.id_empresa
              JOIN item i ON i.part_number = t1.part_number
              AND i.estabelecimento = t1.estabelecimento
              AND i.id_empresa = t1.id_empresa
              LEFT JOIN cad_item c ON c.id_empresa = t1.id_empresa
              AND c.estabelecimento = t1.estabelecimento
              AND c.part_number = t1.part_number
              LEFT JOIN item_log_status_sla t2 ON t2.id_empresa = t1.id_empresa
              AND t2.estabelecimento = t1.estabelecimento
              AND t2.part_number = t1.part_number
              AND t2.data_novo_status = t1.criado_em
          WHERE t1.titulo = 'atualizacao'
              AND t1.motivo LIKE 'Alteração do Status: <em>%'
              AND t2.id_item_log_status_sla IS NULL
              AND e.ativo = 1
              AND i.dat_criacao < '2024-08-26 00:00:00'
          limit 1000;");
        // $CI->db->query("DELETE FROM item_log
        // WHERE id_item_log IN (
        //   SELECT id_item_log 
        //   FROM (
        //     SELECT id_item_log,COUNT(*) AS total_ocorrência  FROM item_log
        //     group by id_item,tipo_homologacao,id_usuario,id_empresa,titulo,motivo,part_number,estabelecimento,criado_em
        //   ) AS subquery
        //   WHERE total_ocorrência > 1
        // );");

        // $CI->db->query("DELETE FROM item_log_status_sla
        // WHERE id_item_log_status_sla IN (
        //   SELECT id_item_log_status_sla 
        //   FROM (
        //     SELECT id_item_log_status_sla, COUNT(*) AS total_ocorrência  FROM item_log_status_sla
        //     WHERE  data_novo_status is not null
        //     group by id_empresa,part_number,estabelecimento,data_novo_status
        //     HAVING total_ocorrência > 1
        //   ) AS subquery
        //   WHERE total_ocorrência > 1
        // );");

        // echo 'Limpeza completa';

        echo 'Registros atualizados';
    }

 
}
