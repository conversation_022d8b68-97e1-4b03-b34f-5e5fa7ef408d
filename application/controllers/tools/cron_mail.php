<?php

// Crontab line to beta
// */5 * * * * php /var/www/gestaotarifaria/current/index.php tools/cron_mail >/dev/null 2>&
//
class Cron_mail extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();

        if (!$this->input->is_cli_request()) {
            die('Leave me alone.');
        }
    }

    public function index()
    {
        $this->notificar_mudanca_status();
        $this->notificar_respostas();
    }


    private function get_users_notification()
    {
        $this->db->select('ctr.id_usuario_pergunta');
        $this->db->where('ctr.notificar', 'notificar');
        $this->db->where('ctr.pendente', 0);

        $this->db->group_by('ctr.id_usuario_pergunta, ctr.id_empresa');
        $query = $this->db->get('ctr_pendencias_pergunta ctr');
        return $query->result();
    }

    private function get_questions_by_users($user)
    {
        $this->db->select('ctr.id,
        ctr.part_number,ctr.estabelecimento,u.nome,u.email,ctr.id_empresa');
        $this->db->where('ctr.notificar', 'notificar');
        $this->db->where('ctr.pendente', 0);
        $this->db->where('ctr.id_usuario_pergunta', $user);
        $this->db->join('usuario u', 'u.id_usuario = ctr.id_usuario_pergunta', 'inner');
        $query = $this->db->get('ctr_pendencias_pergunta ctr');
        
        return  $query->result();
    }

    private function notificar_respostas()
    {
        $this->load->model('empresa_model');
        $this->load->library('email');
        $data_mail = [];
        $itens_respondidos = [];
        $users_notification = $this->get_users_notification();

        if (empty($users_notification))
            return ;

        foreach ($users_notification as $user) {

            $itens_respondidos = $this->get_questions_by_users($user->id_usuario_pergunta);

            if (empty($itens_respondidos))
            {
                continue;
            }
            $itens_part_numbers = [];

            foreach ($itens_respondidos as $item) {
                $itens_part_numbers[$item->part_number][!empty($item->estabelecimento) ? $item->estabelecimento : 'N/A'][] =
                    array(
                        'part_number' => $item->part_number
                    );
                $usuarioNome = $item->nome;
                $usuarioEmail = $item->email;
                $Empresa = $item->id_empresa;
                $this->db->where('id', $item->id);
                $this->db->update('ctr_pendencias_pergunta', array('notificar' => 'notificado'));
            }

            $this->load->library('email');
            $empresa = $this->empresa_model->get_entry($Empresa);
            $data_mail = array('itens' => $itens_part_numbers, 'userName' => $usuarioNome, 'userMail' => $usuarioEmail);
            $data_mail['base_url'] = config_item('online_url') . '/';
            $data_mail['empresaNome'] = $empresa->nome_fantasia;
            $body = $this->load->view('templates/notificacao_resposta_pergunta', $data_mail, true);

            $this->email->from(config_item('mail_from_addr'));
            $this->email->to($usuarioEmail);


            $this->email->subject('[Gestão Tarifária] - Perguntas Respondidas');
            $this->email->message($body);
            $this->email->send();
        }

        return true;
    }

    private function get_itens_alterados()
    {
        $this->load->model('usuario_model');
        $this->load->model('item_model');
        $this->load->model('empresa_model');

        $this->db->select('r.data_alteracao,r.estabelecimento as item_estabelecimento, r.id as registro, e.nome_fantasia, r.id_empresa, r.part_number, r.estabelecimento, r.descricao, r.status_atual, r.status_anterior, r.responsavel_fiscal_anterior, r.responsavel_fiscal_atual, r.responsavel_engenharia_anterior, r.responsavel_engenharia_atual');
        $this->db->join('empresa e', 'e.id_empresa = r.id_empresa', 'inner');
        $this->db->where('r.enviado', 0);
        $this->db->order_by('r.id_empresa, r.status_atual', 'ASC');
        $query = $this->db->get('rotina_email r');
        return  $query->result();
    }

    private function get_status()
    {
        $this->load->model('item_model');
        $lista_status = [];
        $listas = $this->item_model->get_status_list();
        foreach ($listas as $lista) {
            $lista_status[$lista->id]['status'] = $lista->status;
            $lista_status[$lista->id]['slug'] = $lista->slug;
        }
        return $lista_status;
    }

    private function formatar_itens($data)
    {
        $itens_formatados = [];
        foreach ($data as $status => $itens) {

            foreach ($itens as $item) {
                $data_alteracao = null;
                if (!empty($item['data'])) {
                    $data_val = $item['data'];
                    $phpdate = strtotime($data_val);
                    $data_alteracao = date('d/m/Y H:i:s', $phpdate) . ' - ';
                }

                if (!empty($item['message_responsavel_fiscal'])) {
                    $itens_formatados[$item['id_empresa']][$item['estabelecimento']][$status]['part_numbers'][$item['part_number']]['message_responsavel_fiscal'][] = $data_alteracao . $item['message_responsavel_fiscal'];
                }
                if (!empty($item['message_responsavel_engenharia'])) {
                    $itens_formatados[$item['id_empresa']][$item['estabelecimento']][$status]['part_numbers'][$item['part_number']]['message_responsavel_engenharia'][] = $data_alteracao . $item['message_responsavel_engenharia'];
                }
                if (!empty($item['message_status'])) {
                    $itens_formatados[$item['id_empresa']][$item['estabelecimento']][$status]['part_numbers'][$item['part_number']]['message_status'][] = $data_alteracao . $item['message_status'];
                }
                $itens_formatados[$item['id_empresa']][$item['estabelecimento']][$status]['fiscal'][] = $item['fiscal'];
                $itens_formatados[$item['id_empresa']][$item['estabelecimento']][$status]['engenharia'][] = $item['engenharia'];

                $itens_formatados[$item['id_empresa']][$item['estabelecimento']][$status]['lista_usuarios'] = null;
                $itens_formatados[$item['id_empresa']][$item['estabelecimento']][$status]['notificar'] = null;

                $itens_formatados[$item['id_empresa']][$item['estabelecimento']][$status]['part_numbers'][$item['part_number']]['descricao'] = $item['descricao_item'];
                $itens_formatados[$item['id_empresa']][$item['estabelecimento']][$status]['registro'][] = $item['registro'];
                $itens_formatados[$item['id_empresa']][$item['estabelecimento']][$status]['empresa']  = $item['descricao_empresa'];
            }
        }

        return $itens_formatados;
    }

    private function notificar_mudanca_status()
    {
        $this->load->model('usuario_model');
        $this->load->model('empresa_model');
        $data_mail =  $this->get_itens_alterados();

        $responsaveis = [];

        $data = [];
        $count = 0;
        $lista_status = $this->get_status();

        foreach ($data_mail as $data_formatado) {
            $count++;

            $slug_status = $lista_status[$data_formatado->status_atual]['slug'];
            $data[$slug_status][$count]['descricao_status_atual'] = isset($lista_status[$data_formatado->status_atual]['status']) ? $lista_status[$data_formatado->status_atual]['status'] : null;
            $data[$slug_status][$count]['descricao_status_anterior'] = isset($lista_status[$data_formatado->status_anterior]['status']) ? $lista_status[$data_formatado->status_anterior]['status'] : null;
            $data[$slug_status][$count]['descricao_empresa'] = $data_formatado->nome_fantasia;
            $data[$slug_status][$count]['id_empresa'] = $data_formatado->id_empresa;
            $data[$slug_status][$count]['estabelecimento'] = empty($data_formatado->estabelecimento) ? 'vazio' : $data_formatado->estabelecimento;
            $data[$slug_status][$count]['part_number'] = $data_formatado->part_number;
            $data[$slug_status][$count]['descricao_item'] = $data_formatado->descricao;
            $data[$slug_status][$count]['data'] = $data_formatado->data_alteracao;
            $data[$slug_status][$count]['registro'] = $data_formatado->registro;

            if (!empty($data_formatado->responsavel_fiscal_anterior)) {
                // A funcao abaixo reduz a necessidade de ficar realizando requisições no banco de dados em cada registro
                if (!array_key_exists($data_formatado->responsavel_fiscal_anterior, $responsaveis)) {
                    $response = $this->usuario_model->get_entry($data_formatado->responsavel_fiscal_anterior);
                    $responsaveis[$data_formatado->responsavel_fiscal_anterior] = $response;
                    $responsavel_fiscal_anterior =  $responsaveis[$data_formatado->responsavel_fiscal_anterior];
                } else {
                    $responsavel_fiscal_anterior =  $responsaveis[$data_formatado->responsavel_fiscal_anterior];
                }
            }

            if (!empty($data_formatado->responsavel_fiscal_atual)) {
                if (!array_key_exists($data_formatado->responsavel_fiscal_atual, $responsaveis)) {
                    $response = $this->usuario_model->get_entry($data_formatado->responsavel_fiscal_atual);
                    $responsaveis[$data_formatado->responsavel_fiscal_atual] = $response;
                    $responsavel_fiscal_atual =  $responsaveis[$data_formatado->responsavel_fiscal_atual];
                } else {
                    $responsavel_fiscal_atual =  $responsaveis[$data_formatado->responsavel_fiscal_atual];
                }
            }
            if (!empty($data_formatado->responsavel_engenharia_anterior)) {
                if (!array_key_exists($data_formatado->responsavel_engenharia_anterior, $responsaveis)) {
                    $response = $this->usuario_model->get_entry($data_formatado->responsavel_engenharia_anterior);
                    $responsaveis[$data_formatado->responsavel_engenharia_anterior] = $response;
                    $responsavel_engenharia_anterior = $responsaveis[$data_formatado->responsavel_engenharia_anterior];
                } else {
                    $responsavel_engenharia_anterior = $responsaveis[$data_formatado->responsavel_engenharia_anterior];
                }
            }

            if (!empty($data_formatado->responsavel_engenharia_atual)) {
                if (!array_key_exists($data_formatado->responsavel_engenharia_atual, $responsaveis)) {
                    $response = $this->usuario_model->get_entry($data_formatado->responsavel_engenharia_atual);
                    $responsaveis[$data_formatado->responsavel_engenharia_atual] = $response;
                    $responsavel_engenharia_atual = $responsaveis[$data_formatado->responsavel_engenharia_atual];
                } else {
                    $responsavel_engenharia_atual = $responsaveis[$data_formatado->responsavel_engenharia_atual];
                }
            }

            if (!empty($data_formatado->status_anterior) && htmlentities($data[$slug_status][$count]['descricao_status_anterior']) != htmlentities($data[$slug_status][$count]['descricao_status_atual'])) {
                $descricao_status_anterior = htmlentities($data[$slug_status][$count]['descricao_status_anterior']) == 'Homologar' ? 'Pendente de Homologação' : htmlentities($data[$slug_status][$count]['descricao_status_anterior']);
                $descricao_status_atual = htmlentities($data[$slug_status][$count]['descricao_status_atual']) == 'Homologar' ? 'Pendente de Homologação' : htmlentities($data[$slug_status][$count]['descricao_status_atual']);

                $data[$slug_status][$count]['message_status'] = 'Mudança de Status de: ' . $descricao_status_anterior . ' para: ' . $descricao_status_atual;
            }

            if (!empty($data_formatado->responsavel_fiscal_anterior) && $responsavel_fiscal_anterior->nome != $responsavel_fiscal_atual->nome) {
                $data[$slug_status][$count]['message_responsavel_fiscal'] = 'Mudança de Responsavel Fiscal de: ' . $responsavel_fiscal_anterior->nome . ' para: ' . $responsavel_fiscal_atual->nome;
            }

            if (!empty($data_formatado->responsavel_engenharia_anterior) && $responsavel_engenharia_anterior->nome != $responsavel_engenharia_atual->nome) {
                $data[$slug_status][$count]['message_responsavel_engenharia'] = 'Mudança de Responsavel de Engenharia de: ' . $responsavel_engenharia_anterior->nome . ' para: ' . $responsavel_engenharia_atual->nome;
            }
            $data[$slug_status][$count]['engenharia'] = $responsavel_engenharia_atual->email;
            $data[$slug_status][$count]['fiscal'] = $responsavel_fiscal_atual->email;

            if (empty($data[$slug_status][$count]['message_status']) && empty($data[$slug_status][$count]['message_responsavel_fiscal']) && empty($data[$slug_status][$count]['message_responsavel_engenharia'])) {
                unset($data[$slug_status][$count]);
                continue;
            }
        }

        $itens_formatados = $this->formatar_itens($data);
        $data = $this->get_users_mail($itens_formatados);
        $data_mail = [];

        if (!empty($data)) {
            foreach ($data as $id_estabelecimento => $empresa) {
                foreach ($empresa as $id_estabelecimento => $item) {
                    foreach ($item as $status => $estabelecimento) {

                        $data_mail['descricao_empresa'] = $estabelecimento['empresa'];
                        $data_mail['estabelecimento'] = $id_estabelecimento;
                        $data_mail['lista_usuarios'] = $estabelecimento['lista_usuarios'];
                        $data_mail['notificar'] = $estabelecimento['notificar'];
                        $data_mail['part_numbers'] = $estabelecimento['part_numbers'];
                        $data_mail['base_url'] = config_item('online_url') . '/';

                        if ($data_mail['notificar'] == 'todos') {
                            $copia = [];
                            $copia = array_merge($estabelecimento['engenharia'], $estabelecimento['fiscal']);
                        } else if ($data_mail['notificar'] == 'resp_engenharia') {
                            $copia = [];
                            $copia = array_merge($copia, $estabelecimento['engenharia']);
                        } else if ($data_mail['notificar'] == 'resp_fiscal') {
                            $copia = [];
                            $copia =  array_merge($copia, $estabelecimento['fiscal']);
                        }

                        if (empty($data_mail['lista_usuarios']))
                            continue;

                        $this->load->library('email');

                        $body = $this->load->view('templates/notificacao_mudanca_item', $data_mail, true);

                        $this->email->from(config_item('mail_from_addr'));
                        $this->email->to(array_unique($data_mail['lista_usuarios']));

                        if (!empty($copia)) {
                            $this->email->cc(array_unique($copia));
                        }

                        $this->email->subject('[Gestão Tarifária] - Alteração de Status de Part Number');
                        $this->email->message($body);
                        $this->email->send();

                        $this->db->where_in('id', $estabelecimento['registro']);
                        $this->db->update('rotina_email', array('enviado' => 1));
                    }
                }
            }
        }

        $this->db->delete('rotina_email', array('enviado' => '1'));
    }

    public function get_users_mail($itens)
    {
        foreach ($itens as $empresa => $id) {
            foreach ($id as $estabelecimento => $i) {
                foreach ($i as $status => $p) {
                    $this->db->select('*');
                    $this->db->where('id_empresa', $empresa);
                    $this->db->where('estabelecimento', $estabelecimento == 'vazio' ? '' : $estabelecimento);
                    $this->db->where('status', $status);
                    $query = $this->db->get('rel_status');
                    $contatos = $query->result_array();

                    if (empty($contatos))
                        continue;

                    $itens[$empresa][$estabelecimento][$status]['lista_usuarios'] = array_column($contatos, 'email');
                    $itens[$empresa][$estabelecimento][$status]['notificar'] = reset($contatos)['notificar_resp'];
                }
            }
        }

        return $itens;
    }
}
