<?php

// Crontab line to beta
// 0 8 * * 1-5 php /var/www/gestaotarifaria/current/index.php tools/cron_items_without_owner_mail >/dev/null 2>&
//

class Cron_Items_Without_Owner_Mail extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();

        $this->load->model('item_model');
        $this->load->model('unidade_negocio_model');
    }

    public function index()
    {
        $this->notificar_unidade_negocio_itens_sem_owner();
    }

    private function notificar_unidade_negocio_itens_sem_owner()
    {
        $lista_itens_sem_owner = $this->item_model->get_itens_sem_owner_dia_anterior();

        $item_by_estabelecimento = [];

        if (is_array($lista_itens_sem_owner) && count($lista_itens_sem_owner) > 0) {
            foreach ($lista_itens_sem_owner as $item) {
                if (!isset($item_by_estabelecimento[$item->estabelecimento])) {
                    $item_by_estabelecimento[$item->estabelecimento] = [
                        'lista' => []
                    ];
                }

                $item_by_estabelecimento[$item->estabelecimento]['lista'][] = [
                    'part_number' => $item->part_number,
                    'descricao' => $item->descricao,
                    'criacao' => $item->dat_criacao,
                    'prioridade' => $item->prioridade,
                    'estabelecimento' => $item->estabelecimento,
                    'id_empresa' => $item->id_empresa,
                    'nome_empresa' => $item->nome_empresa,
                ];
            }

            $lista_envio = [];

            foreach ($item_by_estabelecimento as $estabelecimento => $item) {
                $usuarios_unidade_negocio = $this->unidade_negocio_model->get_usuarios_email_triagem_owner($estabelecimento);

                if (is_array($usuarios_unidade_negocio) && count($usuarios_unidade_negocio) > 0) {
                    foreach ($usuarios_unidade_negocio as $usuario) {
                        if (!isset($lista_envio[$usuario->email])) {
                            $lista_envio[$usuario->email] = [
                                'nome' => $usuario->nome,
                                'email' => $usuario->email,
                                'estabelecimento' => $estabelecimento,
                                'id_empresa' => $item_by_estabelecimento[$estabelecimento]['lista'][0]['id_empresa'],
                                'nome_empresa' => $item_by_estabelecimento[$estabelecimento]['lista'][0]['nome_empresa'],
                                'lista' => []
                            ];
                        }
                    }

                    foreach ($lista_itens_sem_owner as $item) {
                        if ($item->estabelecimento == $estabelecimento) {
                            foreach ($usuarios_unidade_negocio as $usuario) {
                                $lista_envio[$usuario->email]['lista'][] = [
                                    'part_number' => $item->part_number,
                                    'descricao' => $item->descricao,
                                    'criacao' => $item->dat_criacao,
                                    'prioridade' => $item->prioridade,
                                    'estabelecimento' => $item->estabelecimento,
                                    'id_empresa' => $item->id_empresa,
                                    'nome_empresa' => $item->nome_empresa,
                                ];
                            }
                        }
                    }
                }
            }

            if (count($lista_envio) > 0) {
                foreach ($lista_envio as $email => $data) {
    
                    $unidade_negocio_db = $this->unidade_negocio_model->get_unidade_negocio_by_estabelecimento($data['estabelecimento'], $data['id_empresa']);
                    if (!empty($unidade_negocio_db)) {
                        $unidade_negocio = $unidade_negocio_db->descricao;
                    } else {
                        $unidade_negocio = '';
                    }
                    
                    $data['assunto'] = '[GT] Relação de Itens Aguardando Definição Responsável - ' . date('d/m/Y') . ' - ' . 'Unidade de Negócio: ' . $data['estabelecimento'] . ' - ' . $unidade_negocio;
            
                    // $unidade_negocio_db = $this->unidade_negocio_model->get_unidade_negocio_by_estabelecimento($estabelecimento);
                    // if (!empty($unidade_negocio_db)) {
                    //     $unidade_negocio = $unidade_negocio_db->descricao;
                    // } else {
                    //     $unidade_negocio = '';
                    // }
                    // $data['assunto'] = '[GT] Relação de Itens Aguardando Definição Responsável - ' . date('d/m/Y') . ' - ' . 'Unidade de Negócio: ' . $unidade_negocio;
                    
                    // $data['assunto'] = 'Teste';

                    $data['base_url'] = config_item('online_url');

                    $body = $this->load->view('templates/notificacao_unidade_negocio_itens_sem_owner', $data, TRUE);

                    $this->load->library('email');

                    $this->email->from(config_item('mail_from_addr'), config_item('mail_from_name'));
                    $this->email->to($data['email']);
                    $this->email->subject($data['assunto']);
                    $this->email->message($body);

                    $this->email->send();
                }
            }
        }
    }
}
