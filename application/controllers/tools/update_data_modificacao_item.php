<?php defined('BASEPATH') || exit('No direct script access allowed');
/**
 * Script para atualizar o campo 'data_modificacao' da tabela 'item' com base nas tabelas 'item_log' e 'log_wf_atributos'
 *
 * Uso via CLI:
 * php index.php tools/update_data_modificacao_item/index [dry_run]
 *
 * Exemplos:
 * php index.php tools/update_data_modificacao_item/index true    # Executa em modo de teste (sem atualizar)
 * php index.php tools/update_data_modificacao_item/index         # Executa para todas as empresas
 */
class Update_data_modificacao_item extends CI_Controller {
    
    private $dry_run = false;
    private $total_itens = 0;
    private $total_atualizados = 0;
    private $total_erros = 0;
    
    public function __construct() {
        parent::__construct();
        $this->load->database();
        
        // Verifica se está sendo executado via CLI
        if (!$this->input->is_cli_request()) {
            show_error("Este script deve ser executado via linha de comando.", 403);
        }

        ini_set('memory_limit', '-1');
    }
    
    public function index($dry_run = false) {
        $this->dry_run = ($dry_run === 'true');
        
        echo "=== INÍCIO DO PROCESSAMENTO ===\n";
        echo "Data/Hora: " . date('Y-m-d H:i:s') . "\n";
        echo "Modo de teste (dry run): " . ($this->dry_run ? "SIM" : "NÃO") . "\n\n";
        
        try {
            $this->processar_itens();

            $this->exibir_resumo();
        } catch (Exception $e) {
            echo "ERRO CRÍTICO: " . $e->getMessage() . "\n";
            log_message('error', 'Erro no script update_observacoes_item: ' . $e->getMessage());
            exit(1);
        }
    }

    /**
     * Obtém os itens de uma empresa que precisam ter a data de modificação atualizada  
     *
     * @param int $id_empresa ID da empresa
     * @return array Lista de itens para atualizar
     */
    private function obter_itens_empresa($id_empresa) {
        // $query = $this->db->query("
        //     SELECT
        //         i.part_number,
        //         i.id_empresa,
        //         i.estabelecimento,
        //         i.data_modificacao AS data_modificacao_atual,
        //         GREATEST(il.max_criado_em, lw.max_criado_em) AS data_modificacao_mais_recente
        //     FROM item i
        //     LEFT JOIN (
        //         SELECT
        //             part_number,
        //             id_empresa,
        //             estabelecimento,
        //             MAX(criado_em) AS max_criado_em
        //         FROM item_log
        //         GROUP BY part_number, id_empresa, estabelecimento
        //     ) il ON il.part_number = i.part_number
        //         AND il.id_empresa = i.id_empresa
        //         AND il.estabelecimento = i.estabelecimento
        //     LEFT JOIN (
        //         SELECT
        //             part_number,
        //             id_empresa,
        //             estabelecimento,
        //             MAX(criado_em) AS max_criado_em
        //         FROM log_wf_atributos
        //         GROUP BY part_number, id_empresa, estabelecimento
        //     ) lw ON lw.part_number = i.part_number
        //         AND lw.id_empresa = i.id_empresa
        //         AND lw.estabelecimento = i.estabelecimento
        //     WHERE i.id_empresa = ?
        //     AND GREATEST(
        //         COALESCE(il.max_criado_em, i.data_modificacao),
        //         COALESCE(lw.max_criado_em, i.data_modificacao)
        //     ) > i.data_modificacao;
        // ", [$id_empresa]);

        // $this->total_itens += $query->num_rows();
        // return $query->result();
    }
    
    private function processar_itens() {
        $this->db->trans_begin();
        
        try {
            $this->atualizar_itens();
            
            if ($this->dry_run) {
                $this->db->trans_rollback();
                echo "  - Modo de teste: rollback realizado, nenhuma alteração foi salva\n";
            } else {
                $this->db->trans_commit();
                echo "  - Transação concluída com sucesso\n";
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();
            echo "  - ERRO durante o processamento: " . $e->getMessage() . "\n";
            echo "  - Rollback realizado, nenhuma alteração foi salva\n";
            log_message('error', "Erro durante o processamento de itens: " . $e->getMessage());
            $this->total_erros++;
        }
    }

    /**
     * Atualiza as datas de modificação dos itens na tabela 'item'
     */
    private function atualizar_itens() {
        try {
            if (!$this->dry_run) {
                $this->db->query("
                    UPDATE item i
                    LEFT JOIN (
                        SELECT
                            part_number,
                            id_empresa,
                            estabelecimento,
                            MAX(criado_em) AS max_criado_em
                        FROM item_log
                        GROUP BY part_number, id_empresa, estabelecimento
                    ) il ON il.part_number = i.part_number
                        AND il.id_empresa = i.id_empresa
                        AND il.estabelecimento = i.estabelecimento
                    LEFT JOIN (
                        SELECT
                            part_number,
                            id_empresa,
                            estabelecimento,
                            MAX(criado_em) AS max_criado_em
                        FROM log_wf_atributos
                        GROUP BY part_number, id_empresa, estabelecimento
                    ) lw
                        ON lw.part_number = i.part_number
                        AND lw.id_empresa = i.id_empresa
                        AND lw.estabelecimento = i.estabelecimento
                    SET i.data_modificacao = COALESCE(
                        GREATEST(
                            COALESCE(il.max_criado_em, i.data_modificacao),
                            COALESCE(lw.max_criado_em, i.data_modificacao)
                        ),
                        COALESCE(i.data_modificacao, i.dat_criacao)
                    )
                ", []);
            }

            $this->total_atualizados++;
        } catch (Exception $e) {
            echo "  - ERRO ao atualizar data de modificação dos itens: " . $e->getMessage() . "\n";
            log_message('error', "Erro ao atualizar data de modificação dos itens: " . $e->getMessage());
            $this->total_erros++;
            throw $e;
        }
    }

    /**
     * Exibe o resumo do processamento
     */
    private function exibir_resumo() {
        echo "\n=== RESUMO DO PROCESSAMENTO ===\n";
        echo "Total de itens processados: {$this->total_itens}\n";
        echo "Total de itens atualizados: {$this->total_atualizados}\n";
        echo "Total de erros: {$this->total_erros}\n";
        echo "Data/Hora de conclusão: " . date('Y-m-d H:i:s') . "\n";
        echo "Modo de teste (dry run): " . ($this->dry_run ? "SIM" : "NÃO") . "\n";
        echo "=== FIM DO PROCESSAMENTO ===\n";
    }
}
