<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

use PhpOffice\PhpSpreadsheet\Reader\Xlsx;

/**
 * Controller para processar planilha e atualizar COMEX/Item
 *
 * Lê o arquivo XLSX em assets/tmp/Alteraritensparaimportado.xlsx,
 * obtém os part numbers (removendo asteriscos e ignorando o cabeçalho),
 * insere/atualiza registros na tabela comex e ajusta wf_status_atributos
 * na tabela item quando necessário.
 *
 * Regras:
 * - id_base_comex = 1
 * - id_empresa = 1
 * - part_number_original = PN
 * - part_number_modificado = PN
 * - cnpj_raiz = 16701716
 * - unidade_negocio = estabelecimento do item
 * - ind_ecomex = 'EI'
 * - data_criacao = agora()
 * - Atualizar item.wf_status_atributos = 2 se estiver NULL ou = 1
 *
 * Execução: somente via CLI.
 */
/**
 * @property CI_Input $input
 * @property CI_Loader $load
 * @property CI_DB_query_builder $db
 * @property Item_model $item_model
 */
class Update_Importado_E_Wf extends CI_Controller // NOSONAR: manter nome para compatibilidade CI
{
    // Constantes e configuração
    private const EMPRESA_ID = 1;
    private const ID_BASE_COMEX = 1;
    private const CNPJ_RAIZ = '16701716';
    private const IND_ECOMEX = 'EI';
    private const FILE_RELATIVE_PATH = 'assets/tmp/Alteraritensparaimportado.xlsx';

    // Contadores de processamento
    private $countTotal = 0;
    private $countFound = 0;
    private $countNotFound = 0;
    private $countInserted = 0;
    private $countUpdated = 0;
    private $countItemStatusUpdated = 0;
    private $countSkippedInvalid = 0;
    private $startTime;

    private $triggerName = 'tr_item_update_catalogo_envio';
    private $triggerSql = "CREATE DEFINER=`gestaotarifaria_stellantis`@`%` TRIGGER tr_item_update_catalogo_envio
        AFTER UPDATE ON item
        FOR EACH ROW
        BEGIN
            DECLARE v_id_item BIGINT(20) DEFAULT NULL;

            IF ((NEW.descricao_proposta_completa <> ''
                    OR (OLD.descricao_proposta_completa != NEW.descricao_proposta_completa
                        OR OLD.descricao_proposta_completa IS NULL AND NEW.descricao_proposta_completa IS NOT NULL))
                OR (NEW.id_status = 2)
                OR (NEW.wf_status_atributos = 7)) THEN
                SELECT
                    cad_item.id_item INTO v_id_item
                FROM cad_item
                WHERE 1=1
                    AND cad_item.part_number = NEW.part_number
                    AND cad_item.id_empresa = NEW.id_empresa
                    AND cad_item.estabelecimento = NEW.estabelecimento
                    LIMIT 1;
                IF v_id_item IS NOT NULL THEN
                    CALL sp_cria_catalogo_envio(v_id_item);
                END IF;
            END IF;
        END";

    public function __construct()
    {
        parent::__construct();

        if (!$this->input->is_cli_request()) {
            die('Acesso permitido apenas via linha de comando');
        }

        // Autoload do Composer para usar PhpSpreadsheet
        $autoload1 = FCPATH . 'vendor/autoload.php';
        $autoload2 = APPPATH . '../vendor/autoload.php';
        if (!class_exists(Xlsx::class)) {
            if (file_exists($autoload1)) {
                require_once $autoload1; // NOSONAR: necessário para carregar dependências do Composer
            } elseif (file_exists($autoload2)) {
                require_once $autoload2; // NOSONAR
            }
        }

        $this->load->model('item_model');
        $this->load->database();
    }

    /**
     * Ponto de entrada: processa o XLSX e aplica as alterações.
     */
    public function index()
    {
        ini_set('memory_limit', '-1');
        set_time_limit(0);
        $this->db->query("SET SESSION sql_mode = ''");

        $this->startTime = microtime(true);
        $filePath = $this->resolveFilePath();

        echo "Iniciando processamento da planilha: {$filePath}\n";
        log_message('info', __METHOD__ . ": Início processamento planilha {$filePath}");

        // Remove a trigger se existir
        $this->removerTrigger();

        if (!file_exists($filePath)) {
            $msg = "Arquivo não encontrado: {$filePath}";
            echo "ERRO: {$msg}\n";
            log_message('error', __METHOD__ . ': ' . $msg);
            return;
        }

        try {
            $this->processSpreadsheet($filePath);
            // Recria a trigger
            $this->criarTrigger();
        } catch (Throwable $e) {
            echo "ERRO FATAL: " . $e->getMessage() . "\n";
            log_message('error', __METHOD__ . ': ERRO FATAL: ' . $e->getMessage());
        } finally {
            $this->printSummary();
        }
    }

    /**
     * Resolve o caminho absoluto do arquivo da planilha.
     */
    private function resolveFilePath(): string
    {
        // FCPATH normalmente aponta para a raiz pública (onde está index.php)
        $candidate1 = rtrim(FCPATH, '/\\') . '/' . self::FILE_RELATIVE_PATH;
        if (file_exists($candidate1)) {
            return $candidate1;
        }

        // Fallback: relativo ao APPPATH/.. (raiz do projeto)
        $candidate2 = realpath(APPPATH . '../') . '/' . self::FILE_RELATIVE_PATH;
        return $candidate2 ?: $candidate1;
    }

    /**
     * Processa a planilha XLSX linha por linha.
     */
    private function processSpreadsheet(string $filePath): void
    {
        $reader = new Xlsx();
        $reader->setReadDataOnly(true);

        $spreadsheet = $reader->load($filePath);
        $sheet = $spreadsheet->getActiveSheet();
        $highestRow = $sheet->getHighestRow();

        // Assumimos que o part number está na primeira coluna (A)
        $pnColumn = 'A';

        echo "Linhas detectadas (inclui cabeçalho): {$highestRow}\n";

        for ($row = 2; $row <= $highestRow; $row++) { // pula cabeçalho (linha 1)
            $raw = $sheet->getCell($pnColumn . $row)->getValue();
            $pn = $this->sanitizePartNumber($raw);
            $this->countTotal++;

            if (empty($pn)) {
                $this->countSkippedInvalid++;
                if ($this->countSkippedInvalid <= 5) {
                    echo "Linha {$row}: PN inválido/ vazio. Ignorado.\n";
                }
                continue;
            }

            try {
                $this->processPartNumber($pn);
            } catch (Throwable $e) {
                $this->countSkippedInvalid++;
                $msg = "Falha ao processar PN {$pn} na linha {$row}: " . $e->getMessage();
                echo "ERRO: {$msg}\n";
                log_message('error', __METHOD__ . ': ' . $msg);
            }

            if ($this->countTotal % 1000 === 0) {
                echo $this->progressLine();
            }
        }
    }

    /**
     * Processa um único part number: localiza item, upsert em comex e atualiza status do item.
     */
    private function processPartNumber(string $pn): void
    {
        $item = $this->findItemByPartNumber($pn, self::EMPRESA_ID);
        if (!$item) {
            $this->countNotFound++;
            return;
        }

        $this->countFound++;

        $estabelecimento = $item->estabelecimento ?? null;
        if (empty($estabelecimento)) {
            // Sem estabelecimento não conseguimos montar a chave do COMEX
            $this->countSkippedInvalid++;
            log_message('warning', __METHOD__ . ": Item sem estabelecimento para PN {$pn}. Ignorado.");
            return;
        }

        $action = $this->upsertComex($pn, $estabelecimento);
        if ($action === 'insert') {
            $this->countInserted++;
        } elseif ($action === 'update') {
            $this->countUpdated++;
        }
        
        if ($this->updateItemStatusIfNeeded($pn, $estabelecimento, self::EMPRESA_ID)) {
            $this->countItemStatusUpdated++;
        }
    }

    /**
     * Remove asteriscos e espaços do part number.
     */
    private function sanitizePartNumber($value): string
    {
        if ($value === null) {
            return '';
        }
        $pn = trim((string) $value);
        // Remove asteriscos no início e fim, e espaços excedentes
        $pn = trim($pn, " *\t\n\r\0\x0B");
        return $pn;
    }

    /**
     * Busca o item utilizando item_model->get_entry_by_pn (sem LIKE).
     */
    private function findItemByPartNumber(string $pn, int $idEmpresa)
    {
        try {
            $item = $this->item_model->get_entry_by_pn($pn, $idEmpresa, null);
        } catch (Throwable $e) {
            // Fallback: consulta direta se ocorrer algum problema no model
            log_message('warning', __METHOD__ . ": Falha em get_entry_by_pn para PN {$pn}: " . $e->getMessage());
            $this->db->where('part_number', $pn);
            $this->db->where('id_empresa', $idEmpresa);
            $query = $this->db->get('item');
            $item = $query->row();
        }

        // Alguns models retornam array; padroniza para objeto (primeiro elemento)
        if (is_array($item)) {
            if (empty($item)) {
                return null;
            }
            return (object) $item[0];
        }
        return $item ?: null;
    }

    /**
     * Insere ou atualiza registro na tabela comex para o PN/estabelecimento.
     * Retorna 'insert', 'update' ou 'none'.
     */
    private function upsertComex(string $pn, string $estabelecimento): string
    {
        $now = date('Y-m-d H:i:s');

        $this->db->where('part_number_original', $pn);
        $this->db->where('id_empresa', self::EMPRESA_ID);
        $this->db->where('unidade_negocio', $estabelecimento);
        $existing = $this->db->get('comex')->row();

        $data = [
            'id_base_comex'       => self::ID_BASE_COMEX,
            'id_empresa'          => self::EMPRESA_ID,
            'part_number_original' => $pn,
            'part_number_modificado' => $pn,
            'cnpj_raiz'           => self::CNPJ_RAIZ,
            'unidade_negocio'     => $estabelecimento,
            'ind_ecomex'          => self::IND_ECOMEX,
            'data_criacao'        => $now,
        ];

        if ($existing) {
            // Atualiza somente campos relevantes, mantendo chaves
            $this->db->where('part_number_original', $pn)
                ->where('id_empresa', self::EMPRESA_ID)
                ->where('unidade_negocio', $estabelecimento)
                ->update('comex', $data);
            return 'update';
        } else {
            $this->db->insert('comex', $data);
            return 'insert';
        }
    }

    /**
     * Atualiza item.wf_status_atributos = 2 se estiver NULL ou = 1.
     * Retorna true se atualizou, false caso contrário.
     */
    private function updateItemStatusIfNeeded(string $pn, string $estabelecimento, int $idEmpresa): bool
    {
        // Busca valor atual para decidir
        $this->db->select('wf_status_atributos')
            ->where('part_number', $pn)
            ->where('id_empresa', $idEmpresa)
            ->where('estabelecimento', $estabelecimento);
        $row = $this->db->get('item')->row();

        $current = $row->wf_status_atributos ?? null;
        if ($current === null || (string)$current === '1' || (int)$current === 1) {
            $this->db->set('wf_status_atributos', 2)
                ->where('part_number', $pn)
                ->where('id_empresa', $idEmpresa)
                ->where('estabelecimento', $estabelecimento)
                ->update('item');
            return $this->db->affected_rows() > 0;
        }
        return false;
    }

    /**
     * Linha de progresso compacta a cada 1000 itens.
     */
    private function progressLine(): string
    {
        $elapsed = microtime(true) - $this->startTime;
        return sprintf(
            "[Progresso] total=%d, encontrados=%d, comex_ins=%d, comex_upd=%d, item_upd=%d, não_encontrados=%d, inválidos=%d, t=%.1fs\n",
            $this->countTotal,
            $this->countFound,
            $this->countInserted,
            $this->countUpdated,
            $this->countItemStatusUpdated,
            $this->countNotFound,
            $this->countSkippedInvalid,
            $elapsed
        );
    }

    /**
     * Imprime um resumo final do processamento.
     */
    private function printSummary(): void
    {
        $elapsed = microtime(true) - $this->startTime;
        $summary = sprintf(
            "\nResumo:\n- Lidas (sem cabeçalho): %d\n- Itens encontrados: %d\n- COMEX inseridos: %d\n- COMEX atualizados: %d\n- Itens com wf_status_atributos ajustado: %d\n- Não encontrados: %d\n- Inválidos/erros: %d\n- Tempo total: %.1fs\n",
            $this->countTotal,
            $this->countFound,
            $this->countInserted,
            $this->countUpdated,
            $this->countItemStatusUpdated,
            $this->countNotFound,
            $this->countSkippedInvalid,
            $elapsed
        );

        echo $summary;
        log_message('info', __METHOD__ . ': ' . str_replace("\n", ' ', $summary));
    }

    /**
     * Remove a trigger de atualização de status de atributos de itens se existir
     *
     * Caso a trigger exista, remove-a e imprime uma mensagem de sucesso.
     * Caso haja um erro, imprime uma mensagem de aviso com o erro e o loga como erro.
     */
    private function removerTrigger()
    {
        try {
            $this->db->query("DROP TRIGGER IF EXISTS {$this->triggerName}");
            echo "Trigger {$this->triggerName} removida com sucesso.\n";
        } catch (Exception $e) {
            echo "Aviso: Erro ao remover trigger: " . $e->getMessage() . "\n";
            log_message('error', "Erro ao remover trigger {$this->triggerName}: " . $e->getMessage());
        }
    }

    /**
     * Cria a trigger de atualização de status de atributos de itens
     *
     * Caso a trigger exista, remove-a e a recria com o sql armazenado na
     * propriedade $trigger_sql.
     *
     * Caso haja um erro, imprime uma mensagem de aviso com o erro e o loga como erro.
     */
    private function criarTrigger()
    {
        try {
            $this->db->query($this->triggerSql);
            echo "Trigger {$this->triggerName} criada com sucesso.\n";
        } catch (Exception $e) {
            echo "ERRO: Falha ao criar trigger: " . $e->getMessage() . "\n";
            log_message('error', "Erro ao criar trigger {$this->triggerName}: " . $e->getMessage());
            throw $e;
        }
    }
}
