<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

/**
 * Controller para atualização do status de atributos de itens importados
 *
 * Este script é responsável por fazer uma limpeza no banco de dados,
 * atualizando o status de atributos (wf_status_atributos) dos itens
 * baseado em sua condição de importação.
 *
 * <AUTHOR>
 * @version 1.0
 */
class Update_Importado extends CI_Controller
{
    private $trigger_name = 'tr_item_update_catalogo_envio';
    private $trigger_sql = "CREATE DEFINER=`gestaotarifaria`@`%` TRIGGER tr_item_update_catalogo_envio
        AFTER UPDATE ON item
        FOR EACH ROW
        BEGIN
            DECLARE v_id_item BIGINT(20) DEFAULT NULL;

            IF ((NEW.descricao_proposta_completa <> ''
                    OR (OLD.descricao_proposta_completa != NEW.descricao_proposta_completa
                        OR OLD.descricao_proposta_completa IS NULL AND NEW.descricao_proposta_completa IS NOT NULL))
                OR (NEW.id_status = 2)
                OR (NEW.wf_status_atributos = 7)) THEN
                SELECT
                    cad_item.id_item INTO v_id_item
                FROM cad_item
                WHERE 1=1
                    AND cad_item.part_number = NEW.part_number
                    AND cad_item.id_empresa = NEW.id_empresa
                    AND cad_item.estabelecimento = NEW.estabelecimento
                    LIMIT 1;
                IF v_id_item IS NOT NULL THEN
                    CALL sp_cria_catalogo_envio(v_id_item);
                END IF;
            END IF;
        END";
        
    /**
     * Construtor da classe
     * Verifica se a requisição é via CLI e carrega os modelos necessários
     */
    public function __construct()
    {
        parent::__construct();

        if (!$this->input->is_cli_request()) {
            die('Acesso permitido apenas via linha de comando');
        }

        $this->load->model('empresa_model');
        $this->load->database();
    }

    /**
     * Método principal que inicia o processo de atualização
     * Processa todas as empresas cadastradas no sistema
     */
    public function index()
    {
        ini_set('memory_limit', -1);
        set_time_limit(0);
        $this->db->query("SET SESSION sql_mode = ''");

        try {
            echo "Iniciando processo de atualização de status de atributos...\n";

            // Remove a trigger se existir
            $this->remover_trigger();

            $empresas = $this->obter_empresas();

            if (empty($empresas)) {
                echo "Nenhuma empresa encontrada para processar.\n";
                return;
            }
            // Data hora do inicio do processo
            $data_hora_inicio = date('Y-m-d H:i:s');

            echo "Processo iniciado em: {$data_hora_inicio}\n\n";

            echo "Total de empresas encontradas: " . count($empresas) . "\n\n";

            foreach ($empresas as $empresa) {
                echo "Processando empresa: {$empresa->razao_social} (ID: {$empresa->id_empresa})\n";
                $this->update_importado($empresa->id_empresa);
            }

            // Data hora do fim do processo
            $data_hora_fim = date('Y-m-d H:i:s');

            // Recria a trigger
            $this->criar_trigger();

            echo "\nProcesso finalizado em: {$data_hora_fim}\n";

            echo "\nProcesso finalizado com sucesso!\n";
        } catch (Exception $e) {
            echo "ERRO: " . $e->getMessage() . "\n";
            log_message('error', 'Erro no processo de atualização: ' . $e->getMessage());
        }
    }

    /**
     * Remove a trigger de atualização de status de atributos de itens se existir
     *
     * Caso a trigger exista, remove-a e imprime uma mensagem de sucesso.
     * Caso haja um erro, imprime uma mensagem de aviso com o erro e o loga como erro.
     */
    private function remover_trigger()
    {
        try {
            $this->db->query("DROP TRIGGER IF EXISTS {$this->trigger_name}");
            echo "Trigger {$this->trigger_name} removida com sucesso.\n";
        } catch (Exception $e) {
            echo "Aviso: Erro ao remover trigger: " . $e->getMessage() . "\n";
            log_message('error', "Erro ao remover trigger {$this->trigger_name}: " . $e->getMessage());
        }
    }

    
    /**
     * Cria a trigger de atualização de status de atributos de itens
     *
     * Caso a trigger exista, remove-a e a recria com o sql armazenado na
     * propriedade $trigger_sql.
     *
     * Caso haja um erro, imprime uma mensagem de aviso com o erro e o loga como erro.
     */
    private function criar_trigger()
    {
        try {
            $this->db->query($this->trigger_sql);
            echo "Trigger {$this->trigger_name} criada com sucesso.\n";
        } catch (Exception $e) {
            echo "ERRO: Falha ao criar trigger: " . $e->getMessage() . "\n";
            log_message('error', "Erro ao criar trigger {$this->trigger_name}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Obtém todas as empresas cadastradas
     *
     * @return array Lista de empresas
     */
    private function obter_empresas()
    {
        return $this->db->select('id_empresa, razao_social')
            ->from('empresa')
            ->where('id_empresa >=', 1)
            ->where('id_empresa <=', 500)
            ->get()
            ->result();
    }

    /**
     * Atualiza o status de atributos dos itens de uma empresa específica
     *
     * @param int $id_empresa ID da empresa a ser processada
     * @throws Exception Se ocorrer algum erro durante o processamento
     */
    private function update_importado($id_empresa)
    {
        try {
            $itens = $this->obter_itens_empresa($id_empresa);

            if (empty($itens)) {
                echo "  - Nenhum item encontrado para esta empresa\n";
                return;
            }

            echo "  - Total de itens encontrados: " . count($itens) . "\n";
            $atualizados = $this->processar_itens($itens);
            echo "  - Itens atualizados: {$atualizados}\n";
        } catch (Exception $e) {
            echo "  ERRO ao processar empresa ID {$id_empresa}: " . $e->getMessage() . "\n";
            log_message('error', "Erro ao processar empresa ID {$id_empresa}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Obtém todos os itens de uma empresa específica
     *
     * @param int $id_empresa ID da empresa
     * @return array Lista de itens
     */
    private function obter_itens_empresa($id_empresa)
    {
        return $this->db->select('
            i.part_number,
            i.estabelecimento,
            i.id_empresa,
            i.wf_status_atributos,
            MAX(ci.ncm_proposto) as ncm_proposto,
            MAX(ci.id_item) as id_item,
            MAX(ci.id_grupo_tarifario) as id_grupo_tarifario
            ')
            ->from('item i')
            ->join(
                'cad_item ci',
                'ci.part_number = i.part_number AND ci.estabelecimento = i.estabelecimento AND ci.id_empresa = i.id_empresa',
                'left'
            )
            ->where('i.id_empresa', $id_empresa)
            ->group_by([
                'i.part_number',
                'i.estabelecimento',
                'i.id_empresa'
            ])
            ->get()
            ->result();
    }

    /**
     * Processa a lista de itens, atualizando seus status conforme necessário
     *
     * @param array $itens Lista de itens a serem processados
     * @return int Quantidade de itens atualizados
     */
    private function processar_itens($itens)
    {
        $atualizados = 0;
        $processados = [];

        foreach ($itens as $item) {
            $chave = $item->part_number . '|' . $item->estabelecimento . '|' . $item->id_empresa;
            if (isset($processados[$chave])) {
                continue;
            }
            $processados[$chave] = true;

            $is_importado = $this->verificar_item_importado($item);

            if ($this->deve_atualizar_para_status_2($item, $is_importado)) {
                $this->atualizar_status_item($item, 2);
                $atualizados++;
            } elseif ($this->deve_atualizar_para_status_1($item, $is_importado)) {
                $this->atualizar_status_item($item, 1);
                $atualizados++;
            } elseif ($this->deve_atualizar_para_status_null($item, $is_importado)) {
                $this->atualizar_status_item($item, null);
                $atualizados++;
            }
        }

        return $atualizados;
    }

    /**
     * Verifica se um item é importado
     *
     * @param object $item Item a ser verificado
     * @return object|null Dados do item importado ou null
     */
    private function verificar_item_importado($item)
    {
        return $this->db->select('*')
            ->from('comex')
            ->where('part_number_original', $item->part_number)
            ->where('unidade_negocio', $item->estabelecimento)
            ->where('id_empresa', $item->id_empresa)
            ->where('ind_ecomex', 'EI')
            ->get()
            ->row();
    }

    /**
     * Verifica se o item deve ser atualizado para status 2
     *
     * @param object $item Item a ser verificado
     * @param object|null $is_importado Dados do item importado
     * @return bool
     */
    private function deve_atualizar_para_status_2($item, $is_importado)
    {
        return !empty($is_importado) &&
            ($item->wf_status_atributos == 1 || $item->wf_status_atributos == null) &&
            !empty($item->ncm_proposto);
    }

    /**
     * Verifica se o item deve ser atualizado para status 1
     *
     * @param object|null $is_importado Dados do item importado
     * @return bool
     */
    private function deve_atualizar_para_status_1($item, $is_importado)
    {
        return empty($is_importado) &&
            !empty($item->ncm_proposto);
    }

    /**
     * Verifica se o item deve ser atualizado para status null
     *
     * @param object|null $is_importado Dados do item importado
     * @return bool
     */
    private function deve_atualizar_para_status_null($item, $is_importado)
    {
        return empty($is_importado) &&
            (empty($item->ncm_proposto));
    }

    /**
     * Atualiza o status de um item
     *
     * @param object $item Item a ser atualizado
     * @param int $novo_status Novo status a ser definido
     */
    private function atualizar_status_item($item, $novo_status)
    {
        if (is_null($novo_status)) {
            $this->db->set('wf_status_atributos', 'NULL', false);
        } else {
            $this->db->set('wf_status_atributos', $novo_status);
        }

        $this->db->where([
            'part_number' => $item->part_number,
            'estabelecimento' => $item->estabelecimento,
            'id_empresa' => $item->id_empresa
        ])->update('item');

        $status_log = is_null($novo_status) ? 'NULL' : $novo_status;
        echo "  - Item atualizado para status de atributos '{$status_log}': {$item->part_number} ({$item->estabelecimento}) - Id empresa: {$item->id_empresa}\n";
    }
}
