<?php

class Cron_Delete_Old_Attr extends CI_Controller
{
    public $_attrs = [];
    public $_attr_estrutura_completa = [];

    public $_processar_empresa = ['353'];

    public function __construct()
    {
        parent::__construct();
       // $this->_processar_empresa = range(500, 1, -1);
        $this->load->model('ctr_pendencias_pergunta_model');
        $this->load->model('ctr_grupo_pergunta_model');
        $this->load->model("ctr_resposta_model");

 
        $this->load->model('ncm_atributo_model');
        $this->load->model('cad_item_model');
        $this->load->model([
            "catalogo/produto_model",
        ]);

        set_time_limit(0);
        ini_set('memory_limit', '3048M');

        // Resolve o problema de memory leak
        $this->db->save_queries = FALSE;

        // Desativamos output de erro default do CI (se não dá pau de http headers)
        $this->db->db_debug = FALSE;
    }

    public $_virtual_table = [];

    public function memory_mb($mem)
    {
        return $mem / (1024 * 1024);
    }

    public function index()
    {
        print_r($this->_processar_empresa);
        echo 'Sistema 17/01/2025';
        echo 'Limpando registros sem part_number '.$this->memory_mb(memory_get_usage()).PHP_EOL;
       // $this->limpar_registros_sem_part_number();
 
        echo 'Limpando registros de atributos não relacionados ao part_number '.$this->memory_mb(memory_get_usage()).PHP_EOL;
        $this->limpar_atributos_nao_relacionados();
 
        echo 'Atualiza informacoes cad_item_attr com a ncm_atributo '.$this->memory_mb(memory_get_usage()).PHP_EOL;
        $this->atualiza_obrigatorios();
 
        echo 'Atualizando registros de primeiro nivel  '.$this->memory_mb(memory_get_usage()).PHP_EOL;
        $this->adiciona_registros_primeiro_nivel_faltantes();

        echo 'Atualizando status de prenchimento '.$this->memory_mb(memory_get_usage()).PHP_EOL;
        $this->atualiza_status_preenchimento();

        echo 'fim';
    }

    public function adiciona_registros_primeiro_nivel_faltantes()
    {
        if (!empty($this->_processar_empresa))
        {
            foreach ($this->_processar_empresa as $empresa)
            {

                $this->db->select('ncm.nome_apresentacao,ncm.dominio,ncm.codigo_pai,ncm.codigo,ncm.obrigatorio,ci.id_item, ci.id_grupo_tarifario,ncm.modalidade');
                $this->db->from('ncm_atributo ncm');
                $this->db->join('cad_item ci', 'ci.ncm_proposto = ncm.ncm', 'inner');
                $this->db->join('cad_item_attr attr', 'attr.id_item = ci.id_item AND attr.atributo = ncm.codigo', 'left');
                $this->db->where('attr.id_item', null);
                //  $this->db->where('ci.id_item', '2808697');

                $this->db->where("ci.id_empresa", $empresa);

                $query = $this->db->get();
                $result = $query->result();
                foreach ($result as $row)
                {
                    echo 'Atualizando  '.$this->memory_mb(memory_get_usage()).' '.$row->id_item.PHP_EOL;

                    $this->db->insert('cad_item_attr', [
                        'codigo' => '',
                        'apresentacao' => $row->nome_apresentacao,
                        'descricao' => '',
                        'atributo_pai' => $row->codigo_pai,
                        'atributo' => $row->codigo,
                        'obrigatorio' => $row->obrigatorio,
                        'id_item' => $row->id_item,
                        'id_grupo_tarifario' => $row->id_grupo_tarifario,
                        'id_usuario' => 1,
                        'modalidade' => $row->modalidade,
                        'criado_em' =>  \date("Y-m-d H:i:s"),
                        'atualizado_em' => \date("Y-m-d H:i:s"),
                        'ativo' => 1
                    ]);
                }
            }

        }
 
    }

    public function get_cad_item($id_empresa)
    {
        if (empty($id_empresa))
        {
            return;
        }
        
        $this->db->where('id_empresa', $id_empresa);
        //  $this->db->where('ncm_proposto', '85443000');

        
        return  $this->db->get("cad_item");
    }

    private function filterUniqueAttributes($inputArray) {
        $attributeMap = [];
    
        foreach ($inputArray as $item) {
            $attribute = $item->atributo;
            $attributeMap[$attribute] = $item;
        }
    
        $resultArray = array_values($attributeMap);
    
        usort($resultArray, function($a, $b) {
            return strtotime($b->atualizado_em) - strtotime($a->atualizado_em);
        });
    
        return $resultArray;
    }
    
    public function limpar_registros_sem_part_number()
    {
        echo 'Desativando registros que não existem na tabela cad_item'.PHP_EOL;
        $this->db->query("DELETE FROM cad_item_attr
        WHERE NOT EXISTS (
            SELECT 1
            FROM cad_item
            WHERE cad_item.id_item = cad_item_attr.id_item
        );");
    }
        
    public function get_cad_item_attr()
    {
        $this->db->select("atributo");
        $this->db->where("ativo", 0);
        $this->db->group_by('atributo');
        $query = $this->db->get('cad_item_attr');

        return $query->result();
    }

    public function get_attr($id_item, $empty = FALSE)
    {
        if (empty($id_item)) {
            return FALSE;
        }

        if ($empty) {
            $this->db->where("codigo", "");
        }

        $this->db->where("id_item", $id_item);

        $query = $this->db->get('cad_item_attr');

        if (!$query->num_rows()) {
            return FALSE;
        }

        return $query->result();
    }

    public function filtrarAtributosFilhos($data) {
        $result = [];
    
        foreach ($data as $item) {
    
            if (!is_array($item) || count($item) < 4) {
                continue;
            }
    
            list($atributoPai, $valorPai, $atributoFilho, $condicoes) = $item;

            
            if ($valorPai == '' || is_null($valorPai)) {
                continue;
            }
    
            if (!is_array($condicoes) || !isset($condicoes['operador'], $condicoes['valor'])) {
                continue;
            }
    
            if ($this->avaliarCondicao($valorPai, $condicoes)) {
                $result[] = $atributoFilho;
            }
        }
    
        return array_unique($result);
    }
    
    public function avaliarCondicao($valorPai, $condicao, $attr = null) {
        $operador = $condicao['operador'];
        $valorCondicao = $condicao['valor'];
    
        if ($condicao['valor'] == 'true')
        {
            $valorCondicao = '1';  
        }

        if ($condicao['valor'] == 'false')
        {
            $valorCondicao = '0';  
        }
    
        switch ($operador) {
            case '==':
                $resultado = $valorPai == $valorCondicao;
                break;
            case '!=':
                $resultado = $valorPai != $valorCondicao;
                break;
            case '>':
                $resultado = $valorPai > $valorCondicao;
                break;
            case '<':
                $resultado = $valorPai < $valorCondicao;
                break;
            case '>=':
                $resultado = $valorPai >= $valorCondicao;
                break;
            case '<=':
                $resultado = $valorPai <= $valorCondicao;
                break;
            default:
                $resultado = false;
        }
    
        if (isset($condicao['composicao'], $condicao['condicao'])) {
            $composicao = $condicao['composicao'];
            $subCondicao = $condicao['condicao'];
    
            $subResultado = $this->avaliarCondicao($valorPai, $subCondicao);
    
            if ($composicao === '||') {
                $resultado = $resultado || $subResultado;
            } elseif ($composicao === '&&') {
                $resultado = $resultado && $subResultado;
            }
        }
    
        return $resultado;
    }

    public function get_grp_tarif_item($id_item)
    {
        if (empty($id_item))
        {
            return;
        }

        $this->db->select('id_grupo_tarifario');
        $this->db->where('id_item', $id_item);
        return $this->db->get('cad_item')->row()->id_grupo_tarifario;
    }

    public function limpar_registros_grupo_tarif_divergente($id_item, $grupo_tarifario_item)
    {
        if (empty($id_item) || empty($grupo_tarifario_item))
        {
            echo 'id_item ou grupo_tarifario_item vazio';
            return;
        }

        $this->db->select('*');
        $this->db->from('cad_item_attr');
        $this->db->where('id_item', $id_item);
        $this->db->where("id_grupo_tarifario <> '{$grupo_tarifario_item}'", NULL, FALSE);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $rows = $query->result_array();
            $rows_object = $query->result();

            foreach ($rows_object as $r)
            {
                // grupo tarifario que esta divergente na cad_item_attr em relacao a cad_item mas que é do mesmo id_item
                $this->db->where('id_grupo_tarifario', $r->id_grupo_tarifario);
                $query = $this->db->get('grupo_tarifario');
                $result_grupo_tarifario = $query->row();
                $ncm_grupo_tarifario = $result_grupo_tarifario->ncm_recomendada;
    
                // grupo_tarifario na cad_item
                $this->db->where('id_grupo_tarifario', $grupo_tarifario_item);
                $query = $this->db->get('grupo_tarifario');
                $result_grupo_tarifario = $query->row();
                $ncm_novo_grupo_tarifario = $result_grupo_tarifario->ncm_recomendada;
    
                if (!empty($ncm_grupo_tarifario) && !empty($ncm_novo_grupo_tarifario) && $ncm_grupo_tarifario != $ncm_novo_grupo_tarifario)
                {
                    $this->db->where('id_item', $id_item);
                    $this->db->where("id_grupo_tarifario <> '{$grupo_tarifario_item}'", NULL, FALSE);
                    $this->db->delete('cad_item_attr');

                    $logs_delete = [
                        'id' => $r->id,
                        'codigo' => $r->codigo,
                        'apresentacao' => $r->apresentacao,
                        'descricao' => $r->descricao,
                        'atributo_pai' => $r->atributo_pai,
                        'atributo' => $r->atributo,
                        'obrigatorio' => $r->obrigatorio,
                        'id_item' => $r->id_item,
                        'id_grupo_tarifario' => $r->id_grupo_tarifario,
                        'id_usuario' => $r->id_usuario,
                        'modalidade' => $r->modalidade,
                        'criado_em' => $r->criado_em,
                        'atualizado_em' => $r->atualizado_em,
                        'ativo' => $r->ativo,
                        'tenant_id' => $r->tenant_id
                    ];


                    $this->db->insert('logs_delete_cad_item_attr', $logs_delete);
                } else {
                    $this->db->set('id_grupo_tarifario', $grupo_tarifario_item);
                    $this->db->from('cad_item_attr');
                    $this->db->where('id_item', $id_item);
                    $this->db->where("id_grupo_tarifario <> '{$grupo_tarifario_item}'", NULL, FALSE);
                    $this->db->update('cad_item_attr attr');                    
                }

            }

        }
    }

    public function verificar_atributos_default($atributo, $id_item, $grupo_tarifario_item )
    {
        // adiciona atributos nivel 1 se não existirem
        if (empty($id_item) || empty($atributo) || empty($grupo_tarifario_item)) 
        {
            return;
        }

        $this->db->select("attr.atributo, attr.atributo_pai, attr.codigo");
        $this->db->where('ci.id_item', $id_item);
        $this->db->join('cad_item ci', 'ci.id_item = attr.id_item', 'inner');
        $query = $this->db->get('cad_item_attr attr');
        $attrs_item =  $query->result();

        $attr_db = [];
        if (!empty($attrs_item))
        {
            foreach ($attrs_item as $attr)
            {
                $attr_db[] = $attr->atributo;
            }
        }
 
        foreach ($atributo as $attr)
        {
            if (empty($attr_db) || !in_array($attr['codigo'],$attr_db))
            {

                $descricao = "";
                if (isset($attr["dominio"])) {
                    foreach ($attr["dominio"] as $dominio) {
                        if ($dominio["codigo"] == $attr['codigo']) {
                            $descricao = $dominio["descricao"];
                            break;
                        }
                    }
                }
                
                $dbdata = array(
                    'codigo' => '',
                    'apresentacao' => $attr['nomeApresentacao'],
                    'descricao' => $descricao,
                    'atributo_pai' => '',
                    'atributo' => $attr['codigo'],
                    'obrigatorio' => $attr['obrigatorio'],
                    'id_item' => $id_item,
                    'id_grupo_tarifario' => $grupo_tarifario_item,
                    'id_usuario' => 1,
                    'modalidade' => $attr['modalidade'],
                    'criado_em' =>  \date("Y-m-d H:i:s"),
                    'atualizado_em' => \date("Y-m-d H:i:s"),
                    'ativo' => 1
                );
            
                $this->db->insert('cad_item_attr', $dbdata);
            }
        }
    }

    public function buscar_empresas()
    {
        $this->db->where("ativo", 1);

        if (!empty($this->_processar_empresa))
        {
            $this->db->where_in("id_empresa", $this->_processar_empresa);
        }
        
        $this->db->order_by("id_empresa", "ASC");
        $query = $this->db->get('empresa');

        return  $query->result();
    }

    public function processarAtributosCondicionados($atributos, $attr_salvo_db, &$attr_default_item)
    {
        foreach ($atributos as $atributo) {
 
            // Verifica se o atributo é condicionante
            if (isset($atributo['atributoCondicionante']) && $atributo['atributoCondicionante'] == 1) {
                if (isset($atributo['condicionados']) && is_array($atributo['condicionados'])) {
                    $this->processarAtributosCondicionados($atributo['condicionados'], $attr_salvo_db, $attr_default_item);
                }
            }
            // Processa dbdata dentro de atributo
            if (!empty($atributo['atributo']['dbdata']['codigo'])) {

                if (preg_match("/'([^']+)'/", $atributo['descricaoCondicao'], $matches)) {
                    $attrPai = $matches[1];

                    if (!empty($attrPai) && $this->avaliarCondicao($attr_salvo_db[$attrPai] ?? null, $atributo['condicao'])) {
                        if (!in_array($atributo['atributo']['codigo'], $attr_default_item))
                        {
                            $attr_default_item[] = $atributo['atributo']['codigo'];
                            $this->_attr_estrutura_completa[$atributo['atributo']['codigo']] = [
                                'codigo' => '',
                                'apresentacao' => $atributo['atributo']['nomeApresentacao'],
                                'descricao' => '',
                                'atributo_pai' => $atributo['atributo']['codigo_pai'],
                                'atributo' => $atributo['atributo']['codigo'],
                                'obrigatorio' => !empty($atributo['atributo']['obrigatorio']) ? $atributo['atributo']['obrigatorio'] : 0,                                'id_usuario' => 1,
                                'modalidade' => $atributo['atributo']['modalidade'],
                                'criado_em' =>  \date("Y-m-d H:i:s"),
                                'atualizado_em' => \date("Y-m-d H:i:s"),
                                'ativo' => 1
                            ];
                        }
                        
                    }
                }
            
            }  else if (isset($atributo['atributo']) && !empty($atributo['atributo'])) {

                if (preg_match("/'([^']+)'/", $atributo['descricaoCondicao'], $matches)) {
                    $attrPai = $matches[1];
                        if (!empty($attrPai) && $this->avaliarCondicao($attr_salvo_db[$attrPai] ?? null, $atributo['condicao'] )) {
                            if (!in_array($atributo['atributo']['codigo'], $attr_default_item))
                            {
                                $attr_default_item[] = $atributo['atributo']['codigo'];
                                $this->_attr_estrutura_completa[$atributo['atributo']['codigo']] = [
                                    'codigo' => '',
                                    'apresentacao' => $atributo['atributo']['nomeApresentacao'],
                                    'descricao' => '',
                                    'atributo_pai' => $atributo['atributo']['codigo_pai'],
                                    'atributo' => $atributo['atributo']['codigo'],
                                    'obrigatorio' => !empty($atributo['atributo']['obrigatorio']) ? $atributo['atributo']['obrigatorio'] : 0,
                                    'id_usuario' => 1,
                                    'modalidade' => $atributo['atributo']['modalidade'],
                                    'criado_em' =>  \date("Y-m-d H:i:s"),
                                    'atualizado_em' => \date("Y-m-d H:i:s"),
                                    'ativo' => 1
                                ];
                            }
                            
                        }
                    }  
                }
                
            // Processa dbdata no nível atual
            if (!empty($atributo['dbdata'])) {
                if (!in_array($atributo['codigo'], $attr_default_item))
                {
                    $attr_default_item[] = $atributo['codigo'];
                    $this->_attr_estrutura_completa[$atributo['codigo']] = [
                        'codigo' => '',
                        'apresentacao' => $atributo['nomeApresentacao'],
                        'descricao' => '',
                        'atributo_pai' => $atributo['codigo_pai'],
                        'atributo' => $atributo['codigo'],
                        'obrigatorio' => !empty($atributo['obrigatorio']) ? $atributo['obrigatorio'] : 0,
                        'id_usuario' => 1,
                        'modalidade' => $atributo['modalidade'],
                        'criado_em' =>  \date("Y-m-d H:i:s"),
                        'atualizado_em' => \date("Y-m-d H:i:s"),
                        'ativo' => 1
                    ];
                }
                
            }

            // Verifica níveis adicionais de condicionantes
            if (isset($atributo['atributo']) && isset($atributo['atributo']['atributoCondicionante']) && $atributo['atributo']['atributoCondicionante'] == 1) {
                if (isset($atributo['atributo']['condicionados']) && is_array($atributo['atributo']['condicionados'])) {
                    $this->processarAtributosCondicionados($atributo['atributo']['condicionados'], $attr_salvo_db, $attr_default_item);
                }
            }

            // Verifica níveis adicionais de condicionantes
            if (!empty($atributo['listaSubatributos']) ) {
                $this->processarAtributosCondicionados($atributo['listaSubatributos'], $attr_salvo_db, $attr_default_item);
            }
        }

        return $attr_default_item;
    }

    public function limpar_atributos_nao_relacionados()
    {
        $count = 0;
        $this->_virtual_table = [];        

        $empresas =  $this->buscar_empresas();

        if (empty($empresas))
        {
            return;
        }

        foreach ($empresas as $empresa)
        {
            if (empty($empresa->id_empresa))
            {
                continue;
            }

            echo 'Analisando empresa '.$empresa->nome_fantasia.PHP_EOL;
            $table_cad_item = $this->get_cad_item($empresa->id_empresa);

            $count = 0;
            $total = $table_cad_item->num_rows();
            while ($table = $table_cad_item->unbuffered_row()) {
                $this->_attr_estrutura_completa = [];
                $count++;
                echo 'Analisando item '.$count.' de '.$total.' id_item: '.$table->id_item.' empresa: '.$empresa->nome_fantasia.' '.$this->memory_mb(memory_get_usage()).PHP_EOL;
        
                $grupo_tarifario_item = $this->get_grp_tarif_item($table->id_item);
                if (!empty($grupo_tarifario_item) && !empty($table->id_item))
                {
                    echo 'limpar_registros_grupo_tarif_divergente '.$grupo_tarifario_item. ' ';
                    $this->limpar_registros_grupo_tarif_divergente($table->id_item, $grupo_tarifario_item);
                }

                $this->_attrs = [];
                if (!empty($table->ncm_proposto))
                {
                    $ncm_item = $this->get_required_attrs($this->produto_model->get_attr_ncm($table->ncm_proposto));
                    if (empty($ncm_item))
                    {
                        continue;
                    }
        
                    $array = $this->get_attr($table->id_item);  
                    if (empty($array))
                    {
                        continue;
                    }
                    $ncm_item["defaultAttrs"] = $this->filterUniqueAttributes($array);
                    $ncm_item["assocAttrs"] = $this->create_assoc_attrs_structure($ncm_item);

                    $this->verificar_atributos_default($ncm_item["assocAttrs"], $table->id_item, $grupo_tarifario_item);

                
                    $atributos_ncm = $this->_attrs;
                    $attr_default_item = [];
                    $attr_salvo_db = [];
                    $resultado = $this->filtrarAtributosFilhos($atributos_ncm);
                    $atributos_com_pai = [];
                
                    if (!empty($atributos_ncm))
                    {
                        foreach ($atributos_ncm as $registro)
                        {
                            if (empty($registro[2]))
                            {
                                continue; 
                            }
                            $atributos_com_pai[] = $registro[2];
                        }
                        if (!empty($atributos_com_pai))
                        {
                            $this->db->set('atributo_pai', null);
                            $this->db->from('cad_item_attr');
                            $this->db->where('id_item', $table->id_item);
                            $this->db->where_not_in('atributo', $atributos_com_pai);
                            $this->db->update('cad_item_attr attr');
                        }
        
                        foreach ($atributos_ncm as $registro)
                        {
                            // atributo filho [2] atributo pai  [0]
                            if (!empty($registro[0]) && !empty($registro[2]) && in_array($registro[2], $resultado)  )
                            {
                                    $this->db->query(" UPDATE cad_item_attr
                                    SET atributo_pai = '{$registro[0]}'
                                        WHERE id_item = '{$table->id_item}'
                                        AND atributo = '{$registro[2]}'
                                        AND (atributo_pai <> '{$registro[0]}' OR atributo_pai IS NULL)");
                            }
                        }
        
                        $ncm_item["defaultAttrs"] = $this->filterUniqueAttributes($array);
                        $ncm_item["assocAttrs"] = $this->create_assoc_attrs_structure($ncm_item);
                    }

                    // Construindo os atributos salvos no banco de dados.
                    foreach ($ncm_item["defaultAttrs"] as $i) {
                        $attr_salvo_db[$i->atributo] = $i->codigo;
                    }
 
                    $attr_default_item = $this->processarAtributosCondicionados($ncm_item["assocAttrs"], $attr_salvo_db, $attr_default_item);
 
 

                    if (!empty($attr_salvo_db))
                    {
                        foreach ($attr_default_item as $attr)
                        {
                            if (!array_key_exists($attr, $attr_salvo_db)) 
                            {
               
                                if (!empty($this->_attr_estrutura_completa[$attr]))
                                {
                                    $item = $this->_attr_estrutura_completa[$attr];
                                    $item['id_item'] = $table->id_item;
                                    $item['id_grupo_tarifario'] = $table->id_grupo_tarifario;
                                    
                                    $this->db->insert('cad_item_attr', $item);
                                }
                             
                            }
                        }
                    }

 
                    if (!empty($attr_default_item) && !empty($table->id_item))
                    {
                    
                        $this->db->select('*');
                        $this->db->from('cad_item_attr');
                        $this->db->where('id_item', $table->id_item);
                        $this->db->where_not_in('atributo', $attr_default_item);

                        $query = $this->db->get();
        
                        if ($query->num_rows() > 0) {
                             $rows = $query->result_array();
                             $this->db->insert_batch('logs_delete_cad_item_attr', $rows);
        
                            $this->db->where('id_item', $table->id_item);
                            $this->db->where_not_in('atributo', $attr_default_item);
                            $this->db->delete('cad_item_attr');
                        }
                    }

                }

                // $attr_default_item = [];
                // $attr_salvo_db = [];
                
                // // Construindo os atributos salvos no banco de dados.
                // foreach ($ncm_item["defaultAttrs"] as $i) {
                //     $attr_salvo_db[$i->atributo] = $i->codigo;
                // }

     
       

                // $attr_default_item = $this->processarAtributosCondicionados($ncm_item["assocAttrs"], $attr_salvo_db, $attr_default_item);
                // print_r($table->ncm_proposto);
                // print_r($attr_default_item);
                // exit;


                // $atributos_ncm = $this->_attrs;
                // $registros = $atributos_ncm;
                // $resultado = $this->filtrarAtributosFilhos($atributos_ncm);
 
                // if (!empty($registros))
                // {
                //     foreach ($registros as $registro)
                //     {
                //         // atributo filho [2] atributo pai  [0]
                //         if (in_array($registro[2], $resultado) && !empty($registro[0]) && !empty($registro[2]))
                //         {
                //                 $this->db->query(" UPDATE cad_item_attr
                //                 SET atributo_pai = '{$registro[0]}'
                //                     WHERE id_item = '{$table->id_item}' 
                //                     AND atributo = '{$registro[2]}'
                //                     AND (atributo_pai <> '{$registro[0]}' OR atributo_pai IS NULL)");
                //         }
                //     }
                // }
                // $atributos_ncm = $resultado;
        
                // if (isset($atributos_ncm['sim']))
                // {
                //     unset($atributos_ncm['sim']);
                // }
                
                // if (!empty($attr_default_item) && !empty($table->id_item))
                // {

                //     $this->db->select('*');
                //     $this->db->from('cad_item_attr');
                //     $this->db->where('id_item', $table->id_item);
                //     $this->db->where_not_in('atributo', $attr_default_item);
                //     $query = $this->db->get();
                    
                //     if ($query->num_rows() > 0) {
                //         $rows = $query->result_array();
                //         $this->db->insert_batch('logs_delete_cad_item_attr', $rows);

                //         $this->db->where('id_item', $table->id_item);
                //         $this->db->where_not_in('atributo', $attr_default_item);
                //         $this->db->delete('cad_item_attr');
                //     }
                // }
                
            }
        }
    }

    public function atualiza_obrigatorios()
    {       
        // echo 'Registrando Logs'.PHP_EOL;
        // $this->db->query("INSERT INTO delete_cad_item_attr_log (id_item, atributo, tipo, deleted_at)
        // SELECT attr.id_item, attr.atributo, 'Update - 03' as tipo, NOW()
        // FROM cad_item_attr attr
        // INNER JOIN ncm_atributo n ON n.codigo = attr.atributo
        // WHERE attr.ativo = '1'
        // and attr.atributo = n.codigo
        // and attr.obrigatorio <> n.obrigatorio");

        if (!empty($this->_processar_empresa))
        {
            foreach ($this->_processar_empresa as $empresa)
            {
                $this->db->select('attr.id');
                $this->db->from('cad_item_attr attr');
                $this->db->join('cad_item ci', 'ci.id_item = attr.id_item', 'inner');
        
                $this->db->where("ci.id_empresa", $empresa);

        
                $this->db->where("attr.obrigatorio <> (
                    SELECT n.obrigatorio 
                    FROM ncm_atributo n 
                    WHERE n.codigo = attr.atributo 
                    AND ci.ncm_proposto = n.ncm 
                    LIMIT 1
                )", null, false);
                $this->db->group_by('attr.id');
        
        
                $db = $this->db->get();

                if ($db->num_rows()) {
                    foreach ($db->result() as $item)
                    {
                        echo 'Atualizando obrigatorio'.$item->id.PHP_EOL;
                        
                        $this->db->query(" UPDATE cad_item_attr attr
                        INNER JOIN ncm_atributo n ON n.codigo = attr.atributo
                        INNER JOIN cad_item ci ON ci.id_item = attr.id_item
                        SET attr.obrigatorio = n.obrigatorio
                        WHERE attr.atributo = n.codigo
                        and n.ncm = ci.ncm_proposto
                        and attr.obrigatorio <> n.obrigatorio
                        and attr.id = '{$item->id}'");
                    }
        
                    $this->atualiza_obrigatorios();
                }
            }
        }

    }

    public function atualiza_status_preenchimento()
    {  
        $database_name = $this->db->database;

        $trigger_name = 'tr_item_update_catalogo_envio';
        $table_name = 'item';
        $trigger_exists = false;
        $query = $this->db->query("
            SELECT TRIGGER_NAME 
            FROM INFORMATION_SCHEMA.TRIGGERS 
            WHERE TRIGGER_NAME = '$trigger_name' 
            AND EVENT_OBJECT_TABLE = '$table_name' 
            AND TRIGGER_SCHEMA = DATABASE()
        ");

        if ($query->num_rows() > 0 && $database_name != 'gestaotarifaria') {
            $trigger_exists = true;
            $this->db->query("DROP TRIGGER IF EXISTS tr_item_update_catalogo_envio;");
        } 
  
                $this->db->select("
                ci.part_number,
                ci.estabelecimento,
                ci.id_empresa,
                CASE 
                
                    WHEN EXISTS (
                        SELECT 1
                        FROM ncm_atributo n
                        WHERE n.ncm = ci.ncm_proposto
                            AND (  n.codigo = 'null')
                    ) 
                    THEN '5'

                    WHEN COUNT(*) = 0 
                    OR COUNT(CASE WHEN attr.codigo IS NULL OR attr.codigo = '' THEN 1 END) = COUNT(*) THEN '1'
                    WHEN COUNT(CASE WHEN attr.obrigatorio = 1 AND (attr.codigo IS NULL OR attr.codigo = '') THEN 1 END) > 0 THEN '2'
                    WHEN COUNT(CASE 
                        WHEN (attr.obrigatorio = 0 AND (attr.codigo IS NULL OR attr.codigo = '')) 
                            OR (attr.id_item IS NULL) THEN 1 
                        ELSE NULL 
                        END) > 0 THEN '3'
                    WHEN COUNT(CASE WHEN attr.codigo IS NOT NULL AND attr.codigo <> '' THEN 1 END) = COUNT(*) THEN '4'
                    ELSE '0'
                END AS status_preenchimento", false); // false para evitar escaping do CodeIgniter

            $this->db->from('cad_item ci');
            $this->db->join('cad_item_attr attr', 'ci.id_item = attr.id_item', 'left');

            if (!empty($this->_processar_empresa))
            {
                $this->db->where_in("ci.id_empresa", $this->_processar_empresa);
            }

            $this->db->group_by('ci.id_item');

            $query = $this->db->get();
 
                    $count = 0;
                    $total = $query->num_rows();
                    while ($item = $query->unbuffered_row()) 
                    {
                        $count++;
                        echo 'Atualizando status de atributos '.$count.' de '.$total.' '.$this->memory_mb(memory_get_usage()).PHP_EOL;

                        if (!empty($item->status_preenchimento) || !empty($item->part_number) || !empty($item->estabelecimento) || !empty($item->id_empresa))
                        {
                            $this->db->query(" UPDATE item
                            SET status_attr = '{$item->status_preenchimento}'
                                WHERE part_number = '{$item->part_number}' 
                                AND estabelecimento = '{$item->estabelecimento}'
                                AND id_empresa = '{$item->id_empresa}'
                                AND status_attr <> '{$item->status_preenchimento}'");
                        }
       
                    }


                    if ($trigger_exists == true && $database_name != 'gestaotarifaria') {
 
                        $this->db->query("CREATE DEFINER=`caue_sampaio`@`%` TRIGGER tr_item_update_catalogo_envio
                            AFTER UPDATE ON item
                            FOR EACH ROW
                            BEGIN
                                DECLARE v_id_item BIGINT(20) DEFAULT NULL;
                            
                                IF ((NEW.descricao_proposta_completa <> ''
                                        OR (OLD.descricao_proposta_completa != NEW.descricao_proposta_completa
                                            OR OLD.descricao_proposta_completa IS NULL AND NEW.descricao_proposta_completa IS NOT NULL))
                                    OR (NEW.id_status = 2)
                                    OR (NEW.wf_status_atributos = 7)) THEN
                                    SELECT
                                        cad_item.id_item INTO v_id_item
                                    FROM cad_item
                                    WHERE 1=1
                                        AND cad_item.part_number = NEW.part_number
                                        AND cad_item.id_empresa = NEW.id_empresa
                                        AND cad_item.estabelecimento = NEW.estabelecimento;
                                    IF v_id_item IS NOT NULL THEN
                                        CALL sp_cria_catalogo_envio(v_id_item);
                                    END IF;
                                END IF;
                            END;
                                    ");
                    }
    }


   

    private function get_required_attrs($listaAtributos)
    {
        if (empty($listaAtributos)) {
            return;
        }

        $listaAtributos = \json_decode(\json_encode($listaAtributos), TRUE); // Força a conversão para array.

        $ncm_item = [];
        $ncm_item['listaAtributos'] = $listaAtributos;


        \array_multisort($ncm_item["listaAtributos"]);

        return $ncm_item;
    }


    private function create_assoc_attrs_structure($ncm_item)
    {
        if (empty($ncm_item)) {
            return;
        }

        $ncm_item = \json_decode(\json_encode($ncm_item), TRUE);  

        $arr_dbdata = $ncm_item["defaultAttrs"];  
        $arr_attr   = $ncm_item["listaAtributos"]; 

        $this->assoc_recursively($arr_dbdata, $arr_attr);

        return $arr_attr;
    }

    public function has_attr_cond($attr, $cond)
    {
        $cond_op  = $cond["condicao"]["operador"]; // O operador para comparação.
        $cond_val = $cond["condicao"]["valor"];    // O valor para ser comparado.
        $cond_res = FALSE;                         // O resultado final da comparação.

        if (!empty($attr['multivalorado'])) {
            $attr_vals = $attr["dbdata"]["codigo"]; // Multivalorado.

            if (!\is_array($attr_vals)) {
                $attr_vals = explode(",", $attr_vals);
            }

            foreach($attr_vals as $attr_val) {

                $cond_res = eval("return \"$attr_val\" $cond_op \"$cond_val\";");

                if ($cond_res) {
                    break;
                }
            }

        } else {
            $attr_val = $attr["dbdata"]["codigo"]; // Valor simples.

            if (strtoupper($attr["formaPreenchimento"]) == 'BOOLEANO' && $attr_val > 0) {
                $attr_val = "true";
            }

            $cond_res = eval("return \"$attr_val\" $cond_op \"$cond_val\";");

        }

        return $cond_res;
    }

    private function assoc_recursively($arr_dbdata, &$arr_attr, $parent_attr = NULL)
    {
        if (empty($arr_attr)) {
            return;
        }

        if (empty($arr_dbdata)) {
            $arr_dbdata = [];
        }

        foreach($arr_attr as &$attr) {

            $attr_template = !empty($attr["atributo"])? $attr["atributo"] : $attr; 

            $attr_template["dbdata"] = [ "codigo" => "" ]; 

            foreach($arr_dbdata as $dbdata) {
                if (
                    !empty($parent_attr)
                    && !empty($dbdata["atributo_pai"])
                    && $dbdata["atributo_pai"] == $parent_attr["codigo"]
                    && $dbdata["atributo"] == $attr_template["codigo"]
                ) {
                    $attr_template["dbdata"] = $dbdata;

                } else if($dbdata["atributo"] == $attr_template["codigo"]) {
                    $attr_template["dbdata"] = $dbdata;
                }      
            }

            if ($attr_template["atributoCondicionante"] && !empty($attr_template["condicionados"])) {
                    foreach ($attr_template["condicionados"] as &$cond) {

                        $index_atributo =  isset($attr_template["dbdata"]['atributo']) ? 
                                            $attr_template["dbdata"]['atributo'] : null;
                        $index_atributo_codigo =  isset($attr_template["dbdata"]['codigo']) ? 
                                            $attr_template["dbdata"]['codigo'] : null;

                        $index_atributo_pai =  isset($cond['atributo']['codigo']) ? 
                                            $cond['atributo']['codigo'] : null;
                        $index_atributo_pai_codigo =  isset($cond["condicao"]) ? 
                                            $cond["condicao"] : null;

                        $this->_attrs[] = [$index_atributo, $index_atributo_codigo, $index_atributo_pai, $index_atributo_pai_codigo ];
                        
                        if ($this->has_attr_cond($attr_template, $cond)) 
                        {
                            if (!empty($cond['atributo']["condicionados"]))
                            {  
                            
                                $this->assoc_recursively($arr_dbdata, $cond['atributo']["condicionados"], $attr_template);
                            }
                       
                            if (!empty($attr_template["dbdata"]['atributo']))
                            {
                                $this->_attrs['sim'][] = $attr_template["dbdata"]['atributo'];
                            }
                        }
                    }

                    if (!empty($this->_attrs['sim']) && in_array($attr_template['codigo'], $this->_attrs['sim']))
                    {
                        $this->assoc_recursively($arr_dbdata, $attr_template["condicionados"], $attr_template);
                    }

            } else if (\strtoupper($attr_template["formaPreenchimento"]) == "COMPOSTO" && !empty($attr_template["listaSubatributos"])) {
                $this->assoc_recursively($arr_dbdata, $attr_template["listaSubatributos"], $attr_template);
            }
            
            if (!empty($attr["atributo"])) {
                $attr["atributo"] = $attr_template;

            } else {
                $attr = $attr_template;
            }
        }
    }
}
