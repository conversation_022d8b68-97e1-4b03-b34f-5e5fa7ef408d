<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Home extends MY_Controller {

	public function __construct() {
		parent::__construct();

		if (!is_logged()) {
			redirect('/login');
		}
	}

	// Método obrigatório
	public function black_hole() {}

	public function index()
	{
		if ($this->uri->segment(1) !== 'busca')
		{
			if (!has_role('sysadmin') && has_role('cest'))
				redirect('cest');

			redirect('cockpit');
		}

		if ($this->uri->segment(1) == 'busca')
		{
			if (!has_role('gerenciar_busca')) {
				show_permission();
			}
		}

		$data = array();

        $this->load->model('foto_model');
        $this->load->model('item_model');

        $id_empresa = sess_user_company();

        $view = 'home';

		if ($this->input->post('submit'))
		{

			$part_numbers = $this->input->post('part_numbers');

			if ($part_numbers != NULL && $part_numbers != '*')
			{

				$part_numbers = str_replace(array("\t", "\r\n", "\s", "\n"), ' ', $this->input->post('part_numbers'));

				//Acha todos os partnumbers entre aspas duplas
				$matches = array();
				preg_match_all('/"([^"]*)"/',$part_numbers,$matches);
			    $btw_quotes = $matches[1];

			    $part_numbers = str_replace("*", "%", $part_numbers);

			    //Retira da string todos os partnumbers entre aspas duplas
			    $part_numbers = preg_replace('/"([^"]*)"/', "", $part_numbers);

			    //Acha todos os partnumbers entre aspas simples
			    $matches_simple = array();
			    preg_match_all('~\'(.*?)\'~', $part_numbers, $matches_simple);
			    $btw_simple_quotes = $matches_simple[1];

			    //Retira da string todos os partnumbers entre aspas simples
			    $part_numbers = preg_replace('~\'(.*?)\'~', "", $part_numbers);

			    //Verifica se existe algum widcard na pesquisa, caso exista outra view é carregada
			    if (strrchr($part_numbers, "%") || !empty($btw_quotes) || !empty($btw_simple_quotes))
			    {
			    	$view = 'home_wildcards';
			    }

			    $part_numbers = explode(" ", addslashes($part_numbers));

			    //Escape em tudo que estiver entre aspas duplas
				if (!empty($btw_quotes))
				{
					$addslashes_btw_quotes = implode(',', $btw_quotes);
                	$btw_quotes = explode(",", addslashes($addslashes_btw_quotes));
                }

				$part_numbers = array_merge($part_numbers, $btw_quotes);
				$part_numbers = array_merge($part_numbers, $btw_simple_quotes);

				$stripslashes = implode(" ", $part_numbers);
			    $part_numbers_view = explode(" ", stripslashes($stripslashes));
				$part_numbers_view = str_replace("%", "", $part_numbers_view);

				$part_numbers = array_filter($part_numbers);

				foreach ($part_numbers as $key => $part_number)
				{
					if (strrchr($part_number, "%"))
					{
						$generic_part_numbers[] = $part_number;
						unset($part_numbers[$key]);
					}
				}

				$data = array();

	            if ($id_empresa !== NULL)
	            {
	                $this->item_model->set_state('filter.id_empresa', $id_empresa);
	            }

	            $this->item_model->set_state('filter.part_numbers', $part_numbers);

	            if (isset($generic_part_numbers))
	            {
	            	$this->item_model->set_state('filter.generic_part_numbers', $generic_part_numbers);
	            }

				$data['itens'] = $this->item_model->get_entries();
				$data['part_numbers'] = $part_numbers_view;
			}
		}

        $data['id_empresa'] = $id_empresa;

        $this->load->model('empresa_model');
        $empresa = $this->empresa_model->get_entry($id_empresa);
        $data['multi_estabelecimentos'] = $empresa->multi_estabelecimentos;

		$this->render($view, $data);
	}

    public function change_company($id_empresa)
    {
    	if ((!has_role('admin') && !has_role('consultor')) && !$this->usuario_model->check_permissao_empresa($id_empresa)) {
			show_permission();
    	}
		$this->load->model( 'ctr_pendencias_pergunta_model' );

        $this->session->set_userdata('user_company', $id_empresa);

        // force refresh content
        customer_can('ii', TRUE);

        //Limpar states referentes aos totais de alertas;
		$this->load->model(array(
			'monitor_ex/ctrl_ex_tarifario_model',
			'cad_item_model'
		));
		
		
		$this->cad_item_model->set_state_store_session(TRUE);
		$this->ctrl_ex_tarifario_model->set_state_store_session(TRUE);
		$this->ctr_pendencias_pergunta_model->set_state_store_session(TRUE);
		$this->ctrl_ex_tarifario_model->clear_states();
		$this->cad_item_model->clear_states();

    	$this->cad_item_model->unset_state('total_geral_alertas');
    	$this->cad_item_model->unset_state('total_pendencias_ex_ii');
    	$this->cad_item_model->unset_state('total_pendencias_ex_ipi');
    	$this->cad_item_model->unset_state('total_pendencias_nve');
		$this->ctr_pendencias_pergunta_model->unset_state("filter.bulk_item");
		$this->ctr_pendencias_pergunta_model->unset_state("filter.bulk_state");
		$this->ctr_pendencias_pergunta_model->unset_state('filter.usuarioPergunta');
        $this->ctr_pendencias_pergunta_model->unset_state('filter.partnumbers');
        $this->ctr_pendencias_pergunta_model->unset_state('filter.periodoIni'); 
        $this->ctr_pendencias_pergunta_model->unset_state('filter.periodoFim');    
        $this->ctr_pendencias_pergunta_model->unset_state('filter.status');

    	// Limpa filtro customizado
    	$this->cad_item_model->unset_state('filter.status_simplus', 'cad_item_model_state_homologacao');

        redirect($this->session->userdata('last_url'));
    }

	public function thumb()
	{
		$file = $this->input->get('file');

		$config = array();
		$config['image_library'] = 'gd2';
		$config['source_image']	= FCPATH . 'assets/fotos/'.$file;
		$config['create_thumb'] = TRUE;
		$config['maintain_ratio'] = TRUE;
		$config['width']	 = 130;
		$config['height']	= 100;
		$config['dynamic_output'] = TRUE;

		$this->load->library('image_lib', $config);
		$this->image_lib->resize();
	}

	public function ajax_get_vinculacao()
	{
		if ((customer_can('atr') || customer_can('ii') || customer_can('ipi') || customer_can('nve') || customer_can('cest')) && $this->input->is_ajax_request())
        {
        	$ret = array();

            $this->load->model('cad_item_model');
			$this->load->model('catalogo/produto_model');

            $pendencias = $this->cad_item_model->get_pendencias();

			$data = [];

			if (customer_can('atr')) {
				$data = $this->produto_model->get_all_attr_ncm();
			}

			// Total Geral
			$total = 0;
			if (customer_can('ii')) {
				$total += $pendencias->pendencias_ex_ii;

				$ret['ex_ii'] = array(
					'pendencias' => $pendencias->pendencias_ex_ii,
					'classificado' => $pendencias->itens_classif_ex_ii
				);
			}

			if (customer_can('ipi')) {
				$total += $pendencias->pendencias_ex_ipi;

				$ret['ex_ipi'] = array(
					'pendencias' => $pendencias->pendencias_ex_ipi,
					'classificado' => $pendencias->itens_classif_ex_ipi
				);
			}

			if (customer_can('nve')) {
				$total += $pendencias->pendencias_nve;

				$ret['nve'] = array(
					'pendencias' => $pendencias->pendencias_nve,
					'classificado' => $pendencias->itens_classif_nve
				);
			}

			if (customer_can('cest')) {
				$total += $pendencias->pendencias_cest;

				$ret['cest'] = array(
					'pendencias' => $pendencias->pendencias_cest,
					'classificado' => $pendencias->itens_classif_cest
				);
			}

			if (customer_can('atr')) {
				$total += $data['total'];

				$ret['attrs'] = array(
					'pendencias' => $data['total'],
					'classificado' => $data['total_atribuidos']
				);
			}

			$ret['total_geral_alertas'] = $total;

            echo json_encode($ret);
        }
	}

	public function ajax_get_pendencias()
	{
		$this->load->model(array(
			'usuario_model', 'ctr_pendencias_pergunta_model', 'version_model', 'empresa_model', 'item_model',
		));

		$this->ctr_pendencias_pergunta_model->set_state_store_session(TRUE);
		$search_part_number = $this->ctr_pendencias_pergunta_model->get_state('filter.partnumbers');
		$remover_busca_estabelecimento = true;
		$partNumbersPerguntas = [];

		$empresa = $this->empresa_model->get_entry(sess_user_company());
        $campos_adicionais = explode("|", $empresa->campos_adicionais);
        $hasOwner =  in_array('owner', $campos_adicionais);

		$owner_user = array();
		
		
		if (!customer_has_role('editar_todos_part_numbers', sess_user_id()) && $hasOwner) {
			$owner_user = $this->item_model->get_user_owner_codes(sess_user_id());
		}
		$empresa = $this->empresa_model->get_entry(sess_user_company());

        $campos_adicionais = explode("|", $empresa->campos_adicionais); 
        $hasOwner = in_array('owner', $campos_adicionais);
		$id_usuario_logado = sess_user_id();

		$this->ctr_pendencias_pergunta_model->set_state('filter.usuarioContagemLogado', $id_usuario_logado);

		if (!customer_has_role('editar_todos_part_numbers', sess_user_id()) && $hasOwner) {
            $partNumbersPerguntas = $this->ctr_pendencias_pergunta_model->TotaisGetPartnumbersComPerguntasPendentes(null, null, false,$search_part_number,$remover_busca_estabelecimento, FALSE, $owner_user);
        } else {
            $partNumbersPerguntas = $this->ctr_pendencias_pergunta_model->TotaisGetPartnumbersComPerguntasPendentes(null, null, false,$search_part_number,$remover_busca_estabelecimento);
        }
		$this->ctr_pendencias_pergunta_model->unset_state('filter.usuarioContagemLogado');
		$total = 0;
		foreach ( $partNumbersPerguntas  as $p)
		{
			$total = $total + $p->total;
		}
		
		if ($total == 0) {
			$this->session->unset_userdata('perguntas_pendentes');
		} else {
			$this->session->set_userdata('perguntas_pendentes', true);
		}

		return response_json(array(
			'status' => 200,
			'data' => $total
		), 200);
	}
}
