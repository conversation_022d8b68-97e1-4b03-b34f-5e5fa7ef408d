<?php 

class <PERSON><PERSON>_diana extends MY_Controller
{
	private $apps = array(
		'API1' => 'Grupo tarifário',
		'API2' => 'NCMs',
		'API3' => 'Solução de consulta',
	);

	public function __construct()
	{
	    parent::__construct();

	    if (!is_logged()) {
	        redirect('/login');
		}

		if (!has_role('consulta_diana')) {
            show_permission();
	    }

		$this->load->library('diana');

		$this->load->model(array(
			'empresa_model'
		));
	}

	public function index()
	{
		$data = array();

		$data['apps'] = $this->apps;

		$this->load->model(
			array(
				'app_ia_segmento_model',
				'app_ia_segmento_modelo_model',
				'grupo_tarifario_model'
			)
		);

		$result = null;

		$get = $this->input->get();

		$data['segmentos'] = $this->app_ia_segmento_model->get_all_entries();

		if (!empty($get))
	    {
	    	$search_data = array();

	    	$segmento_id                   = (int) isset($get['id_segmento']) ? $get['id_segmento'] : null;
	    	$search_data['num_predicoes']  = (int) (!empty($get['num_predicoes']) ? $get['num_predicoes'] : 3);
	    	$search_data['termo_pesquisa'] = $get['termo_pesquisa'];
	    	$modelos = null;

	    	if (!empty($segmento_id))
	    	{
	    		$modelos = $this->app_ia_segmento_modelo_model->get_entry($segmento_id);
	    	}

			$result = $this->diana->get_entries($search_data, $modelos);

			if (isset($result['API1']) && !empty($result['API1']))
			{
				$api_data = $result['API1'];
				for ($i=0; $i < count($api_data->data); $i++) { 
					for ($e=0; $e < count($api_data->data[$i]->predictions); $e++) { 
						if (isset($api_data->data[$i]->predictions[$e]->pred))
						{
							$grupo_tarifario = $this->grupo_tarifario_model->get_entry_by_desc_no_exception($api_data->data[$i]->predictions[$e]->pred);
							$result['API1']->data[$i]->predictions[$e]->grupo_tarifario = $grupo_tarifario;
						}
					}
				}
			}
			
			$data['result']     = $result;
			$data['status_api'] = $result['status_api'];
			
			if (!empty($get['exportar'])) 
			{
				$this->exportar($result);
			}
	    }
		
		$this->include_js(array(
            'bootstrap-select/bootstrap-select.js',
            'bootstrap-notify.min.js',
            'jquery.cookie.js'
        ));

        $this->include_css(array(
            'bootstrap-select/bootstrap-select.css',
            'animate.css',
            'consulta-diana.css',
        ));

		$this->render('consulta_diana/default', $data);
	}

	public function ajax_get_info_grupo_tarifario()
	{
		$this->load->model("grupo_tarifario_model");

		$post = $this->input->post();

		if (empty($post)) {
			$post = json_decode(file_get_contents('php://input'),true); 
		}

		$data = $this->grupo_tarifario_model->get_entry($post["id_grupo_tarifario"]);

		$data->observacao = strip_tags($data->observacao);

		echo json_encode($data); exit;
	}

	public function exportar($data = NULL)
	{
		$this->load->library('Excel');
		
		$this->excel->setActiveSheetIndex(0);
		$this->excel->getActiveSheet()->setTitle('Diana');
		$this->excel->getActiveSheet()->setCellValue('A1', 'APP');
		$this->excel->getActiveSheet()->setCellValue('B1', 'Acurácia');
		$this->excel->getActiveSheet()->setCellValue('C1', 'Predição');
		$this->excel->getActiveSheet()->setCellValue('D1', 'Sol');
		
		$this->excel->getActiveSheet()->getStyle('A1:D1')->applyFromArray(
            array(
                'fill' => array(
                    'type' => PHPExcel_Style_Fill::FILL_SOLID,
                    'color' => array('rgb' => '16365C')
                ),
                'font'  => array(
                    'bold'  => true,
                    'color' => array('rgb' => 'FFFFFF'),
                )
            )
        );

		$this->excel->getActiveSheet()->getDefaultColumnDimension()->setWidth('25');
		$this->excel->getActiveSheet()->getStyle('A1:D1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

		$i = 2;
		
		foreach ($this->apps as $api_key => $title)
		{	
			$data_array = array_values($data[$api_key]->data);
			$data_api = array_shift($data_array);
			
			if (empty($data_api))
			{
				continue;
			}

    		$predictions = $data_api->predictions;

    		if (empty($predictions))
    		{
    			continue;
    		}

			foreach ($predictions as $prediction)
			{
				$this->excel->getActiveSheet()->setCellValueExplicit('A' . $i, $title, PHPExcel_Cell_DataType::TYPE_STRING);

				$this->excel->getActiveSheet()->setCellValueExplicit('B' . $i, number_format($prediction->acc * 100, 2, '.', '')  . '%', PHPExcel_Cell_DataType::TYPE_STRING);				
				$this->excel->getActiveSheet()->setCellValueExplicit('C' . $i, $prediction->pred, PHPExcel_Cell_DataType::TYPE_STRING);				
				
				if (isset($prediction->sol))
				{
					$this->excel->getActiveSheet()->setCellValueExplicit('D' . $i, $prediction->sol, PHPExcel_Cell_DataType::TYPE_STRING);				
				}
				$i++;
			}
		}

        $filename = 'consulta_diana_'.date('Y-m-d_H-i-s').'.xlsx';
	    $objWriter = PHPExcel_IOFactory::createWriter($this->excel, 'Excel2007');

	    header('Content-Type: application/vnd.ms-excel');
	    header('Content-Disposition: attachment;filename="'.$filename.'"');
	    header('Cache-Control: max-age=0');

	    $objWriter->save('php://output');
		exit;
	}

    //colocar em algum outro lugar
    private function get_post_values()
    {
        return json_decode(file_get_contents('php://input'), true);
    }

    public function save_values_company()
    {
        $post = $this->get_post_values();

		$empresa = $this->empresa_model->get_entry($post['id_empresa']);

        if (!empty($post['listSort'])) {
            $this->empresa_model->remove_rel_diana_inf(array(
                'id_empresa' => $post['id_empresa']
            ));
            
            foreach($post['listSort'] as $item) {
                $this->empresa_model->rel_diana_inf(array(
                    'id_empresa' => $post['id_empresa'],
                    'index' => $item['index'],
                    'slug' => $item['id'],
                    'checked' => $item['checked'] ? 1 : 0
                ));
            }
        }
        
        $data = array(
            'app_a_seguimento'   => $post['app_a_seguimento'],
            'app_b_seguimento'   => $post['app_b_seguimento'],
			'funcoes_adicionais' => $empresa->funcoes_adicionais.'|has_diana'
        );

        $where = array(
            'id_empresa' => $post['id_empresa']
        );

        if (!empty($data) && !empty($where)) {
            $this->empresa_model->update_values($data, $where);
        }

        return response_json(array(
            'success' => true
        ), 200);
    }

    public function get_values_company()
    {
		$id_empresa = $this->input->get('id_empresa');
        $empresa = $this->empresa_model->get_full_entry($id_empresa);

        return response_json($empresa);
    }

    public function get_predictions()
    {
        $this->load->model(array(
			'app_ia_segmento_model',
			'app_ia_segmento_modelo_model',
			'grupo_tarifario_model',
			'cad_item_model'
		));

        $empresa = $this->empresa_model->get_full_entry(sess_user_company());

        $search_data = array();

        $post = $this->get_post_values();

		$segmento_id = 0;

		$part_number = $post['part_number'];
		$descricao = $post['descricao_item_part_number'];

		$termoPesquisa = $this->cad_item_model->get_termo_pesquisa($part_number, $descricao);

        $search_data['num_predicoes'] = 3;
        $search_data['termo_pesquisa'] = $termoPesquisa;
		$app = $post['app'];
        $modelos = null;
		if ($app == 'a') {
			$segmento_id = $empresa->app_a_seguimento;
		} else if ($app == 'b') {
			$segmento_id = $empresa->app_b_seguimento;
		}

        if (!empty($segmento_id)) {
            $modelos = $this->app_ia_segmento_modelo_model->get_entry($segmento_id);
        }
		
        $result = $this->diana->get_entries_by_app($search_data, $modelos, $app);


		if (isset($result['result']) && !empty($result['result']))
			{
				$api_data = $result['result'];
				for ($i=0; $i < count($api_data->data); $i++) { 
					for ($e=0; $e < count($api_data->data[$i]->predictions); $e++) { 
						if (isset($api_data->data[$i]->predictions[$e]->pred))
						{
							$grupo_tarifario = $this->grupo_tarifario_model->get_entry_by_desc_no_exception($api_data->data[$i]->predictions[$e]->pred);

							if ($grupo_tarifario) {
								$result['result']->data[$i]->predictions[$e]->grupo_tarifario = $grupo_tarifario;
							} else {
								$result['result']->data[$i]->predictions[$e]->grupo_tarifario = '';
							}

						}
					}
				}
			}

        return response_json($result);
    }
}