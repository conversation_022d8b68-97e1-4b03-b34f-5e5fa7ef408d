<?php

class Lessin extends MY_Controller {
    public function __construct()
    {
        parent::__construct();

        $this->load->library('breadcrumbs');

        if (! is_logged()) {
            redirect('login');
        }

        if (!company_can("lessin")) {
            show_permission();
        }

        $this->load->library("Item/LessinUseCases");
    }

    public function index()
    {
        $data = array();
        
        $this->load->library('pagination');

        if (!empty($this->input->get())) {
            $this->lessinusecases->apply_filters($this->input->get());
        }

        $per_page = $this->input->get('per_page');
        $offset = $per_page;
        $limit = 15;
        
        $query_string = !empty($this->input->get()) ? "?" . http_build_query(array("id" => $this->input->get("id"), "ncm" => $this->input->get("ncm"))) : "";
        $data['items'] = $this->lessinusecases->get_items($limit, ($per_page > 0 ? $per_page - 1 : 0) * $limit);
        $data['query_string'] = $query_string;
        
        $result = $this->lessinusecases->get_total_items();

        $this->pagination->initialize(array(
            'base_url' => base_url("lessin/index{$query_string}"),
            'use_page_numbers' => TRUE,
            'per_page' => $limit,
            'page_query_string' => TRUE,
            'total_rows' => $result->total
        ));
        
        $data['pagination'] = $this->pagination->create_links();

        $this->title = "Lessin";
        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Lessin', '/lessin/');

        $this->include_js(array(
            'bootstrap-select/bootstrap-select.js',
        ));

        $this->include_css(array(
            'bootstrap-select/bootstrap-select.css'
        ));

        $this->render('lessin/default', $data);
    }

    public function import()
    {
        $msg = "";
        $type= "";
       
        if ($this->input->post('submit')) {
            $result = $this->lessinusecases->upload_xlsx();

            $msg = $result['msg'];
            $type = $result['type'];
        }

        $message_on_render = $this->message_config($msg, $type);

        $this->message_next_render($message_on_render, NULL, TRUE);

        redirect('/lessin/');
    }

    public function export()
    {
        $this->load->library('Excel');

        if (!empty($this->input->get())) {
            $this->lessinusecases->apply_filters($this->input->get());
        }

        $items = $this->lessinusecases->get_items();

        $xlsx_writer = $this->lessinusecases->generate_xlsx($this->excel, $items);

        $filename = 'lessin_' . date('Y-m-d_H-i-s') . '.xlsx';

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $objWriter = PHPExcel_IOFactory::createWriter($xlsx_writer, 'Excel2007');
        $objWriter->save('php://output'); 
    }

    public function unapply_filters()
    {
        $this->lessinusecases->unapply_filters();

        redirect("lessin");
    }

    public function get_related_items()
    {
        return response_json(array(
            "items" => $this->lessinusecases->get_related_items($this->input->get("id_lessin"))
        ));
    } 
}