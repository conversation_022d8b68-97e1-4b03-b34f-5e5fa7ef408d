<?php

class Base_Estatisticas extends MY_Controller {

    public function __construct() {
        parent::__construct();

        if ($this->uri->segment(3) !== 'cron') {
            if (!is_logged()) {
                redirect('/login');
            }

            if (!has_role('ncm_consulta_estatisticas')) {
                show_404();
            }
        }

        $this->load->library('breadcrumbs');

        $this->load->model("score_model");

        set_time_limit(0);
    }

    public function index()
    {
        $data = array();

        $data['consultas'] = array(
            'importacao_cliente' => null, 'xmls_cliente' => null,
            'consultas_rf' => null, 'estatisticas_br' => null,
            'lista_importacoes_ncm' => null
        );

        if ($this->input->post('commit'))
        {
            $this->load->library('form_validation');
            $oracle = $this->load->database('estat_brasil', TRUE);

            $raw_mes_ano = $this->input->post('mes_ano');
            $descricao_produto = $this->input->post('descricao_produto');
            $cnpj_cliente = $this->input->post('cnpj_cliente');

            $importacao_cliente = $this->input->post('importacao_cliente');
            $xmls_cliente = $this->input->post('xmls_cliente');
            $consultas_rf = $this->input->post('consultas_rf');
            $estatisticas_br = $this->input->post('estatisticas_br');
            $lista_importacoes_ncm = $this->input->post('lista_importacoes_ncm');
            $ncm = $this->input->post('ncm');
            $num_max_importacoes = (int) $this->input->post('num_max_importacoes');

            if ($importacao_cliente || $xmls_cliente || $consultas_rf || $estatisticas_br)
            {
                $this->form_validation->set_rules('descricao_produto', 'Termo de Pesquisa', 'trim|required');
            }

            if ($importacao_cliente || $estatisticas_br || $lista_importacoes_ncm)
            {
                $this->form_validation->set_rules('mes_ano', 'Mês/Ano', 'trim|required|callback_valid_date');
            }

            if ($importacao_cliente || $xmls_cliente)
            {
                $this->form_validation->set_rules('cnpj_cliente', 'CNPJ do Cliente', 'trim|required');
            }

            if ($lista_importacoes_ncm)
            {
                $this->form_validation->set_rules('ncm', 'NCM', 'trim|required');
                $this->form_validation->set_rules('num_max_importacoes', 'Número Máximo de Importações', 'trim|required');
            }

            if ( ! $this->form_validation->run() ) {
                $data['erros'] = validation_errors();
            }else
            {
                if (!$this->input->is_set('em_background'))
                {
                    $mes_ano = explode("-", $raw_mes_ano);
                    $mes_ano = sprintf("%04d-%02d", $mes_ano[1], $mes_ano[0]);

                    $str_descricao_produto = strtoupper($descricao_produto);

                    if ($importacao_cliente)
                    {
                        $ic_mes_ano = date("Y-m-d", strtotime($mes_ano));

                        $query = $oracle->query("
                            select
                                adicao.cod_ncm, merc.des_mercadoria, merc.num_di,
                                merc.num_adicao, merc.num_seq_merc, di.dat_di
                            from
                                becomex.tb_di di,
                                becomex.tb_di_adicao adicao,
                                becomex.tb_di_adicao_merc merc
                            where
                                di.cnpj_import like '{$cnpj_cliente}%'
                                and di.num_versao = 0 and di.dat_di > TO_DATE('{$ic_mes_ano}', 'YYYY-MM-DD')
                                and adicao.num_di = di.num_di and adicao.num_versao = 0
                                and merc.num_di = di.num_di and merc.num_adicao = adicao.num_adicao
                                and merc.num_versao = 0 and UPPER(merc.des_mercadoria) like '%{$str_descricao_produto}%'
                        ");

                        $sheet = array(
                            'data' => array(),
                            'title' => 'Importação do Cliente',
                            'headers' => array(
                                'cod_ncm' => 'string',
                                'des_mercadoria' => 'string',
                                'num_di' => 'string',
                                'num_adicao' => 'string',
                                'num_seq_merc' => 'string',
                                'dat_di' => 'string'
                            )
                        );

                        foreach ($query->result() as $result)
                        {
                            $sheet['data'][] = array(
                                (string) $result->COD_NCM,
                                (string) $result->DES_MERCADORIA,
                                (string) $result->NUM_DI,
                                (string) $result->NUM_ADICAO,
                                (string) $result->NUM_SEQ_MERC,
                                (string) $result->DAT_DI
                            );
                        }

                        $xlsx[] = $sheet;
                    }

                    if ($xmls_cliente)
                    {
                        $str_descricao_produto = strtoupper($descricao_produto);

                        $query = $oracle->query("
                            select
                                nf.emit_xnome, item.PROD_CPROD COD_PROD,
                                item.prod_ncm, item.PROD_XPROD, item.PROD_VPROD
                            from
                                xmlnf_nf nf,
                                xmlnf_nf_item item
                            where nf.dest_cnpj like '{$cnpj_cliente}%'
                                and item.id_arquivo = nf.id_arquivo
                                and UPPER(item.PROD_XPROD) LIKE ('%{$str_descricao_produto}%')
                            order by 2, 3, 4
                        ");

                        $sheet = array(
                            'data' => array(),
                            'title' => 'XMLs do Cliente',
                            'headers' => array(
                                'emit_xnome' => 'string',
                                'cod_prod' => 'string',
                                'prod_ncm' => 'string',
                                'prod_xprod' => 'string',
                                'prod_vprod' => 'string'
                            )
                        );

                        foreach ($query->result() as $result)
                        {
                            $sheet['data'][] = array(
                                (string) $result->EMIT_XNOME,
                                (string) $result->COD_PROD,
                                (string) $result->PROD_NCM,
                                (string) $result->PROD_XPROD,
                                (string) $result->PROD_VPROD
                            );
                        }

                        $xlsx[] = $sheet;
                    }

                    if ($consultas_rf)
                    {
                        $query = $oracle->query("
                            SELECT
                                C.COD_NCM, C.NR_DOCTO, C.ORGAO,
                                C.DAT_CONSULTA, C.ASSUNTO, C.EMENTA
                            FROM CONSULTA_SRF C
                            WHERE UPPER(ementa) like UPPER('%{$descricao_produto}%')
                        ");

                        $sheet = array(
                            'data' => array(),
                            'title' => 'Consulta RF',
                            'headers' => array(
                                'cod_ncm' => 'string',
                                'nr_docto' => 'string',
                                'orgao' => 'string',
                                'dat_consulta' => 'string',
                                'assunto' => 'string',
                                'ementa' => 'string'
                            )
                        );

                        foreach ($query->result() as $result)
                        {
                            $sheet['data'][] = array(
                                (string) $result->COD_NCM,
                                (string) $result->NR_DOCTO,
                                (string) $result->ORGAO,
                                (string) $result->DAT_CONSULTA,
                                (string) $result->ASSUNTO,
                                (string) $result->EMENTA
                            );
                        }

                        $xlsx[] = $sheet;
                    }

                    if ($estatisticas_br)
                    {
                        $eb_mes_ano = date("Ym", strtotime($mes_ano));
                        $search_descricao = str_replace(' ', '%', $descricao_produto);

                        $query = $oracle->query("
                            select ncm, descricao_produto
                            from estat_brasil.estat_importacao
                            where id_mes_ano >= '{$eb_mes_ano}' and ( upper(descricao_produto) like upper('%{$search_descricao}%') )
                        ");

                        $sheet = array(
                            'data' => array(),
                            'title' => 'Estatísticas Brasil',
                            'headers' => array(
                                'ncm' => 'string',
                                'descricao_produto' => 'string'
                            )
                        );

                        foreach ($query->result() as $result)
                        {
                            $desc = preg_replace("/\v/", "", $result->DESCRICAO_PRODUTO);
                            $desc = str_replace("=", "'=", $desc);

                            $sheet['data'][] = array(
                                (string) $result->NCM,
                                (string) $desc
                            );
                        }

                        $xlsx[] = $sheet;
                    }

                    if ($lista_importacoes_ncm)
                    {
                        $ncm = $this->input->post('ncm');
                        $num_max_importacoes = (int) $this->input->post('num_max_importacoes');

                        $id_mes_ano = date("Ym", strtotime($mes_ano));

                        $query = $oracle->query("
                            SELECT * FROM (
                                select inner_query.*, rownum rnum FROM
                                (
                                    select * from estat_brasil.estat_importacao
                                    where ncm = '{$ncm}' and id_mes_ano >= '{$id_mes_ano}'
                                ) inner_query WHERE rownum < {$num_max_importacoes}
                            )
                        ");

                        $sheet = array(
                            'data' => array(),
                            'title' => 'Importações NCM',
                            'headers' => array(
                                'nr_ordem' => 'string',
                                'id_mes_ano' => 'string',
                                'mes_ano' => 'string',
                                'ncm' => 'string',
                                'descricao_ncm' => 'string',
                                'cod_pais_origem' => 'string',
                                'desc_pais_origem' => 'string',
                                'cod_pais_aquisicao' => 'string',
                                'desc_pais_aquisicao' => 'string',
                                'cod_ume' => 'string',
                                'desc_ume' => 'string',
                                'unidade_comercializacao' => 'string',
                                'descricao_produto' => 'string',
                                'qtde_ume' => 'string',
                                'peso_liquido_kg' => 'string',
                                'val_fob_us' => 'string',
                                'val_frete_us' => 'string',
                                'val_seguro_us' => 'string',
                                'val_unit_produto_us' => 'string',
                                'qtde_comerc' => 'string',
                                'val_total_produto_us' => 'string'
                            )
                        );

                        foreach ($query->result() as $result)
                        {
                            $sheet['data'][] = array(
                                (string) $result->NR_ORDEM,
                                (string) $result->ID_MES_ANO,
                                (string) $result->MES_ANO,
                                (string) $result->NCM,
                                (string) $result->DESCRICAO_NCM,
                                (string) $result->COD_PAIS_ORIGEM,
                                (string) $result->DESC_PAIS_ORIGEM,
                                (string) $result->COD_PAIS_AQUISICAO,
                                (string) $result->DESC_PAIS_AQUISICAO,
                                (string) $result->COD_UME,
                                (string) $result->DESC_UME,
                                (string) $result->UNIDADE_COMERCIALIZACAO,
                                (string) $result->DESCRICAO_PRODUTO,
                                (string) $result->QTDE_UME,
                                (string) $result->PESO_LIQUIDO_KG,
                                (string) $result->VAL_FOB_US,
                                (string) $result->VAL_FRETE_US,
                                (string) $result->VAL_SEGURO_US,
                                (string) $result->VAL_UNIT_PRODUTO_US,
                                (string) $result->QTDE_COMERC,
                                (string) $result->VAL_TOTAL_PRODUTO_US
                            );
                        }

                        $xlsx[] = $sheet;
                    }

                    $this->session->set_userdata('xlsx_data', $xlsx);

                    $this->message_next_render('
                        <h4>Arquivo gerado com sucesso!</h4>
                        <p>O download iniciará em alguns segundos...</p>
                    ');

                    redirect('base_estatisticas', 'refresh');
                }else
                {
                    $query_str  = 'raw_mes_ano='.$raw_mes_ano;
                    $query_str .= '&descricao_produto='.$descricao_produto;
                    $query_str .= '&cnpj_cliente='.$cnpj_cliente;
                    $query_str .= '&importacao_cliente='.$importacao_cliente;
                    $query_str .= '&xmls_cliente='.$xmls_cliente;
                    $query_str .= '&consultas_rf='.$consultas_rf;
                    $query_str .= '&estatisticas_br='.$estatisticas_br;
                    $query_str .= '&lista_importacoes_ncm='.$lista_importacoes_ncm;
                    $query_str .= '&ncm='.$ncm;
                    $query_str .= '&num_max_importacoes='.$num_max_importacoes;
                    $query_str .= '&id_usuario='.sess_user_id();

                    $query_str = base64_encode($query_str);

                    if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                        shell_exec('php index.php cron/gerar_xls/'.$query_str);
                    } else {
                        shell_exec('/usr/bin/php index.php cron/gerar_xls/'.$query_str.' > /dev/null 2>/dev/null &');
                    }

                    $this->message_next_render('<h5><strong>OK!</strong> A planilha será enviada para o seu e-mail.</h5>');

                    redirect("base_estatisticas");
                }
            }

            $data['consultas'] = array(
                'importacao_cliente'    => $importacao_cliente,
                'xmls_cliente'          => $xmls_cliente,
                'consultas_rf'          => $consultas_rf,
                'estatisticas_br'       => $estatisticas_br,
                'lista_importacoes_ncm' => $lista_importacoes_ncm
            );
        }

        $this->include_css('b3-datetimepicker.min.css');
        $this->include_js('b3-datetimepicker.min.js?v=3.1.1');

        $this->include_js('jquery.maskedinput.js');

        $data['download'] = FALSE;

        if ($sess_xlsx_data = $this->session->userdata('xlsx_data')) {
            $data['download'] = TRUE;
        }

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Consulta Base Estatísticas', '/base_estatisticas/');

        $this->render('base_estatisticas', $data);
    }

    public function gerar_xlsx()
    {
        if ( !($data = $this->session->userdata('xlsx_data')) ) {
            return;
        }

        $this->session->unset_userdata('xlsx_data');

        $this->load->library('xlsxwriter');

        $this->xlsxwriter->setAuthor('Becomex Consulting');

        $writer = new XLSXWriter();

        foreach ($data as $sheet) {
            $writer->writeSheet($sheet['data'], $sheet['title'], $sheet['headers']);
        }

        $filename = 'base-estatisticas-'.date('d-m-Y_H-i-s').'.xlsx';

        header('Content-disposition: attachment; charset=UTF-8; filename='.$filename);
        header('Content-type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

        $writer->writeToStdOut($filename);
    }

    public function valid_date($date)
    {
        $format = "m-Y";
        $d = DateTime::createFromFormat($format, $date);

        if($d && $d->format($format) == $date) {
            return true;
        } else {
            $this->form_validation->set_message('valid_date', 'Data inválida. A data deve seguir o formato '. $format .'.');
            return false;
       }
    }
}
