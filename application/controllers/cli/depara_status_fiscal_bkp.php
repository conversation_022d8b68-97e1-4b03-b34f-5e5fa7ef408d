<?php

class Depara_status_fiscal_bkp extends CI_Controller {

    public $_processar_empresa = ['160','161','177','183','184','202','213','219','239','252','258','272','281','282','283','285','292','299','300','311','349','354','377','383','397','400','402','406','413','424','426','437','461'];

    public function __construct()
    {
        parent::__construct();
       // $this->_processar_empresa = range(490, 1, -1);
        if (!$this->input->is_cli_request()) {
            die('Permission denied');
        }

        
        set_time_limit(0);

        ini_set('memory_limit', '4096M');
    }

    
    public function buscar_bkp($part_number, $estabelecimento, $id_empresa)
    {
        $base_bkp = $this->load->database('gt060325', TRUE);

        $base_bkp->where('part_number', $part_number);
        $base_bkp->where('estabelecimento', $estabelecimento);
        $base_bkp->where('id_empresa', $id_empresa);

        $query = $base_bkp->get('item');
        $row = $query->row();
    
        return $row;
    }

    public function buscar_empresas()
    {
        $this->db->where("ativo", 1);

        if (!empty($this->_processar_empresa))
        {
            $this->db->where_in("id_empresa", $this->_processar_empresa);
        }
        
        $this->db->order_by("id_empresa", "ASC");
        $query = $this->db->get('empresa');

        return  $query->result();
    }
 
    public function index() {
        echo 'inicio';
        print_r($this->_processar_empresa);

        //$this->load->model(array("empresa_model")); // Não parece necessário.
        $this->db->query("SET sql_mode=''"); // Cuidado com isso, pode ter efeitos colaterais.  Verifique se é realmente necessário.
        $empresas = $this->buscar_empresas();

        foreach ($empresas as $empresa) {
            if ($empresa->ativo == 0) {
                continue;
            }

            echo ' Processando ' . $empresa->nome_fantasia . PHP_EOL;
            $this->buscar($empresa->id_empresa); 
        }

        echo 'fim';
    }

    public function buscar($id_empresa) {
        $offset = 0;
        $limit = 500;
        $continuar = true;
        $itens = [];
        while ($continuar) {
            $this->db->select('it.part_number, it.estabelecimento, it.id_empresa, it.data_modificacao, it.id_status as ant_status');
            $this->db->from('gestaotarifaria.item it');
            $this->db->join('gestaotarifaria.item cdit', 'it.part_number = cdit.part_number AND it.estabelecimento = cdit.estabelecimento AND it.id_empresa = cdit.id_empresa', 'inner');
            $this->db->join('empresa emp', 'it.id_empresa = emp.id_empresa AND cdit.id_empresa = emp.id_empresa', 'inner'); //Join correto
       
            
            $this->db->where('it.id_empresa', $id_empresa);
            $this->db->where('it.data_modificacao <', '2025-03-05 21:48:27');
            $this->db->where('emp.ativo', 1);
            $this->db->limit($limit, $offset); // LIMIT e OFFSET para paginação.

            $query = $this->db->get();
            $results = $query->result();

            if (empty($results)) {
                $continuar = false; // Se não houver mais resultados, para o loop.
            } else {
                // Processa os resultados DENTRO do loop de busca.
                foreach ($results as $result) {
                    $busca = $this->buscar_bkp($result->part_number, $result->estabelecimento, $result->id_empresa);

                    if (!empty($busca)) {
                        $result->status_numero = $busca->id_status;
                        $result->ultima_modificacao = $busca->data_modificacao;
                    } else {
                        continue; // Se não encontrar no backup, pula.
                    }

                    if ($result->ant_status == $busca->id_status)
                    {
                        continue;
                    }

                    if (!empty($result->status_numero) && !empty($result->ultima_modificacao) && !empty($result->part_number) && !empty($result->estabelecimento) && !empty($result->id_empresa)) {
                         echo 'part_number |' . $result->part_number . '| estabelecimento |' . $result->estabelecimento . '| empresa ' . $result->id_empresa . '| status ' . $result->status_numero . '| ultima_modificacao |' . $result->ultima_modificacao . PHP_EOL;
                         $itens[] = $result->part_number.' - '.$result->estabelecimento . ' - '.$result->status_numero;
                        $this->db->query(
                            "UPDATE item i 
                            SET i.id_status = {$result->status_numero}
                            WHERE i.part_number = '{$result->part_number}' 
                            AND i.estabelecimento = '{$result->estabelecimento}' 
                            AND i.id_empresa = '{$result->id_empresa}'"
                        );
                    }
                }

                $offset += $limit; // Incrementa o offset para a próxima página de resultados.
                echo "Processados {$offset} registros da empresa {$id_empresa}...\n";
            }
            $query->free_result(); //libera a memoria
        }
         echo "Empresa {$id_empresa} finalizada.\n";
         print_r($itens);
    }
 
    
}
