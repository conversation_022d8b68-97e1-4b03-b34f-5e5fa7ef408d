<?php

class Depara_prioridades_novos extends CI_Controller {

    public function __construct()
    {
        parent::__construct();

        if (!$this->input->is_cli_request()) {
            die('Permission denied');
        }
    }

    public function index() {
    
        $query = "INSERT INTO empresa_prioridades (id_empresa, nome, ordem, qdt_horas_uteis, valor_padrao, valor_quimico)
                    SELECT id_empresa, 'NORMAL', 1, 40, 0.00, 0.00
                    FROM empresa
                    WHERE id_empresa NOT IN (SELECT id_empresa FROM empresa_prioridades GROUP BY id_empresa)
                    AND ativo = 1 AND id_empresa NOT IN (154, 201)";

        $this->db->query($query, null, false);

        $query_2 = "UPDATE item i
                        JOIN empresa_prioridades ep ON i.id_empresa = ep.id_empresa AND ep.nome = 'NORMAL'
                        SET i.id_prioridade = ep.id_prioridade
                        WHERE (UPPER(TRIM(i.prioridade) = 'NORMAL'))
                        AND i.id_prioridade IS NULL
                        AND i.id_empresa NOT IN (154, 201)";

        $this->db->query($query_2, null, false);
   
    }
}