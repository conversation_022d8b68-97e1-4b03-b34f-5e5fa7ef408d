<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

require_once  APPPATH . 'libraries/Spout/Autoloader/autoload.php';

use Box\Spout\Reader\ReaderFactory;
use Box\Spout\Common\Type;

class Xls_to_json_atributos extends MY_Controller
{
    public function index()
    {
        set_time_limit(0);

        $data = array(
            'listaNcm' => array()
        );
       
        $this->load->library('unzip');

        $this->load->helper('text_helper');
        $this->load->helper('formatador_helper');

        $filepath = '/home/<USER>/dev/php/becomex/gestaotarifaria/assets/atributos-json/planilha-completa-atributos-por-ncm.xlsx';

        $reader = ReaderFactory::create(Type::XLSX);
        $reader->open($filepath);

        foreach ($reader->getSheetIterator() as $sheet) {
            if ($sheet->getIndex() === 0) {
                $colunas = array(
                    'ncm' => 0,
                    'atributo' => 1,
                    'orientacao_de_preenchimento' => 2,
                    'forma_de_preenchimento' => 3,
                    'lista_de_dominio' => 4,
                    'cod_atributo' => 5,
                    'data_inicio_vigencia' => 6,
                    'data_fim_vigencia' => 7,
                    'cod_objetivo' => 8,
                    'descricao_objetivo' => 9,
                    'orgao' => 10,
                    'condicionante' => 11,
                    'condicionado' => 12,
                    'regra_de_condicao' => 13,
                    'id_atributo_pai' => 14
                );
                foreach ($sheet->getRowIterator() as $i => $row) {
                    if ($i > 1) {
                        $ncm = $row[$colunas['ncm']];

                        $dominios = explode(';', $row[$colunas['lista_de_dominio']]);

                        foreach($dominios as $key => $value) 
                        {
                            $dominios[] = array(
                                "codigo" => $key,
                                "descricao" => $value
                            );
                        }
                        
                        $listaAtributos = array(
                            'codigo' => $row[$colunas['cod_atributo']],
                            'definicao' => '',
                            'nomeApresentacao' => $row[$colunas['atributo']],
                            'orientacaoPreenchimento' => $row[$colunas['orientacao_de_preenchimento']],
                            'formaPreenchimento' => str_replace(" ", "_", remove_acentos($row[$colunas['forma_de_preenchimento']])),
                            'modalidade' => 'Exportação',
                            'obrigatorio' => '',
                            'objetivos' => array(
                                array(
                                    "codigo" => 7,
                                    "descricao" => "Produto"
                                ), array(
                                    "codigo" => 3,
                                    "descricao" => "Tratamento administrativo"
                                )
                            ),
                            'dominio' => $dominios,
                            'dataInicioVigencia' => $row[$colunas['data_inicio_vigencia']]->format('Y-m-d'),
                            'dataFimVigencia' => $row[$colunas['data_fim_vigencia']],
                            'orgaos' => array(
                                !empty($row[$colunas['orgao']]) ? $row[$colunas['orgao']] : null
                            ),
                            'atributoCondicionante' => $row[$colunas['condicionante']] == 'S' ? true : false,
                            'condicionados' => array(
                                $row[$colunas['condicionado']]
                            )
                        );
                        if (isset($data['listaNcm'][$ncm])) {
                            $data['listaNcm'][$ncm]['listaAtributos'][] = $listaAtributos;
                        } else {
                            $data['listaNcm'][$ncm] = array(
                                'codigoNcm' => $ncm,
                                'listaAtributos' => array(
                                    $listaAtributos
                                )
                            );
                        }
                    }
                }
            }
        }

        $indice = 0;
        $novoData = array('listaNcm' => array());

        foreach($data['listaNcm'] as $item) {
            $novoData['listaNcm'][] = $item;
        }

        $fp = fopen('/home/<USER>/dev/php/becomex/gestaotarifaria/assets/atributos-json/atributos.json', 'w');
        fwrite($fp, json_encode($novoData));
        fclose($fp);
    }
}
