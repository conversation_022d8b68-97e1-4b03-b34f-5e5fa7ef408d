<?php

class Depara_prioridades extends CI_Controller {

    public function __construct()
    {
        parent::__construct();

        if (!$this->input->is_cli_request()) {
            die('Permission denied');
        }
    }

    public function index() {
    
        $query = "UPDATE item i
                    JOIN empresa_prioridades ep ON i.id_empresa = ep.id_empresa AND ep.nome = 'NORMAL'
                    SET i.id_prioridade = ep.id_prioridade
                    WHERE (i.prioridade = '' OR i.prioridade IS NULL  OR TRIM(i.prioridade) = '')
                    AND i.id_prioridade IS NULL
                    AND i.id_empresa NOT IN (154, 201)
                    AND UPPER(i.prioridade) NOT IN ('CRÍTICO', 'CRITICO', 'SUPER CRÍTICO', 'SUPER CRITICO', 'SUPERCRÍTICO', 'SUPERCRITICO')";

        $this->db->query($query, null, false);

        $query_2 = "UPDATE item i
                        JOIN empresa_prioridades ep ON i.id_empresa = ep.id_empresa AND ep.nome = 'NORMAL'
                        SET i.id_prioridade = ep.id_prioridade
                        WHERE (UPPER(i.prioridade) = 'ALTA' OR UPPER(TRIM(i.prioridade) = 'NORMAL'))
                        AND i.id_prioridade IS NULL
                        AND i.id_empresa NOT IN (154, 201)";

        $this->db->query($query_2, null, false);

        $query_3 = "UPDATE item i
                        JOIN empresa_prioridades ep ON i.id_empresa = ep.id_empresa AND ep.nome = '0'
                        SET i.id_prioridade = ep.id_prioridade
                        WHERE (i.prioridade = '0 - máxima')
                        AND i.id_prioridade IS NULL";

        $this->db->query($query_3, null, false);

        print("Rodou com sucesso!");
    
    }
}