<?php

class Depara_perguntas_respostas extends CI_Controller {

    public function __construct()
    {
        parent::__construct();

        if (!$this->input->is_cli_request()) {
            die('Permission denied');
        }
    }

    public function index() {

        $this->load->model("item_model");
        $this->db->select('cad.id_empresa as empresa, cad.part_number as part_number,cad.estabelecimento as cad_estabelecimento, ctr.estabelecimento as ctr_estabelecimento');
        $this->db->join('ctr_resposta ctr', 'ctr.part_number = cad.part_number', 'inner');
        $this->db->where(" cad.estabelecimento <> ctr.estabelecimento group by cad.part_number", null, false);
        $this->db->order_by('cad.part_number', 'asc');
        $query = $this->db->get(" cad_item cad");

        $itens = $query->result();
        $total = $query->num_rows();
        $count = 0;

        foreach ($itens as $item) {
            $count++;

            $cad_estabelecimento = $item->cad_estabelecimento;
            $ctr_estabelecimento = $item->ctr_estabelecimento;
            $part_number = $item->part_number;
            $empresa =  $item->empresa;
            
            $this->db->where('id_empresa', $empresa);
            $this->db->where('part_number', $part_number);
            $this->db->where('estabelecimento', $ctr_estabelecimento);
            $query = $this->db->get(" cad_item");
            if($query->num_rows() == 0){

                $data = array(
                    "estabelecimento" => $cad_estabelecimento
                );
    
                $this->db->where('id_empresa', $empresa);
                $this->db->where('part_number', $part_number);
                $this->db->update('ctr_resposta', $data);
            }

            echo 'Processado item '.$count.' de '.$total . PHP_EOL;

        }
    }
}