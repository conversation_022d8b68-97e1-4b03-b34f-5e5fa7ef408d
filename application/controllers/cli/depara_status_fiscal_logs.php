<?php

class Depara_status_fiscal_logs extends CI_Controller {

    public $_processar_empresa = [];

    public function __construct()
    {
        parent::__construct();
        $this->_processar_empresa = range(490, 1, -1);
        if (!$this->input->is_cli_request()) {
            die('Permission denied');
        }

        
        set_time_limit(0);

        ini_set('memory_limit', '4096M');
    }

    
    public function buscar_bkp($part_number, $estabelecimento, $id_empresa)
    {
        $base_bkp = $this->load->database('gt060325', TRUE);

        $base_bkp->where('part_number', $part_number);
        $base_bkp->where('estabelecimento', $estabelecimento);
        $base_bkp->where('id_empresa', $id_empresa);

        $query = $base_bkp->get('item');
        $row = $query->row();
    
        return $row;
    }

    public function buscar_empresas()
    {
        $this->db->where("ativo", 1);

        if (!empty($this->_processar_empresa))
        {
            $this->db->where_in("id_empresa", $this->_processar_empresa);
        }
        
        $this->db->order_by("id_empresa", "ASC");
        $query = $this->db->get('empresa');

        return  $query->result();
    }
 
    public function index() {
        echo 'inicio';
        print_r($this->_processar_empresa);

        //$this->load->model(array("empresa_model")); // Não parece necessário.
        $this->db->query("SET sql_mode=''"); // Cuidado com isso, pode ter efeitos colaterais.  Verifique se é realmente necessário.
        $empresas = $this->buscar_empresas();

        foreach ($empresas as $empresa) {
            if ($empresa->ativo == 0) {
                continue;
            }

            echo ' Processando ' . $empresa->nome_fantasia . PHP_EOL;
            $this->buscar($empresa->id_empresa); 
        }

        echo 'fim';
    }

    public function buscar($id_empresa) {
        $offset = 0;
        $limit = 500;
        $continuar = true;
 
        while ($continuar) {
             
            $sql = "
            SELECT 
                TRIM(SUBSTRING(
                    log.motivo,
                    LOCATE('<strong>', log.motivo) + LENGTH('<strong>'),
                    LOCATE('</strong>', log.motivo) - (LOCATE('<strong>', log.motivo) + LENGTH('<strong>'))
                )) AS status_atual,
                
                CASE 
                    WHEN TRIM(SUBSTRING(log.motivo, LOCATE('<strong>', log.motivo) + LENGTH('<strong>'), LOCATE('</strong>', log.motivo) - LOCATE('<strong>', log.motivo) - LENGTH('<strong>'))) = 'Pendente de Homologação' THEN 1
                    WHEN TRIM(SUBSTRING(log.motivo, LOCATE('<strong>', log.motivo) + LENGTH('<strong>'), LOCATE('</strong>', log.motivo) - LOCATE('<strong>', log.motivo) - LENGTH('<strong>'))) = 'Homologado' THEN 2
                    WHEN TRIM(SUBSTRING(log.motivo, LOCATE('<strong>', log.motivo) + LENGTH('<strong>'), LOCATE('</strong>', log.motivo) - LOCATE('<strong>', log.motivo) - LENGTH('<strong>'))) = 'Reprovado' THEN 3
                    WHEN TRIM(SUBSTRING(log.motivo, LOCATE('<strong>', log.motivo) + LENGTH('<strong>'), LOCATE('</strong>', log.motivo) - LOCATE('<strong>', log.motivo) - LENGTH('<strong>'))) = 'Inativo' THEN 4
                    WHEN TRIM(SUBSTRING(log.motivo, LOCATE('<strong>', log.motivo) + LENGTH('<strong>'), LOCATE('</strong>', log.motivo) - LOCATE('<strong>', log.motivo) - LENGTH('<strong>'))) = 'Em Revisão' THEN 5
                    WHEN TRIM(SUBSTRING(log.motivo, LOCATE('<strong>', log.motivo) + LENGTH('<strong>'), LOCATE('</strong>', log.motivo) - LOCATE('<strong>', log.motivo) - LENGTH('<strong>'))) = 'Em Análise' THEN 6
                    WHEN TRIM(SUBSTRING(log.motivo, LOCATE('<strong>', log.motivo) + LENGTH('<strong>'), LOCATE('</strong>', log.motivo) - LOCATE('<strong>', log.motivo) - LENGTH('<strong>'))) = 'Pendente de Informações' THEN 7
                    WHEN TRIM(SUBSTRING(log.motivo, LOCATE('<strong>', log.motivo) + LENGTH('<strong>'), LOCATE('</strong>', log.motivo) - LOCATE('<strong>', log.motivo) - LENGTH('<strong>'))) = 'Perguntas Respondidas' THEN 8
                    WHEN TRIM(SUBSTRING(log.motivo, LOCATE('<strong>', log.motivo) + LENGTH('<strong>'), LOCATE('</strong>', log.motivo) - LOCATE('<strong>', log.motivo) - LENGTH('<strong>'))) = 'Revisar Informações ERP' THEN 9
                    WHEN TRIM(SUBSTRING(log.motivo, LOCATE('<strong>', log.motivo) + LENGTH('<strong>'), LOCATE('</strong>', log.motivo) - LOCATE('<strong>', log.motivo) - LENGTH('<strong>'))) = 'Homologado em Revisão' THEN 10
                    WHEN TRIM(SUBSTRING(log.motivo, LOCATE('<strong>', log.motivo) + LENGTH('<strong>'), LOCATE('</strong>', log.motivo) - LOCATE('<strong>', log.motivo) - LENGTH('<strong>'))) = 'Revisar Informações Técnicas' THEN 11
                    WHEN TRIM(SUBSTRING(log.motivo, LOCATE('<strong>', log.motivo) + LENGTH('<strong>'), LOCATE('</strong>', log.motivo) - LOCATE('<strong>', log.motivo) - LENGTH('<strong>'))) = 'Informações ERP Revisadas' THEN 12
                    WHEN TRIM(SUBSTRING(log.motivo, LOCATE('<strong>', log.motivo) + LENGTH('<strong>'), LOCATE('</strong>', log.motivo) - LOCATE('<strong>', log.motivo) - LENGTH('<strong>'))) = 'Aguardando definição responsável' THEN 13
                    WHEN TRIM(SUBSTRING(log.motivo, LOCATE('<strong>', log.motivo) + LENGTH('<strong>'), LOCATE('</strong>', log.motivo) - LOCATE('<strong>', log.motivo) - LENGTH('<strong>'))) = 'Aguardando Descrição' THEN 14
                    WHEN TRIM(SUBSTRING(log.motivo, LOCATE('<strong>', log.motivo) + LENGTH('<strong>'), LOCATE('</strong>', log.motivo) - LOCATE('<strong>', log.motivo) - LENGTH('<strong>'))) = 'Perguntas Respondidas (Novas)' THEN 15
                    ELSE NULL
                END AS status_numero_como_deve_ser,
            
                it.id_status AS status_agora,
            
                (SELECT MAX(criado_em)
                 FROM item_log
                 WHERE item_log.part_number = it.part_number
                   AND item_log.estabelecimento = it.estabelecimento
                   AND item_log.id_empresa = it.id_empresa
                ) AS ultima_modificacao,
            
                it.part_number,
                it.estabelecimento,
                it.id_empresa
            
            FROM item it
            
            INNER JOIN (
                SELECT il.*
                FROM item_log il
                INNER JOIN (
                    SELECT 
                        part_number,
                        estabelecimento,
                        id_empresa,
                        MAX(criado_em) AS max_criado_em
                    FROM item_log
                    WHERE titulo = 'atualizacao'
                      AND motivo LIKE 'Alteração do Status%'
                    GROUP BY part_number, estabelecimento, id_empresa
                ) ult ON 
                    il.part_number = ult.part_number
                    AND il.estabelecimento = ult.estabelecimento
                    AND il.id_empresa = ult.id_empresa
                    AND il.criado_em = ult.max_criado_em
            ) log ON 
                log.part_number = it.part_number
                AND log.estabelecimento = it.estabelecimento
                AND log.id_empresa = it.id_empresa
            
            WHERE 
                it.data_modificacao > '2025-03-05 21:48:27'
                AND log.titulo = 'atualizacao'
                AND log.motivo LIKE 'Alteração do Status%'
                AND it.id_empresa = '{$id_empresa}'
            
            HAVING status_agora <> status_numero_como_deve_ser
             
                LIMIT {$offset}, {$limit}
            ";
            
            $query = $this->db->query($sql);
            
           // $query = $this->db->get();
            $results = $query->result();

            if (empty($results)) {
                $continuar = false;
            } else {

                foreach ($results as $result) {

                    if (!empty($result->status_numero_como_deve_ser) && !empty($result->part_number) && !empty($result->estabelecimento) && !empty($result->id_empresa)) {
                         echo 'part_number |' . $result->part_number . '| estabelecimento |' . $result->estabelecimento . '| empresa ' . $result->id_empresa . '| status ' . $result->status_numero . '| ultima_modificacao |' . $result->ultima_modificacao . PHP_EOL;
 
                        $this->db->query(
                            "UPDATE item i 
                            SET i.id_status = '{$result->status_numero_como_deve_ser}'
                            WHERE i.part_number = '{$result->part_number}' 
                            AND i.estabelecimento = '{$result->estabelecimento}' 
                            AND i.id_empresa = '{$result->id_empresa}'"
                        );
                    }
                }

                $offset += $limit; // Incrementa o offset para a próxima página de resultados.
                echo "Processados {$offset} registros da empresa {$id_empresa}...\n";
            }
            $query->free_result(); //libera a memoria
        }
         echo "Empresa {$id_empresa} finalizada.\n";
    }
 
    
}
