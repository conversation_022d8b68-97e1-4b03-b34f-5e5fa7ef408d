<?php

class Depara_peso extends CI_Controller {

    public function __construct()
    {
        parent::__construct();

        if (!$this->input->is_cli_request()) {
            die('Permission denied');
        }
    }

    public function index() {
        $this->load->model("item_model");

        $this->db->where("info_adicional LIKE '%;peso:=>%'", null, false);
        
        $query = $this->db->get("item");
        $itens = $query->result();

        foreach ($itens as $item) {
            $peso = "";

            $info_adicional_full = explode(";", $item->info_adicional);
            $info_adicional_restrict = array();
            $info_adicional_concat = "";

            foreach ($info_adicional_full as $inf_adicional) {
                $valores = explode(":=>", $inf_adicional);
                
                $identificador = !empty($valores[0]) ? $valores[0] : "";
                
                if ($identificador == "peso") {
                    echo "Item[PN: {$item->part_number}] com campo peso concatenado encontrado." . PHP_EOL;
                    echo "Campo Peso[{$valores[1]}] desconcatenado e adicionado na coluna 'peso'." . PHP_EOL;

                    $peso = $valores[1];
                } else {
                    $info_adicional_restrict[$identificador] = !empty($valores[1]) ? $valores[1] : "";
                }
            }

            foreach ($info_adicional_restrict as $k => $info_adicional) {
                if (empty($info_adicional_concat)) {
                    $info_adicional_concat .= "{$k}:=>{$info_adicional}";
                    continue;
                } 

                $info_adicional_concat .= ";{$k}:=>{$info_adicional}";
            }
            
            $data = array(
                "peso" => $peso,
                "info_adicional" => $info_adicional_concat
            );

            $motivo = "Alteração da localização do Peso: <em>Coluna 'info_adicional'</em> &rarr; <strong>Coluna 'peso'</strong>";

            $this->item_model->update_item($item->part_number, $item->id_empresa, $data, $motivo, $item->estabelecimento);
        }
    }
}