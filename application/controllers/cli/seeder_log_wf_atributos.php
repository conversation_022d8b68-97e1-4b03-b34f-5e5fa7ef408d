<?php

class Seeder_Log_Wf_Atributos extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
    }
  
    public function get_all_cad_item_wf_atributo()
    {
        $query = $this->db->get("cad_item_wf_atributo");
        return $query->result();
    }

    public function get_all_status_wf_atributos()
    {
        $query = $this->db->get("status_wf_atributos");
        return $query->result();
    }

    public function get_all_status_wf_atributos_integracao()
    {
        $query = $this->db->get("status_wf_atributos_integracao");
        return $query->result();
    }
    public function get_user_becomex()
    {
        $this->db->where('email', '<EMAIL>');
        $query = $this->db->get('usuario', 1);
        return $query->row();
    }
    public function insert_log_wf_atributos($data)
    {
        $this->db->insert('log_wf_atributos', $data);
    }
 

    public function index()
    {
        $result_cad_item_wf_atributo = $this->get_all_cad_item_wf_atributo();
        $result_status_wf_atributos = $this->get_all_status_wf_atributos();
        $result_status_wf_atributos_integracao = $this->get_all_status_wf_atributos_integracao();
        $user_becomex = $this->get_user_becomex();
        foreach ($result_cad_item_wf_atributo as $r)
        {
            $data = [];

            for ($i = 0; $i < count($result_status_wf_atributos) -1; $i++)
            {
                $data['atualizado_em'] = date('Y-m-d H:i:s', strtotime("-" . (10 - $i) . " days"));
                $data['status_anterior'] = $result_status_wf_atributos[$i]->status;
                $data['status_atual'] = $result_status_wf_atributos[$i +1]->status;
                $data['justificativa'] = NULL;
                $data['part_number'] = $r->part_number;
                $data['estabelecimento'] = $r->estabelecimento;
                $data['id_empresa'] = $r->id_empresa;

                $data['id_usuario'] = $user_becomex->id_usuario;
                if ($i == 3)
                {
                    $data['justificativa'] = 'Análise por alteração do NCM';
                }
                if ($i == 5)
                {
                    $data['informacoes_integracao'] = 'Integrado com PUCOMEX com sucesso'; 
                    $data['justificativa'] = '  - testes';
                }
                $this->insert_log_wf_atributos($data);
                
            }
            echo 'Atualizando... ' .$r->part_number. PHP_EOL;
        }


        // $this->db->query("UPDATE item i
        //     JOIN cad_item_wf_atributo c 
        //     ON i.part_number = c.part_number 
        //     AND i.id_empresa = c.id_empresa 
        //     AND i.estabelecimento = c.estabelecimento
        //     SET i.wf_status_integracao = c.wf_status_integracao;");
        
        // $this->db->query("UPDATE item i
        //     JOIN cad_item_wf_atributo c 
        //     ON i.part_number = c.part_number 
        //     AND i.id_empresa = c.id_empresa 
        //     AND i.estabelecimento = c.estabelecimento
        //     SET i.wf_status_atributos = c.wf_status_atributos;");
            
     }
}
