<?php

class Depar<PERSON>_acesso extends CI_Controller {

    public function __construct()
    {
        parent::__construct();

        if (!$this->input->is_cli_request()) {
            die('Permission denied');
        }
    }

    public function index() {
        $acessos = $this->get_acessos();
        
        $this->load->model(array(
            'perfil_model',
            'permissao_model',
            'perfil_permissao_model'
        ));

        foreach($acessos as $acesso) {
            $permissao = $this->permissao_model->get_entry_by_slug($acesso['slug']);

            if (!empty($permissao)) {
                foreach ($acesso['perfis'] as $perfil) {
                    $perfil = $this->perfil_model->get_entry_by_description($perfil);

                    $checkPerfilPermissao = $this->perfil_permissao_model->check_perfil_permissao_exists($perfil->id_perfil, $permissao->id_permissao);

                    if (empty($checkPerfilPermissao)) {
                        $insert_acesso = array(
                            'id_perfil'    => (int)$perfil->id_perfil,
                            'id_permissao' => (int)$permissao->id_permissao
                        );
                        
                        $this->perfil_permissao_model->save($insert_acesso);
                        echo "Acesso entre perfil '".$perfil->descricao."' e permissão '".$permissao->descricao."' cadastrados." . PHP_EOL;
                    } else {
                        echo "Cadastro cancelado. Acesso entre perfil '".$perfil->descricao."' e permissão '".$permissao->descricao."' já existem." . PHP_EOL;
                    }
                }
            } else {
                echo "Nenhuma permissão foi encontrada com o slug '".$acesso['slug']."'." . PHP_EOL;
            }
        }
    }

    public function get_acessos() {
        $json_data = file_get_contents(FCPATH.'assets/de-para-acessos/acessos.txt');
        
        return json_decode($json_data, true);
    }
}