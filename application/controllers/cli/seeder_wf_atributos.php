<?php

class Seeder_Wf_Atributos extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
    }
 
    public function populate_cad_item_wf_atributo()
    {
        $this->db->query("INSERT INTO cad_item_wf_atributo (
                            part_number,
                            id_empresa,
                            estabelecimento
                        )
                        SELECT  
                            cx.part_number_original,
                            cx.id_empresa,
                            ci.estabelecimento
                        FROM comex cx
                        INNER JOIN cad_item ci ON ci.part_number = cx.part_number_original 
                            AND ci.estabelecimento = cx.unidade_negocio 
                            AND ci.id_empresa = cx.id_empresa
                            where cx.ind_ecomex = 'EI'
                        AND ((ci.id_grupo_tarifario IS NOT NULL AND ci.id_grupo_tarifario <> '') OR (ci.ncm_proposto IS NOT NULL AND ci.ncm_proposto <> ''));");
 
    }

    public function get_all_cad_item_wf_atributo()
    {
        $query = $this->db->get("cad_item_wf_atributo");
        return $query->result();
    }

    public function update_status_cad_item_wf_atributo()
    {

        $result_cad_item_wf_atributo = $this->get_all_cad_item_wf_atributo();
        $count_wf_status_atributos = 0;
        $count_wf_status_integracao = 0;
        
        foreach ($result_cad_item_wf_atributo as $r)
        {
            $count_wf_status_atributos++;
            $count_wf_status_integracao++;

            $dbdata = [];
            $dbdata['wf_status_atributos']   = 2;
            $dbdata['wf_status_integracao']  = 1;
 
            // if ($count_wf_status_atributos == 7)
            // {
            //     $count_wf_status_atributos = 0; 
            // }

            // if ($count_wf_status_integracao == 3)
            // {
            //     $count_wf_status_integracao = 0; 
            // }
            $this->db->update('item', $dbdata, ['part_number' =>  $r->part_number, 'estabelecimento' => $r->estabelecimento, 'id_empresa' => $r->id_empresa]);
            echo 'Atualizando... ' .$r->part_number. PHP_EOL;
        }
    }

    public function index()
    {
    //    $this->populate_cad_item_wf_atributo();
        $this->update_status_cad_item_wf_atributo();
    }
}
