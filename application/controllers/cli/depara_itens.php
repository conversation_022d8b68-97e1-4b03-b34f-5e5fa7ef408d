<?php

class Depara_itens extends CI_Controller {

    public function __construct()
    {
        parent::__construct();

        if (!$this->input->is_cli_request()) {
            die('Permission denied');
        }
    }

    public function index() {
        $this->load->model(array("empresa_model"));
		
        $empresas = $this->empresa_model->get_entries();
        $i = 0;
        foreach($empresas as $empresa) {
            $i++;

            $funcoes_adicionais = explode('|', $empresa->funcoes_adicionais);

            $query_fiscal_engenharia_homologar = "";
            $query_fiscal_engenharia_homologado = "";
            $query_fiscal_engenharia_nao_homologado = "";
            $query_fiscal_engenharia_obsoleto = "";
            $query_fiscal_engenharia_revisao = "";

            if (in_array('homologacao_fiscal', $funcoes_adicionais) && in_array('homologacao_engenharia', $funcoes_adicionais)) {
                
                $query_fiscal_engenharia_homologar = " AND (
                    cad_item.id_resp_fiscal IS NOT NULL AND cad_item.id_resp_engenharia IS NOT NULL
                ) AND (
                    NOT EXISTS (
                        SELECT 1 FROM cad_item_homologacao cih 
                            WHERE cih.id_item = cad_item.id_item 
                                and tipo_homologacao = 'Engenharia'
                    ) OR NOT EXISTS (
                        SELECT 1 FROM cad_item_homologacao cih 
                            WHERE cih.id_item = cad_item.id_item 
                                and tipo_homologacao = 'Fiscal'
                    )
                ) ";

                $query_fiscal_engenharia_homologado = " AND (
                    cad_item.id_resp_fiscal IS NOT NULL AND cad_item.id_resp_engenharia IS NOT NULL
                ) AND EXISTS ( 
                    SELECT 1 FROM cad_item_homologacao cih 
                        WHERE cih.id_item = cad_item.id_item 
                            AND cih.tipo_homologacao = 'Fiscal' 
                            AND cih.homologado = 1
                ) AND EXISTS ( 
                    SELECT 1 FROM cad_item_homologacao cih 
                        WHERE cih.id_item = cad_item.id_item 
                            AND cih.tipo_homologacao = 'Engenharia' 
                            AND cih.homologado = 1
                ) ";

                $query_fiscal_engenharia_nao_homologado = " AND (
                    cad_item.id_resp_fiscal IS NOT NULL AND cad_item.id_resp_engenharia IS NOT NULL
                ) AND ( EXISTS (
                    SELECT 1 FROM cad_item_homologacao cih 
                        WHERE cih.id_item = cad_item.id_item 
                        AND cih.tipo_homologacao = 'Engenharia' 
                ) OR EXISTS (
                    SELECT 1 FROM cad_item_homologacao cih 
                        WHERE cih.id_item = cad_item.id_item 
                        AND cih.tipo_homologacao = 'Fiscal' 
                )) ";
                
                $query_fiscal_engenharia_obsoleto = " AND ( 
                    cad_item.id_resp_fiscal IS NOT NULL AND cad_item.id_resp_engenharia IS NOT NULL
                ) AND EXISTS (
                    SELECT 1 FROM cad_item_homologacao cih 
                        WHERE cih.id_item = cad_item.id_item 
                        AND homologado = 2
                        AND cih.tipo_homologacao = 'Engenharia' 
                ) AND EXISTS (
                    SELECT 1 FROM cad_item_homologacao cih 
                        WHERE cih.id_item = cad_item.id_item 
                        AND homologado = 2
                        AND cih.tipo_homologacao = 'Fiscal' 
                ) ";
                            
                $query_fiscal_engenharia_revisao = "OR cad_item.id_resp_fiscal IS NULL OR cad_item.id_resp_engenharia IS NULL";
            } else if (in_array('homologacao_fiscal', $funcoes_adicionais)) {
                
                $query_fiscal_engenharia_homologar = " AND (
                    cad_item.id_resp_fiscal IS NOT NULL
                ) AND (
                         NOT EXISTS (
                        SELECT 1 FROM cad_item_homologacao cih 
                            WHERE cih.id_item = cad_item.id_item 
                                and tipo_homologacao = 'Fiscal'
                    )
                ) ";

                $query_fiscal_engenharia_homologado = " AND (
                    cad_item.id_resp_fiscal IS NOT NULL
                ) AND EXISTS ( 
                    SELECT 1 FROM cad_item_homologacao cih 
                        WHERE cih.id_item = cad_item.id_item 
                            AND cih.tipo_homologacao = 'Fiscal' 
                            AND cih.homologado = 1
                ) ";

                $query_fiscal_engenharia_nao_homologado = " AND (
                    cad_item.id_resp_fiscal IS NOT NULL
                ) AND EXISTS (
                    SELECT 1 FROM cad_item_homologacao cih 
                        WHERE cih.id_item = cad_item.id_item 
                        AND cih.tipo_homologacao = 'Fiscal' 
                ) ";

                $query_fiscal_engenharia_obsoleto = " AND ( 
                    cad_item.id_resp_fiscal IS NOT NULL
                ) AND EXISTS (
                    SELECT 1 FROM cad_item_homologacao cih 
                        WHERE cih.id_item = cad_item.id_item 
                        AND homologado = 2
                        AND cih.tipo_homologacao = 'Fiscal' 
                ) ";

                $query_fiscal_engenharia_revisao = "OR cad_item.id_resp_fiscal IS NULL";
            } else if (in_array('homologacao_engenharia', $funcoes_adicionais)) {

                $query_fiscal_engenharia_homologar = " AND (
                    cad_item.id_resp_fiscal IS NOT NULL
                ) AND (
                    NOT EXISTS (
                        SELECT 1 FROM cad_item_homologacao cih 
                            WHERE cih.id_item = cad_item.id_item 
                                and tipo_homologacao = 'Engenharia'
                    ) 
                ) ";

                $query_fiscal_engenharia_homologado = " AND (
                    cad_item.id_resp_engenharia IS NOT NULL
                ) AND EXISTS ( 
                    SELECT 1 FROM cad_item_homologacao cih 
                        WHERE cih.id_item = cad_item.id_item 
                            AND cih.tipo_homologacao = 'Engenharia' 
                            AND cih.homologado = 1
                ) ";

                $query_fiscal_engenharia_nao_homologado = " AND (
                    cad_item.id_resp_engenharia IS NOT NULL
                ) AND EXISTS (
                    SELECT 1 FROM cad_item_homologacao cih 
                        WHERE cih.id_item = cad_item.id_item 
                        AND cih.tipo_homologacao = 'Engenharia' 
                ) ";

                $query_fiscal_engenharia_obsoleto = " AND ( 
                    cad_item.id_resp_fiscal IS NOT NULL
                ) AND EXISTS (
                    SELECT 1 FROM cad_item_homologacao cih 
                        WHERE cih.id_item = cad_item.id_item 
                        AND homologado = 2
                        AND cih.tipo_homologacao = 'Engenharia' 
                ) ";

                $query_fiscal_engenharia_revisao = "OR cad_item.id_resp_engenharia IS NULL";
            } else {
                continue;
            }

            $full = count($empresas);
            $process = (100 / $full) * $i;
            echo '('.$full.')'.' Processando '.$process.' %'.PHP_EOL;

            $this->get_itens_homologar($empresa->id_empresa, $query_fiscal_engenharia_homologar);
            $this->get_itens_homologado($empresa->id_empresa, $query_fiscal_engenharia_homologado);            
            $this->get_itens_nao_homologado($empresa->id_empresa, $query_fiscal_engenharia_nao_homologado);
            $this->get_itens_obsoleto($empresa->id_empresa, $query_fiscal_engenharia_obsoleto);
            $this->get_itens_em_revisao($empresa->id_empresa, $query_fiscal_engenharia_revisao);
            $this->get_itens_em_analise($empresa->id_empresa);
            $this->get_itens_pendente_duvidas($empresa->id_empresa);
            $this->get_itens_respondidos($empresa->id_empresa);
        }
        echo ' fim '.PHP_EOL;

    }

    public function get_itens_homologar($id_empresa, $query_fiscal_engenharia)
    {
        $this->db->query(
            "UPDATE `cad_item`
            INNER JOIN `item` i
                ON cad_item.`part_number` = `i`.`part_number` 
                AND cad_item.id_empresa = i.id_empresa 
                AND cad_item.estabelecimento = i.estabelecimento 
            SET i.id_status = 1
            WHERE cad_item.`status_exportacao` IN ('0', '1') 
                AND cad_item.`id_empresa` = {$id_empresa} 
                {$query_fiscal_engenharia}
                AND cad_item.`descricao_mercado_local` IS NOT NULL 
                AND cad_item.`status_implementacao` IN ('I', 'N', 'R')"
        );
    }

    public function get_itens_homologado($id_empresa, $query_fiscal_engenharia)
    {
        $this->db->query(
            "UPDATE `cad_item`
                INNER JOIN `item` i  
                    ON `cad_item`.`part_number` = `i`.`part_number` 
                    AND cad_item.id_empresa = i.id_empresa 
                    AND cad_item.estabelecimento = i.estabelecimento 
                SET i.id_status = 2
                WHERE `cad_item`.`status_exportacao` IN ('0', '1') 
                    AND cad_item.`id_empresa` = {$id_empresa} 
                    {$query_fiscal_engenharia}
                    AND NOT EXISTS (
                        SELECT 1 FROM cad_item_homologacao cih 
                            WHERE cih.id_item = cad_item.id_item 
                            AND (cih.homologado = 0 OR cih.homologado = 2)
                    )
                    AND `cad_item`.`descricao_mercado_local` IS NOT NULL 
                    AND `cad_item`.`status_implementacao` IN ('I', 'N', 'R')" 
        );
    }

    public function get_itens_nao_homologado($id_empresa, $query_fiscal_engenharia)
    {
        $this->db->query(
            "UPDATE `cad_item`
            INNER JOIN `item` i
                ON `cad_item`.`part_number` = `i`.`part_number` 
                AND cad_item.id_empresa = i.id_empresa 
                AND cad_item.estabelecimento = i.estabelecimento 
            SET i.id_status = 3
            WHERE `cad_item`.`status_exportacao` IN ('0', '1') 
                AND cad_item.`id_empresa` = {$id_empresa} 
                {$query_fiscal_engenharia}
                AND EXISTS (
                    SELECT 1 FROM cad_item_homologacao cih 
                        WHERE cih.id_item = cad_item.id_item 
                        AND (cih.homologado = 0)
                )
                AND `cad_item`.`descricao_mercado_local` IS NOT NULL 
                    AND `cad_item`.`status_implementacao` IN ('I', 'N', 'R')"
        );
    }

    public function get_itens_obsoleto($id_empresa, $query_fiscal_engenharia)
    {    
        $query = $this->db->query(
            "UPDATE `cad_item`
            INNER JOIN `item` i
                ON `cad_item`.`part_number` = `i`.`part_number` 
                AND cad_item.id_empresa = i.id_empresa 
                AND cad_item.estabelecimento = i.estabelecimento 
            SET i.id_status = 4
            WHERE `cad_item`.`status_exportacao` IN ('0', '1') 
                AND cad_item.`id_empresa` = {$id_empresa}
                {$query_fiscal_engenharia}
                AND `cad_item`.`descricao_mercado_local` IS NOT NULL 
                AND `cad_item`.`status_implementacao` IN ('I', 'N', 'R')"
        );
    }

    public function get_itens_em_revisao($id_empresa, $query_fiscal_engenharia)
    {
        $query = $this->db->query(
            "UPDATE `cad_item`
            INNER JOIN `item` i
                ON `cad_item`.`part_number` = `i`.`part_number` 
                AND cad_item.id_empresa = i.id_empresa 
                AND cad_item.estabelecimento = i.estabelecimento 
            SET i.id_status = 5
            WHERE `cad_item`.`status_exportacao` IN ('0', '1') 
                AND cad_item.`id_empresa` = {$id_empresa} 
                AND (
                    cad_item.descricao_mercado_local IS NULL 
                    {$query_fiscal_engenharia} 
                ) 
                AND `cad_item`.`status_implementacao` IN ('I', 'N', 'R')"
        );
    }

    public function get_itens_em_analise($id_empresa)
    {
        $this->db->query(
            "UPDATE item i 
            SET i.id_status = 6
                WHERE NOT EXISTS(
                    SELECT 1 FROM ctr_pendencias_pergunta pp 
                        WHERE i.part_number = pp.part_number 
                            AND pp.id_empresa = i.id_empresa 
                            AND pp.estabelecimento = i.estabelecimento 
                            LIMIT 1
                ) AND NOT EXISTS(
                    SELECT 1 FROM cad_item c 
                        WHERE i.part_number = c.part_number 
                            AND c.id_empresa = i.id_empresa 
                            AND c.estabelecimento = i.estabelecimento 
                            LIMIT 1
                ) AND i.id_empresa = {$id_empresa}"
        );
    }

    public function get_itens_pendente_duvidas($id_empresa)
    {   
        $this->db->query(
            "UPDATE item i 	
            SET i.id_status = 7
                WHERE NOT EXISTS (
                    SELECT 1 FROM cad_item ci 
                        WHERE i.part_number = ci.part_number 
                            AND ci.id_empresa = i.id_empresa 
                            AND ci.estabelecimento = i.estabelecimento
                            LIMIT 1
                ) AND EXISTS (
                    SELECT * FROM ctr_pendencias_pergunta pp 
                        WHERE i.part_number = pp.part_number 
                            AND pp.id_empresa = i.id_empresa 
                            AND pp.estabelecimento = i.estabelecimento 
                            AND pp.pendente = 1
                ) AND i.id_empresa = {$id_empresa}"
        );
    }

    public function get_itens_respondidos($id_empresa)
    {
        $this->db->query(
            "UPDATE item i 	
            SET i.id_status = 8
                WHERE EXISTS (
                    SELECT 1 FROM ctr_pendencias_pergunta pp 
                        WHERE i.part_number = pp.part_number 
                            AND pp.id_empresa = i.id_empresa 
                            AND pp.estabelecimento = i.estabelecimento
                            AND pp.pendente = 0
                            LIMIT 1
                ) AND NOT EXISTS (
                    SELECT 1 FROM ctr_pendencias_pergunta pp 
                        WHERE i.part_number = pp.part_number 
                            AND pp.id_empresa = i.id_empresa 
                            AND pp.estabelecimento = i.estabelecimento 
                            AND pp.pendente = 1
                ) AND NOT EXISTS(
                    SELECT 1 FROM cad_item c 
                        WHERE i.part_number = c.part_number 
                            AND c.id_empresa = i.id_empresa 
                            AND c.estabelecimento = i.estabelecimento 
                            LIMIT 1
                ) AND i.id_empresa = {$id_empresa}"
        );
    }
}
