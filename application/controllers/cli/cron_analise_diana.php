<?php
defined('BASEPATH') || exit('No direct script access allowed');

/**
 * Cron para análise de triagem DIANA
 *
 * Este script deve ser executado via crontab do sistema
 * Exemplo de configuração no crontab: Inserir a barra no primeiro campo da cron
 */
// */30 * * * * php /var/www/gestaotarifaria/production/current/index.php cli/cron_analise_diana >> /var/log/diana_cron.log 2>&1

class Cron_analise_diana extends CI_Controller
{
    private $id_execucao = null;
    private $limite_itens_por_empresa = 3000;

    // Propriedades para o sistema de log estruturado
    private $log_file_path = null;
    private $log_start_time = null;
    private $log_level = 'INFO'; // DEBUG, INFO, WARNING, ERROR
    private $log_enabled = true;

    public function __construct()
    {
        parent::__construct();

        // Verificar se está sendo executado via CLI e permitir apenas no ambiente de desenvolvimento
        if (!$this->input->is_cli_request() && ENVIRONMENT !== 'development') {
            show_error('Recurso não permitido', 403);
        }

        // Configurar output buffering para exibição em tempo real via navegador
        if (!$this->input->is_cli_request()) {
            $this->configurar_output_tempo_real();

            if (session_status() === PHP_SESSION_ACTIVE) {
                session_write_close();
            }
        }

        // Carregar model de controle de execução
        $this->load->model('cron_diana_execucao_model');

        // Inicializar sistema de log estruturado
        $this->init_log_system();
    }

    /**
     * Inicializa o sistema de log estruturado
     */
    private function init_log_system()
    {
        // Definir diretório de logs
        $log_dir = APPPATH . 'logs/cron_diana/';

        // Criar diretório se não existir
        if (!is_dir($log_dir)) {
            if (!mkdir($log_dir, 0777, true)) {
                $this->log_enabled = false;
                log_message('error', 'DIANA Cron: Não foi possível criar diretório de logs: ' . $log_dir);
                return;
            }
        }

        // Verificar se o diretório é gravável
        if (!is_writable($log_dir)) {
            $this->log_enabled = false;
            log_message('error', 'DIANA Cron: Diretório de logs não é gravável: ' . $log_dir);
            return;
        }

        // Gerar nome único para o arquivo de log desta execução
        $timestamp = date('Y-m-d_H-i-s');
        $execution_id = uniqid();
        $this->log_file_path = $log_dir . "diana_execution_{$timestamp}_{$execution_id}.log";

        // Testar se conseguimos criar o arquivo
        try {
            $test_content = "=== TESTE DE ESCRITA ===\n";
            if (file_put_contents($this->log_file_path, $test_content, LOCK_EX) === false) {
                $this->log_enabled = false;
                log_message('error', 'DIANA Cron: Não foi possível escrever no arquivo de log: ' . $this->log_file_path);
                return;
            }
            // Remover conteúdo de teste
            file_put_contents($this->log_file_path, '', LOCK_EX);
        } catch (Exception $e) {
            $this->log_enabled = false;
            log_message('error', 'DIANA Cron: Erro ao testar escrita no arquivo de log: ' . $e->getMessage());
            return;
        }

        // Marcar tempo de início
        $this->log_start_time = microtime(true);

        // Definir nível de log baseado no ambiente
        if (ENVIRONMENT === 'development') {
            $this->log_level = 'DEBUG';
        }

        // Log inicial do sistema
        $this->log('INFO', '=== SISTEMA DE LOG INICIALIZADO ===');
        $this->log('INFO', "Arquivo de log: " . basename($this->log_file_path));
        $this->log('INFO', "Ambiente: " . ENVIRONMENT);
        $this->log('INFO', "Nível de log: " . $this->log_level);
        $this->log('INFO', "Execução via CLI: " . (php_sapi_name() === 'cli' ? 'Sim' : 'Não'));
    }

    /**
     * Sistema de log estruturado com níveis e formatação
     * 
     * @param string $level Nível do log (DEBUG, INFO, WARNING, ERROR)
     * @param string $message Mensagem do log
     * @param array $context Contexto adicional (opcional)
     */
    private function log($level, $message, $context = [])
    {
        if (!$this->log_enabled || !$this->log_file_path) {
            return;
        }

        // Verificar se o nível deve ser logado
        $levels = ['DEBUG' => 1, 'INFO' => 2, 'WARNING' => 3, 'ERROR' => 4];
        $current_level = $levels[$this->log_level] ?? 2;
        $message_level = $levels[$level] ?? 2;

        if ($message_level < $current_level) {
            return;
        }

        try {
            // Verificar se o arquivo ainda é gravável
            if (!is_writable($this->log_file_path) && file_exists($this->log_file_path)) {
                log_message('error', 'DIANA Cron: Arquivo de log não é gravável: ' . $this->log_file_path);
                return;
            }

            // Calcular tempo decorrido desde o início
            $elapsed = $this->log_start_time ? round((microtime(true) - $this->log_start_time) * 1000, 2) : 0;

            // Formatar timestamp
            $timestamp = date('Y-m-d H:i:s');

            // Preparar contexto
            $context_str = '';
            if (!empty($context)) {
                $context_str = ' | Context: ' . json_encode($context, JSON_UNESCAPED_UNICODE);
            }

            // Formatar linha do log
            $log_line = sprintf(
                "[%s] [%s] [%sms] %s%s%s",
                $timestamp,
                str_pad($level, 7),
                str_pad($elapsed, 8),
                $message,
                $context_str,
                PHP_EOL
            );

            // Escrever no arquivo com retry em caso de falha
            $max_retries = 3;
            $retry_count = 0;

            while ($retry_count < $max_retries) {
                $result = file_put_contents($this->log_file_path, $log_line, FILE_APPEND | LOCK_EX);
                if ($result !== false) {
                    break; // Sucesso
                }

                $retry_count++;
                if ($retry_count < $max_retries) {
                    usleep(100000); // 0.1 segundo de pausa antes de tentar novamente
                }
            }

            if ($result === false) {
                log_message('error', 'DIANA Cron: Falha ao escrever no arquivo de log após ' . $max_retries . ' tentativas: ' . $this->log_file_path);
            }
        } catch (Exception $e) {
            // Se falhar ao escrever no arquivo, usar o log padrão do CodeIgniter
            log_message('error', "Falha ao escrever log estruturado DIANA: " . $e->getMessage());
        }
    }

    /**
     * Limpa logs antigos (mais de 30 dias)
     */
    private function cleanup_old_logs()
    {
        $log_dir = APPPATH . 'logs/cron_diana/';
        $cutoff_time = time() - (30 * 24 * 60 * 60); // 30 dias atrás

        if (!is_dir($log_dir)) {
            return;
        }

        $files = glob($log_dir . 'diana_execution_*.log');
        $deleted_count = 0;

        foreach ($files as $file) {
            if (filemtime($file) < $cutoff_time) {
                if (unlink($file)) {
                    $deleted_count++;
                }
            }
        }

        if ($deleted_count > 0) {
            $this->log('INFO', "Limpeza automática: {$deleted_count} arquivos de log antigos removidos");
        }
    }

    /**
     * Método principal da cron que executa a análise DIANA
     *
     * Este método realiza:
     * - Verificação da configuração da API DIANA
     * - Busca empresas com funcionalidade de triagem DIANA ativa
     * - Para cada empresa:
     *   1. Processa itens pendentes (status 6)
     *   2. Processa itens reprovados com respostas (status 8)
     * - Gera logs e estatísticas de execução
     *
     * @return void
     */
    public function index()
    {
        // Verificar se já existe uma execução em andamento
        $execucao_em_andamento = $this->cron_diana_execucao_model->verificar_execucao_em_andamento();
        if ($execucao_em_andamento) {
            $this->log('WARNING', 'Execução em andamento detectada', [
                'id_execucao' => $execucao_em_andamento->id_execucao,
                'data_inicio' => $execucao_em_andamento->data_inicio
            ]);

            $this->debug('AVISO: Já existe uma execução em andamento desde ' . $execucao_em_andamento->data_inicio);
            $this->debug('ID da execução: ' . $execucao_em_andamento->id_execucao);
            $this->debug('Abortando nova execução para evitar conflitos.');

            // Finalizar HTML se executado via navegador
            if (!$this->input->is_cli_request()) {
                $this->finalizar_output_tempo_real();
            }
            return;
        }

        // Iniciar nova execução
        $this->id_execucao = $this->cron_diana_execucao_model->iniciar_execucao();

        $this->load->library('benchmark');
        $this->benchmark->mark('diana_start');

        $this->log('INFO', '=== INICIANDO ANÁLISE DIANA ===', [
            'id_execucao' => $this->id_execucao,
            'limite_itens_por_empresa' => $this->limite_itens_por_empresa
        ]);

        $this->debug('=== INICIANDO ANÁLISE DIANA ===');
        $this->debug('Data/Hora: ' . date('Y-m-d H:i:s'));
        $this->debug('ID da Execução: ' . $this->id_execucao);
        $this->debug('Limite de itens por empresa: ' . $this->limite_itens_por_empresa);

        $this->load->model('item_model');
        $this->load->model('empresa_model');
        $this->load->library('Diana_api');

        try {
            // Verificar se a API está configurada
            if (!$this->diana_api->is_configurada()) {
                $this->log('ERROR', 'API DIANA não configurada', [
                    'config_url' => $this->config->item('diana_api_url')
                ]);

                $this->debug('ERRO: API DIANA não está configurada corretamente');
                $this->debug('Verifique a configuração diana_api_url no config.php');

                // Finalizar execução com erro
                $this->cron_diana_execucao_model->finalizar_execucao($this->id_execucao, [
                    'status_execucao' => 'erro',
                    'observacoes' => 'API DIANA não configurada corretamente'
                ]);

                // Finalizar HTML se executado via navegador
                if (!$this->input->is_cli_request()) {
                    $this->finalizar_output_tempo_real();
                }
                return;
            }


            $this->log('INFO', 'API DIANA configurada', [
                'url' => $this->config->item('diana_api_url')
            ]);

            $this->debug('API DIANA configurada: ' . $this->config->item('diana_api_url'));

            $empresas = $this->empresa_model->get_empresas_with_status_triagem_diana();

            if (empty($empresas)) {
                $this->log('WARNING', 'Nenhuma empresa encontrada com funcionalidade ativa');

                $this->debug('Nenhuma empresa encontrada com funcionalidade status_triagem_diana ativa');

                // Finalizar HTML se executado via navegador
                if (!$this->input->is_cli_request()) {
                    $this->finalizar_output_tempo_real();
                }
                return;
            }

            $this->log('INFO', 'Empresas encontradas para processamento', [
                'total_empresas' => count($empresas)
            ]);

            $this->debug('Encontradas ' . count($empresas) . ' empresas com funcionalidade ativa');

            $processados_total = 0;
            $sucessos_total = 0;
            $erros_total = 0;
            $empresas_processadas = [];

            // Para cada empresa, buscar itens para análise passando o id_empresa
            foreach ($empresas as $empresa) {
                // Aplicar limite de itens por empresa para melhor performance
                $itens = $this->item_model->get_itens_para_triagem_diana_pendente($empresa->id_empresa, $this->limite_itens_por_empresa);

                // Para itens reprovados, usar o restante do limite se houver
                $limite_restante = max(0, $this->limite_itens_por_empresa - count($itens));
                $itens_reprovados = $this->item_model->get_itens_para_triagem_diana_reprovada($empresa->id_empresa, $limite_restante);

                if (empty($itens) && empty($itens_reprovados)) {
                    $this->log('INFO', 'Nenhum item encontrado para empresa', [
                        'empresa_id' => $empresa->id_empresa,
                        'empresa_nome' => $empresa->nome_fantasia
                    ]);

                    $this->debug("Nenhum item encontrado para análise DIANA na empresa {$empresa->nome_fantasia} (ID: {$empresa->id_empresa})");
                    continue;
                }

                $total_itens = count($itens) + count($itens_reprovados);

                $this->log('INFO', 'Itens encontrados para empresa', [
                    'empresa_id' => $empresa->id_empresa,
                    'empresa_nome' => $empresa->nome_fantasia,
                    'total_itens' => $total_itens,
                    'itens_pendentes' => count($itens),
                    'itens_com_respostas' => count($itens_reprovados),
                    'limite_atingido' => $total_itens >= $this->limite_itens_por_empresa
                ]);

                $this->debug("Encontrados {$total_itens} itens para análise na empresa {$empresa->nome_fantasia} (ID: {$empresa->id_empresa})");
                $this->debug("  - Pendentes (passo 1): " . count($itens));
                $this->debug("  - Com respostas (passo 2): " . count($itens_reprovados));

                if ($total_itens >= $this->limite_itens_por_empresa) {
                    $this->debug("  ⚠ Limite de {$this->limite_itens_por_empresa} itens atingido para esta empresa");
                }

                $empresas_processadas[] = $empresa->id_empresa;

                // PASSO 1: Processar itens pendentes (status 6, triagem pendente)
                foreach ($itens as $item) {
                    $this->log('DEBUG', 'Processando item pendente', [
                        'part_number' => $item->part_number,
                        'empresa_id' => $item->id_empresa,
                        'estabelecimento' => $item->estabelecimento
                    ]);

                    $this->debug("Processando: {$item->part_number} (Empresa: {$item->id_empresa}, Estabelecimento: {$item->estabelecimento})");

                    try {
                        // Chamar API DIANA
                        $resposta_api = $this->diana_api->analisar_triagem(
                            $item->funcao,
                            $item->aplicacao,
                            $item->material_constitutivo,
                            $item->descricao
                        );

                        if ($resposta_api === false) {
                            $this->log('ERROR', 'Falha na chamada da API DIANA', [
                                'part_number' => $item->part_number,
                                'empresa_id' => $item->id_empresa
                            ]);

                            $this->debug("  ✗ Falha na chamada da API");
                            $erros_total++;
                            continue;
                        }

                        // Processar resposta
                        $resultado = $this->diana_api->processar_resposta($resposta_api);

                        // Atualizar item com resultado
                        $sucesso = $this->item_model->processar_resultado_diana($item, $resultado);

                        if ($sucesso) {
                            $status_triagem = $resultado['triagem_aprovada'] ? 'aprovada' : 'reprovada';

                            $this->log('INFO', 'Item processado com sucesso', [
                                'part_number' => $item->part_number,
                                'status_triagem' => $status_triagem,
                                'perguntas_inseridas' => !$resultado['triagem_aprovada'] ? count($resultado['perguntas_faltantes']) : 0
                            ]);

                            $this->debug("  ✓ Triagem {$status_triagem}");

                            if (!$resultado['triagem_aprovada']) {
                                $this->debug("    Perguntas inseridas: " . count($resultado['perguntas_faltantes']));
                            }

                            $sucessos_total++;
                        } else {
                            $this->log('ERROR', 'Falha ao processar resultado do item', [
                                'part_number' => $item->part_number,
                                'empresa_id' => $item->id_empresa
                            ]);

                            $this->debug("  ✗ Falha ao processar resultado");
                            $erros_total++;
                        }
                    } catch (Exception $e) {
                        $this->log('ERROR', 'Exceção ao processar item', [
                            'part_number' => $item->part_number,
                            'empresa_id' => $item->id_empresa,
                            'error_message' => $e->getMessage(),
                            'error_file' => $e->getFile(),
                            'error_line' => $e->getLine()
                        ]);

                        $this->debug("  ✗ Exceção: " . $e->getMessage());
                        log_message('error', "DIANA Cron: Erro ao processar item {$item->part_number}: " . $e->getMessage());
                        // Detalhes da origem da exceção, se possível
                        if (method_exists($e, 'getTraceAsString')) {
                            $this->debug("    Rastreamento: " . $e->getTraceAsString());
                        }
                        $erros_total++;
                    }

                    $processados_total++;

                    // Pequena pausa para não sobrecarregar a API
                    usleep(100000); // 0.1 segundo
                }

                // PASSO 2: Processar itens com respostas (status 8, triagem reprovada)
                foreach ($itens_reprovados as $item) {
                    $this->debug("Processando item com respostas: {$item->part_number} (Empresa: {$item->id_empresa}, Estabelecimento: {$item->estabelecimento})");

                    try {
                        // Chamar API DIANA incluindo perguntas e respostas
                        $resposta_api = $this->diana_api->analisar_triagem_com_respostas(
                            $item->funcao,
                            $item->aplicacao,
                            $item->material_constitutivo,
                            $item->descricao,
                            $item->perguntas_respostas
                        );

                        if ($resposta_api === false) {
                            $this->debug("  ✗ Falha na chamada da API (com respostas)");
                            $erros_total++;
                            continue;
                        }

                        // Processar resposta
                        $resultado = $this->diana_api->processar_resposta($resposta_api);

                        // Atualizar item com resultado (passo 2)
                        $sucesso = $this->item_model->processar_resultado_diana_com_respostas($item, $resultado);

                        if ($sucesso) {
                            $status_triagem = $resultado['triagem_aprovada'] ? 'aprovada' : 'falha na triagem';
                            $this->debug("  ✓ Triagem {$status_triagem} (com respostas)");

                            if (!$resultado['triagem_aprovada']) {
                                $this->debug("    Status final: Falha na triagem (evita loop infinito)");
                            }

                            $sucessos_total++;
                        } else {
                            $this->debug("  ✗ Falha ao processar resultado (com respostas)");
                            $erros_total++;
                        }
                    } catch (Exception $e) {
                        $this->debug("  ✗ Exceção (com respostas): " . $e->getMessage());
                        log_message('error', "DIANA Cron: Erro ao processar item com respostas {$item->part_number}: " . $e->getMessage());
                        if (method_exists($e, 'getTraceAsString')) {
                            $this->debug("    Rastreamento: " . $e->getTraceAsString());
                        }
                        $erros_total++;
                    }

                    $processados_total++;

                    // Pequena pausa para não sobrecarregar a API
                    usleep(100000); // 0.1 segundo
                }
            }

            $this->benchmark->mark('diana_end');
            $elapsed_time = $this->benchmark->elapsed_time('diana_start', 'diana_end');

            $taxa_sucesso = $processados_total > 0 ? round(($sucessos_total / $processados_total) * 100, 2) : 0;

            $this->log('INFO', '=== ANÁLISE DIANA FINALIZADA ===', [
                'empresas_processadas' => count($empresas_processadas),
                'itens_processados' => $processados_total,
                'sucessos' => $sucessos_total,
                'erros' => $erros_total,
                'tempo_total_segundos' => $elapsed_time,
                'taxa_sucesso_percentual' => $taxa_sucesso
            ]);

            $this->debug('=== ANÁLISE DIANA FINALIZADA ===');
            $this->debug("Empresas processadas: " . count($empresas_processadas));
            $this->debug("Itens processados: {$processados_total}");
            $this->debug("Sucessos: {$sucessos_total}");
            $this->debug("Erros: {$erros_total}");
            $this->debug("Tempo total: {$elapsed_time} segundos");
            $this->debug("Taxa de sucesso: " . $taxa_sucesso . "%");

            // Registrar estatísticas da execução no banco
            $log_resumo = "Empresas: " . count($empresas_processadas) .
                " | Itens: {$processados_total}" .
                " | Sucessos: {$sucessos_total}" .
                " | Erros: {$erros_total}" .
                " | Taxa: " . ($processados_total > 0 ? round(($sucessos_total / $processados_total) * 100, 2) : 0) . "%";

            $this->cron_diana_execucao_model->finalizar_execucao($this->id_execucao, [
                'total_empresas' => count($empresas_processadas),
                'total_itens_processados' => $processados_total,
                'total_sucessos' => $sucessos_total,
                'total_erros' => $erros_total,
                'tempo_execucao_segundos' => $elapsed_time,
                'log_resumo' => $log_resumo,
                'status_execucao' => 'finalizada'
            ]);

            // Log de resumo
            log_message('info', "DIANA Cron executada: {$processados_total} itens processados, {$sucessos_total} sucessos, {$erros_total} erros em {$elapsed_time}s");

            // Finalizar HTML se executado via navegador
            if (!$this->input->is_cli_request()) {
                $this->finalizar_output_tempo_real();
            }
        } catch (Exception $e) {
            $this->log('ERROR', 'ERRO CRÍTICO na execução', [
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'id_execucao' => $this->id_execucao
            ]);

            $this->debug('ERRO CRÍTICO: ' . $e->getMessage());
            log_message('error', 'DIANA Cron: Erro crítico - ' . $e->getMessage());

            // Finalizar execução com erro
            if ($this->id_execucao) {
                $this->cron_diana_execucao_model->finalizar_execucao($this->id_execucao, [
                    'status_execucao' => 'erro',
                    'observacoes' => 'Erro crítico: ' . $e->getMessage()
                ]);
            }

            // Finalizar HTML se executado via navegador
            if (!$this->input->is_cli_request()) {
                $this->finalizar_output_tempo_real();
            }
        }
    }

    /**
     * Configura o output para exibição em tempo real via navegador
     */
    private function configurar_output_tempo_real()
    {
        // Desabilitar output buffering do PHP
        if (ob_get_level()) {
            ob_end_clean();
        }

        // Configurar headers para streaming
        header('Content-Type: text/html; charset=utf-8');
        header('Cache-Control: no-cache');
        header('X-Accel-Buffering: no'); // Para nginx

        // Desabilitar compressão
        if (function_exists('apache_setenv')) {
            apache_setenv('no-gzip', '1');
        }

        // Configurar output buffering para flush automático
        ob_implicit_flush(true);

        // Enviar cabeçalho HTML básico
        echo '<!DOCTYPE html><html><head><meta charset="utf-8"><title>DIANA Cron - Execução em Tempo Real</title>';
        echo '<style>body{font-family:monospace;background:#000;color:#0f0;padding:20px;} .timestamp{color:#888;} .error{color:#f00;} .success{color:#0f0;} .info{color:#ff0;}</style>';
        echo '</head><body><h2>DIANA Cron - Execução em Tempo Real</h2><div id="output">';

        // Forçar envio do cabeçalho
        $this->flush_output();
    }

    /**
     * Força o envio do output para o navegador
     */
    private function flush_output()
    {
        if (ob_get_level()) {
            ob_flush();
        }
        flush();

        // Pequena pausa para garantir que o output seja enviado
        usleep(1000); // 1ms
    }

    /**
     * Finaliza o HTML quando executado via navegador
     */
    private function finalizar_output_tempo_real()
    {
        echo '</div>';
        echo '<script>';
        echo 'window.scrollTo(0, document.body.scrollHeight);'; // Auto-scroll para o final
        echo 'document.title = "DIANA Cron - Finalizado";';
        echo '</script>';
        echo '</body></html>';
        $this->flush_output();
    }

    /**
     * Método para debug/log
     */
    private function debug($message)
    {
        // Determinar nível do log baseado no conteúdo da mensagem
        $level = 'INFO';
        if (strpos($message, '✗') !== false || strpos($message, 'ERRO') !== false || strpos($message, 'Falha') !== false) {
            $level = 'ERROR';
        } elseif (strpos($message, '⚠') !== false || strpos($message, 'AVISO') !== false) {
            $level = 'WARNING';
        } elseif (strpos($message, '✓') !== false || strpos($message, 'sucesso') !== false) {
            $level = 'INFO';
        } elseif (strpos($message, '===') !== false) {
            $level = 'INFO';
        }

        // Log estruturado
        $this->log($level, $message);

        // Se executado via navegador exibir na tela
        if (!$this->input->is_cli_request()) {
            $timestamp = date('Y-m-d H:i:s');
            $class = '';

            // Determinar classe CSS baseada no conteúdo da mensagem
            if (strpos($message, '✗') !== false || strpos($message, 'ERRO') !== false || strpos($message, 'Falha') !== false) {
                $class = 'error';
            } elseif (strpos($message, '✓') !== false || strpos($message, 'sucesso') !== false) {
                $class = 'success';
            } elseif (strpos($message, '===') !== false) {
                $class = 'info';
            }

            echo '<div class="' . $class . '">';
            echo '<span class="timestamp">[' . $timestamp . ']</span> ';
            echo htmlspecialchars($message);
            echo '</div>' . PHP_EOL;

            // Forçar envio imediato
            $this->flush_output();
            return;
        }

        echo "[" . date('Y-m-d H:i:s') . "] " . $message . PHP_EOL;
    }

    /**
     * Método para verificar status das execuções
     */
    public function status()
    {
        $this->debug('=== STATUS DAS EXECUÇÕES DIANA ===');

        // Verificar execução em andamento
        $execucao_em_andamento = $this->cron_diana_execucao_model->verificar_execucao_em_andamento();
        if ($execucao_em_andamento) {
            $this->debug('✓ Execução em andamento:');
            $this->debug("  ID: {$execucao_em_andamento->id_execucao}");
            $this->debug("  Iniciada em: {$execucao_em_andamento->data_inicio}");
            if ($execucao_em_andamento->pid_processo) {
                $this->debug("  PID: {$execucao_em_andamento->pid_processo}");
            }
        } else {
            $this->debug('✓ Nenhuma execução em andamento');
        }

        // Buscar execuções recentes
        $execucoes_recentes = $this->cron_diana_execucao_model->get_execucoes_recentes(5);
        $this->debug('');
        $this->debug('Últimas 5 execuções:');

        foreach ($execucoes_recentes as $execucao) {
            $status_icon = $execucao->status_execucao == 'finalizada' ? '✓' : ($execucao->status_execucao == 'erro' ? '✗' : '⚠');

            $this->debug("  {$status_icon} ID {$execucao->id_execucao} - {$execucao->data_inicio} - {$execucao->status_execucao}");
            if ($execucao->log_resumo) {
                $this->debug("    {$execucao->log_resumo}");
            }
        }

        // Estatísticas dos últimos 7 dias
        $stats = $this->cron_diana_execucao_model->get_estatisticas_execucoes(7);
        $this->debug('');
        $this->debug('Estatísticas dos últimos 7 dias:');
        $this->debug("  Total de execuções: {$stats->total_execucoes}");
        $this->debug("  Finalizadas: {$stats->finalizadas}");
        $this->debug("  Com erro: {$stats->com_erro}");
        $this->debug("  Interrompidas: {$stats->interrompidas}");
        if ($stats->tempo_medio_segundos) {
            $this->debug("  Tempo médio: " . round($stats->tempo_medio_segundos, 2) . " segundos");
        }
        $this->debug("  Total de itens processados: {$stats->total_itens}");
        $this->debug("  Total de sucessos: {$stats->total_sucessos}");
        $this->debug("  Total de erros: {$stats->total_erros}");

        // Finalizar HTML se executado via navegador
        if (!$this->input->is_cli_request()) {
            $this->finalizar_output_tempo_real();
        }
    }

    /**
     * Método para limpar execuções antigas
     */
    public function cleanup()
    {
        $this->debug('=== LIMPEZA DE EXECUÇÕES ANTIGAS ===');

        $removidos = $this->cron_diana_execucao_model->limpar_execucoes_antigas();
        $this->debug("Execuções antigas removidas: {$removidos}");

        // Limpar logs antigos também
        $this->cleanup_old_logs();

        // Finalizar HTML se executado via navegador
        if (!$this->input->is_cli_request()) {
            $this->finalizar_output_tempo_real();
        }
    }

    /**
     * Método para visualizar logs de execuções
     */
    public function logs($execution_id = null, $lines = 50)
    {
        $this->debug('=== VISUALIZAÇÃO DE LOGS ===');

        $log_dir = APPPATH . 'logs/cron_diana/';

        if (!is_dir($log_dir)) {
            $this->debug('✗ Diretório de logs não encontrado');
            return;
        }

        if ($execution_id) {
            // Buscar log específico
            $files = glob($log_dir . "*{$execution_id}*.log");
            if (empty($files)) {
                $this->debug("✗ Log não encontrado para execução: {$execution_id}");
                return;
            }
            $log_file = $files[0];
        } else {
            // Mostrar logs mais recentes
            $files = glob($log_dir . 'diana_execution_*.log');
            if (empty($files)) {
                $this->debug('✗ Nenhum arquivo de log encontrado');
                return;
            }

            // Ordenar por data de modificação (mais recente primeiro)
            usort($files, function ($a, $b) {
                return filemtime($b) - filemtime($a);
            });

            $log_file = $files[0];
        }

        $this->debug("Visualizando: " . basename($log_file));
        $this->debug("Tamanho: " . $this->format_bytes(filesize($log_file)));
        $this->debug("Última modificação: " . date('Y-m-d H:i:s', filemtime($log_file)));

        // Ler últimas linhas do arquivo
        $content = file($log_file);
        $total_lines = count($content);
        $start_line = max(0, $total_lines - $lines);

        $this->debug("Mostrando últimas {$lines} linhas de {$total_lines}:");

        for ($i = $start_line; $i < $total_lines; $i++) {
            $line = trim($content[$i]);
            if (!empty($line)) {
                $this->debug($line);
            }
        }

        // Finalizar HTML se executado via navegador
        if (!$this->input->is_cli_request()) {
            $this->finalizar_output_tempo_real();
        }
    }

    /**
     * Método para listar execuções com logs disponíveis
     */
    public function list_logs()
    {
        $this->debug('=== LISTA DE EXECUÇÕES COM LOGS ===');

        $log_dir = APPPATH . 'logs/cron_diana/';

        if (!is_dir($log_dir)) {
            $this->debug('✗ Diretório de logs não encontrado');
            return;
        }

        $files = glob($log_dir . 'diana_execution_*.log');

        if (empty($files)) {
            $this->debug('✗ Nenhum arquivo de log encontrado');
            return;
        }

        // Ordenar por data de modificação (mais recente primeiro)
        usort($files, function ($a, $b) {
            return filemtime($b) - filemtime($a);
        });

        $this->debug("Encontrados " . count($files) . " arquivos de log:");
        $this->debug("");

        foreach ($files as $file) {
            $filename = basename($file);
            $size = $this->format_bytes(filesize($file));
            $modified = date('Y-m-d H:i:s', filemtime($file));

            // Extrair informações do nome do arquivo
            if (preg_match('/diana_execution_(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})_([a-f0-9]+)\.log/', $filename, $matches)) {
                $timestamp = str_replace('_', ' ', $matches[1]);
                $execution_id = $matches[2];

                $this->debug("📄 {$timestamp} | ID: {$execution_id} | {$size} | {$modified}");
            } else {
                $this->debug("📄 {$filename} | {$size} | {$modified}");
            }
        }

        // Finalizar HTML se executado via navegador
        if (!$this->input->is_cli_request()) {
            $this->finalizar_output_tempo_real();
        }
    }

    /**
     * Formata bytes em formato legível
     */
    private function format_bytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Método para testar a configuração
     */
    public function test_config()
    {
        $this->debug('=== TESTE DE CONFIGURAÇÃO DIANA ===');

        $this->load->library('Diana_api');

        if ($this->diana_api->is_configurada()) {
            $this->debug('✓ API DIANA está configurada');
            $this->debug('URL: ' . $this->config->item('diana_api_url'));
        } else {
            $this->debug('✗ API DIANA não está configurada');
            $this->debug('URL atual: ' . ($this->config->item('diana_api_url') ?: 'Não definida'));
        }

        $this->load->model('item_model');
        $itens = $this->item_model->get_itens_para_triagem_diana();
        $this->debug('Itens encontrados para análise: ' . count($itens));

        if (count($itens) > 0) {
            $this->debug('Exemplo de item:');
            $item = $itens[0];
            $this->debug("  Part Number: {$item->part_number}");
            $this->debug("  Empresa: {$item->id_empresa}");
            $this->debug("  Função: " . substr($item->funcao, 0, 50) . "...");
            $this->debug("  Aplicação: " . substr($item->aplicacao, 0, 50) . "...");
            $this->debug("  Material: " . substr($item->material_constitutivo, 0, 50) . "...");
        }

        // Finalizar HTML se executado via navegador
        if (!$this->input->is_cli_request()) {
            $this->finalizar_output_tempo_real();
        }
    }

    /**
     * Método para processar apenas um item específico (para testes)
     */
    public function test_item($part_number = null, $id_empresa = null, $estabelecimento = '')
    {
        if (!$part_number || !$id_empresa) {
            $this->debug('Uso: php index.php cli/cron_analise_diana test_item [part_number] [id_empresa] [estabelecimento]');
            return;
        }

        $this->debug('=== TESTE DE ITEM ESPECÍFICO ===');
        $this->debug("Part Number: {$part_number}");
        $this->debug("Empresa: {$id_empresa}");
        $this->debug("Estabelecimento: {$estabelecimento}");

        $this->load->model('item_model');
        $this->load->library('Diana_api');

        // Buscar o item específico
        $this->db->where('part_number', $part_number);
        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('estabelecimento', $estabelecimento);
        $item = $this->db->get('item')->row();

        if (!$item) {
            $this->debug('✗ Item não encontrado');
            return;
        }

        $this->debug('✓ Item encontrado');
        $this->debug("Status atual: {$item->id_status}");
        $this->debug("Status triagem DIANA: {$item->status_triagem_diana}");

        if (!$this->diana_api->is_configurada()) {
            $this->debug('✗ API DIANA não está configurada');
            return;
        }

        try {
            $resposta_api = $this->diana_api->analisar_triagem(
                $item->funcao,
                $item->aplicacao,
                $item->material_constitutivo,
                $item->descricao
            );

            if ($resposta_api === false) {
                $this->debug('✗ Falha na chamada da API');
                return;
            }

            $this->debug('✓ API respondeu com sucesso');
            $this->debug('Resposta: ' . json_encode($resposta_api, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

            $resultado = $this->diana_api->processar_resposta($resposta_api);
            $this->debug('Resultado processado: ' . json_encode($resultado, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        } catch (Exception $e) {
            $this->debug('✗ Erro: ' . $e->getMessage());
        }

        // Finalizar HTML se executado via navegador
        if (!$this->input->is_cli_request()) {
            $this->finalizar_output_tempo_real();
        }
    }

    /**
     * Método para processar apenas uma empresa específica (para testes)
     */
    public function test_empresa($id_empresa = null)
    {
        if (!$id_empresa) {
            $this->debug('Uso: php index.php cli/cron_analise_diana test_empresa [id_empresa]');
            return;
        }

        $this->debug('=== TESTE DE EMPRESA ESPECÍFICA ===');
        $this->debug("Empresa: {$id_empresa}");

        $this->load->model('item_model');
        $this->load->library('Diana_api');

        // Buscar os itens da empresa
        $itens = $this->item_model->get_itens_para_triagem_diana_pendente($id_empresa);

        if (empty($itens)) {
            $this->debug('✗ Nenhum item encontrado para análise');
            return;
        }

        $this->debug('✓ Itens encontrados: ' . count($itens));

        // Processar cada item
        foreach ($itens as $item) {
            $this->debug("Processando: {$item->part_number} (Empresa: {$item->id_empresa}, Estabelecimento: {$item->estabelecimento})");

            try {
                // Chamar API DIANA
                $resposta_api = $this->diana_api->analisar_triagem(
                    $item->funcao,
                    $item->aplicacao,
                    $item->material_constitutivo,
                    $item->descricao
                );

                if ($resposta_api === false) {
                    $this->debug('✗ Falha na chamada da API');
                    continue;
                }

                $this->debug('✓ API respondeu com sucesso');
                $this->debug('Resposta: ' . json_encode($resposta_api, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

                $resultado = $this->diana_api->processar_resposta($resposta_api);
                $this->debug('Resultado processado: ' . json_encode($resultado, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

                // Atualizar item com resultado
                $sucesso = $this->item_model->processar_resultado_diana($item, $resultado);

                if ($sucesso) {
                    $this->debug('✓ Item atualizado com sucesso');
                } else {
                    $this->debug('✗ Falha ao atualizar item');
                }
            } catch (Exception $e) {
                $this->debug('✗ Erro: ' . $e->getMessage());
            }
        }

        // Finalizar HTML se executado via navegador
        if (!$this->input->is_cli_request()) {
            $this->finalizar_output_tempo_real();
        }
    }
}
