<?php
class Json_database extends CI_Controller 
{
    public function __construct()  
    { 
        parent::__construct(); 
    } 

    private function clean($text)  
    {
        return str_replace([
            '.',
            '-',
            '/'
        ], '', $text);
    }

    private function can_import($attr, $codigo_pai = null) 
    {
        if (!empty($codigo_pai))
            return true;

        $lista_codigos = array_pluck($attr->objetivos, 'codigo');

        if ($attr->modalidade == 'Importação' && in_array(7, $lista_codigos))
            return true;

        return false;
    }

    private function log($message)
    {
        echo '[' . date('Y-m-d H:i:s') . '] - ' . $message . PHP_EOL;
    }

    private function insert_data($ncm, $attr, $codigo_pai = null)
    {
        if ($this->can_import($attr, $codigo_pai))
        {
            $this->db->insert('ncm_atributo', [
                'ncm' => $this->clean($ncm),
                'codigo' => $attr->codigo,
                'codigo_pai' => $codigo_pai,
                'nome_apresentacao' => $attr->nomeApresentacao,
                'forma_preenchimento' => $attr->formaPreenchimento,
                'orientacao_preenchimento' => !empty($attr->orientacaoPreenchimento) ? $attr->orientacaoPreenchimento : NULL,
                'tamanho_maximo' => !empty($attr->tamanhoMaximo) ? $attr->tamanhoMaximo : NULL,
                'modalidade' => !empty($attr->modalidade) ? $attr->modalidade : NULL,
                'definicao' => !empty($attr->definicao) ? $attr->definicao : NULL,
                'obrigatorio' => $attr->obrigatorio,
                'data_inicio_vigencia' => $attr->dataInicioVigencia,
                'data_fim_vigencia' => !empty($attr->dataFimVigencia) ? $attr->dataFimVigencia : NULL,
                'dominio' => !empty($attr->dominio) ? json_encode($attr->dominio) : NULL,
                'objetivos' => !empty($attr->objetivos) ? json_encode($attr->objetivos) : NULL,
                'orgaos' => !empty($attr->orgaos) ? json_encode($attr->orgaos) : NULL,
                'atributo_condicionante' => $attr->atributoCondicionante,
                'condicionados' => !empty($attr->condicionados) ? json_encode($attr->condicionados) : NULL,
                'multivalorado' => !empty($attr->multivalorado) ? $attr->multivalorado : NULL
            ]);

            $this->log('Importando atributo: ' . $attr->codigo);
        }
    }

    public function import_data() 
    {
        ini_set('memory_limit', '2048M');
        
        $data = json_decode(file_get_contents(FCPATH . 'assets/atributos-json/atributos.json'));
        
        $this->db->truncate('ncm_atributo');
        $this->db->truncate('informacoes_ncm');

        foreach ($data->listaNcm as $ncm) 
        {
            $cod_ncm = $ncm->codigoNcm;
       
            foreach ($ncm->listaAtributos as $attr) 
            {
                $db = array(
                    'codigo'             => $attr->codigo,
                    'modalidade'         => $attr->modalidade,
                    'obrigatorio'        => $attr->obrigatorio,
                    'multivalorado'      => $attr->multivalorado,
                    'dataInicioVigencia' => $attr->dataInicioVigencia,
                    'codigoNcm'                => $cod_ncm
                );
        
                $this->db->insert('informacoes_ncm', $db);
            }
        }

        unset($data);
        $data = json_decode(file_get_contents(FCPATH . 'assets/atributos-json/atributos.json'));

        foreach ($data->detalhesAtributos as $detalhes) 
        {
            $this->db->where('codigo', $detalhes->codigo);
            $query = $this->db->get('informacoes_ncm');
            $dbNcms = $query->result();
            foreach ($dbNcms as $ncm)
            {
                $dataAttr = array_merge((array) $ncm,(array) $detalhes);
                $attr = (object) $dataAttr;
    
                $this->insert_data($ncm->codigoNcm, $attr);
                if (!empty($attr->listaSubatributos))
                {
                    foreach ($attr->listaSubatributos as $subattr) 
                    {
                        $this->insert_data($ncm->codigoNcm, $subattr, $attr->codigo);
                    }
                }
            }
        }
    }

    public function get_data()
    {
        $this->load->model('ncm_atributo_model');
        $result = $this->ncm_atributo_model->get_entry('04032000');
        echo '<pre>';
        foreach ($result as $item) {
            var_dump($item);
        }
    }
}