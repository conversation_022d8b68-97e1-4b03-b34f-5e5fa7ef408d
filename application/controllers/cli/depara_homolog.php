<?php

class Depara_itens extends CI_Controller {

    public $total = [
        'total' => 0,
        'part_number' => [],
        'empresa' => [],
        'estabelecimento' => [],
        'responsavel' => [],
        'homologado' => []
    ];
    public function __construct()
    {
        parent::__construct();

        if (!$this->input->is_cli_request()) {
            die('Permission denied');
        }
    }

    public function index() {
        $this->load->model(array("empresa_model"));
		
      $empresas = $this->empresa_model->get_entries();
        $i = 0;
         foreach($empresas as $empresa) {
            $i++;

            $funcoes_adicionais = explode('|', $empresa->funcoes_adicionais);

            $query_fiscal_engenharia_homologar = "";
            $query_fiscal_engenharia_homologado = "";
            $query_fiscal_engenharia_nao_homologado = "";
            $query_fiscal_engenharia_obsoleto = "";
            $query_fiscal_engenharia_revisao = "";
            $total_homologacao = 0;

            if (in_array('homologacao_fiscal', $funcoes_adicionais) && in_array('homologacao_engenharia', $funcoes_adicionais)) {
                $total_homologacao = 2;

            } else if (in_array('homologacao_fiscal', $funcoes_adicionais)) {
                $total_homologacao = 1;
                
            } else if (in_array('homologacao_engenharia', $funcoes_adicionais)) {
                $total_homologacao = 1;
                
            } else {
                continue;
            }

            echo 'Processando Reprovados';

            $sql =  $this->db->query("SELECT * FROM gestaotarifaria.cad_item  cad
                left join cad_item_homologacao hom on cad.id_item = hom.id_item
                inner join item i on i.part_number = cad.part_number and i.estabelecimento = cad.estabelecimento and
                i.id_empresa = cad.id_empresa
                where i.id_status = 3 and
                i.id_empresa = {$empresa->id_empresa}");
        
 
            $full = count($sql->num_rows());
            $process = (100 / $full) * $i;
            echo '('.$full.')'.' Processando '.$process.' %'.PHP_EOL;
         
            $this->get_itens_nao_homologado($total_homologacao, $sql->result(), $empresa->id_empresa, $query_fiscal_engenharia_nao_homologado);

            // homologado
            echo 'Processando Aprovados';

            $sql =  $this->db->query("SELECT * FROM gestaotarifaria.cad_item  cad
            left join cad_item_homologacao hom on cad.id_item = hom.id_item
            inner join item i on i.part_number = cad.part_number and i.estabelecimento = cad.estabelecimento and
            i.id_empresa = cad.id_empresa
            where i.id_status = 2 and
            i.id_empresa = {$empresa->id_empresa}");
    

        $full = count($sql->num_rows());
        $process = (100 / $full) * $i;
        echo '('.$full.')'.' Processando '.$process.' %'.PHP_EOL;
     
        $this->get_itens_homologado($total_homologacao, $sql->result(), $empresa->id_empresa, $query_fiscal_engenharia_nao_homologado);


 
        }
        echo ' fim '.PHP_EOL;


        print_r($this->total);

    }

    public function get_itens_homologado($total_homologacao, $dados, $id_empresa, $query_fiscal_engenharia)
    {

        foreach ($dados as $dado )
        {

            $logs = $this->db->query(
                "SELECT * FROM item_log where part_number = '{$dado->part_number}'
                and id_empresa = '{$dado->id_empresa}' and estabelecimento = '{$dado->estabelecimento}'
                and titulo = 'reprovado'
                and tipo_homologacao in ('Engenharia','Fiscal')
                group by tipo_homologacao
                order by criado_em desc "
            );

            if ($logs->num_rows() == 0)
            {
                return;
            }
            
            $cad_item_homolog = $this->db->query(
                "SELECT * FROM  cad_item_homologacao where id_item = '{$dado->id_item}'"
            );
 
            if ($cad_item_homolog->num_rows() < $total_homologacao) {
                 if ($cad_item_homolog->num_rows() == 1)
                 {
                    foreach ($cad_item_homolog->result() as $homolog)
                    {
                        if ($homolog->tipo_homologacao == 'Fiscal')
                        {
                            if ($logs->num_rows() > 0)
                            {
                                foreach ($logs->result() as $log)
                                {
                                    if ($log->tipo_homologacao == 'Engenharia')
                                    {
                                        $result = $this->db->query(
                                            "INSERT INTO cad_item_homologacao (id_item, id_usuario, tipo_homologacao, homologado, motivo, criado_em)
                                            VALUES ('{$dado->id_item}', '{$log->id_usuario}', 'Engenharia', 1, '{$log->motivo}', '{$log->id_usuario}')"
                                        );
                                        if ($result) {
                                            $this->total['total']++;
                                            $this->total['part_number'][] = $dado->part_number;
                                            $this->total['empresa'][] = $dado->id_empresa;
                                            $this->total['estabelecimento'][] = $dado->estabelecimento;
                                            $this->total['responsavel'][] = $log->id_usuario;
                                            $this->total['homologado'][] = 'Aprovado';
                                        }
                                   
                                    }
                                }
                            }
                        }
                        else
                        {
                            if ($homolog->tipo_homologacao == 'Engenharia')
                            {
                                if ($logs->num_rows() > 0)
                                {
                                    foreach ($logs->result() as $log)
                                    {
                                        if ($log->tipo_homologacao == 'Fiscal')
                                        {
                                            $result = $this->db->query(
                                                "INSERT INTO cad_item_homologacao (id_item, id_usuario, tipo_homologacao, homologado, motivo, criado_em)
                                                VALUES ('{$dado->id_item}', '{$log->id_usuario}', 'Fiscal', 1, '{$log->motivo}', '{$log->id_usuario}')"
                                            );
                                            if ($result) {
                                                $this->total['total']++;
                                                $this->total['part_number'][] = $dado->part_number;
                                                $this->total['empresa'][] = $dado->id_empresa;
                                                $this->total['estabelecimento'][] = $dado->estabelecimento;
                                                $this->total['responsavel'][] = $log->id_usuario;
                                                $this->total['homologado'][] = 'Aprovado';
                                            }
                                       
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
                    if ($cad_item_homolog->num_rows() == 0)
                    {
                        if ($logs->num_rows() > 0)
                        {
                            foreach ($logs->result() as $log)
                            {
                                $result = $this->db->query(
                                    "INSERT INTO cad_item_homologacao (id_item, id_usuario, tipo_homologacao, homologado, motivo, criado_em)
                                    VALUES ('{$dado->id_item}', '{$log->id_usuario}', '{$log->tipo_homologacao}', 1, '{$log->motivo}', '{$log->id_usuario}')"
                                );
                                if ($result) {
                                    $this->total['total']++;
                                    $this->total['part_number'][] = $dado->part_number;
                                    $this->total['empresa'][] = $dado->id_empresa;
                                    $this->total['estabelecimento'][] = $dado->estabelecimento;
                                    $this->total['responsavel'][] = $log->id_usuario;
                                    $this->total['homologado'][] = 'Aprovado';
                                }
                            }
                        }
                    }
                }
            }

 

        }
 
    }

    public function get_itens_nao_homologado($total_homologacao, $dados, $id_empresa, $query_fiscal_engenharia)
    {

        foreach ($dados as $dado )
        {

            $logs = $this->db->query(
                "SELECT * FROM item_log where part_number = '{$dado->part_number}'
                and id_empresa = '{$dado->id_empresa}' and estabelecimento = '{$dado->estabelecimento}'
                and titulo = 'reprovado'
                and tipo_homologacao in ('Engenharia','Fiscal')
                group by tipo_homologacao
                order by criado_em desc "
            );

            if ($logs->num_rows() == 0)
            {
                return;
            }

            $cad_item_homolog = $this->db->query(
                "SELECT * FROM  cad_item_homologacao where id_item = '{$dado->id_item}'"
            );
 
            if ($cad_item_homolog->num_rows() < $total_homologacao) {
                 if ($cad_item_homolog->num_rows() == 1)
                 {
                    foreach ($cad_item_homolog->result() as $homolog)
                    {
                        if ($homolog->tipo_homologacao == 'Fiscal')
                        {
                            if ($logs->num_rows() > 0)
                            {
                                foreach ($logs->result() as $log)
                                {
                                    if ($log->tipo_homologacao == 'Engenharia')
                                    {
                                        $result = $this->db->query(
                                            "INSERT INTO cad_item_homologacao (id_item, id_usuario, tipo_homologacao, homologado, motivo, criado_em)
                                            VALUES ('{$dado->id_item}', '{$log->id_usuario}', 'Engenharia', 0, '{$log->motivo}', '{$log->id_usuario}')"
                                        );
                                        if ($result) {
                                            $this->total['total']++;
                                            $this->total['part_number'][] = $dado->part_number;
                                            $this->total['empresa'][] = $dado->id_empresa;
                                            $this->total['estabelecimento'][] = $dado->estabelecimento;
                                            $this->total['responsavel'][] = $log->id_usuario;
                                            $this->total['homologado'][] = 'Reprovado';
                                        }
                                   
                                    }
                                }
                            }
                        }
                        else
                        {
                            if ($homolog->tipo_homologacao == 'Engenharia')
                            {
                                if ($logs->num_rows() > 0)
                                {
                                    foreach ($logs->result() as $log)
                                    {
                                        if ($log->tipo_homologacao == 'Fiscal')
                                        {
                                            $result = $this->db->query(
                                                "INSERT INTO cad_item_homologacao (id_item, id_usuario, tipo_homologacao, homologado, motivo, criado_em)
                                                VALUES ('{$dado->id_item}', '{$log->id_usuario}', 'Fiscal', 0, '{$log->motivo}', '{$log->id_usuario}')"
                                            );
                                            if ($result) {
                                                $this->total['total']++;
                                                $this->total['part_number'][] = $dado->part_number;
                                                $this->total['empresa'][] = $dado->id_empresa;
                                                $this->total['estabelecimento'][] = $dado->estabelecimento;
                                                $this->total['responsavel'][] = $log->id_usuario;
                                                $this->total['homologado'][] = 'Reprovado';
                                            }
                                       
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
                    if ($cad_item_homolog->num_rows() == 0)
                    {
                        if ($logs->num_rows() > 0)
                        {
                            foreach ($logs->result() as $log)
                            {
                                $result = $this->db->query(
                                    "INSERT INTO cad_item_homologacao (id_item, id_usuario, tipo_homologacao, homologado, motivo, criado_em)
                                    VALUES ('{$dado->id_item}', '{$log->id_usuario}', '{$log->tipo_homologacao}', 0, '{$log->motivo}', '{$log->id_usuario}')"
                                );
                                if ($result) {
                                    $this->total['total']++;
                                    $this->total['part_number'][] = $dado->part_number;
                                    $this->total['empresa'][] = $dado->id_empresa;
                                    $this->total['estabelecimento'][] = $dado->estabelecimento;
                                    $this->total['responsavel'][] = $log->id_usuario;
                                    $this->total['homologado'][] = 'Reprovado';
                                }
                            }
                        }
                    }
                }
            }

 

        }
 
    }

    // public function get_itens_obsoleto($id_empresa, $query_fiscal_engenharia)
    // {    
    //     $query = $this->db->query(
    //         "UPDATE `cad_item`
    //         INNER JOIN `item` i
    //             ON `cad_item`.`part_number` = `i`.`part_number` 
    //             AND cad_item.id_empresa = i.id_empresa 
    //             AND cad_item.estabelecimento = i.estabelecimento 
    //         SET i.id_status = 4
    //         WHERE `cad_item`.`status_exportacao` IN ('0', '1') 
    //             AND cad_item.`id_empresa` = {$id_empresa}
    //             {$query_fiscal_engenharia}
    //             AND `cad_item`.`descricao_mercado_local` IS NOT NULL 
    //             AND `cad_item`.`status_implementacao` IN ('I', 'N', 'R')"
    //     );
    // }

    // public function get_itens_em_revisao($id_empresa, $query_fiscal_engenharia)
    // {
    //     $query = $this->db->query(
    //         "UPDATE `cad_item`
    //         INNER JOIN `item` i
    //             ON `cad_item`.`part_number` = `i`.`part_number` 
    //             AND cad_item.id_empresa = i.id_empresa 
    //             AND cad_item.estabelecimento = i.estabelecimento 
    //         SET i.id_status = 5
    //         WHERE `cad_item`.`status_exportacao` IN ('0', '1') 
    //             AND cad_item.`id_empresa` = {$id_empresa} 
    //             AND (
    //                 cad_item.descricao_mercado_local IS NULL 
    //                 {$query_fiscal_engenharia} 
    //             ) 
    //             AND `cad_item`.`status_implementacao` IN ('I', 'N', 'R')"
    //     );
    // }

    // public function get_itens_em_analise($id_empresa)
    // {
    //     $this->db->query(
    //         "UPDATE item i 
    //         SET i.id_status = 6
    //             WHERE NOT EXISTS(
    //                 SELECT 1 FROM ctr_pendencias_pergunta pp 
    //                     WHERE i.part_number = pp.part_number 
    //                         AND pp.id_empresa = i.id_empresa 
    //                         AND pp.estabelecimento = i.estabelecimento 
    //                         LIMIT 1
    //             ) AND NOT EXISTS(
    //                 SELECT 1 FROM cad_item c 
    //                     WHERE i.part_number = c.part_number 
    //                         AND c.id_empresa = i.id_empresa 
    //                         AND c.estabelecimento = i.estabelecimento 
    //                         LIMIT 1
    //             ) AND i.id_empresa = {$id_empresa}"
    //     );
    // }

    // public function get_itens_pendente_duvidas($id_empresa)
    // {   
    //     $this->db->query(
    //         "UPDATE item i 	
    //         SET i.id_status = 7
    //             WHERE NOT EXISTS (
    //                 SELECT 1 FROM cad_item ci 
    //                     WHERE i.part_number = ci.part_number 
    //                         AND ci.id_empresa = i.id_empresa 
    //                         AND ci.estabelecimento = i.estabelecimento
    //                         LIMIT 1
    //             ) AND EXISTS (
    //                 SELECT * FROM ctr_pendencias_pergunta pp 
    //                     WHERE i.part_number = pp.part_number 
    //                         AND pp.id_empresa = i.id_empresa 
    //                         AND pp.estabelecimento = i.estabelecimento 
    //                         AND pp.pendente = 1
    //             ) AND i.id_empresa = {$id_empresa}"
    //     );
    // }

    // public function get_itens_respondidos($id_empresa)
    // {
    //     $this->db->query(
    //         "UPDATE item i 	
    //         SET i.id_status = 8
    //             WHERE EXISTS (
    //                 SELECT 1 FROM ctr_pendencias_pergunta pp 
    //                     WHERE i.part_number = pp.part_number 
    //                         AND pp.id_empresa = i.id_empresa 
    //                         AND pp.estabelecimento = i.estabelecimento
    //                         AND pp.pendente = 0
    //                         LIMIT 1
    //             ) AND NOT EXISTS (
    //                 SELECT 1 FROM ctr_pendencias_pergunta pp 
    //                     WHERE i.part_number = pp.part_number 
    //                         AND pp.id_empresa = i.id_empresa 
    //                         AND pp.estabelecimento = i.estabelecimento 
    //                         AND pp.pendente = 1
    //             ) AND NOT EXISTS(
    //                 SELECT 1 FROM cad_item c 
    //                     WHERE i.part_number = c.part_number 
    //                         AND c.id_empresa = i.id_empresa 
    //                         AND c.estabelecimento = i.estabelecimento 
    //                         LIMIT 1
    //             ) AND i.id_empresa = {$id_empresa}"
    //     );
    // }
}
