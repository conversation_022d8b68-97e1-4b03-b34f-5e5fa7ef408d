<?php

class Depara_attr extends CI_Controller {

    public function __construct()
    {
        parent::__construct();

        if (!$this->input->is_cli_request()) {
            die('Permission denied');
        }
    }

    public function index() {
        ini_set('memory_limit', -1);
    	set_time_limit(0);
        $this->db->group_by('id_item');
        $query = $this->db->get('cad_item_attr')->result();
        $reg = [];
        foreach ($query  as $dados)
        {
            $id_item_registro = $dados->id_item;
 
            $reg[] = $id_item_registro;
            $this->db->query("
            DELETE FROM cad_item_attr
            WHERE (atributo, atualizado_em) NOT IN (
              SELECT t.atributo, t.max_atualizado_em
              FROM (
                SELECT atributo, MAX(atualizado_em) AS max_atualizado_em
                FROM cad_item_attr
                WHERE id_item = '{$id_item_registro}'
                GROUP BY atributo 
              ) t
            ) and id_item = '{$id_item_registro}';");
        }
        
        echo '<pre>';
        print_r($reg);
        exit;
    }
}
