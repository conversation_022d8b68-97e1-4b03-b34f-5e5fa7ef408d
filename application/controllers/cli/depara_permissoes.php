<?php

class Depara_permissoes extends CI_Controller {

    public function __construct()
    {
        parent::__construct();

        if (!$this->input->is_cli_request()) {
            die('Permission denied');
        }
    }

    public function index() {
        $permissoes = $this->get_permissoes();

        $this->load->model(array(
            'permissao_model'
        ));

        foreach($permissoes as $permissao) {
            $checkPermissao = $this->permissao_model->check_permissao_exists($permissao['slug']);
            
            if (empty($checkPermissao)) {

                $insert_permissao = array(
                    'descricao' => $permissao['descricao'],
                    'slug'      => $permissao['slug'],
                    'pagina'    => $permissao['pagina'],
                );
    
                $this->permissao_model->save($insert_permissao);
            }
        }
    }

    public function get_permissoes() {
        $json_data = file_get_contents(FCPATH.'assets/de-para-permissoes/permissoes.txt');

        return json_decode($json_data, true);
    }
}