<?php

class <PERSON>lisa_atributos extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
    }
    public $_virtual_table = [];

    public function get_attrs_id_item()
    {

        $this->db->group_by('attr.id_item');
        $this->db->join('grupo_tarifario u', 'u.id_grupo_tarifario = attr.id_grupo_tarifario', 'inner');

        $this->db->where('YEAR(attr.criado_em) = 2023', null, false);

        return $query = $this->db->get("cad_item_attr attr");

        if ($query->num_rows()) {
            return $query->result();
        }

        return NULL;
    }


    public function index()
    {

        set_time_limit(0);
        ini_set('memory_limit', '2048M');

        $this->load->model(array(
            'cad_item_attr_model'
        ));
        $this->load->model('ncm_atributo_model');
        $this->load->model('cad_item_model');
        $this->load->model([
            "catalogo/produto_model",
            "grupo_tarifario_attr_model",
            "cad_item_attr_model",
        ]);

        $count = 0;
        $this->_virtual_table = [];

        $table_cad_attr = $this->get_attrs_id_item();

        // $codigoNcm = '30049069';
        // $id_item = 1;
        $diff = [];
        while ($table = $table_cad_attr->unbuffered_row()) {

            // foreach ($table_cad_attr as $table)
            // {

            $ncm_item = $this->get_required_attrs($this->produto_model->get_attr_ncm($table->ncm_recomendada));

            $ncm_item["defaultAttrs"] = $this->cad_item_attr_model->get_attr($table->id_item);

            $ncm_item["assocAttrs"] = $this->create_assoc_attrs_structure($ncm_item);

            $dados_registrados = $ncm_item['defaultAttrs'];
            $atributos_ncm = $this->_virtual_table;


            foreach ($dados_registrados as $value1) {
                $match = false;
                foreach ($atributos_ncm as $value2) {
                    if ($value1->id_grupo_tarifario == $value2['id_grupo_tarifario'] && $value1->atributo == $value2['atributo']) {
                        $match = true;
                        break;
                    }
                }
                if (!$match) {
                    $diff[] = $value1;
                    $count++;
                }
            }
        }
        if (empty($diff)) {
            echo "Todos os campos id_grupo_tarifario e atributo do array 1 estão no 2";
        } else {
            print_r($diff);
        }
    }

    private function get_required_attrs($listaAtributos)
    {
        if (empty($listaAtributos)) {
            throw new \Exception("Item NCM nao informado ou invalido");
        }

        $listaAtributos = \json_decode(\json_encode($listaAtributos), TRUE); // Força a conversão para array.

        $ncm_item = [];
        $ncm_item['listaAtributos'] = $listaAtributos;


        \array_multisort($ncm_item["listaAtributos"]);

        return $ncm_item;
    }


    private function create_assoc_attrs_structure($ncm_item)
    {
        if (empty($ncm_item)) {
            throw new \Exception("Item NCM nao informado ou invalido");
        }

        $ncm_item = \json_decode(\json_encode($ncm_item), TRUE); // Força a conversão para array.

        $arr_dbdata = $ncm_item["defaultAttrs"];   // Os dados do banco.
        $arr_attr   = $ncm_item["listaAtributos"]; // Os atributos do JSON.

        $this->assoc_recursively($arr_dbdata, $arr_attr); // Executa a associação.

        return $arr_attr;
    }

    private function assoc_recursively($arr_dbdata, &$arr_attr, $parent_attr = NULL)
    {
        if (empty($arr_attr)) {
            throw new \Exception("Dados invalidos para associacao");
        }

        if (empty($arr_dbdata)) {
            $arr_dbdata = [];
        }

        foreach ($arr_attr as &$attr) {
            $attr_template = !empty($attr["atributo"]) ? $attr["atributo"] : $attr; // Cria o template com base no atributo alvo.

            $attr_template["dbdata"] = ["codigo" => ""]; // Por padrão adiciona a propiedade dbdata e codigo.

            foreach ($arr_dbdata as $dbdata) {
                if (
                    !empty($parent_attr)
                    && !empty($dbdata["atributo_pai"])
                    && $dbdata["atributo_pai"] == $parent_attr["codigo"]
                    && $dbdata["atributo"] == $attr_template["codigo"]
                ) {
                    $attr_template["dbdata"] = $dbdata;
                } else if ($dbdata["atributo"] == $attr_template["codigo"]) {
                    $attr_template["dbdata"] = $dbdata;
                }
            }

            if (!empty($attr_template["dbdata"]["id_item"])) {
                $this->_virtual_table[] = $attr_template["dbdata"];
            }

            if ($attr_template["atributoCondicionante"] && !empty($attr_template["condicionados"])) {
                $this->assoc_recursively($arr_dbdata, $attr_template["condicionados"], $attr_template);
            } else if (\strtoupper($attr_template["formaPreenchimento"]) == "COMPOSTO" && !empty($attr_template["listaSubatributos"])) {
                $this->assoc_recursively($arr_dbdata, $attr_template["listaSubatributos"], $attr_template);
            }

            if (!empty($attr["atributo"])) {
                $attr["atributo"] = $attr_template;
            } else {
                $attr = $attr_template;
            }
        }
    }
}
