<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Cest extends MY_Controller {

	public function __construct() {
		parent::__construct();

		if (!is_logged()) {
			redirect('/login');
		}

        $this->load->library('breadcrumbs');

        if (!has_role('gerenciar_atribuicao')) {
            show_permission();
        }
	}

	public function index()
	{
        if (!has_role('gerenciar_atribuicao')) {
            redirect('/');
        }

        $this->load->model(
            array(
                'item_cest_model',
                'empresa_model'
            )
        );

        $data = array();

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('CEST', '/cest/');

        $empresa = $this->empresa_model->get_entry(sess_user_company());

        $busca = $this->input->get('search');
        
        if (!$busca)
        {
            $this->item_cest_model->clear_states();        
        }

        $estabelecimento = $this->input->get('estabelecimento');
        $ncm = $this->input->get('ncm');

        $this->item_cest_model->set_state('filter.id_empresa', sess_user_company());
        $this->item_cest_model->set_state('filter.estabelecimento', $estabelecimento);
        $this->item_cest_model->set_state('filter.ncm', $ncm);
        $this->item_cest_model->set_state('filter.search', $busca);
        $this->item_cest_model->set_state('filter.status', 0);
        $this->item_cest_model->set_state('filter.porta_a_porta', $empresa->porta_a_porta);

        $data['porcentagem'] = $this->item_cest_model->get_porcentagem_atribuido();
        $data['estabelecimentos'] = $this->item_cest_model->get_all_estabelecimentos();

        $items = $this->item_cest_model->get_entries();

        $custom_list = array();
        foreach ($items as $item) {
            $custom_list[$item->ncm_atual][] = $item;
        }

        $data['custom_list'] = $custom_list;

        $data['filtro'] = array(
            'busca' => $busca,
            'estabelecimento' => $estabelecimento,
            'ncm' => $ncm
        );

        $this->include_js('bootstrap-select/bootstrap-select.js');
        $this->include_css('bootstrap-select/bootstrap-select.css');

	    $this->render('cest/index', $data);
	}

    public function desfazer()
    {
        if (!has_role('cest') && !has_role('sysadmin')) {
            redirect('/');
        }

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('CEST', '/cest');
        $this->breadcrumbs->push('Desfazer atribuição', '/cest/desfazer/');

        $data = array();

        $this->load->model(
            array(
                'item_cest_model',
                'empresa_model'
            )
        );

        $data['search'] = NULL;
        $data['ncm'] = NULL;
        $data['estabelecimento'] = NULL;

        if ($this->input->get())
        {
            if ($this->input->is_set('search'))
            {
                $search = $this->input->get('search');
                $this->item_cest_model->set_state('filter.search', $search);
                $data['search'] = $search;
            }

            if ($this->input->is_set('ncm'))
            {
                $ncm = $this->input->get('ncm');
                $this->item_cest_model->set_state('filter.ncm', $ncm);
                $data['ncm'] = $ncm;
            }

            if ($this->input->is_set('estabelecimento'))
            {
                $estabelecimento = $this->input->get('estabelecimento');
                $this->item_cest_model->set_state('filter.estabelecimento', $estabelecimento);
                $data['estabelecimento'] = $estabelecimento;
            }
        }

        $id_empresa = sess_user_company();

        $error = FALSE;

        if ($this->input->post())
        {
            if ($this->input->is_set('item'))
            {
                $itens = $this->input->post('item');
                $estabelecimentos = $this->input->post('estabelecimento');

                foreach ($itens as $k => $item)
                {
                    $dbdata = array(
                        'codigo_cest_alterado' => NULL,
                        'status' => 0
                    );

                    $item_row = $this->item_cest_model->get_entry($item, $id_empresa, $estabelecimentos[$k]);

                    $motivo = 'Desatribuição de código CEST (de '.$item_row->codigo_cest_alterado.', para nulo).';

                    if (!$this->item_cest_model->update_item($item, $id_empresa, $estabelecimentos[$k], $dbdata, $motivo))
                    {
                        $error - TRUE;
                    }
                }

                if ($error === FALSE)
                {
                    $this->message_next_render('<strong>OK!</strong> A desatribuição de códigos CEST foi realizada com sucesso.', 'success');
                    redirect('cest/desfazer');
                }else
                {
                    $this->message_next_render('<strong>Oops!</strong> Ocorreu um erro ao realizar a desatribuição de códigos CEST.', 'error');
                    redirect('cest/desfazer');
                }
            }
        }

        $data['estabelecimentos'] = $this->item_cest_model->get_all_estabelecimentos($id_empresa);

        $this->item_cest_model->set_state('filter.id_empresa', $id_empresa);
        $this->item_cest_model->set_state('filter.status', 1);

        $this->item_cest_model->set_state('filter.porta_a_porta', 1);
        $data['itens'] = $this->item_cest_model->get_entries();

        $this->include_js('bootstrap-select/bootstrap-select.js');
        $this->include_css('bootstrap-select/bootstrap-select.css');

        $this->render('cest/desfazer', $data);
    }

    public function explorar()
    {
        if (!has_role('gerenciar_cest'))
        {
            show_permission();
        }
        $data = array();

        $this->load->model('cest_segmento_model');

        if ($this->input->is_set('search'))
        {
            $search = $this->input->post('search');
            $search_keys = $this->input->post('search_key');

            $this->cest_segmento_model->set_state('filter.search', $search);
            $this->cest_segmento_model->set_state('filter.search_keys', $search_keys);
        }

        if ($this->input->is_set('reset_filter'))
        {
            $this->cest_segmento_model->clear_states();
        }

        $itens = $this->cest_segmento_model->get_entries();
        $data['itens'] = $itens;

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('CEST', '/cest/');
        $this->breadcrumbs->push("Explorar CEST", '/cest/explorar/');

        $this->include_js('jquery.expander.min.js');

        $this->render('cest/explorar', $data);
    }

    public function importar()
    {
        if (!has_role('cest') && !has_role('sysadmin')) {
            redirect('/');
        }

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('CEST', '/cest');
        $this->breadcrumbs->push('Importação', '/cest/importar');

        $data = array();

        $this->load->model('item_cest_model');

        if ($this->input->post())
        {
            $this->load->library('form_validation');

            $this->form_validation->set_rules('id_empresa', 'Empresa', 'trim|required');

            if ($this->form_validation->run() == TRUE)
            {
                $id_empresa = $this->input->post('id_empresa');

                $upload_path = config_item('upload_tmp_path');

                $config['upload_path'] = $upload_path;
                $config['allowed_types'] = 'xlsx';
                $config['max_size'] = 2147483648;

                $this->load->library('upload', $config);

                if (!$this->upload->do_upload('arquivo'))
                {
                    $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . $this->upload->display_errors('<li>', '</li>') . "</ul>";
                    $this->message_on_render($err, 'error');
                }
                else
                {
                    $upload_data = $this->upload->data();

                    include APPPATH . 'libraries/xlsxreader.php';

                    $xlsx = new XLSXReader($upload_data['full_path']);
                    $sheetNames = $xlsx->getSheetNames();
                    $sheetActive = current($sheetNames);
                    $sheet = $xlsx->getSheet($sheetActive);

                    $i = 0;

                    $log_erros     = array();
                    $log_sucesso   = array();
                    $total_erros   = 0;
                    $total_sucesso = 0;

                    $idx = array(
                        'part_number'       => 0,
                        'estabelecimento'   => 1,
                        'descricao'         => 2,
                        'ncm_atual'         => 3,
                        'codigo_cest_atual' => 4,
                        'valor'             => 5
                    );

                    foreach($sheet->getData() as $key => $row)
                    {
                        //Primeira linha é o cabeçalho da planilha
                        if ($i > 0)
                        {
                            $dbdata = array();

                            $part_number       = trim($row[$idx['part_number']]);
                            $estabelecimento   = trim($row[$idx['estabelecimento']]);
                            $descricao         = trim($row[$idx['descricao']]);
                            $ncm_atual         = trim($row[$idx['ncm_atual']]);
                            $codigo_cest_atual = trim($row[$idx['codigo_cest_atual']]);
                            $valor             = trim($row[$idx['valor']]);

                            $dbdata['id_empresa'] = $id_empresa;
                            $dbdata['status'] = 0;
                            $dbdata['codigo_cest_alterado'] =  NULL;

                            $valor_num = str_replace(array('.', ','), array('', '.'), $valor);
                            $dbdata['valor'] = number_format($valor_num, 2, '.', '');

                            if (empty($part_number))
                            {
                                $log_erros[] = 'Part number <strong>não informado</strong> na Linha <strong>'.$i.'.</strong>';
                                $total_erros++;
                                continue;
                            }else
                            {
                                $dbdata['part_number'] = $part_number;
                            }

                            if (empty($estabelecimento))
                            {
                                $log_erros[] = 'Part number <strong>'.$part_number.'</strong>: Estabelecimento não informado para o item.';
                                $total_erros++;
                                continue;
                            }else
                            {
                                $dbdata['estabelecimento'] = $estabelecimento;
                            }

                            if (empty($ncm_atual))
                            {
                                $log_erros[] = 'Part number <strong>'.$part_number.'</strong>: NCM Atual não informada para o item.';
                                $total_erros++;
                                continue;
                            }else
                            {
                                $ncm_atual = str_replace('.', '', $ncm_atual);
                                $dbdata['ncm_atual'] = trim($ncm_atual);
                            }

                            if (!empty($codigo_cest_atual))
                            {
                                $codigo_cest_atual = str_replace('.', '', $codigo_cest_atual);
                                $dbdata['codigo_cest_atual'] = trim($codigo_cest_atual);
                            }

                            $dbdata['descricao'] = trim($descricao);

                            if (!$this->item_cest_model->check_item_exists($part_number, $id_empresa, $estabelecimento))
                            {
                                if ($this->item_cest_model->save($dbdata))
                                {
                                    $log_sucesso[] = 'Part number <strong>'.$part_number.'</strong> inserido.';
                                    $total_sucesso++;
                                }
                            }else
                            {
                                if ($this->item_cest_model->save($dbdata, array('part_number' => $part_number, 'id_empresa' => $id_empresa, 'estabelecimento' => $estabelecimento)))
                                {
                                    $log_sucesso[] = 'Part number <strong>'.$part_number.'</strong> atualizado.';
                                    $total_sucesso++;
                                }
                            }
                        }

                        $i++;
                    }

                    $log_final = '';

                    if ($total_sucesso > 0)
                    {
                        $msg = '<h4>'.$total_sucesso.' item(ns) importado(s) com sucesso:</h4><ul>';

                        foreach ($log_sucesso as $log_msg)
                        {
                            $msg .= '<li>'.$log_msg.'</li>';
                        }
                        $msg .= '</ul>';

                        $log_final .= $this->message_config($msg, 'success');
                    }

                    if ($total_erros > 0)
                    {
                        $msg = '<h4>'.$total_erros.' item(ns) importado(s) com erro:</h4><ul>';

                        foreach ($log_erros as $log_msg)
                        {
                            $msg .= '<li>'.$log_msg.'</li>';
                        }
                        $msg .= '</ul>';

                        $log_final .= $this->message_config($msg, 'error');
                    }

                    if (!empty($log_final))
                    {
                        $this->message_next_render($log_final, NULL, TRUE);
                        redirect('cest/importar');
                    }
                }
            }else
            {
                $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
                $this->message_on_render($err, 'error');
            }
        }

        $this->load->model('empresa_model');
        $data['empresas'] = $this->empresa_model->get_all_entries();

        $this->include_js('bootstrap-select/bootstrap-select.js');
        $this->include_css('bootstrap-select/bootstrap-select.css');

        $this->render('cest/importar', $data);
    }

    public function exportar()
    {
        if (!has_role('atribuicao_cest')) {
            show_permission();
        }
        
        if ($this->input->post())
        {
            $this->load->model('item_cest_model');

            $status = $this->input->post('status');

            if (preg_match('/^0|1$/', $status)) {
                $this->item_cest_model->set_state('filter.status', $status);
            }

            $id_empresa = sess_user_company();

            $this->item_cest_model->set_state('filter.id_empresa', $id_empresa);

            $itens = $this->item_cest_model->get_entries();

            $this->load->library('Excel');

            $this->excel->setActiveSheetIndex(0);
            $this->excel->getActiveSheet()->setTitle('Atribuição de CEST');

            $this->excel->getActiveSheet()->setCellValue('A1', 'Part number');
            $this->excel->getActiveSheet()->setCellValue('B1', 'Estabelecimento');
            $this->excel->getActiveSheet()->setCellValue('C1', 'Descrição do item');
            $this->excel->getActiveSheet()->setCellValue('D1', 'NCM Atual');
            $this->excel->getActiveSheet()->setCellValue('E1', 'CEST Atual');
            $this->excel->getActiveSheet()->setCellValue('F1', 'CEST Alterado');

            if (has_role('sysadmin')) {
                $this->excel->getActiveSheet()->setCellValue('G1', 'Valor');
            }

            $this->excel->getActiveSheet()->getStyle('A1:G1')->getFont()->setBold(true);
            $this->excel->getActiveSheet()->getStyle('A1:G1')->getFill()
                        ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
                        ->getStartColor()->setARGB('F9FF00');
            $this->excel->getActiveSheet()->getStyle('A1:G1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

            foreach(range('A','G') as $columnID)
            {
                $this->excel->getActiveSheet()->getColumnDimension($columnID)->setAutoSize(true);
                $this->excel->getActiveSheet()
                            ->getStyle($columnID)
                            ->getAlignment()
                            ->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);
            }

            foreach ($itens as $k=> $item)
            {
                $i = $k+2;

                $cell = 'A'.$i;
                $this->excel->getActiveSheet()->getCell($cell)->setValue($item->part_number);

                $cell = 'B'.$i;
                $this->excel->getActiveSheet()->getCell($cell)->setValue($item->estabelecimento);

                $cell = 'C'.$i;
                $this->excel->getActiveSheet()->getCell($cell)->setValue($item->descricao);

                $cell = 'D'.$i;
                $this->excel->getActiveSheet()->getCell($cell)->setValue($item->ncm_atual);

                $cell = 'E'.$i;
                $this->excel->getActiveSheet()->getCell($cell)->setValue($item->codigo_cest_atual);

                $cell = 'F'.$i;
                $this->excel->getActiveSheet()->getCell($cell)->setValue($item->codigo_cest_alterado);

                if (has_role('sysadmin')) {
                    $cell = 'G'.$i;
                    $valor = number_format($item->valor, 2, ',', '.');
                    $this->excel->getActiveSheet()->getCell($cell)->setValue($valor);
                }
            }

            $filename='cest_'.date('Y-m-d_H-i-s').'.xlsx';
            header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment;filename="'.$filename.'"');
            header('Cache-Control: max-age=0');

            $objWriter = PHPExcel_IOFactory::createWriter($this->excel, 'Excel2007');
            $objWriter->save('php://output');

            exit();
        }
    }

    public function ajax_get_cests()
    {
        if ($post = $this->input->post())
        {
            $this->load->model('cest_model');

            $cod_cest_segmento = $this->input->post('cod_cest_segmento');

            if ($this->input->is_set('search'))
            {
                $search = $this->input->post('search');
                $search_keys = $this->input->post('search_key');

                $this->cest_model->set_state('filter.search', $search);
                $this->cest_model->set_state('filter.search_keys', $search_keys);
            }

            // É CHAMADO APENAS NO EXPLORAR CEST

            $itens = $this->cest_model->get_entries_by_cod_cest_segmento($cod_cest_segmento);

            echo json_encode($itens);
            return TRUE;
        }
    }

    public function ajax_get_cest()
    {
        $this->load->model('cest_model');

        if ($ncm = $this->input->post('ncm'))
        {
            $data = array();

            $id_empresa = sess_user_company();
            $this->load->model('empresa_model');
            $empresa = $this->empresa_model->get_entry($id_empresa);

            if ($empresa->porta_a_porta == 1)
            {
                $this->cest_model->set_state('filter.porta_a_porta', TRUE);
            }else
            {
                $this->cest_model->set_state('filter.porta_a_porta', FALSE);
            }

            $this->cest_model->set_state('filter.ncm', $ncm);
            $data['cest'] = $this->cest_model->get_entries();

            echo json_encode($data);

            return TRUE;
        }
    }

    public function ajax_submit_form()
    {
        $this->load->model('item_cest_model');

        if (
            ($items = $this->input->post('item')) &&
            ($cod_cest = $this->input->post('cod_cest'))
        ) {
            $id_empresa = sess_user_company();
            $cod_cest = ($cod_cest == -1) ? NULL : $cod_cest;

            foreach ($items as $item) {
                $this->item_cest_model->update_item(
                    $item['part_number'],
                    $id_empresa,
                    $item['estabelecimento'],
                    $data = array(
                        'status' => 1,
                        'codigo_cest_alterado' => $cod_cest
                    ),
                    $motivo = 'Vinculação'
                );
            }

            $this->message_next_render('<strong>OK!</strong> A atribuição foi realizada com sucesso!');
        } else {
            $this->message_next_render('<strong>Ops!</strong> Não foi possível realizar a atribuição.', 'error');
        }
    }

    public function ajax_get_item_cest()
    {
        if ($post = $this->input->post())
        {

            $this->load->model('item_cest_model');

            $item_cest = $this->item_cest_model->get_entry($post['part_number'], sess_user_company(), $post['estabelecimento']);

            echo json_encode($item_cest);

            return TRUE;
        }
    }

    public function update_item()
    {
        if ($this->input->post())
        {
            $this->load->library('form_validation');

            $id_empresa = sess_user_company();

            $part_number = $this->input->post('part_number');
            $estabelecimento_post = $this->input->post('estabelecimento');
            $estabelecimento = $this->input->post('estabelecimento_orig');

            if (!empty($estabelecimento_post) && $estabelecimento_post != $estabelecimento)
            {
                $this->form_validation->set_rules('part_number', 'Part number', 'trim|required|callback_check_item_exists');
            }else
            {
                $this->form_validation->set_rules('part_number', 'Part number', 'trim|required');
            }

            $this->form_validation->set_rules('estabelecimento', 'Estabelecimento', 'trim|required');
            $this->form_validation->set_rules('descricao', 'Descrição', 'trim');
            $this->form_validation->set_rules('ncm_atual', 'NCM Atual', 'trim|required|is_numeric');
            $this->form_validation->set_rules('codigo_cest_atual', 'Código CEST Atual', 'trim|is_numeric');

            if ($this->form_validation->run())
            {
                $this->load->model('item_cest_model');

                $entry = $this->item_cest_model->get_entry($part_number, $id_empresa, $estabelecimento);

                $log_item_pendente = FALSE;

                $codigo_cest_alterado = $entry->codigo_cest_alterado;
                $status = $entry->status;

                if ($entry->status == 1 && ($entry->ncm_atual !== $this->input->post('ncm_atual')))
                {
                    $log_item_pendente = TRUE;

                    $codigo_cest_alterado = null;
                    $status = 0;
                }

                $dbdata = array(
                    'part_number'          => $part_number,
                    'estabelecimento'      => $estabelecimento_post,
                    'id_empresa'           => $id_empresa,
                    'descricao'            => $this->input->post('descricao'),
                    'ncm_atual'            => $this->input->post('ncm_atual'),
                    'status'               => $status,
                    'codigo_cest_atual'    => $this->input->post('codigo_cest_atual'),
                    'codigo_cest_alterado' => $codigo_cest_alterado
                );

                $arr_label = array(
                    'estabelecimento' => 'Estabelecimento',
                    'ncm_atual' => 'NCM Atual',
                    'codigo_cest_atual' => 'Código CEST Atual',
                    'descricao' => 'Descrição'
                );

                $motivo_html = NULL;

                foreach ($this->input->post() as $key => $val)
                {
                    if (isset($entry->{$key}))
                    {
                        if ($val != $entry->{$key})
                        {
                            $motivo_html .= '<strong>'.$arr_label[$key].' de: </strong>'.$entry->{$key}.' <strong> para: </strong>'.$val.'<br>';
                        }
                    }
                }

                if ($log_item_pendente == TRUE) {
                    $motivo_html .= "<strong>Obs.:</strong> Atribuição de CEST desfeita devido a alteração no NCM do item.";
                }

                $motivo = ($motivo_html) ? "Atualização dos dados do item<br />{$motivo_html}" : FALSE;

                if (!$this->item_cest_model->update_item($part_number, $id_empresa, $estabelecimento, $dbdata, $motivo))
                {
                    $this->message_on_render('<strong>Oops!</strong> Ocorreu um erro ao tentar editar o item selecionado.', 'error');
                } else
                {
                    $this->load->model('empresa_model');
                    $empresa = $this->empresa_model->get_entry($id_empresa);

                    if ($empresa->multi_estabelecimentos == 1)
                    {
                        $this->message_next_render('<strong>OK!</strong> O item ['.$part_number.']['.$estabelecimento.'] foi editado com sucesso.');
                    } else
                    {
                        $this->message_next_render('<strong>OK!</strong> O item ['.$part_number.'] foi editado com sucesso.');
                    }

                    $return_data = array('status' => 'success');
                }
            } else
            {
                $err = $this->message_config("<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>", 'error', TRUE);

                $return_data = array('status' => 'error', 'message' => $err);
            }

            echo json_encode($return_data);

            return TRUE;
        }
    }

    public function check_item_exists()
    {
        $this->load->model('item_cest_model');

        $part_number = $this->input->post('part_number');
        $estabelecimento = $this->input->post('estabelecimento');

        if ($this->item_cest_model->check_item_exists($part_number, sess_user_company(), $estabelecimento))
        {
            $this->form_validation->set_message('check_item_exists', 'O item <strong>'.$part_number.'</strong> já existe para o estabelecimento <strong>'.$estabelecimento.'</strong>.');
            return FALSE;
        }else
        {
            return TRUE;
        }
    }
}
