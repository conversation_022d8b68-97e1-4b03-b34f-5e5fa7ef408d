<?php

require_once  APPPATH . 'libraries/Spout/Autoloader/autoload.php';
use Box\Spout\Reader\ReaderFactory;
use Box\Spout\Common\Type;

class Score extends MY_Controller
{

    private $sort_fields = array();

	public function __construct() {
		parent::__construct();

		if ($this->uri->segment(3) !== 'cron') {
			if (!is_logged()) {
				redirect('/login');
			}

			if (!has_role('gerenciar_score')) {
                show_permission();
			}
		}

        $this->load->library('breadcrumbs');

		$this->load->model("score_model");

        $this->sort_fields =  array('nome', 'total_itens', 'pendentes',
            'aprovacoes_pelo_usuario', 'aprovacoes_por_outro_usuario',
            'reanalise', 'percentual_conclusao', 'percentual_participacao',
            'data_atualizacao'
        );
	}

	public function index()
	{
		$id_empresa = sess_user_company();
		$this->load->model('empresa_model');

		$pacote = '';

		if ($this->input->post('commit'))
		{
			if ($query_data = $this->input->post('query_data'))
			{
				list($_day, $_month, $_year) = explode("/", $query_data);

				if ($unix_date = mktime(0, 0, 0, (int) $_month, (int) $_day, (int) $_year)) {
					$db_format_date = gmdate("Y-m-d", $unix_date);
					$this->score_model->set_state('filter.date', $db_format_date);
				}
			}

			$pacote = $this->input->post('pacote');
			$this->score_model->set_state('filter.pacote', $pacote);
		}

        $sort = $this->input->post('sort');

        if ((!empty($sort['order']) && !empty($sort['order'])) &&
            in_array($sort['field'], $this->get_sort_fields()) &&
            in_array($sort['order'], array('asc', 'desc'))
        ) {
            $this->score_model->set_state('filter.sort', $sort);
        } else {
            $sort = array(
                'field' => 'nome',
                'order' => 'asc'
            );
        }

		$this->score_model->set_state('filter.id_empresa', $id_empresa);
        $this->score_model->set_state('filter.perfil', 'engenharia');
        $list['engenharia'] = $this->score_model->get_entries();

        $this->score_model->set_state('filter.perfil', 'fiscal');
        $list['fiscal'] = $this->score_model->get_entries();

		$data['list'] = $list;
		$data['pacotes'] = $this->score_model->get_pacotes();
		$data['empresas'] = $this->empresa_model->get_all_entries();

        $data['sort'] = $sort;
        $data['valid_fields'] = $this->get_sort_fields();

		$this->include_css('b3-datetimepicker.min.css');

		$this->include_js(array(
            'b3-datetimepicker.min.js?v=3.1.1',
            'jquery.maskedinput.js'
        ));

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Score de aprovação', '/relatorios/score/');

		$this->render('relatorios/score/default', $data);
	}

    public function get_sort_fields()
    {
        return $this->sort_fields;
    }

	public function reanalise($id_usuario = NULL)
	{
		$this->load->library('pagination');

		$id_empresa = sess_user_company();

		$per_page = $this->input->get('per_page');
		$limit = 4;
        $offset = $per_page;

        $perfil = $this->input->get('perfil');
        $pacote = $this->input->get('pacote');
        $date   = $this->input->get('data');

        switch ($perfil)
        {
            case 'fiscal':
                $id_perfil = 2;
                break;

            case 'engenharia':
                $id_perfil = 1;
                break;

            default:
                $id_perfil = 1;
        }

        $this->score_model->set_state('filter.id_usuario', $id_usuario);
        $this->score_model->set_state('filter.id_empresa', $id_empresa);
        $this->score_model->set_state('filter.pacote', urldecode($pacote));
        $this->score_model->set_state('filter.date', urldecode($date));
        $this->score_model->set_state('filter.id_perfil', $id_perfil);

        $total_entries = $this->score_model->get_reanalise_list(NULL, NULL, TRUE);

        if ($this->input->get('import_xls')) {
            return $this->importar();
        }

		if ($this->input->get('generate_xls'))
        {
            $this->load->library('Excel');

            $this->excel->setActiveSheetIndex(0);
            $this->excel->getActiveSheet()->setTitle('Log - Atribuição de grupos');

            $headerRows = array(
                'CÓDIGO PRODUTO',
                'DESCRIÇÃO PROPOSTA RESUMIDA',
                'GRUPO',
                'CARACTERÍSTICA',
                'SUBSÍDIO',
                'RESPONSÁVEL FISCAL',
                'RESPONSÁVEL ENGENHARIA',
                'ATUALIZAR (SIM/NAO)',
                'MEMÓRIA DE CLASSIFICAÇÃO',
                'EVENTO',
                'DISPOSITIVOS LEGAIS',
                'SOLUÇÃO DE CONSULTA',
                'FUNÇÃO',
                'APLICAÇÃO',
                'MARCA',
                'DESCRIÇÃO PROPOSTA COMPLETA',
                'ESTABELECIMENTO',
                'MATERIAL CONSTITUTIVO',
                'CEST',
                'MOTIVO',
                'DATA DE CRIAÇÃO',
                'NCM ATUAL',
                'CEST ATUAL',
                'NCM PROPOSTO',
                'USUÁRIO QUE REPROVOU',
                'HISTÓRICO',
            );

            $this->excel->getActiveSheet()->fromArray($headerRows, NULL, 'A1');

            $this->excel->getActiveSheet()->getStyle('A1:S1')->applyFromArray(array(
                'font'      => array('bold'  => true, 'underline' => true, 'color' => array('rgb' => 'FFFFFF')),
                'fill'      => array('type' => 'solid', 'color' => array('rgb' => '000080')),
                'alignment' => array('horizontal' => 'center')
            ));

            $this->excel->getActiveSheet()->getStyle('T1:Z1')->applyFromArray(array(
                'font'      => array('bold'  => true, 'underline' => true, 'color' => array('rgb' => '000000')),
                'fill'      => array('type' => 'solid', 'color' => array('rgb' => 'FFFF00')),
                'alignment' => array('horizontal' => 'center')
            ));

            foreach(range('A','X') as $columnID)
            {
                $this->excel->getActiveSheet()->getColumnDimension($columnID)->setAutoSize(true);
                $this->excel->getActiveSheet()
                            ->getStyle($columnID)
                            ->getAlignment()
                            ->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);
            }

			$list = $this->score_model->get_reanalise_list();

            if (!empty($list))
            {
                $item_data = array();

                foreach ($list as $item)
                {
                    $item_data[] = array(
                        $item->part_number,
                        $item->descricao_mercado_local,
                        $item->desc_grupo,
                        $item->caracteristicas,
                        $item->subsidio,
                        $item->email_resp_fiscal,
                        $item->email_resp_engenharia,
                        'NAO',
                        $item->memoria_classificacao,
                        $item->evento,
                        $item->dispositivo_legal,
                        $item->solucao_consulta,
                        $item->funcao,
                        $item->aplicacao,
                        $item->marca,
                        $item->descricao_proposta_completa,
                        $item->estabelecimento,
                        $item->material_constitutivo,
                        $item->cod_cest == '-1' || $item->cod_cest == -1 ? 'Não Atende' : $item->cod_cest,
                        $item->motivo,
                        $item->criado_em,
                        $item->ncm_atual,
                        $item->cod_cest_atual == '-1' || $item->cod_cest_atual == -1 ? 'Não Atende' : $item->cod_cest_atual,
                        $item->ncm_proposto,
                        $item->email_resp_homolog,
                        $item->historico_reanalise
                    );
                }

                // Corrige HTML Chars
                array_walk_recursive($item_data, function(&$item) {
                    $item = html_entity_decode($item, ENT_QUOTES);
                });

                // Inserindo os dados
                list ($start_column, $start_row) = PHPExcel_Cell::coordinateFromString('A2');

                foreach ($item_data as $row_data)
                {
                    $curr_column = $start_column;

                    foreach($row_data as $cell_v)
                    {
                        $this->excel->getActiveSheet()->setCellValueExplicit($curr_column . $start_row, $cell_v, PHPExcel_Cell_DataType::TYPE_STRING);
                        ++$curr_column;
                    }

                    ++$start_row;
                }
            }

            $filename='GT-SCORE-'.date('d-m-Y').'.xlsx';
            header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment;filename="'.$filename.'"');
            header('Cache-Control: max-age=0');

            $objWriter = PHPExcel_IOFactory::createWriter($this->excel, 'Excel2007');
            $objWriter->save('php://output');

            exit();
		}

		$config['base_url'] = base_url('relatorios/score/reanalise/' . $id_usuario);
        $config['uri_segment'] = 4;
        $config['total_rows'] = $total_entries;
        $config['per_page'] = $limit;
        $config['page_query_string'] = TRUE;
        $config['use_page_numbers'] = FALSE;
        $config['reuse_query_string'] = TRUE;
        $config['num_links'] = 2;

        $this->pagination->initialize($config);

		$data['list'] = $this->score_model->get_reanalise_list($limit, $offset);
		$data['pacote'] = $pacote;
		$data['id_usuario'] = $id_usuario;

		$this->load->view('relatorios/score/modal_reanalise', $data);
	}

    public function importar()
    {
        $config['upload_path'] = config_item('upload_tmp_path');
        $config['allowed_types'] = 'xlsx';
        $config['max_size'] = 2147483648;

        $this->load->library('upload', $config);

        if ( ! $this->upload->do_upload('arquivo'))
        {
            $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . $this->upload->display_errors('<li>', '</li>') . "</ul>";
            $this->message_next_render($err, 'error');

            redirect('relatorios/score');
        }

        $this->load->model(array(
            'empresa_model',
            'item_log_model'
        ));

        $this->load->helper('text');

        $empresa = $this->empresa_model->get_entry(sess_user_company());

        $upload_data = $this->upload->data();

        $reader = ReaderFactory::create(Type::XLSX);
        $reader->open($upload_data['full_path']);

        $colunas = array(
            'part_number'     => array(
                'required'    => true,
                'description' => 'Part Number',
                'rules'       => array('PN', 'Codigo', 'Codigo\s(do\s)?(Item|Produto)', 'Part Number')
            ),

            'estabelecimento' => array(
                'required'    => false,
                'description' => 'Estabelecimento',
                'rules'       => array('Estab(\.)?', 'Estabelecimento')
            ),

            'historico'       => array(
                'required'    => true,
                'description' => 'Histórico',
                'rules'       => array('Historico')
            )
        );

        $log_sucesso = $log_erro = array();

        foreach ($reader->getSheetIterator() as $sheet)
        {
            if ($sheet->getIndex() === 0)
            {
                foreach ($sheet->getRowIterator() as $i => $row)
                {
                    if ($i == 1)
                    {
                        $idx = array();

                        $rowcols = array_map(function($item) {
                            return convert_accented_characters($item);
                        }, array_values($row));

                        // Mapeamento dinâmico
                        foreach ($rowcols as $rk => $rowcol)
                        {
                            foreach ($colunas as $ck => $coluna)
                            {
                                if (array_key_exists($ck, $idx)) continue;

                                if (!preg_match('/^(' . implode("|", $coluna['rules']) . ')$/i', $rowcol)) continue;

                                $idx[$ck] = $rk;

                                continue 2;
                            }
                        }

                        // Validação das colunas obrigatórias
                        $colerr = array();

                        foreach ($colunas as $ck => $coluna) {
                            if ($coluna['required'] === true && !array_key_exists($ck, $idx)) {
                                $colerr[] = '<li><strong>' . $coluna['description'] . '</strong></li>';
                            }
                        }

                        if (count($colerr)) {
                            $err  = 'Não foi possível realizar a importação. As seguintes colunas são obrigatórias e não foram encontradas na planilha enviada:';
                            $err .= '<ul>' . implode("", $colerr) . '</ul>';

                            $this->message_next_render($err, 'error');

                            redirect('relatorios/score');
                        }
                    } else
                    {
                        array_walk($row, function(&$v) {
                            $v = clean_str($v);
                        });

                        $part_number = clean_str($row[$idx['part_number']], true);

                        if (isset($idx['estabelecimento']))
                        {
                            $estabelecimento = $row[$idx['estabelecimento']];
                            $estabelecimento = preg_replace('/[\xA0]/u', '', trim($estabelecimento));

                            // Caso não tenha nenhum Estabelecimento
                            if (empty($estabelecimento)) {
                                $estabelecimento = $empresa->estabelecimento_default;
                            }
                        } else {
                            $estabelecimento = $empresa->estabelecimento_default;
                        }

                        $historico = null;

                        if (isset($row[$idx['historico']])) {
                            $historico = trim($row[$idx['historico']]);
                        }

                        // Verifica se o item existe
                        $query = $this->db->get_where('cad_item ci',
                            array(
                                'part_number'     => $part_number,
                                'estabelecimento' => $estabelecimento,
                                'id_empresa'      => $empresa->id_empresa
                            )
                        , 1);

                        if ($query->num_rows())
                        {
                            $row = $query->row();

                            $this->db->update(
                                'cad_item',
                                array('historico_reanalise' => $historico),
                                array('id_item'             => $row->id_item)
                            );

                            if ($historico !== $row->historico_reanalise)
                            {
                                if ($historico == '') {
                                    $motivo = '<em>Histórico removido.</em>';
                                } else {
                                    $motivo = $historico;
                                }

                                $logdata = array(
                                    'part_number'       => $row->part_number,
                                    'estabelecimento'   => $row->estabelecimento,
                                    'id_empresa'        => $row->id_empresa,
                                    'id_usuario'        => sess_user_id(),
                                    'titulo'            => 'reanalise',
                                    'criado_em'         => date("Y-m-d H:i:s"),
                                    'motivo'            => $motivo
                                );

                                $this->item_log_model->save($logdata);
                            }

                            $log_sucesso[] = array('part_number' => $row->part_number, 'estabelecimento' => $row->estabelecimento);
                        } else {
                            $log_erro[] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);
                        }
                    }
                }
            }
        }

        // Parse dos erros
        if (count($log_erro) && !count($log_sucesso)) {
            $msg    = 'A importação falhou.';
            $alert  = 'error';
        } else if (count($log_erro) && count($log_sucesso)) {
            $msg    = 'A importação ocorreu parcialmente com sucesso.';
            $alert  = 'warning';
        } else {
            $msg    = 'A importação foi realizada com sucesso!';
            $alert  = 'success';
        }

        $msg .= '<ul>';

        if (count($log_sucesso)) {
            $html_li_item_att = '';

            foreach ($log_sucesso as $item) {
                $html_li_item_att .= '<li>' . $item['part_number'] . ' (' . $item['estabelecimento'] . ')</li>';
            }

            $msg .= '<li>';
            $msg .= '<a data-toggle="collapse" data-parent="#log-accordion" href="#atualizados">';
            $msg .= 'Itens atualizados: <b>' . count($log_sucesso) . '</b></a>';
            $msg .= '<div id="atualizados" class="collapse"><ul>';
            $msg .= $html_li_item_att;
            $msg .= '</ul></div>';
            $msg .= '</li>';
        }

        if (count($log_erro))
        {
            $html_li_item_err = '';

            foreach ($log_erro as $item) {
                $html_li_item_err .= '<li>' . $item['part_number'] . ' (' . $item['estabelecimento'] . ')</li>';
            }

            $msg .= '<li>';
            $msg .= '<a data-toggle="collapse" data-parent="#log-accordion" href="#com-erro">';
            $msg .= 'Itens com erro: <b>' . count($log_erro) . '</b></a>';
            $msg .= '<div id="com-erro" class="collapse"><ul>';
            $msg .= $html_li_item_err;
            $msg .= '</ul></div>';
            $msg .= '</li>';
        }

        $msg .= '</ul>';

        $this->message_next_render($msg, $alert);

        redirect('relatorios/score');
    }

	public function cron()
	{
		$this->score_model->cron();
	}
}



