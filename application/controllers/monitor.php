<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

ini_set('default_socket_timeout', 600);

function get_fluxo_documento_by_id($id) {
    $result = '';

    switch ($id) {
        case 1:
            $result = 'Notas de Transferência';
        break;

        case 2:
            $result = "Notas de Entradas";
        break;

        case 3:
            $result = "Notas de Saída";
        break;

        case 4:
            $result = "Todas as situações";
        break;
    }

    return $result;
}

class Monitor extends MY_Controller {

	public function __construct() {
		parent::__construct();

		if (!is_logged()) {
			redirect('/login');
		}

        setlocale(LC_MONETARY, 'pt_BR.UTF-8');

        if (!has_role('gerenciar_auditoria_monitor'))
        {
            show_permission();
        }

        if (has_role('becomex_pmo') && !has_role('sysadmin'))
        {
            show_permission();
        }

        $this->load->library('breadcrumbs');
	}

    public function graficos()
    {
        $this->load->model('empresa_model');

        $empresa_entry = $this->empresa_model->get_entry(sess_user_company());

        $cnpj = $empresa_entry->cnpj;

        // sem tax_code/iva
        $query_sem_iva = $this->db->query("SELECT
            pgr, count(*) as total from preco where cnpj_cliente = '{$cnpj}' and tax_code IS NULL group by pgr order by total desc
            LIMIT 15
        ");

        $query_total_sem_iva = $this->db->query("SELECT
            count(*) as total from preco where cnpj_cliente = '{$cnpj}' and tax_code IS NULL
        ");

        $result_total_sem_iva = $query_total_sem_iva->row()->total;

        // com iva
        $query_com_iva = $this->db->query("SELECT
            pgr, count(*) as total from preco where cnpj_cliente = '{$cnpj}' and tax_code IS NOT NULL group by pgr order by total desc
            LIMIT 15
        ");

        $query_total_com_iva = $this->db->query("SELECT
            count(*) as total from preco where cnpj_cliente = '{$cnpj}' and tax_code IS NOT NULL and tax_code <> ''
        ");

        $result_total_com_iva = $query_total_com_iva->row()->total;

        $query_lista = $this->db->query("SELECT tax_code, count(*) as total from
        	preco where
            cnpj_cliente = '{$cnpj}'  group by tax_code  order by total desc");

        $query_total_lista = $this->db->query("SELECT count(*) as total from
        	preco where
            cnpj_cliente = '{$cnpj}' order by total desc");

        $data['result_sem_iva'] = $query_sem_iva->result();
        $data['result_com_iva'] = $query_com_iva->result();
        $data['result_lista']   = $query_lista->result();
        $data['result_total_lista'] = $query_total_lista->row()->total;
        $data['result_total_sem_iva'] = $result_total_sem_iva;
        $data['result_total_com_iva'] = $result_total_com_iva;

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Monitor de NFe', '/monitor/');
        $this->breadcrumbs->push('Gráficos', '/monitor/graficos/');

        $this->render('monitor/graficos', $data);
    }

    public function download($chaveDocumento) {
        // RetornaDanfe
        $chaveDocumento = str_replace('NFe', '', $chaveDocumento);

        $this->load->library('receiverservicews');

        $input = new RetornaDanfe();
        $input->param = new RetornaDanfeParam();
        $input->param->Token = $this->receiverservicews->token;
        $input->param->ChaveDocumento = $chaveDocumento;

        try {
            $resultDanfe = $this->receiverservicews->RetornaDanfe($input);

            if (
                isset($resultDanfe->RetornaDanfeResult->WsError->WSError) &&
                $resultDanfe->RetornaDanfeResult->WsError->WSError->ErrorMsg
            ) {
                header('Content-Type: text/html; charset='.config_item('charset'));
                ?>
                <script type="text/javascript">
                    swal('Atenção', "Erro código #<?php echo $resultDanfe->RetornaDanfeResult->WsError->WSError->ErrorCode ?>\n<?php echo ($resultDanfe->RetornaDanfeResult->WsError->WSError->ErrorMsg) ?>", 'warning');
                    window.close();
                </script>
                <?php
                exit;
            } else {

                $fileToDownload = $resultDanfe->RetornaDanfeResult->PdfDanfeResult->FileBytes;
                $fileName = $resultDanfe->RetornaDanfeResult->PdfDanfeResult->FileName;

                header('Content-Description: File Transfer');
                header('Content-Type: application/octet-stream');
                header('Content-Disposition: attachment; filename='.$fileName);
                header('Content-Transfer-Encoding: binary');
                header('Expires: 0');
                header('Cache-Control: must-revalidate');
                header('Pragma: public');
                header('Content-Length: '. strlen($fileToDownload));

                echo $fileToDownload;
            }

        } catch (Exception $e) {
            show_404();
        }

    }

	public function index()
	{
        $data = array();

        $this->load->model('empresa_model');

        $empresa_entry = $this->empresa_model->get_entry(sess_user_company());

        if ($this->input->post('commit'))
        {
            $dt_inicio = explode("/", $this->input->post('dt_inicio'));
            $dt_inicio_us = $dt_inicio[2] . '-' . $dt_inicio[1] . '-' . $dt_inicio[0];

            $dt_fim = explode("/", $this->input->post('dt_fim'));
            $dt_fim_us = $dt_fim[2] . '-' . $dt_fim[1] . '-' . $dt_fim[0];

            $this->load->library('receiverservicews');

            $input = new ConsultaDocInconsist();
            $input->param = new ConsultaDocInconsistParam();
            $input->param->Token = $this->receiverservicews->token;
            $input->param->DataFim = $dt_fim_us;
            $input->param->DataInicio = $dt_inicio_us;
            $input->param->TipInfo = "A";

            if ($this->input->post('reprocessar')) {
                $input->param->Reprocessar = (int) $this->input->post('reprocessar');
            }


            if ($this->input->post('cod_emitente_ini')) {
                $input->param->CodEmitenteIni = $this->input->post('cod_emitente_ini');
            }

            if ($this->input->post('cod_emitente_fim')) {
                $input->param->CodEmitenteFim = $this->input->post('cod_emitente_fim');
            }

            if ($this->input->post('fluxo_documento')) {
                $input->param->FluxoDocumento = $this->input->post('fluxo_documento');
            } else {
                $input->param->FluxoDocumento = 4;
            }

            $input->param->CNPJEstab = $empresa_entry->cnpj;

            try {

                $output = $this->receiverservicews->ConsultaDocInconsist($input);

                if (isset($output->ConsultaDocInconsistResult->WsError->WSError->ErrorMsg)) {
                    $data['error'] = $output->ConsultaDocInconsistResult->WsError->WSError->ErrorMsg;
                } else
                {

                    if (!empty($output->ConsultaDocInconsistResult->DocInconsistResult)) {
                        $docInconsistResult = $output->ConsultaDocInconsistResult->DocInconsistResult->DocInconsistResult;
                    }

                    // Download da planilha
                    if ($this->input->post('download')) {

                        $this->load->library('xlsxwriter');

                        $this->xlsxwriter->setAuthor('Becomex Consulting');

                        $header = array(
                          'Número Documento'=>'string',
                          'Tipo da NF'=>'string',
                          'Data Emissão Documento'=>'date',
                          'CFOP'=>'string',
                          'Código do Produto'=>'string',
                          'Código do Item Interno'=>'string',
                          'Razão Social Cliente/Fornecedor'=>'string',
                          'Codigo Inconsistência'=>'int',
                          'Titulo Inconsistência'=>'string',
                          'Mensagem Inconsistencia'=>'string',
                        );

                        $data = array();
                        /*
                        <td><?php echo $produto->NumeroDocumento ?></td>
                        <td><?php echo get_fluxo_documento_by_id($produto->FluxoDocumento) ?>
                        <td><?php echo date("d/m/Y", strtotime($produto->DataEmissaoDocumento)) ?></td>
                        <td><?php echo isset($produto->NfeProdutoIncons) ? $produto->NfeProdutoIncons->CodFop : 'N/A' ?></td>
                        <td><?php echo $produto->CodProduto ?></td>
                        <td><?php echo ($produto->CodItemInterno ? $produto->CodItemInterno : 'N/A') ?></td>
                        <td><?php echo $produto->RazaoSocialEmitente ?></td>
                        <td><?php echo $produto->MsgInconsist ?></td> */
                        if (!empty($docInconsistResult))
                        {
                            foreach ($docInconsistResult as $item)
                            {
                                $data[] = array(
                                    (string) $item->NumeroDocumento,
                                    (string) get_fluxo_documento_by_id($item->FluxoDocumento),
                                    (string) date("Y-m-d", strtotime($item->DataEmissaoDocumento)),
                                    (string) (isset($item->NfeProdutoIncons) ? $item->NfeProdutoIncons->CodFop : 'N/A'),
                                    (string) $item->CodProduto,
                                    (string) ($item->CodItemInterno ? $item->CodItemInterno : 'N/A'),
                                    (string) $item->RazaoSocialEmitente,
                                    (int) $item->CodInconsist,
                                    (string) $item->TitInconsist,
                                    (string) html_entity_decode($item->MsgInconsist)
                                );
                            }
                        }

                        // var_dump($data);

                        $writer = new XLSXWriter();
                        $writer->writeSheet($data, 'MonitorNFe', $header);

                        // header('Content-Type: ')
                        $filename = 'GT-MONITOR-NFE-'.date('d-m-Y').'.xlsx';

                        header('Content-disposition: attachment; filename='.$filename);
                        header('Content-type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

                        echo $writer->writeToStdOut();

                        exit;
                    }

                    $data['inconsistencias'] = array();
                    $data['total_inconsistencias'] = 0;

                    if (!is_array($docInconsistResult)) {
                        $aux = $docInconsistResult;
                        $docInconsistResult = array($aux);
                    }

                    if (!empty($docInconsistResult)) {

                        $total_inconsistencias = 0;
                        $inconsistencias = array();
                        $graph           = array();

                        foreach ($docInconsistResult as $docInconsist)
                        {
                            /**
                             * Suprimimos o erro 102 (Código interno não cadastrado)
                             */
                            // if ($docInconsist->CodInconsist == 102) continue;

                            if (!isset($graph[$docInconsist->CodInconsist]['titInconsist'])) {
                                $graph[$docInconsist->CodInconsist] = array();
                                $graph[$docInconsist->CodInconsist]['titInconsist'] = $docInconsist->TitInconsist;
                                $graph[$docInconsist->CodInconsist]['produtos'] = array();
                            }

                            $graph[$docInconsist->CodInconsist]['produtos'][] = $docInconsist;

                            if (!isset($inconsistencias[$docInconsist->CodProduto]))
                            {
                                $inconsistencias[$docInconsist->CodProduto] = array();
                                $inconsistencias[$docInconsist->CodProduto]['inconsistencias'] = array();
                                $inconsistencias[$docInconsist->CodProduto]['info'] = $docInconsist;
                            }

                            $inconsistencias[$docInconsist->CodProduto]['inconsistencias'][] = $docInconsist;
                            $total_inconsistencias++;
                        }

                        $data['graph'] = $graph;
                        $data['inconsistencias'] = $inconsistencias;
                        $data['total_inconsistencias'] = $total_inconsistencias;
                        $data['resumo'] = $output->ConsultaDocInconsistResult->ResumoInconsistResult;
                    }

                }
            } catch (Exception $e) {
                $data['error'] = 'Erro processando dados do WebService: ' . $e->getMessage();
            }
        }

		$this->include_css('b3-datetimepicker.min.css');
		$this->include_js('b3-datetimepicker.min.js?v=3.1.1');
		$this->include_js('jquery.maskedinput.js');

        /*if ($this->input->post('submit')) {
            $data['show_monitor'] = TRUE;
        }*/

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Monitor de NFe', '/monitor/');

		$this->render('monitor/default', $data);
	}

}

