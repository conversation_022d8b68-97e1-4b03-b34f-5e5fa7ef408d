<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Cockpit extends MY_Controller
{
    public $homologacoes;

    public $statusExportacao = array("0", "1"); 
    public $statusImplementacao = array("I", "N", "R");

    public $statusItens;
    public $sla;
    public $dianaUtilizacao;
    public $dianaAcuracidade;
    public $atribuicao;
    public $ncmDivergentes;
    public $perguntasRespostas;

    public function __construct() {
        parent::__construct();

        $this->load->library('breadcrumbs');

        if (!is_logged()) {
            redirect('/login');
        }

        $this->load->model(array(
            'empresa_model',
            'cad_item_model',
            'item_model',
            'cockpit_model'
        ));

        $this->cad_item_model->set_namespace('cockpit');
        $this->cockpit_model->set_namespace('cockpit');
    }

    public function index()
    {
        $data = array();

        $data['has_diana'] = customer_can('has_diana', false, false);
        $data['eventos'] = $this->cad_item_model->getPacoteEventos();
        
        $this->title = "Cockpit";
        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Cockpit', '/cockpit/');

        $this->include_js(array(
            'bootstrap-select/bootstrap-select.js',
            'highcharts/highcharts.js'
        ));

        $this->include_css(array(
            'bootstrap-select/bootstrap-select.css'
        ));

        $this->render('cockpit/default', $data);
    }

    public function getEventoPacote()
    {
        try {
            $eventoPacote = $this->cad_item_model->getPacoteEventos();

            return response_json(array(
                'status' => 200,
                'data' => $eventoPacote
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }
    }

    public function getEstabelecimentos()
    {
        try {
            $estabelecimentos = $this->cad_item_model->getEstabelecimentos();

            return response_json(array(
                'status' => 200,
                'data' => $estabelecimentos
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }
    }

    /**
     * SESSÃO PARA OS DADOS DO GRÁFICO "Status Itens" 
     */

    public function getStatusItens()
    {
        try {
            $this->homologacoes = $this->empresa_model->get_homologacao_by_id_empresa(sess_user_company());

            $empresa = $this->empresa_model->get_entry(sess_user_company());
            $campos_adicionais = explode("|", $empresa->campos_adicionais);

            
            if (in_array('owner', $campos_adicionais)) {
                $this->getItensAguardandoDefinicaoResponsavel();
            }

            $this->getItensEmAnalise();
            $this->getItensRespondidos();
            $this->getItensInformacoes();

            if (in_array('owner', $campos_adicionais)) {
                $this->getItensRevisarInformacoesTecnicas();                    
                $this->getItensRevisarInformacoesERP();
                $this->getItensInformacoesErpRevisadas();
            }

            $this->getItensHomologacao();

            if (in_array('owner', $campos_adicionais)) {
                $this->getItensHomologadoEmRevisao();
            }
            
            $this->getItensRevisao();
            $this->getItensAprovados();
            $this->getItensReprovados();

            return response_json(array(
                'status' => 200,
                'data' => $this->statusItens
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }        
    }

    public function getItensEmAnalise()
    {
        // Itens que não estão na cad item e não possuem perguntas atreladas

        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
        $this->cockpit_model->set_state('filter.list_opt', 'sem_perguntas');
        
        $totalRows = $this->cockpit_model->getItensEmAnalise();

        $this->statusItens[] = array(
            'descricao'  => "Em Análise",
            'quantidade' => $totalRows,
        );
    }

    public function getItensRespondidos()
    {
        // Itens que não estão na cad item e possuem somente perguntas respondidas

        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
        $this->cockpit_model->set_state('filter.list_opt', 'perguntas_respondidas');
        
        $totalRows = $this->cockpit_model->getItensEmAnalise();

        $this->statusItens[] = array(
            'descricao'  => "Perguntas respondidas",
            'quantidade' => $totalRows,
        );
    }

    public function getItensRevisarInformacoesERP()
    {
        // Itens que não estão na cad item e possuem somente perguntas pendentes
        
        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
        $this->cockpit_model->set_state('filter.list_opt', 'revisar_informacoes_erp');
        
        $totalRows = $this->cockpit_model->getItensRevisarInformacoesERP();
        
        $this->statusItens[] = array(
            'descricao'  => "Revisar Informações ERP",
            'quantidade' => $totalRows,
        );
    }

    public function getItensInformacoes()
    {
        // Itens que não estão na cad item e possuem somente perguntas pendentes
        
        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
        $this->cockpit_model->set_state('filter.list_opt', 'pendente_informacoes');
        
        $totalRows = $this->cockpit_model->getItensEmInformacoesPendentes();
        
        $this->statusItens[] = array(
            'descricao'  => "Pendente de Informações",
            'quantidade' => $totalRows,
        );
    }

    public function getItensHomologacao()
    {
        $status = "homologar";

        $this->applyDefaultFiltersCadItem($status);

        $totalRows = $this->cad_item_model->get_total_entries($this->homologacoes);
        
        $this->statusItens[] = array(
            'descricao'  => "Em Homologação",
            'quantidade' => $totalRows,
        );
    }

    public function getItensRevisao()
    {
        $status = "revisao";
        $this->applyDefaultFiltersCadItem($status);

        $totalRows = $this->cad_item_model->get_total_entries($this->homologacoes);
        
        $this->statusItens[] = array(
            'descricao'  => "Em Revisão",
            'quantidade' => $totalRows,
        );
    }

    public function getItensAprovados()
    {
        $status = "homologado";
        $this->applyDefaultFiltersCadItem($status);

        $totalRows = $this->cad_item_model->get_total_entries($this->homologacoes);
        
        $this->statusItens[] = array(
            'descricao'  => "Aprovados",
            'quantidade' => $totalRows,
        );
    }

    public function getItensReprovados()
    {
        $status = "nao_homologado";
        $this->applyDefaultFiltersCadItem($status);

        $totalRows = $this->cad_item_model->get_total_entries($this->homologacoes);
        
        $this->statusItens[] = array(
            'descricao'  => "Reprovados",
            'quantidade' => $totalRows,
        );
    }

    public function applyDefaultFiltersCadItem($status)
    {

        /* Filtro - Evento/Pacote **/
        if ($this->input->get('evento') && count($this->input->get('evento')) > 0) {
            $evento = $this->input->get('evento');
            
            if (is_array($evento) && $evento[0] != "") {
                if (empty($evento[0])) {
                    unset($evento[0]);
                }
                $this->cad_item_model->set_state('filter.evento', $evento);
                $this->cockpit_model->set_state('filter.evento', $evento);
            } else {
                $this->cad_item_model->unset_state('filter.evento');
                $this->cockpit_model->unset_state('filter.evento');
            }
        } else {
            $this->cad_item_model->unset_state('filter.evento');
            $this->cockpit_model->unset_state('filter.evento');
        }

        /* Filtro - Status [Análise, Informações, Homologação, Revisão, Aprovado, Reprovado] **/
        $this->cad_item_model->unset_state('filter.list_opt');
        $this->cad_item_model->set_state('filter.list_opt', $status);
        $this->cad_item_model->set_state('filter.habilitar_pr', false);

        /* Filtro - Atribuído para todos **/
        $this->cad_item_model->set_state('filter.atribuido_para', '-1');

        /* Filtro - Status Exportação selecionados **/
        $this->cad_item_model->set_state('filter.status_exportacao', $this->statusExportacao);
        
        /* Filtro - Status Implementação selecionados **/
        $this->cad_item_model->set_state('filter.status_implementacao', $this->statusImplementacao);
        
        /* Filtro - Empresa do usuário logado **/
        $this->cad_item_model->set_state('filter.id_empresa', sess_user_company());

        /* Filtro - Estabelecimento **/
        $this->cad_item_model->unset_state('filter.estabelecimento');
        $this->cad_item_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));

        // $this->cad_item_model->set_state_store_session(TRUE);
        // $this->cad_item_model->restore_state_from_session('filter.', 'get');
    }

    /**
     * SESSÃO PARA OS DADOS DO GRÁFICO "SLA" 
     */

    public function getSLA()
    {
        try {
            $this->sla = array(
                array(
                    'descricao'  => 'sla',
                    'quantidade' => 0
                )
            );

            return response_json(array(
                'status' => 200,
                'data' => $this->sla
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }  
    }

    /**
     * SESSÃO PARA OS DADOS DO GRÁFICO "Diana Utilização" 
     */

    public function getDianaUtilizacao()
    {
        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));

        try {
            /* Itens que possuem predição **/
            $diana = $this->cockpit_model->getItemsPredicao(1);
            /* Itens que não possuem predição **/
            $manual = $this->cockpit_model->getItemsPredicao(0);
            /* Itens em análise (retroativos - antes de haver opções geradas pela Diana) **/

            $analise = $this->cockpit_model->getItemsRetroativosDiana();

            $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
            $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
            $this->cockpit_model->set_state('filter.list_opt', 'perguntas_respondidas');
            
            $perguntas_respondidas = $this->cockpit_model->getItensEmAnalise();

            $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
            $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
            $this->cockpit_model->set_state('filter.list_opt', 'pendente_informacoes');
            
            $pendente_informacoes = $this->cockpit_model->getItensEmInformacoesPendentes();
            $analise = $analise + $perguntas_respondidas + $pendente_informacoes;

            /* Itens que passaram pela Diana e foram homologados sem necessitar de confirmação do responsavel **/
            $itens_na = $this->cockpit_model->getItemsNa();

            $this->dianaUtilizacao = array(
                array(
                    'descricao'  => 'Diana',
                    'quantidade' => $diana
                ),
                array(
                    'descricao'  => 'Escolha Diferente Diana',
                    'quantidade' => $manual
                ),
                array(
                    'descricao'  => 'Pendente de Atribuição',
                    'quantidade' => $analise
                ),
                array(
                    'descricao'  => 'N/A',
                    'quantidade' => $itens_na
                )
            );

            return response_json(array(
                'status' => 200,
                'data' => $this->dianaUtilizacao
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        } 
    }

    /**
     * SESSÃO PARA OS DADOS DO GRÁFICO "Diana Acuracidade" 
     */

    public function getDianaAcuracidade()
    {
        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
        
        try {
            /* Itens que possuem predição, e a sugestão escolhida foi a primeira **/
            $primeiraSugestao = $this->cockpit_model->getItemsPredicao(1, 1);

            /* Itens que possuem predição, e a sugestão escolhida foi a segunda **/
            $segundaSugestao = $this->cockpit_model->getItemsPredicao(1, 2);
    
            /* Itens que possuem predição, e a sugestão escolhida foi a terceira **/
            $terceiraSugestao = $this->cockpit_model->getItemsPredicao(1, 3);
    
            /* Itens que não possuem predição **/
            $escolhaDiferenteDiana = $this->cockpit_model->getItemsPredicao(0);
            
            /* Itens em análise (retroativos - antes de haver opções geradas pela Diana) **/
            $analise = $this->cockpit_model->getItemsRetroativosDiana();

            $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
            $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
            $this->cockpit_model->set_state('filter.list_opt', 'perguntas_respondidas');
            
            $perguntas_respondidas = $this->cockpit_model->getItensEmAnalise();

            $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
            $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
            $this->cockpit_model->set_state('filter.list_opt', 'pendente_informacoes');
            
            $pendente_informacoes = $this->cockpit_model->getItensEmInformacoesPendentes();
            $semInformacao = $analise + $perguntas_respondidas + $pendente_informacoes;
    
            /* Itens que passaram pela Diana e foram homologados sem necessitar de confirmação do responsavel **/
            $itens_na = $this->cockpit_model->getItemsNa();

            $this->dianaAcuracidade = array(
                array(
                    'descricao'  => '1º Sugestão',
                    'quantidade' => $primeiraSugestao
                ),
                array(
                    'descricao'  => '2º Sugestão',
                    'quantidade' => $segundaSugestao
                ),
                array(
                    'descricao'  => '3º Sugestão',
                    'quantidade' => $terceiraSugestao
                ),
                array(
                    'descricao'  => 'Escolha Diferente Diana',
                    'quantidade' => $escolhaDiferenteDiana
                ),
                array(
                    'descricao'  => 'Sem Informação',
                    'quantidade' => $semInformacao
                ),
                array(
                    'descricao'  => 'N/A',
                    'quantidade' => $itens_na
                )
            );

            return response_json(array(
                'status' => 200,
                'data' => $this->dianaAcuracidade
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }
    }

    /**
     * SESSÃO PARA OS DADOS DO GRÁFICO "Atribuição" 
     */

    public function getAtribuicao()
    {
        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));

        try {
            $this->homologacoes = $this->empresa_model->get_homologacao_by_id_empresa(sess_user_company());
            /* Itens atribuídos - Itens que existem na tabela item, que também estão na cad_item **/
            $atribuido = $this->cockpit_model->getItemsAtribuidos(); 

            $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
            $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
            $this->cockpit_model->set_state('filter.list_opt', 'pendente_informacoes');
            
            $totalPendentes = $this->cockpit_model->getItensEmInformacoesPendentes();
            $this->cockpit_model->unset_state('filter.evento');
            $this->cockpit_model->unset_state('filter.estabelecimento');
            $this->cockpit_model->unset_state('filter.list_opt');

            $status = "homologado";
            $this->applyDefaultFiltersCadItem($status);
            $aprovados = $this->cad_item_model->get_total_entries($this->homologacoes);

            $status = "homologar";
            $this->applyDefaultFiltersCadItem($status);
            $homologacao = $this->cad_item_model->get_total_entries($this->homologacoes);

            $status = "nao_homologado";
            $this->applyDefaultFiltersCadItem($status);   
            $reprovados = $this->cad_item_model->get_total_entries($this->homologacoes);

            $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
            $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
            $this->cockpit_model->set_state('filter.list_opt', 'perguntas_respondidas');
            
            $perguntas_respondidas = $this->cockpit_model->getItensEmAnalise();

            $status = "revisao";
            $this->applyDefaultFiltersCadItem($status);
            
            $emRevisao = $this->cad_item_model->get_total_entries($this->homologacoes);
            $atribuido = $homologacao + $totalPendentes + $perguntas_respondidas + $aprovados + $reprovados;
           // $atribuido = $aprovados + $totalPendentes + $homologacao + $reprovados;

           $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
           $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
           $this->cockpit_model->set_state('filter.list_opt', 'sem_perguntas');
           
           $naoAtribuido = $this->cockpit_model->getItensEmAnalise();
           // $naoAtribuido = $this->cockpit_model->getItemsRetroativos(true); 

            $this->atribuicao = array(
                array(
                    'descricao'  => 'Atribuído',
                    'quantidade' => $atribuido
                ),
                array(
                    'descricao'  => 'Em Revisão',
                    'quantidade' => $emRevisao
                ),
                array(
                    'descricao'  => 'Não Atribuído',
                    'quantidade' => $naoAtribuido
                )
            );

            return response_json(array(
                'status' => 200,
                'data' => $this->atribuicao
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }
    }

    /**
     * SESSÃO PARA OS DADOS DO GRÁFICO "NCM Divergentes" 
     */

    public function getNcmDivergentes()
    {
        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));

        try {
            /* Ncm's divergentes **/
            $alteradas = $this->cockpit_model->getNcmDivergentes(true); 
            $ncm_alteradas = 0;
            $ncm_mantidas = 0;
            foreach ($alteradas as $alterada)
            {
                if (preg_replace("/[^0-9]/","", $alterada->ncm) != preg_replace("/[^0-9]/","", $alterada->ncm_proposto)  ) {
                    $ncm_alteradas++;
                } else {
                    $ncm_mantidas++;
                }
            }

            /* Ncm's não divergentes **/
            $mantida = $this->cockpit_model->getNcmDivergentes(false);
            $ncm_mantidas = $ncm_mantidas + $mantida;
            $empty = $this->cockpit_model->getEmptyNcm();

            $this->ncmDivergentes = array(
                array(
                    'descricao'  => 'Alterada',
                    'quantidade' => $ncm_alteradas
                ),
                array(
                    'descricao'  => 'Mantida',
                    'quantidade' => $ncm_mantidas
                ),
                array(
                    'descricao'  => 'NCM vazia',
                    'quantidade' => $empty
                )
            );

            return response_json(array(
                'status' => 200,
                'data' => $this->ncmDivergentes
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }
    }

    /**
     * SESSÃO PARA OS DADOS DO GRÁFICO "Perguntas e Respostas" 
     */

    public function getPerguntasRespostas()
    {
        $this->ctr_pendencias_pergunta_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));

        try {
            $perguntas = $this->ctr_pendencias_pergunta_model->getPerguntasByEmpresa();
            
            $pendentes = $perguntas->pendentes; 
    
            $respondidas = $perguntas->respondidas;

            $total = $perguntas->total;
            
            $this->perguntasRespostas = array(
                array(
                    'descricao'  => 'Pendentes',
                    'quantidade' => (int) $pendentes
                ),
                array(
                    'descricao'  => 'Respondidas',
                    'quantidade' => (int) $respondidas
                ),
                array(
                    'descricao'  => 'Total',
                    'quantidade' => (int) $total
                )
            );

            return response_json(array(
                'status' => 200,
                'data' => $this->perguntasRespostas
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'status' => 422,
                'data' => array(
                    'message' => $e->getMessage()
                )
            ), 422);
        }
    }

    public function getItensRevisarInformacoesTecnicas()
    {        
        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
        $this->cockpit_model->set_state('filter.list_opt', 'revisar_informacoes_tecnicas');
        
        $totalRows = $this->cockpit_model->getItensRevisarInformacoesTecnicas();
        
        $this->statusItens[] = array(
            'descricao'  => "Revisar Informações Técnicas",
            'quantidade' => $totalRows,
        );
    }

    public function getItensHomologadoEmRevisao()
    {        
        // $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        // $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
        // $this->cockpit_model->set_state('filter.list_opt', 'homologado_em_revisao');
        
        // $totalRows = $this->cockpit_model->getItensHomologadoEmRevisao();
        
        // $this->statusItens[] = array(
        //     'descricao'  => "Homologados em Revisão",
        //     'quantidade' => $totalRows,
        // );

        $status = "homologado_em_revisao";
        $this->applyDefaultFiltersCadItem($status);

        $totalRows = $this->cad_item_model->get_total_entries($this->homologacoes);
        
        $this->statusItens[] = array(
            'descricao'  => "Homologados em Revisão",
            'quantidade' => $totalRows,
        );
    }

    public function getItensInformacoesErpRevisadas()
    {
        // Itens que não estão na cad item e possuem somente perguntas pendentes
        
        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
        $this->cockpit_model->set_state('filter.list_opt', 'informacoes_erp_revisadas');
        
        $totalRows = $this->cockpit_model->getItensInformacoesErpRevisadas();
        
        $this->statusItens[] = array(
            'descricao'  => "Informações ERP Revisadas",
            'quantidade' => $totalRows,
        );
    }

    public function getItensAguardandoDefinicaoResponsavel()
    {
        // Itens que não estão na cad item e não possuem owner definido com status 13
        
        $this->cockpit_model->set_state('filter.evento', $this->input->get('evento'));
        $this->cockpit_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));
        $this->cockpit_model->set_state('filter.list_opt', 'aguardando_definicao_responsavel');
        
        $totalRows = $this->cockpit_model->getItensAguardandoDefinicaoResponsavel();
        
        $this->statusItens[] = array(
            'descricao'  => "Aguardando Definição Responsável",
            'quantidade' => $totalRows,
        );
    }
}

