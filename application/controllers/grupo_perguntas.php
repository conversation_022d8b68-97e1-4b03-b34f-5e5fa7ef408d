<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

class Grupo_perguntas extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }

        if (!has_role('sysadmin') && !has_role('consultor')) {
            show_permission();
        }

        $this->load->library('breadcrumbs');
        $this->load->library('pagination');

        $this->load->model(array(
            'ctr_grupo_model',
            'ctr_grupo_pergunta_model'
        ));

        $this->ctr_grupo_model->set_namespace('grupo_prguntas');
    }

    public function index()
    {
        $page  = $this->input->get('per_page');
        $limit = 15;
        
        $this->setStates();

        $data['gruposPerguntas'] = $this->ctr_grupo_model->getEntries($limit, ($page > 0 ? $page - 1 : 0) * $limit);

        $totalEntries = $this->ctr_grupo_model->getTotalEntries();
        $queryString  = '';

        $config['page_query_string'] = TRUE;
        $config['use_page_numbers']  = TRUE;
        $config['total_rows']        = $totalEntries;
        $config['base_url']          = base_url("grupo_perguntas/index?{$queryString}");
        $config['per_page']          = $limit;

        $this->pagination->initialize($config);

        $data['pagination'] = $this->pagination->create_links();
        
        $this->title = "Grupo de Perguntas";

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Grupo de Perguntas', '/grupo_perguntas/');

        $this->include_js(array(
            'bootstrap-select/bootstrap-select.js',
            'bootstrap-notify.min.js',
            'jquery.cookie.js',
            'sweetalert.min.js',
        ));

        $this->include_css(array(
            'bootstrap-select/bootstrap-select.css',
            'animate.css',
            'sweetalert.css'
        ));

        $this->render('grupo_perguntas/default', $data);
    }

    public function setStates()
    {
        $unsetFilters = $this->input->is_set('unsetFilters'); 
        $nome = $this->input->is_set('nome');
        $ncm = $this->input->is_set('ncm');

        if ($unsetFilters) {
            $this->ctr_grupo_model->set_state_store_session(TRUE);
            $this->ctr_grupo_model->clear_states();
        } else {
            $this->ctr_grupo_model->set_state_store_session(TRUE);
            $this->ctr_grupo_model->restore_state_from_session('filter.', 'post');
        }

        if ($nome) {
            $this->ctr_grupo_model->set_state('filter.nome', $this->input->post('nome'));
        }

        if ($ncm) {
            $this->ctr_grupo_model->set_state('filter.ncm', $this->input->post('ncm'));
        }
    }  
    
    public function delete()
    {
        $idArray = $this->input->post('grupoPergunta'); 

        if (!empty($idArray)) {
            foreach ($idArray as $id) {
                $delete[] = $this->ctr_grupo_model->delete($id);

                $this->ctr_grupo_pergunta_model->deleteByGroup($id);
            }
        }

        if (!in_array(false, $delete)) {
            $this->session->set_flashdata('deleted', 'true');
        } else {
            $this->session->set_flashdata('deleted', 'false');
        }

        redirect("/grupo_perguntas");
    }

    public function editar($id = null)
    {
        $data = array();

        $data['title'] = "Editar grupo de pergunta"; 

        if (!empty($id)) {
            $data['grupo'] = json_encode($this->ctr_grupo_model->getEntry($id));
        }

        $post = json_decode(file_get_contents('php://input'),true); 

        if (!empty($post)) {
            $this->ctr_grupo_model->update($post['id'], array(
                "nome" => $post['nome'],
                "ncm"  => !empty($post['ncm']) ? $post['ncm'] : ""
            ));

            return true;
        }

        $this->title = "Grupo de Perguntas &gt; Editar";

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Grupo de Perguntas', '/grupo_perguntas/');
        $this->breadcrumbs->push('Editar grupo de pergunta', '/grupo_perguntas/edit');

        $this->include_js(array(
            'bootstrap-select/bootstrap-select.js',
            'bootstrap-notify.min.js',
            'jquery.cookie.js',
            'sweetalert.min.js',
        ));

        $this->include_css(array(
            'bootstrap-select/bootstrap-select.css',
            'animate.css',
            'sweetalert.css'
        ));

        $this->render('grupo_perguntas/addedit', $data);
    }

    public function novo()
    {
        $data = array();

        $data['title'] = "Novo grupo de pergunta"; 

        $post = json_decode(file_get_contents('php://input'),true); 

        if (!empty($post)) {
            $idGrupo = $this->ctr_grupo_model->save(array(
                "nome"       => $post['nome'],
                "ncm"        => !empty($post['ncm']) ? $post['ncm'] : "",
                "id_usuario" => sess_user_id()
            ));

            return response_json(array(
                "idGrupo"  => $idGrupo
            ));
        }

        $this->title = "Grupo de Perguntas &gt; Novo";

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Grupo de Perguntas', '/grupo_perguntas/');
        $this->breadcrumbs->push('Novo grupo de perguntas', '/grupo_perguntas/create');

        $this->include_js(array(
            'bootstrap-select/bootstrap-select.js',
            'bootstrap-notify.min.js',
            'jquery.cookie.js',
            'sweetalert.min.js',
        ));

        $this->include_css(array(
            'bootstrap-select/bootstrap-select.css',
            'animate.css',
            'sweetalert.css'
        ));

        $this->render('grupo_perguntas/addedit', $data);
    }
}