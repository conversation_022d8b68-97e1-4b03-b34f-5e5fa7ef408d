<?php

class <PERSON><PERSON>z extends MY_Controller {

    public function __construct( ) {
        parent::__construct();

		if (!is_logged()) {
			redirect('/login');
		}

        setlocale(LC_MONETARY, 'pt_BR');
    }

    public function index() {
        $data = array();

        if ($this->input->get('dt_inicio') && $this->input->get('dt_inicio')) {

            $dt_inicio = explode("/", $this->input->get('dt_inicio'));
            $dt_inicio_us = $dt_inicio[2] . '-' . $dt_inicio[1] . '-' . $dt_inicio[0];

            $dt_fim = explode("/", $this->input->get('dt_fim'));
            $dt_fim_us = $dt_fim[2] . '-' . $dt_fim[1] . '-' . $dt_fim[0];

            $this->load->library('receiverservicews');

            $input = new ConsultaDocSefaz();
            $input->param = new ConsultaDocSefazParam();
            $input->param->Token = $this->receiverservicews->token;
            $input->param->DataFim = $dt_fim_us;
            $input->param->DataInicio = $dt_inicio_us;

            $output = $this->receiverservicews->ConsultaDocSefaz($input);

            $docSefazResult = $output->ConsultaDocSefazResult->DocSefazResult->DocSefazResult;

            $data['docSefazResult'] = $docSefazResult;
        }

		$this->include_css('b3-datetimepicker.min.css');
		$this->include_js('b3-datetimepicker.min.js?v=3.1.1');
		$this->include_js('jquery.maskedinput.js');

		$this->render('sefaz/default', $data);
    }

}
