<?php

namespace App\Exceptions;

// application/exceptions/FilterManagerException.php
defined('BASEPATH') || exit('No direct script access allowed');

use Exception;

class FilterManagerException extends Exception
{
    protected $payload;

    public function __construct($message, $payload = null, $code = 0, Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
        $this->payload = $payload;
    }

    public function getPayload()
    {
        return $this->payload;
    }
}
