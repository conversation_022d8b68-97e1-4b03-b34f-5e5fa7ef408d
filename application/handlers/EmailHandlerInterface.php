<?php

namespace App\Handlers;

// application/handlers/EmailHandlerInterface.php
defined('BASEPATH') || exit('No direct script access allowed');

/**
 * Interface para handlers de email.
 * Define o contrato que todos os handlers de email devem seguir.
 */
interface EmailHandlerInterface
{
    /**
     * Processa o payload do email.
     *
     * @param array $payload Os dados necessários para processar o email
     * @throws EmailQueueException Se houver erro no processamento
     * @return bool True se processado com sucesso
     */
    public function handle(array $payload);

    /**
     * Valida se o payload contém os dados necessários.
     *
     * @param array $payload
     * @return bool
     */
    public function validatePayload(array $payload);

    /**
     * Retorna o tipo de email que este handler processa.
     *
     * @return string
     */
    public function getType();
}
