<?php

namespace App\Handlers;

// application/handlers/HomologacaoMassaHandler.php
defined('BASEPATH') || exit('No direct script access allowed');

use Exception;
use App\Handlers\EmailHandlerInterface;
use App\Exceptions\EmailQueueException;

/**
 * Handler específico para homologação em massa.
 *
 * Esta classe implementa a lógica específica para processar jobs
 * de homologação de itens em massa de forma assíncrona.
 */
class HomologacaoMassaHandler implements EmailHandlerInterface
{
    /**
     * The CodeIgniter super-object.
     *
     * @var CI_Controller & object{load: CI_Loader}
     */
    protected $CI;

    public function __construct()
    {
        $this->CI = &get_instance();
        $this->CI->load->model([
            'log_wf_atributos_model',
            'cad_item_wf_atributo_model'
        ]);
        $this->CI->load->library('email');
    }

    /**
     * {@inheritdoc}
     */
    public function handle(array $payload)
    {
        if (!$this->validatePayload($payload)) {
            throw new EmailQueueException('Payload inválido para o tipo ' . $this->getType());
        }

        try {
            // Processa a homologação
            $this->processarHomologacao($payload);

            // Envia email de notificação de conclusão
            /** Retirado o envio de email com aviso para o usuário logado, a pedido de Douglas. */
            // $this->enviarEmailHomologacaoConcluida($payload);

            return true;
        } catch (Exception $e) {
            // Em caso de erro, envia email de falha
            $this->enviarEmailHomologacaoFalha($payload, $e->getMessage());
            throw new EmailQueueException('Erro ao processar homologação em massa: ' . $e->getMessage());
        }
    }

    /**
     * {@inheritdoc}
     */
    public function validatePayload(array $payload)
    {
        $required_fields = [
            'ids_validos',
            'status_novo',
            'id_empresa',
            'id_usuario',
            'justificativa',
            'user_email'
        ];

        // Verifica se todos os campos obrigatórios existem
        foreach ($required_fields as $field) {
            if (!isset($payload[$field])) {
                return false;
            }
        }

        // Validações específicas
        return is_array($payload['ids_validos'])
            && !empty($payload['ids_validos'])
            && is_array($payload['status_novo'])
            && isset($payload['status_novo']['slug'])
            && isset($payload['status_novo']['id'])
            && isset($payload['status_novo']['desc'])
            && is_numeric($payload['id_empresa'])
            && is_numeric($payload['id_usuario'])
            && filter_var($payload['user_email'], FILTER_VALIDATE_EMAIL);
    }

    /**
     * {@inheritdoc}
     */
    public function getType()
    {
        return 'homologacao_massa';
    }

    /**
     * Processa a homologação dos itens.
     */
    private function processarHomologacao($payload)
    {
        $this->CI->cad_item_wf_atributo_model->set_status(
            $payload['status_novo']['slug'],
            $payload['ids_validos'],
            null,
            null,
            $payload['id_empresa'],
            null
        );

        $this->CI->log_wf_atributos_model->registrar_log(
            $payload['ids_validos'],
            null,
            null,
            $payload['id_empresa'],
            $payload['status_novo']['id'],
            'movimentacao_em_massa',
            $payload['id_usuario'],
            $payload['justificativa']
        );
    }

    /**
     * Envia email de notificação de homologação concluída.
     */
    private function enviarEmailHomologacaoConcluida($payload)
    {
        $total_itens = count($payload['ids_validos']);

        $email_data = [
            'base_url' => config_item('online_url'),
            'usuario_nome' => $payload['user_nome'] ?? 'Usuário',
            'total_itens' => $total_itens,
            'status_novo' => $payload['status_novo']['desc'],
            'justificativa' => $payload['justificativa'],
            'data_processamento' => date('d/m/Y H:i:s')
        ];

        $body = $this->CI->load->view('templates/homologacao_massa_concluida', $email_data, true);

        $this->CI->email->from(config_item('mail_from_addr'), config_item('mail_from_name'));
        $this->CI->email->to($payload['user_email']);
        $this->CI->email->subject('[Gestão Tarifária] - Homologação em massa concluída');
        $this->CI->email->message($body);

        $this->CI->email->send();
    }

    /**
     * Envia email de notificação de falha na homologação.
     */
    private function enviarEmailHomologacaoFalha($payload, $erro)
    {
        $total_itens = count($payload['ids_validos']);

        $email_data = [
            'base_url' => config_item('online_url'),
            'usuario_nome' => $payload['user_nome'] ?? 'Usuário',
            'total_itens' => $total_itens,
            'erro' => $erro,
            'data_processamento' => date('d/m/Y H:i:s')
        ];

        $body = $this->CI->load->view('templates/homologacao_massa_falha', $email_data, true);

        $this->CI->email->from(config_item('mail_from_addr'), config_item('mail_from_name'));
        $this->CI->email->to($payload['user_email']);
        $this->CI->email->subject('[Gestão Tarifária] - Falha na homologação em massa');
        $this->CI->email->message($body);

        $this->CI->email->send();
    }
}
