<?php

namespace App\Handlers;

// application/handlers/ExportacaoPlanilhaHandler.php
defined('BASEPATH') || exit('No direct script access allowed');

use Exception;
use App\Handlers\EmailHandlerInterface;
use App\Exceptions\EmailQueueException;

/**
 * Handler específico para exportação de planilhas.
 *
 * Esta classe implementa a lógica específica para processar jobs
 * de exportação de planilhas de forma assíncrona.
 */
class ExportacaoPlanilhaHandler implements EmailHandlerInterface
{
    /**
     * The CodeIgniter super-object.
     *
     * @var CI_Controller & object{load: CI_Loader, email: CI_Email}
     */
    protected $CI;

    public function __construct()
    {
        $this->CI = &get_instance();
        $this->CI->load->model('cad_item_model');
        $this->CI->load->model('usuario_model');
        $this->CI->load->library('email');
    }

    /**
     * {@inheritdoc}
     */
    public function handle(array $payload)
    {
        if (!$this->validatePayload($payload)) {
            throw new EmailQueueException('Payload inválido para o tipo ' . $this->getType());
        }

        try {
            // 1. Aplicar filtros salvos na sessão
            $this->aplicarFiltros($payload);
            
            // 2. Gerar planilha
            $filePath = $this->gerarPlanilha($payload);
            
            // 3. Enviar email com link de download
            $this->enviarEmailComLink($payload, $filePath);
            
            return true;
            
        } catch (Exception $e) {
            throw new EmailQueueException('Erro ao processar exportação de planilha: ' . $e->getMessage());
        }
    }

    /**
     * {@inheritdoc}
     */
    public function validatePayload(array $payload)
    {
        $required_fields = ['user_id', 'user_email', 'export_type'];
        
        foreach ($required_fields as $field) {
            if (!isset($payload[$field]) || empty($payload[$field])) {
                return false;
            }
        }

        // Validar tipo de exportação suportado
        $supported_types = ['homologacao', 'atributos', 'lessin'];
        if (!in_array($payload['export_type'], $supported_types)) {
            return false;
        }

        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function getType()
    {
        return 'exportacao_planilha';
    }

    /**
     * Aplica os filtros salvos para a exportação.
     * 
     * @param array $payload
     */
    private function aplicarFiltros($payload)
    {
        if (isset($payload['filters']) && is_array($payload['filters'])) {
            foreach ($payload['filters'] as $key => $value) {
                $this->CI->cad_item_model->set_state($key, $value);
            }
        }
    }

    /**
     * Gera a planilha baseada no tipo de exportação.
     * 
     * @param array $payload
     * @return string Caminho do arquivo gerado
     */
    private function gerarPlanilha($payload)
    {
        switch ($payload['export_type']) {
            case 'homologacao':
                return $this->gerarPlanilhaHomologacao($payload);
            
            case 'atributos':
                return $this->gerarPlanilhaAtributos($payload);
                
            default:
                throw new EmailQueueException('Tipo de exportação não suportado: ' . $payload['export_type']);
        }
    }

    /**
     * Gera planilha de homologação.
     * 
     * @param array $payload
     * @return string
     */
    private function gerarPlanilhaHomologacao($payload)
    {
        // Reutilizar a lógica do método exportar() da homologação
        // mas salvando o arquivo ao invés de fazer download
        
        $fileName = 'Homologacao_' . date('Y-m-d_H-i-s') . '.xlsx';
        $filePath = config_item('upload_anexos_path') . $fileName;
        
        // Aqui você moveria toda a lógica do método exportar()
        // Por brevidade, vou simular a geração
        
        require_once APPPATH . 'libraries/XlsxGenerator/XlsxGenerator.php';
        $relatorio = new XlsxGenerator();
        $writer = $relatorio->init();
        
        // ... toda a lógica de geração da planilha ...
        
        // Salvar arquivo ao invés de fazer download
        $relatorio->save($filePath);
        
        return $filePath;
    }

    /**
     * Gera planilha de atributos.
     *
     * @param array $payload
     * @return string
     */
    private function gerarPlanilhaAtributos($payload)
    {
        // Implementar lógica específica para atributos
        $fileName = 'Atributos_' . date('Y-m-d_H-i-s') . '.xlsx';
        $filePath = config_item('upload_anexos_path') . $fileName;
        
        // ... lógica de geração ...
        
        return $filePath;
    }

    /**
     * Envia email com link para download da planilha.
     *
     * @param array $payload
     * @param string $filePath
     */
    private function enviarEmailComLink($payload, $filePath)
    {
        $usuario = $this->CI->usuario_model->get_entry($payload['user_id']);
        $fileName = basename($filePath);
        $downloadUrl = config_item('online_url') . '/downloads/planilhas/' . $fileName;
        
        $html_message = $this->construirMensagemEmail($usuario, $payload, $downloadUrl);
        
        $temp_data = [
            'base_url' => config_item('online_url'),
            'html_message' => $html_message
        ];

        $body = $this->CI->load->view('templates/basic_template', $temp_data, TRUE);
        
        $this->CI->email->from(config_item('mail_from_addr'), config_item('mail_from_name'));
        $this->CI->email->to($payload['user_email']);
        $this->CI->email->subject('[Gestão Tarifária] - Planilha pronta para download');
        $this->CI->email->message($body);
        
        $this->CI->email->send();
    }

    /**
     * Constrói a mensagem HTML do email.
     *
     * @param object $usuario
     * @param array $payload
     * @param string $downloadUrl
     * @return string
     */
    private function construirMensagemEmail($usuario, $payload, $downloadUrl)
    {
        $exportType = ucfirst($payload['export_type']);
        
        return "
            <h3>Exportação de {$exportType} Concluída</h3>
            <br>
            <p>Olá, {$usuario->nome}!</p>
            <p>Sua exportação de {$exportType} foi processada com sucesso.</p>
            <div class='panel panel-default'>
                <div class='panel-body' style='padding: 20px;border-left: 3px solid #ccc;background: #FBFBFB;'>
                    <p><strong>Tipo:</strong> {$exportType}</p>
                    <p><strong>Data de geração:</strong> " . date('d/m/Y H:i:s') . "</p>
                    <p><strong>Status:</strong> Pronta para download</p>
                </div>
            </div>
            <p style='text-align: center; margin: 20px 0;'>
                <a href='{$downloadUrl}' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>
                    Baixar Planilha
                </a>
            </p>
            <p><small>Este link estará disponível por 7 dias.</small></p>
        ";
    }
}
