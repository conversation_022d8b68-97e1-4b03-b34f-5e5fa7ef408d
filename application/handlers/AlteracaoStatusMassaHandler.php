<?php

namespace App\Handlers;

// application/handlers/AlteracaoStatusMassaHandler.php
defined('BASEPATH') || exit('No direct script access allowed');

use Exception;
use App\Services\CadItemWfAtributoService;
use App\Exceptions\EmailQueueException;

/**
 * Handler específico para emails de alteração de status em massa.
 *
 * Esta classe implementa a lógica específica para processar emails
 * relacionados à alteração de status de itens em massa.
 */
class AlteracaoStatusMassaHandler implements EmailHandlerInterface
{
    /**
     * The CodeIgniter super-object.
     *
     * @var CI_Controller & object{load: CI_Loader, caditemwfatributoservice: CadItemWfAtributoService}
     */
    protected $CI;

    /**
     * @var CadItemWfAtributoService
     */
    protected $service;

    public function __construct()
    {
        $this->CI = &get_instance();
        $this->CI->load->service('CadItemWfAtributoService');
        $this->service = $this->CI->caditemwfatributoservice;
    }

    /**
     * {@inheritdoc}
     */
    public function handle(array $payload)
    {
        if (!$this->validatePayload($payload)) {
            throw new EmailQueueException('Payload inválido para o tipo ' . $this->getType());
        }

        try {
            $this->service->enviarAlteracaoStatusMassa(
                $payload['item_ids'],
                $payload['id_empresa'],
                $payload['status_id']
            );

            return true;
        } catch (Exception $e) {
            throw new EmailQueueException('Erro ao processar alteração de status em massa: ' . $e->getMessage());
        }
    }

    /**
     * {@inheritdoc}
     */
    public function validatePayload(array $payload)
    {
        $required_fields = ['item_ids', 'id_empresa', 'status_id'];

        // Verifica se todos os campos obrigatórios existem e não estão vazios
        foreach ($required_fields as $field) {
            if (!isset($payload[$field]) || empty($payload[$field])) {
                return false;
            }
        }

        // Combina todas as validações específicas em uma única expressão
        return is_array($payload['item_ids'])
            && is_numeric($payload['id_empresa'])
            && is_numeric($payload['status_id']);
    }

    /**
     * {@inheritdoc}
     */
    public function getType()
    {
        return 'alteracao_status_massa';
    }
}
