<?php

// application/core/MY_Loader.php
defined('BASEPATH') || exit('No direct script access allowed');

/**
 * Extensão do Loader do CodeIgniter para suporte a namespaces.
 *
 * Esta classe adiciona suporte básico a namespaces para handlers e exceptions
 * mantendo compatibilidade com o CodeIgniter 2.
 */
class MY_Loader extends CI_Loader
{
    /**
     * Autoloader personalizado para namespaces do projeto.
     *
     * @param string $class Nome completo da classe com namespace
     * @return bool
     */
    public static function autoload($class)
    {
        // Mapear namespaces para diretórios
        $namespace_map = [
            'App\\Handlers\\' => APPPATH . 'handlers/',
            'App\\Exceptions\\' => APPPATH . 'exceptions/',
            'App\\Services\\' => APPPATH . 'services/',
        ];

        foreach ($namespace_map as $namespace => $directory) {
            if (strpos($class, $namespace) === 0) {
                $class_name = substr($class, strlen($namespace));
                $file_path = $directory . $class_name . '.php';

                if (file_exists($file_path)) {
                    require_once $file_path;
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Registra o autoloader personalizado.
     */
    public static function register_autoloader()
    {
        spl_autoload_register([__CLASS__, 'autoload']);
    }

    /**
     * Service Loader com suporte a namespaces.
     *
     * Sobrescreve o método service() para usar o autoloader.
     *
     * @param string|array $service Service name or array of service names
     * @param array $params Optional parameters to pass to the service constructor
     * @param string $object_name Optional object name to assign the service to
     * @return CI_Loader|FALSE
     */
    public function service($service = '', $params = null, $object_name = null)
    {
        if (is_array($service)) {
            foreach ($service as $class) {
                $this->service($class, $params);
            }
            return $this;
        }

        if ($service === '' || isset($this->_base_classes[$service])) {
            return false;
        }

        if (!is_null($params) && !is_array($params)) {
            $params = null;
        }

        // Definir o caminho do arquivo do serviço
        $filepath = APPPATH . 'services/' . $service . '.php';

        // Verificar se o arquivo existe
        if (!file_exists($filepath)) {
            log_message('error', "Unable to load the requested service: " . $service);
            show_error("Unable to load the requested service: " . $service);
        }

        // Incluir o arquivo do serviço
        require_once $filepath;

        // Tentar primeiro com namespace, depois sem namespace
        $namespaced_class = "App\\Services\\{$service}";
        $regular_class = $service;

        $class = null;
        if (class_exists($namespaced_class)) {
            $class = $namespaced_class;
        } elseif (class_exists($regular_class)) {
            $class = $regular_class;
        }

        // Verificar se a classe foi encontrada
        if ($class === null) {
            log_message('error', "Non-existent service class: " . $service . " (tried both namespaced and regular)");
            show_error("Non-existent service class: " . $service);
        }

        // Definir o nome da variável para o service
        if (is_null($object_name)) {
            $classvar = strtolower($service);
        } else {
            $classvar = $object_name;
        }

        // Instanciar o service
        $CI = &get_instance();
        if ($params !== null) {
            $CI->$classvar = new $class($params);
        } else {
            $CI->$classvar = new $class();
        }

        // Registrar o service carregado
        $this->_ci_classes[strtolower($service)] = $classvar;

        log_message('debug', "Service loaded: " . $class);

        return $this;
    }

    /**
     * Handler Loader com suporte a namespaces.
     *
     * Sobrescreve o método handler() para usar o autoloader.
     *
     * @param string|array $handler Handler name or array of handler names
     * @param array $params Optional parameters to pass to the handler constructor
     * @param string $object_name Optional object name to assign the handler to
     * @return CI_Loader|FALSE
     */
    public function handler($handler = '', $params = null, $object_name = null)
    {
        if (is_array($handler)) {
            foreach ($handler as $class) {
                $this->handler($class, $params);
            }
            return $this;
        }

        if ($handler === '' || isset($this->_base_classes[$handler])) {
            return false;
        }

        if (!is_null($params) && !is_array($params)) {
            $params = null;
        }

        // Tentar carregar com namespace primeiro
        $namespaced_class = "App\\Handlers\\{$handler}";

        if (class_exists($namespaced_class)) {
            $class = $namespaced_class;
        } else {
            // Fallback para o método original
            $filepath = APPPATH . 'handlers/' . $handler . '.php';

            if (!file_exists($filepath)) {
                log_message('error', "Unable to load the requested handler: " . $handler);
                show_error("Unable to load the requested handler: " . $handler);
            }

            require_once $filepath;
            $class = $handler;
        }

        // Verificar se a classe existe
        if (!class_exists($class)) {
            log_message('error', "Non-existent handler class: " . $class);
            show_error("Non-existent handler class: " . $class);
        }

        // Verificar se implementa a interface necessária
        $interfaces = class_implements($class);
        $required_interface = 'App\\Handlers\\EmailHandlerInterface';

        if (!in_array($required_interface, $interfaces) && !in_array('EmailHandlerInterface', $interfaces)) {
            log_message('error', "Handler class must implement EmailHandlerInterface: " . $class);
            show_error("Handler class must implement EmailHandlerInterface: " . $class);
        }

        // Definir o nome da variável para o handler
        if (is_null($object_name)) {
            $classvar = strtolower($handler);
        } else {
            $classvar = $object_name;
        }

        // Instanciar o handler
        $CI = &get_instance();
        if ($params !== null) {
            $CI->$classvar = new $class($params);
        } else {
            $CI->$classvar = new $class();
        }

        // Registrar o handler carregado
        $this->_ci_classes[strtolower($handler)] = $classvar;

        log_message('debug', "Handler loaded: " . $class);

        return $this;
    }
}
