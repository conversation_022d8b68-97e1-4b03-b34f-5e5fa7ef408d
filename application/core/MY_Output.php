<?php

/*
 * Custom router function v 0.2
 *
 * Add functionality : read into more than one sub-folder
 *
 */

Class MY_Output extends CI_Output
{
    Function __construct()
    {
        parent::__construct();

        // $this->headers[] = array('Expect-CT: max-age=86400, enforce, report-uri="https://qa.gestaotarifaria.com.br/report"', true);
        // $this->headers[] = array('Feature-Policy: microphone "none"; geolocation "none"; fullscreen "none"; camera "none";', true);
        // $this->headers[] = array('Referrer-Policy: strict-origin-when-cross-origin', true);
        // $this->headers[] = array("Content-Security-Policy: default-src * 'unsafe-inline' 'unsafe-hashes' 'unsafe-eval'; script-src * 'unsafe-inline' 'unsafe-hashes' 'unsafe-eval'; script-src-elem 'self' 'unsafe-inline' 'unsafe-hashes' 'unsafe-eval'; script-src-attr * 'unsafe-inline' 'unsafe-hashes' 'unsafe-eval'; style-src * 'unsafe-inline' 'unsafe-hashes'; style-src-elem * 'unsafe-inline' 'unsafe-hashes'; style-src-attr * 'unsafe-inline' 'unsafe-hashes'", true);
        // $this->headers[] = array('X-Permitted-Cross-Domain-Policies: none', true);
    }
}