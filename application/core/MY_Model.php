<?php
class MY_Model extends CI_Model {

    private $states = array();
    public $state_prefix = 'state';
    public $state_store_session = FALSE;
    public $ns = ''; // Namespace
    public $dbora;

    public function __construct() {
        parent::__construct();

        $this->load->library('session');
        $this->dbora = $this->load->database('oracle', TRUE);
    }

    public function alter_ora_sess() {
        $this->dbora->query("ALTER SESSION SET NLS_LANGUAGE = 'AMERICAN' NLS_TERRITORY='AMERICA'");
        $this->dbora->query("ALTER SESSION SET NLS_DATE_FORMAT = 'yyyy-mm-dd'");
        $this->dbora->query("ALTER SESSION SET NLS_TIMESTAMP_FORMAT = 'yyyy-mm-dd\"T\"hh24:mi:ss.ff3\"Z\"'");
    }

    public function set_namespace($namespace) {
        $this->ns = $namespace;
    }

    public function get_state_prefix() {
        $state_prefix = strtolower(get_class($this)) . '_' . $this->state_prefix;
        $state_prefix .= $this->ns ? "_{$this->ns}" : "";
        return $state_prefix . '.';
    }

    public function save($data, $update_where = array())
    {
        // Set @user_id if user_id is available in session
        if ($this->session->userdata('user_id')) {
            $id_usuario = $this->session->userdata('user_id');
            $this->db->query("SET @user_id = " . $id_usuario);
        }
        
        if (count($update_where) > 0) {
            return $this->db->update($this->_table, $data, $update_where);
        } else {
            $this->db->insert($this->_table, $data);

            return $this->db->insert_id();
        }
    }

    public function set_state_store_session($bool)
    {
        $this->state_store_session = (boolean) $bool;
    }

    public function restore_state_from_session($filter_prefix = 'filter.', $method = 'get')
    {
        $sessdata = $this->session->all_userdata();

        foreach ($sessdata as $key => $value) {
            $querystr_key = str_replace($this->get_state_prefix() . $filter_prefix, '', $key);

            if ($method == 'get')
            {
                if (preg_match('/' . $this->state_prefix . '/i', $key) && !isset($_GET[$querystr_key])) {
                    $_GET[$querystr_key] = $value;
                }
            }else if ($method == 'post')
            {
                if (preg_match('/' . $this->state_prefix . '/i', $key) && !isset($_POST[$querystr_key])) {
                    $_POST[$querystr_key] = $value;
                }
            }
        }
    }

    public function set_state($name, $value)
    {
        $this->states[$name] = $value;

        if ($value && $this->state_store_session) {
            $this->session->set_userdata($this->get_state_prefix() . $name, $value);
        }
    }

    public function unset_state($name, $prefix = null)
    {

        if (! $prefix)
            $prefix = $this->get_state_prefix();
        else
            $prefix = $prefix . '.';

        if ($this->state_store_session) {
            $this->session->unset_userdata($prefix . $name);
        }

        if (isset($this->states[$name])) {
            unset($this->states[$name]);
        }
    }

    public function get_state($name, $default_value = NULL) {

        if ($this->state_store_session && $this->session->userdata($this->get_state_prefix() . $name)) {
            // echo "<pre>";
            // var_dump($this->get_state_prefix() . $name);
            // echo "</pre>";

            return $this->session->userdata($this->get_state_prefix() . $name);
        }

        if (isset($this->states[$name])) {
            return $this->states[$name];
        }

        return $default_value;
    }

    public function clear_states() {
        unset($this->states);
        $this->states = array();

        if ($this->state_store_session) {
            $sessdata = $this->session->all_userdata();

            foreach ($sessdata as $key => $value)
            {
                if (preg_match('/' . $this->get_state_prefix() . '/i', $key)) {
                    $this->session->unset_userdata($key);
                }
            }
        }
    }
}
