<?php

// application/core/bootstrap.php
defined('BASEPATH') || exit('No direct script access allowed');

/**
 * Bootstrap para inicialização de recursos customizados.
 *
 * Este arquivo é carregado automaticamente e configura:
 * - Autoloader para namespaces
 * - Configurações globais do projeto
 */

// Registrar autoloader personalizado
if (class_exists('MY_Loader')) {
    MY_Loader::register_autoloader();
}

// Configurações globais do projeto
if (!function_exists('project_autoload')) {
    /**
     * Autoloader global para classes do projeto.
     *
     * @param string $class
     */
    function project_autoload($class)
    {
        // Mapear namespaces para diretórios
        $namespace_map = [
            'App\\Handlers\\' => APPPATH . 'handlers/',
            'App\\Exceptions\\' => APPPATH . 'exceptions/',
            'App\\Services\\' => APPPATH . 'services/',
        ];

        foreach ($namespace_map as $namespace => $directory) {
            if (strpos($class, $namespace) === 0) {
                $class_name = substr($class, strlen($namespace));
                $file_path = $directory . $class_name . '.php';
                
                if (file_exists($file_path)) {
                    require_once $file_path;
                    return;
                }
            }
        }
    }
}

// Registrar o autoloader global
spl_autoload_register('project_autoload');

// Log de inicialização
log_message('debug', 'Bootstrap carregado com autoloader de namespaces');
