<div class="modal fade" id="modal-transferencia-owner" tabindex="-1" role="dialog" aria-labelledby="Transferência-Owner" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="" id="form_transferir_owner">
                <input type="hidden" name="id_usuario" id="id_usuario" value="<?php echo $this->session->userdata('user_id') ?>">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    <h4 class="modal-title" id="myModalLabel">Transferência de owner de <span id="total_itens_owners">0</span> item(ns)</h4>
                </div>
                <div class="modal-body" style="margin: 15px;">
                    <div class="form-group">
                        <div id="message_user_transfer_owner"></div>
                        <label for="owner_responsavel">Owner responsável:</label>
                        <select class="form-control selectpicker" name="owner_responsavel" id="owner_responsavel" data-live-search="true">
                            <option value="">Selecione o novo owner</option>
                            <?php foreach ($ownersToTransfer as $owner) : ?>
                                <option value="<?php echo $owner->codigo ?>"><?php echo $owner->codigo ?> - <?php echo $owner->descricao ?> - <?php echo $owner->nomes ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Motivo:</label>
                        <textarea class="form-control" name="motivo_owner" id="motivo_owner" rows="3" placeholder="Motivo"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Fechar</button>
                    <button type="submit" class="btn btn-primary" name="transferir-owner" id="form_owner_submit" data-loading-text="Aguarde..." value="1">Salvar</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(function() {
        $('#form_transferir_owner').on('submit', function(e) {
            e.preventDefault();

            let itens = [];

            let checked_itens = $('input[type="checkbox"][name="item[]"]:checked');

            $(checked_itens).each(function() {
                let item = {
                    part_number: $(this).val(),
                    estabelecimento: $(this).attr("data-estabelecimento"),
                    // empresa: $(this).attr("data-empresa"),
                };

                itens.push(item);
            });

            let motivo = $('#motivo_owner').val();
            let novo_owner = $('#owner_responsavel').val();

            if (novo_owner === "") {
                swal("Atenção", "Por favor, selecione o novo owner.", "warning");
                return;
            }

            if (motivo === "") {
                swal("Atenção", "Por favor, informe o motivo da transferência.", "warning");
                return;
            }


            $.ajax({
                url: '<?= base_url('atribuir_grupo/atualizar_owner') ?>',
                method: 'POST',
                data: {
                    itens: itens,
                    owner_responsavel: novo_owner,
                    motivo_owner: motivo
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        $('#ajax_validate').html('<div class="alert alert-success">Owner atualizado com sucesso!</div>');
                        $('html, body').animate({scrollTop: 0}, 'slow');
                        $('#modal-transferencia-owner').modal('hide');
                        $('#form_transferir_owner')[0].reset();

                        swal({
                            title: "Sucesso",
                            text: "Owner atualizado com sucesso!",
                            type: "success"
                        }).then(function() {
                            location.reload();
                        });
                    } else {
                        $('#message_user_transfer_owner').html('<div class="alert alert-danger">Erro ao atualizar owner.</div>');
                    }
                },
                error: function() {
                    $('#message_user_transfer_owner').html('<div class="alert alert-danger">Erro ao enviar os dados para o servidor.</div>');
                }
            });
        });
    });
</script>