<script type="text/javascript" src="<?php echo base_url("assets/js/jquery.sticky-kit.min.js") ?>"></script>
<script type="text/javascript" src="<?php echo base_url("assets/js/jquery.moment.min.js") ?>"></script>


<script type="text/javascript">
    const hasPeso = "<?php echo in_array('peso', $campos_adicionais); ?>";
    const hasPrioridade = "<?php echo in_array('prioridade', $campos_adicionais); ?>";
    const hasDescricaoGlobal = "<?php echo in_array('descricao_global', $campos_adicionais); ?>";
    const hasStatusTriagemDiana = "<?php echo in_array('status_triagem_diana', $funcoes_adicionais); ?>";
</script>

<script type="text/javascript" src="<?php echo base_url('assets/js/atribuir_grupos/atribuir_grupos.js?version='.config_item('assets_version')); ?>"></script>
<style>
    #loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        display: none;
    }
    #loading-message {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 24px;
    }

    /* Toggle Switch para Triagem DIANA */
    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 100%;
        margin-top: 5px;
    }

    .toggle-checkbox {
        display: none;
    }

    .toggle-label {
        display: flex;
        align-items: center;
        cursor: pointer;
        margin-bottom: 0;
        font-weight: normal;
    }

    .toggle-inner {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
        background-color: #ccc;
        border-radius: 24px;
        transition: background-color 0.3s;
        margin-right: 10px;
        flex-shrink: 0;
    }

    .toggle-inner:before {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 20px;
        height: 20px;
        background-color: white;
        border-radius: 50%;
        transition: transform 0.3s;
    }

    .toggle-checkbox:checked+.toggle-label .toggle-inner {
        background-color: #CD1F73;
    }

    .toggle-checkbox:checked+.toggle-label .toggle-inner:before {
        transform: translateX(26px);
    }

    .toggle-switch-text {
        font-size: 14px;
        color: #333;
        user-select: none;
    }

    .toggle-checkbox:checked+.toggle-label .toggle-switch-text {
        color: #CD1F73;
        font-weight: 500;
    }
</style>
<style>
    @media (min-width: 1200px) {
        .container {
            width: 1470px;
            max-width: 95%;
        }
    }
    .alert-black {
        background-color: #444;
        border-color: #333;
        border-radius: 3px;
        color: white;
        padding: 10px;
    }


    .alert-black>[data-notify="message"] {
        font-size: 14px;
    }

    .alert-black .close {
        color: white;
    }

    .btn-group.bootstrap-select.show-tick.col-md-12 {
        padding-right: 0px;
        padding-left: 0px;
    }  
</style>

<script type="text/javascript">
    $(function() {
        atribuir_grupos.init('<?php echo site_url(); ?>');

        $('#send_search_itens').click(function(e) {
            $('#loading-overlay').show();
            atribuir_grupos.get_itens();
            e.preventDefault();
        });

        $('#search_itens').on('submit', function(e) {
            e.preventDefault();
            atribuir_grupos.get_itens();
        })

        $(document).on('change', '.item_selected, #grupo_selected', function(e) {
            atribuir_grupos.safe_submit();
        });

        $(document).on('click', '#grupo_selected', function(e) {
            atribuir_grupos.selected_group(this);
        });

        $(document).on('click', '.hide_group', function(e) {
            atribuir_grupos.hide_group(this.id);
        });

        $(document).on('click', '.show_group', function(e) {
            atribuir_grupos.show_group(this.id, $(this).attr('data-caracteristica'));
        });

        $('#send_search_grupos_tarifarios').click(function(e) {
            e.preventDefault();

            atribuir_grupos.refresh_hidden();
            atribuir_grupos.get_grupos('caracteristica', true);
        });

        $(document).on('click', '#ncm_info', function(e) {
            atribuir_grupos.show_ncm_detail(this);
        });

        $(document).on('click', '.show-obs', function(e) {
            atribuir_grupos.show_obs_detail(this);
        });

        $('#save_association').click(function(e) {
            e.preventDefault();
            atribuir_grupos.save();
        });

        $('.desbloqueia_item').click(function(e) {
            unsetUsuarioBloqueadorItens();
        });

        function unsetUsuarioBloqueadorItens() {
            return $.ajax({
                url: 'pr/perguntas/unsetUsuarioBloqueadorItens',
                method: 'POST'
            });
        }

        
        $('#bt_desbloquear_item').click(function(e) {
            e.preventDefault();
            var itens_checked = [];
            var itens = $('.item_selected:checked').length;
            var checked_itens = $('.item_selected:checked');
            $(checked_itens).each(function () {
                var item = {
                    part_number: $(this).val(),
                    estabelecimento: $(this).attr("data-estabelecimento")
                };

                itens_checked.push(item);
            });
            
            if (!itens) {
                swal('Atenção!', 'Nenhum item foi selecionado!', 'warning');
                e.preventDefault();
            }  else {

                swal({
                    title: "Atenção!",
                    text: 'Você deseja realmente Desbloquear este(s) item(s)?' ,
                    type: "warning",
                    confirmButtonText: "OK",
                    cancelButtonText: "Cancelar",
                    showConfirmButton: true,
                    showCancelButton: true,
                    allowOutsideClick: false
                }).then(function () {
                    //Ajax de Desbloqueio dos items
                    $.ajax({
                        url: base_url + "atribuir_grupo/ajax_desbloqueia_item",
                        data: {
                            itens_checked
                        },
                        async: false,
                        method: 'POST',
                        success: function (data) {
                            console.log(data);
                            atribuir_grupos.get_itens();
                            $(".btn-success").removeAttr('disabled');
                            $(".btn-primary").removeAttr('disabled');
                            $("#desvincularGrupo").removeAttr('disabled');
                            
                        }
                    })
                }, function (dismiss) {
                    
                });
            }
        });

        selected_tag = "<?php echo $this->item_model->get_state('filter.tag'); ?>";
        atribuir_grupos.refresh_tags('#select_tag', true, true, selected_tag);

        // owner = "<?php echo $this->item_model->get_state('filter.owner'); ?>";

        $('#owner').on('change', function(e) {
            atribuir_grupos.get_itens();
        });

        $('#select_tag').on('change', function(e) {
            atribuir_grupos.selected_tag(this);
        });

        $('#change_tag').click(function(e) {
            e.preventDefault();
            atribuir_grupos.open_modal_change_tag();
        });

        $('#send_change_tag').click(function(e) {
            e.preventDefault();
            atribuir_grupos.save_tag();
        });

        $('#item_order_pnumber').click(function() {
            atribuir_grupos.change_order('#order_item', 'part_number');
            atribuir_grupos.get_itens();
        });

        $('#item_order_ncm').click(function() {
            atribuir_grupos.change_order('#order_item', 'ncm');
            atribuir_grupos.get_itens();
        });
        
        $('#item_order_peso').click(function() {
            atribuir_grupos.change_order('#order_item', 'peso');
            atribuir_grupos.get_itens();
        });

        $('#item_order_prioridade').click(function() {
            atribuir_grupos.change_order('#order_item', 'prioridade');
            atribuir_grupos.get_itens();
        });

        $('#item_order_descricao').click(function() {
            atribuir_grupos.change_order('#order_item', 'descricao');
            atribuir_grupos.get_itens();
        });

        $(document).on('click', '.grupo_order_descricao', function() {
            atribuir_grupos.change_order('#order_grupo', 'descricao');
            atribuir_grupos.get_grupos(null, atribuir_grupos.selected_caracteristica, true);
        });

        $(document).on('click', '.grupo_order_ncm_recomendada', function() {
            atribuir_grupos.change_order('#order_grupo', 'ncm_recomendada');
            atribuir_grupos.get_grupos(null, atribuir_grupos.selected_caracteristica, true);
        });

        $('#select-all').on('change', function(e) {
            atribuir_grupos.select_all('#table-itens', 'item', e);
        });

        $('#select-all-modal').on('change', function(e) {
            atribuir_grupos.select_all('#table-itens-modal', 'item-new-tag', e);
        });

        $('#atribuido_para').on('change', function(e) {
            atribuir_grupos.refresh_tags('#select_tag', true, true);
            atribuir_grupos.clear_grupos();
            atribuir_grupos.clear_tag();
            atribuir_grupos.get_itens();
        });

        atribuir_grupos.update_countdown('observacoes', 'countdown_obs');

        $('#observacoes').on('change', function(e) {
            atribuir_grupos.update_countdown('observacoes', 'countdown_obs');
        });
        $('#observacoes').on('keyup', function(e) {
            atribuir_grupos.update_countdown('observacoes', 'countdown_obs');
        });

        atribuir_grupos.countCharacters('descricao_mercado_local', 'count_proposta_resumida');

        $('#descricao_mercado_local').on('input', function(e) {
            atribuir_grupos.countCharacters('descricao_mercado_local', 'count_proposta_resumida');
        });

        atribuir_grupos.update_countdown('descricao_mercado_local', 'countdown_proposta_resumida');
        atribuir_grupos.update_countdown('motivo', 'countdown_mtivo');

        $('#motivo').on('change', function(e) {
            atribuir_grupos.update_countdown('motivo', 'countdown_mtivo');
        });
        $('#motivo').on('keyup', function(e) {
            atribuir_grupos.update_countdown('motivo', 'countdown_mtivo');
        });

        $('#disable_filter_by_tag').on('change', function() {
            atribuir_grupos.get_grupos();
        });

        $('#form_transfer_submit').click(function(e) {
            e.preventDefault();
            atribuir_grupos.user_transfer();
        });

        $('#btn-transferir').click(function(e) {
            atribuir_grupos.refresh_total();
        });

        $('#btn-transferir-diana').click(function(e) {
            atribuir_grupos.refresh_total();
        });

        $('#btn-transferencia-owner').click(function(e) {
            $("#message_user_transfer_owner").empty();
		    let checked_itens_to_owners = $('input[type="checkbox"][name="item[]"]:checked');

            if (checked_itens_to_owners.length == 0) {
                swal('Atenção', 'Selecione no mínimo um item para transferir', 'warning');
                return false;
            }

            console.log(checked_itens_to_owners);
            $("#total_itens_owners").text(checked_itens_to_owners.length);
        });

        $('#btn-transferencia-owner-diana').click(function(e) {
            $("#message_user_transfer_owner").empty();
		    let checked_itens_to_owners_diana = $('input[type="checkbox"][name="item[]"]:checked');

            if (checked_itens_to_owners_diana.length == 0) {
                swal('Atenção', 'Selecione no mínimo um item para transferir', 'warning');
                return false;
            }

            console.log(checked_itens_to_owners_diana);
            $("#total_itens_owners").text(checked_itens_to_owners_diana.length);
        });

        $('#modal-multi-paises-diana').click(function(e) {
            e.preventDefault();
            atribuir_grupos.save(1);
        });

        $(document).on('show.bs.collapse', '.panel_carac_grupos', function() {
            var caracteristica = $(this).attr('data-caracteristica');
            atribuir_grupos.get_grupos(null, caracteristica);
        });

        atribuir_grupos.refresh_hidden();

        $('.panel').on('shown.bs.collapse hidden.bs.collapse', function() {
            atribuir_grupos.recalc_sticky_kit();
        });

        $('.tooltip-wrapper').tooltip({
            position: "bottom"
        });

        $('.selectpicker').selectpicker();
    });
</script>

<div id="edit-item">
    <v-edit-item 
        has-prioridade="<?php echo in_array('prioridade', $campos_adicionais); ?>" 
        has-peso="<?php echo in_array('peso', $campos_adicionais); ?>"
        per-criticidade="<?php echo customer_has_role('alterar_criticidade',sess_user_id()); ?>"
    ></v-edit-item>
</div>
<script type="text/javascript" src="<?php echo base_url('assets/vuejs/dist/edit-item.js?version='.config_item('assets_version')) ?>"></script>
<link rel="stylesheet" href="<?php echo base_url('assets/vuejs/dist/edit-item.css?version='.config_item('assets_version')) ?>" type="text/css" media="screen" />

<input type="hidden" id="integracao_simplus" value="<?php echo isset($integracao_simplus) && $integracao_simplus == TRUE ? 1 : '' ?>" />

<input type="hidden" id="desblquear_itens" value="<?php echo customer_has_role('desblquear_itens', sess_user_id())?>" />

<div id="ajax_validate"></div>
<div class="row" style="display: flex; align-items: center;">
    <div class="col-sm-5" style="padding-right: 0px">
        <h2>Dados Técnicos <small>Indique itens e atribua um grupo tarifário</small></h2>
    </div>

    <div class="col-sm-7">
        <div class="text-right">
            
        <?php if (customer_has_role('exportar_padrao_dados_tecnicos', sess_user_id()) || customer_has_role('exportar_multi_paises_dados_tecnicos', sess_user_id())) : ?>
            <?php if ($empresa_pais) : ?>
                <div class="dropdown" style="position: absolute !important; z-index: 1000; right: 649px;">
                    <button class="btn btn-success dropdown-toggle" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true" data-loading-text="Gerando...">
                        Exportar <i class="glyphicon glyphicon-cloud-download"></i>
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">
                    <?php if (customer_has_role('exportar_padrao_dados_tecnicos', sess_user_id())) : ?>
                        <li><a href="#" id="send_generate_log" data-loading-text="Gerando...">Padrão</a></li>
                    <?php endif;?> 
                    <?php if (customer_has_role('exportar_multi_paises_dados_tecnicos', sess_user_id())) : ?>
                        <li><a href="#" id="send_multipaises_log" data-loading-text="Gerando...">Multi Paises</a></li>
                    <?php endif;?> 
                    </ul>
                </div>
            <?php else : ?>
                <?php if (customer_has_role('exportar_padrao_dados_tecnicos', sess_user_id())) : ?>
                    <button id="send_generate_log"  type="button" class="btn btn-success" data-loading-text="Gerando...">
                        Exportar <i class="glyphicon glyphicon-cloud-download"></i>
                    </button>
                <?php endif;?>
            <?php endif;?>  
        <?php endif;?>  
            <!-- <button 
                class="btn btn-success " id="change_tag" 
                data-toggle="modal" data-target="#modal_change_tag" 
                <?php echo has_role('becomex_pmo') && (!has_role('sysadmin') && !has_role('consultor')) ? 'disabled' : NULL; ?>
            >
                Alterar valores dos campos
            </button> -->
            <?php if (in_array('prioridade', $campos_adicionais) || in_array('peso', $campos_adicionais)) : ?>
                <button 
                    type="submit" class="btn btn-success" 
                    data-toggle="modal" data-target="#editItem" 
                    <?php echo (has_role('becomex_pmo') && (!has_role('sysadmin') && !has_role('consultor'))) && customer_has_role('inserir_informacoes_tecnicas', sess_user_id()) ? 'disabled' : NULL; ?>
                >
                    Editar itens  <i class="glyphicon glyphicon-pencil"></i> 
                </button>
            <?php endif; ?>
            <button 
                class="btn btn-success " id="change_tag" 
                data-toggle="modal" data-target="#modal_change_tag" 
                <?php echo has_role('becomex_pmo') && (!has_role('sysadmin') && !has_role('consultor')) ? 'disabled' : NULL; ?>
            >
                Alterar pré-agrupamento  <i class="glyphicon glyphicon-transfer"></i>
            </button>
            <a class="btn btn-primary" data-toggle="modal" data-target="#perguntasRespostas">
                Perguntas e Respostas  <i class="glyphicon glyphicon-question-sign"></i>
            </a>
           
            <?php if  (customer_has_role('desvincular_grupo', sess_user_id())) : ?>
            <a href="<?php echo site_url('atribuir_grupo/deletar'); ?>" id="desvincularGrupo" class="btn btn-danger" >
                Desvincular <i class="glyphicon glyphicon-remove"></i>
            </a>
            <?php endif; ?>
            <?php if  (customer_has_role('desblquear_itens', sess_user_id())) : ?>
                <br/><br/>
            <button class="btn btn-danger " id="bt_desbloquear_item">
                Desbloquear Item <i class="glyphicon glyphicon-remove"></i>
            </button>
            <?php endif; ?>
        </div>
    </div>
</div>

<hr />

<div class="row" style="margin-bottom: 20px">
    <div class="col-md-6" id="column_list_left" style="border-right: 1px solid #E2E2E2;">
        <form class="form-horizontal" id="generate_log" action="<?php echo site_url('atribuir_grupo/log_xls'); ?>">
            <input type="hidden" name="search" id="search_input_log">
            <input type="hidden" name="download_token" value="<?php echo md5(uniqid()) ?>">
            <input type="hidden" name="tipo_relatorio" id="tipo_relatorio_log" value="">
            <input type="hidden" name="id_empresa" id="id_empresa" value="<?php echo $entry->id_empresa ?>">
            <input type="hidden" name="busca_automatica" id="busca_automatica" value="<?php echo $part_number_direto?>" />

            <div class="form-group">
                <label class="col-md-3 control-label" style="padding-right: 8px">Atribuídos para:</label>
                <div class="col-md-9">
                    <select
                        class="selectpicker form-control"
                        style="width: 100%"
                        name="atribuido_para"
                        id="atribuido_para"
                        data-live-search="true"
                        title="Selecione o usuário"
                    >
                        <option value="-1">Todos</option>
                    </select>
                </div>
            </div>
            <?php if (in_array('owner', $campos_adicionais)) : ?>
                <div class="form-group">
                    <label class="col-md-3 control-label" style="padding-right: 8px">Owner:</label>
                    <div class="col-md-9">
                        <select
                            class="selectpicker form-control"
                            style="width: 100%"
                            name="owner[]"
                            id="owner"
                            data-live-search="true"
                            multiple
                            data-selected-text-format="count > 1"
                            data-count-selected-text="Owner ({0})"
                            title="Selecione Owner"
                        >
                            <option data-divider="true"></option>
                            
                        </select>
                    </div>
                </div>
            <?php endif; ?>

            <div class="form-group">
                <label class="col-md-3 control-label" style="padding-right: 8px">Pré-agrupamento:</label>
                <div class="col-md-9">
                    <select class="form-control selectpicker" title="Pré-agrupamento"  id="select_tag" name="tag"></select>
                </div>
            </div>

            <div class="form-group">
                <label class="col-md-3 control-label" style="padding-right: 8px">Evento/Pacote:</label>
                <div class="col-md-9">
                    <select
                        class="form-control selectpicker"
                        data-count-selected-text="Evento/Pacote ({0})"
                        data-selected-text-format="count > 1"
                        data-deselect-all-text="Nenhum"
                        data-select-all-text="Todos"
                        data-actions-box="true"
                        data-live-search="true"
                        name="evento[]"
                        id="eventoPacote"
                        title="Evento/Pacote"
                        multiple
                        >
                            <option value="sem_evento"><strong>Sem Evento/Pacote</strong></option>
                    </select>
                </div>
            </div>

            <?php if (in_array('prioridade', $campos_adicionais)) : ?>
                <div class="form-group">
                    <label class="col-md-3 control-label" style="padding-right: 8px">Prioridade:</label>
                    <div class="col-md-9">
                        <select
                            class="form-control selectpicker"
                            data-count-selected-text="Prioridade ({0})"
                            data-selected-text-format="count > 1"
                            data-deselect-all-text="Nenhum"
                            data-select-all-text="Todos"
                            data-actions-box="true"
                            data-live-search="true"
                            name="prioridade[]"
                            id="prioridadeSelect"
                            title="Prioridade"
                            multiple
                        >

                        </select>
                    </div>
                </div>
            <?php endif; ?>

            <div class="form-group">
                <label class="col-md-3 control-label" style="padding-right: 8px">Status:</label>
                <div class=<?php echo ($has_status_triagem_diana && has_role('consultor')) ? 'col-md-4' : 'col-md-9'; ?>>
                    <select 
                        class="form-control selectpicker" 
                        data-count-selected-text="Status ({0})" 
                        data-selected-text-format="count > 1" 
                        data-deselect-all-text="Nenhum" 
                        data-select-all-text="Todos" 
                        data-actions-box="true" 
                        data-live-search="true" 
                        name="status[]" 
                        id="status"
                        title="Status" 
                        multiple 
                    >
                        <?php if (in_array('owner', $campos_adicionais) && in_array('triagem_owner', $campos_adicionais)) : ?>
                            <option value="aguardando_definicao_responsavel">Aguardando Definição Responsável</option>
                        <?php endif; ?>
                        <option value="analise">Em Análise</option>
                        <option value="pendente">Pendentes de Informações</option>
                        <?php if (in_array('descricao_global', $campos_adicionais)) : ?>
                            <option value="aguardando_descricao">Aguardando Descrição</option>
                        <?php endif; ?>
                        <option value="perguntasRespondidas">Perguntas Respondidas</option>
                        <?php if (in_array('owner', $campos_adicionais)) : ?>
                            <option value="revisar_informacoes_erp">Revisar Informações ERP</option>
                            <option value="informacoes_erp_revisadas">Informações ERP Revisadas</option>
                            <option value="revisar_informacoes_tecnicas">Revisar Informações Técnicas</option>
                            <option value="perguntas_respondidas_novas">Perguntas Respondidas (Novas)</option>
                        <?php endif; ?>
                    </select>
                </div>

                <!-- Status Triagem Diana -->
                 <?php if ($has_status_triagem_diana && has_role('consultor')) : ?>
                    <label class="col-md-1" style="font-size: 13px;" for="triagem_diana_toggle">Triagem DIANA:</label>
                    <div class="col-md-4">
                        <?php $triagem_diana_falha = $this->input->post('triagem_diana_falha') ?: $this->item_model->get_state('filter.triagem_diana_falha');  ?>
                        <div class="toggle-switch">
                            <!-- O input hidden é necessário para garantir que o valor '0' seja enviado quando o checkbox estiver desmarcado -->
                            <input type="hidden" name="triagem_diana_falha" value="0">
                            <input
                                type="checkbox"
                                id="triagem_diana_falha"
                                name="triagem_diana_falha"
                                value="1"
                                <?php echo $triagem_diana_falha === '1' ? 'checked' : null; ?>
                                class="toggle-checkbox">
                            <label for="triagem_diana_falha" class="toggle-label">
                                <span class="toggle-inner"></span>
                                <span class="toggle-switch-text">Falha na triagem</span>
                            </label>
                        </div>
                    </div>
                <?php endif; ?>
            </div>


            <div class="form-group">
                <label class="col-md-3 control-label" style="padding-right: 8px">Sistema de Origem:</label>
                <div class="col-md-9">
                    <select
                        class="form-control selectpicker"
                        data-count-selected-text="Sistema de Origem ({0})"
                        data-selected-text-format="count > 1"
                        data-deselect-all-text="Nenhum"
                        data-select-all-text="Todos"
                        data-actions-box="true"
                        data-live-search="true"
                        name="sistemaorigem[]"
                        id="sistemaorigemSelect"
                        title="Sistema de Origem"
                        multiple
                    >
                    </select>
                </div>
            </div>
            
        </form>
<!-- 
        <form class="form-horizontal" id="multipaises" action="<?php echo site_url('atribuir_grupo/xls_multipaises'); ?>">
            <input type="hidden" name="download_token" value="<?php echo md5(uniqid()) ?>">
        </form> -->

        <div id="search_itens_holder">
            <form id="search_itens" action="<?php echo site_url('atribuir_grupo/ajax_get_itens'); ?>">
                <input type="hidden" id="order_item" name="descricao" value="asc" />
                <div class="input-group">
                    <textarea onkeypress="removeTextareaHeightLimit()"type="text" class="form-control" name="item_input" id="item_input" placeholder="Digite o(s) código(s) ou descrição do(s) item(ns)" ></textarea>
                    <div class="input-group-btn">
                        <button type="button" data-loading-text="..." class="btn btn-primary" id="send_search_itens"><i class="glyphicon glyphicon-search"></i></button>
                        <a style="margin-left: 10px" href="<?php echo site_url("atribuir_grupo") ?>" class="btn btn-default">Limpar</a>
                    </div>
                </div>
            </form>
        </div>

        <table class="table table-striped table-hover" id="table-itens" data-multi-estabelecimentos="<?php echo $multi_estabelecimentos; ?>">
            <thead>
                <th width="1%">
                    <input type="checkbox" id="select-all" />
                </th>
                <th width="20%"><a href="javascript:void(0);" id="item_order_pnumber">Part Number</a></th>
                <?php if (in_array('owner', $campos_adicionais)) : ?>
                    <th><a href="javascript:void(0)" id="item_order_owner">Owner</a></th>
                <?php elseif (!in_array('owner', $campos_adicionais) && in_array('ncm_dados_tecnicos', $funcoes_adicionais)) : ?>
                    <th><a href="javascript:void(0)" id="item_order_ncm">NCM</a></th>
                <?php endif; ?>
                <?php if ($multi_estabelecimentos == 1) { ?>
                    <th width="5%" class="text-center">Estab.</th>
                <?php } ?>

                <?php if (in_array('peso', $campos_adicionais)) : ?>
                    <th width="10%" class="text-center">
                        <a href="javascript:void(0);" id="item_order_peso">
                            Peso
                        </a>
                    </th>
                <?php endif; ?>

                <?php if (in_array('prioridade', $campos_adicionais)) : ?>
                    <th width="5%" class="text-center">
                        <a href="javascript:void(0);" id="item_order_prioridade">
                            Prior.
                        </a>
                    </th>
                <?php endif; ?>

                <th width="30%" style="padding-right: 0px;">
                    <a href="javascript:void(0);" id="item_order_descricao">
                        <p class="pull-left" style="margin-bottom: 0px;margin-top: 14px;">Descrição</p>
                    </a>
                </th>
            </thead>
            <tbody id="itens_holder"  style="white-space: break-spaces;"></tbody>
        </table>
    </div>

    <div class="col-md-6" id="column_list_right">
        <ul class="nav nav-tabs" id="myTab" role="tablist">
        <?php if (customer_has_role('atribuir_grupo_tarifario_part_number', sess_user_id()) == TRUE && customer_has_role('classificar_itens_sem_pn', sess_user_id()) == TRUE) : ?>  
            <li class="nav-item">
                <a class="nav-link <?php if (!$has_diana) echo 'active'; ?>" id="tab-atribuir" data-toggle="tab" href="#atribuir" role="tab" aria-controls="atribuir" aria-selected="true">Atribuir</a>
            </li>
        <?php endif; ?>
            <?php if ($has_diana) :?>
                <li class="nav-item">
                    <a class="nav-link <?php if ($has_diana) echo 'active'; ?>" id="tab-diana" data-toggle="tab" href="#diana" role="tab" aria-controls="diana" aria-selected="false">Diana</a>
                </li>
            <?php endif; ?>
        </ul>

        <div class="tab-content" id="myTabContent">
            <div class="tab-pane fade <?php if (!$has_diana) echo 'show active'; ?>" id="atribuir" role="tabpanel" aria-labelledby="tab-atribuir">
                <div class="row">
                    <div class="col-md-12">
                        <div class="text-right" style="height: 26px; line-height: 26px">
                            <div id="response-ajax" style="display: none;">
                                <img src="<?php echo base_url('assets/img/ajax-loader.gif'); ?>" /> Aguarde, buscando dados...
                            </div>
                        </div>
                        <?php if (customer_has_role('atribuir_grupo_tarifario_part_number', sess_user_id()) == TRUE && customer_has_role('classificar_itens_sem_pn', sess_user_id()) == TRUE) : ?>  
                        <button class="btn btn-primary pull-right" data-toggle="modal" onclick="clearInputs()" data-target="#modal_motivo" <?php echo has_role('becomex_pmo') && (!has_role('sysadmin') && !has_role('consultor')) ? 'disabled' : NULL; ?> style="width: 118px;">
                            Atribuir <i class="glyphicon glyphicon-ok"></i>
                        </button>
                        <?php endif; ?>
                        <button id="btn-transferir" class="pull-right btn btn-success" data-toggle="modal" data-target="#transferencia-modal" title="Transfira o responsável pelos itens selecionados" style="margin-right: 10px;">
                            Transferir Responsável <i class="glyphicon glyphicon-user"></i>
                        </button>
                        <?php if (in_array('owner', $campos_adicionais) && customer_has_role('alterar_owner',sess_user_id())) : ?>
                            <button id="btn-transferencia-owner" class="btn btn-success pull-right" data-toggle="modal" data-target="#modal-transferencia-owner" style="margin-right: 10px;">
                                Transferir Owner <i class="glyphicon glyphicon-user"></i>
                            </button>
                        <?php endif; ?>
                        <div id="search_grupos_tarifarios_holder">
                            <form id="search_grupos_tarifarios" action="<?php echo site_url('atribuir_grupo/ajax_get_grupos_tarifarios'); ?>">
                                <label  style="padding-right: 8px">Status:</label>
                                <div class="input-group">
                                    <select class="form-control selectpicker" 
                                        id="filter_selected" name="filter_selected"
                                        title="Tipo" >
                                            <option value="id_grupo_tarifario" >Por Grupo Tarifário</option>
                                            <option value="caracteristica" selected>Por Característica</option>
                                    </select>
                                </div>

                                <div class="checkbox">
                                    <label><input type="checkbox" id="disable_filter_by_tag" name="disable_filter_by_tag" value="1" />
                                        Todos
                                        <i class="glyphicon glyphicon-info-sign" data-toggle="tooltip" title="Utilize essa opção caso prefira pesquisar por todos os grupos tarifários, desabilitando o filtro automático por TAG."></i>
                                    </label>
                                </div>
                                <div class="input-group">
                                    <input type="text" class="form-control" name="grupo_input" placeholder="Digite a descrição ou NCM do grupo tarifário" />
                                    <div class="input-group-btn">
                                        <button type="submit" data-loading-text="..." class="btn btn-primary" id="send_search_grupos_tarifarios"><i class="glyphicon glyphicon-search"></i></button>
                                    </div>
                                </div>
                                <input type="hidden" id="order_grupo" name="ncm_recomendada" value="asc" />
                            </form>
                        </div>
                        <div class="clearfix" style="margin-top: 10px;"></div>

                        <div class="panel-group" id="accordion_grupos" role="tablist" aria-multiselectable="true"></div>

                        <hr />

                        <div class="panel panel-default" id="painel_grupos_ocultos" data-restricted="<?php echo has_role('becomex_pmo') && !has_role('sysadmin') ? TRUE : FALSE; ?>">
                            <div class="panel-heading">
                                <a href="#grupos_collapse" id="grupos_collapse_link" class="accordion-toggle collapsed" data-toggle="collapse" aria-expanded="false"><strong>Grupos ocultos</strong></a>
                            </div>
                            <div class="panel-collapse collapse" id="grupos_collapse">
                                <table class="table table-striped" style="margin-bottom: 0px;">
                                    <tbody id="grupos_ocultos_holder"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <?php if ($has_diana): ?>
                <div class="tab-pane fade <?php if ($has_diana) echo 'show active'; ?>" id="diana" role="tabpanel" aria-labelledby="tab-diana">
                <?php if ( customer_has_role('atribuir_grupo_tarifario_part_number',sess_user_id()) && customer_has_role('classificar_itens_sem_pn',sess_user_id())) : ?>  
                    <button class="btn btn-primary pull-right mt-15" data-toggle="modal" onclick="clearInputs()" data-target="#modal_motivo" <?php echo has_role('becomex_pmo') && (!has_role('sysadmin') && !has_role('consultor')) ? 'disabled' : NULL; ?> style="width: 118px;">
                        <i class="glyphicon glyphicon-ok"></i> Atribuir
                    </button>
                <?php endif; ?>
                    <button id="btn-transferir-diana" class="pull-right btn btn-success mt-15" data-toggle="modal" data-target="#transferencia-modal" title="Transfira o responsável pelos itens selecionados" style="margin-right: 10px;">
                            Transferir Responsável <i class="glyphicon glyphicon-user"></i>
                    </button>
                    <?php if (in_array('owner', $campos_adicionais) && customer_has_role('alterar_owner',sess_user_id())) : ?>
                        <button id="btn-transferencia-owner-diana" class="btn btn-success pull-right mt-15" data-toggle="modal" data-target="#modal-transferencia-owner" style="margin-right: 10px;">
                                Transferir Owner <i class="glyphicon glyphicon-user"></i>
                        </button>
                    <?php endif; ?> 
                    <div id="atribuir-diana-app" class="col-md-12">
                        <v-atribuir-diana></v-atribuir-diana>
                    </div>

                    <script type="text/javascript" src="<?php echo base_url('assets/vuejs/dist/atribuir-diana.js?version='.config_item('assets_version')) ?>"></script>
                    <link rel="stylesheet" href="<?php echo base_url('assets/vuejs/dist/atribuir-diana.css?version='.config_item('assets_version')) ?>" type="text/css" media="screen" />
                </div>
            <?php endif; ?>
        </div>
    </div>    
</div>

<div class="clearfix" style="margin-top: 20px;"></div>

<?php $this->load->view('atribuir_grupo/modal-tag') ?>
<?php $this->load->view('atribuir_grupo/modal-atribuir-grupo') ?>
<?php $this->load->view('atribuir_grupo/modal-transferencia') ?>
<?php $this->load->view('atribuir_grupo/modal-transferencia-owner', 'owners') ?>
<?php if ($empresa_pais) : ?>
    <?php $this->load->view('cadastros/mestre_itens/modal-multi-paises', 'empresa_pais', 'entry'); ?>
<?php endif;?>  

<?php if  (customer_has_role('desblquear_itens', sess_user_id())) : ?>
    <?php $this->load->view('atribuir_grupo/modal-desbloquear-itens') ?>
<?php endif; ?>

<?php if (isset($integracao_simplus) && $integracao_simplus == TRUE) : ?>
    <div class="modal fade" id="detalhes-simplus" tabindex="-1" role="dialog" aria-labelledby="Detalhes SIMPLUS" aria-hidden="true">

    </div>
<?php else : ?>
    <div class="modal fade" id="detalhes-part-number" tabindex="-1" role="dialog" aria-labelledby="Detalhes Part Number" aria-hidden="true">

    </div>
<?php endif; ?>

<div class="float-bottom-alert screen-info">
    <span class="badge total_itens">0</span> item(ns) encontrados. <span class="badge total_selected">0</span> itens selecionados.
</div>
<div id="loading-overlay">
    <div id="loading-message">Carregando...</div>
</div>
<style>
    .screen-info {
        display: none;
        background: #3276b1;
        color: white;
        padding: 10px;
        text-align: center;
        width: 350px;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        margin: 0 auto;
    }

    .is_stuck {
        top: 70px !important;
    }

    .screen-info .badge {
        font-weight: bold;
        background: white;
        color: #357ebd;
    }

    #accordion_grupos {
        max-height: 70vh;
        overflow-y: auto;
    }

    .mt-15 {
        margin-top: 15px;
    }
</style>

<?php
/*
 * GT - INCLUSÃO CAMPOS APLICAÇÃO/MATERIAL/FUNÇÃO
 * #10494 - Alteração na função “Atribuir Grupo” na tela Dados Técnicos
 */
?>
<script type="text/javascript">
    $(document).ready(function() {
        $('#adicionar-nova-tag, #pre-agrupamento-select').on('click', function(ev) {
            var elem = ev.target;
            var self = $(this);
            if (elem.getAttribute('type') != 'radio') {
                self.find('input[type="radio"]').trigger('click');
            }
        })

        $('#btn-diana-by-step').on('click', function() {
            $('#atribuir-diana-app').addClass('hide');
            $('#column_list_right').removeClass('hide');
        });

        '<?php echo $has_diana?>' == '1' ? $("#tab-diana").trigger("click") : $("#tab-atribuir").trigger("click");

        busca_automatica();

        let eventosSelecionados = <?php echo json_encode($this->item_model->get_state('filter.evento')) ?> || [];
        let sistemasOrigemSelecionados = <?php echo json_encode($this->item_model->get_state('filter.sistema_origem_modal')) ?> || [];
        let ownersSelecionados = <?php echo json_encode($this->item_model->get_state('filter.owner')) ?> || [];
        let prioridadesSelecionadas = <?php echo json_encode($this->item_model->get_state('filter.prioridade')) ?> || [];
        let usuariosSelecionados = <?php echo json_encode($this->item_model->get_state('filter.usuario')) ?> || [];

        let eventoLoaded = false;
        let sistemasOrigemLoaded = false;
        let ownersLoaded = false;
        let prioridadesLoaded = false;
        let usuariosLoaded = false;

        function inicializarEventosSelecionados() {
            if (eventosSelecionados && eventosSelecionados.length > 0) {
                $.each(eventosSelecionados, function(index, value) {
                    if ($('#eventoPacote option[value="' + value + '"]').length === 0) {
                        $('#eventoPacote').append(`<option value="${value}" selected>${value}</option>`);
                    }
                });
                $('#eventoPacote').selectpicker('refresh');
            }
        }

        function inicializarSistemasOrigemSelecionados() {
            if (sistemasOrigemSelecionados && sistemasOrigemSelecionados.length > 0) {
                $.each(sistemasOrigemSelecionados, function(index, value) {
                    if ($('#sistemaorigemSelect option[value="' + value + '"]').length === 0) {
                        $('#sistemaorigemSelect').append(`<option value="${value}" selected>${value}</option>`);
                    }
                });
                $('#sistemaorigemSelect').selectpicker('refresh');
            }
        }

        function inicializarOwnersSelecionados() {
            if (ownersSelecionados && ownersSelecionados.length > 0) {
                if (!ownersLoaded) {
                    carregarOwners();
                } else {
                    $('#owner').selectpicker('val', ownersSelecionados);
                    $('#owner').selectpicker('refresh');
                }
            }
        }

        function inicializarPrioridadesSelecionadas() {
            if (prioridadesSelecionadas && prioridadesSelecionadas.length > 0) {
                if (!prioridadesLoaded) {
                    carregarPrioridades();
                } else {
                    $('#prioridadeSelect').selectpicker('val', prioridadesSelecionadas);
                    $('#prioridadeSelect').selectpicker('refresh');
                }
            }
        }

        function carregarEventos() {
            if (eventoLoaded) return;

            $('#eventoPacote').empty().append('<option value="">Carregando...</option>');
            $('#eventoPacote').selectpicker('refresh');

            $.ajax({
                url: base_url + 'atribuir_grupo/get_list_eventos',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    $('#eventoPacote').empty().append('<option value="sem_evento"><strong>Sem Evento/Pacote</strong></option>');

                    if (response && response.length > 0) {
                        $.each(response, function(_, item) {
                            $('#eventoPacote').append(`<option value="${item.evento}">${item.evento}</option>`);
                        });
                    }

                    if (eventosSelecionados && eventosSelecionados.length > 0) {
                        $('#eventoPacote').selectpicker('val', eventosSelecionados);
                    }

                    $('#eventoPacote').selectpicker('refresh');
                    eventoLoaded = true;
                },
                error: function(err) {
                    console.error('Erro ao carregar Eventos:', err);
                    $('#eventoPacote').empty().append('<option value="">Erro ao carregar</option>');
                    $('#eventoPacote').selectpicker('refresh');
                }
            });
        }

        function carregarSistemasOrigem() {
            if (sistemasOrigemLoaded) return;

            $('#sistemaorigemSelect').html('<option value="">Carregando...</option>');
            $('#sistemaorigemSelect').selectpicker('refresh');

            $.ajax({
                url: base_url + 'cadastros/mestre_itens/get_list_sistemas_origens',
                type: 'GET',
                dataType: 'json',
                success: function(response) {

                    if (response && response.length > 0) {
                        let options = '';
                        $.each(response, function(_, sistemaOrigem) {
                            options += `<option value="${sistemaOrigem}">${sistemaOrigem}</option>`;
                        });
                        $('#sistemaorigemSelect').html(options);
                        $('#sistemaorigemSelect').selectpicker('refresh');
                        sistemasOrigemLoaded = true;
                    }

                    if (sistemasOrigemSelecionados && sistemasOrigemSelecionados.length > 0) {
                        $('#sistemaorigemSelect').selectpicker('val', sistemasOrigemSelecionados);
                    }

                    $('#sistemaorigemSelect').selectpicker('refresh');
                    sistemasOrigemLoaded = true;
                },
                error: function(err) {
                    console.error('Erro ao carregar Sistemas de Origem:', err);
                    $('#sistemaorigemSelect').empty().append('<option value="">Erro ao carregar</option>');
                    $('#sistemaorigemSelect').selectpicker('refresh');
                }
            });
        }

        function carregarOwners() {
            if (ownersLoaded) return;

            $("#owner").empty().append('<option value="-1">Carregando...</option>');
            $("#owner").selectpicker("refresh");

            $.ajax({
                url: base_url + "atribuir_grupo/get_list_owners_by_empresa",
                type: "GET",
                dataType: "json",
                success: function (response) {
                if (response && !response.error) {
                    let options = '<option value="-1">Todos os owners</option>';

                    response.forEach(function (owner) {
                    const value = owner.codigo;
                    const text = `${owner.codigo} - ${owner.descricao}` + " - " + `${owner.nomes}`;
                    const isSelected =
                        ownersSelecionados && ownersSelecionados.includes(value);

                    options += `<option value="${value}" ${
                        isSelected ? "selected" : ""
                    }>${text}</option>`;
                    });

                    $("#owner").html(options);
                    $("#owner").selectpicker("refresh");
                    ownersLoaded = true;
                } else {
                    console.error(
                    "Erro ao carregar Owners:",
                    response ? response.message : "Erro desconhecido"
                    );
                }
                },
                error: function (xhr, status, error) {
                console.error("Erro na requisição:", error);
                console.log("Resposta do servidor:", xhr.responseText);
                },
            });
        }

        function carregarPrioridades() {
            if (prioridadesLoaded) return;

            $("#prioridadeSelect").empty().append('<option value="-1">Carregando...</option>');
            $("#prioridadeSelect").selectpicker("refresh");

            $.ajax({
                url: base_url + "cadastros/mestre_itens/get_list_prioridades_by_empresa",
                type: "GET",
                dataType: "json",
                success: function (response) {
                if (response && !response.error) {
                    let options = '';
                    
                    response.forEach(function (prioridadeSelect) {
                    const value = prioridadeSelect.id_prioridade;
                    const text = `${prioridadeSelect.nome}`;
                    const isSelected =
                        prioridadesSelecionadas && prioridadesSelecionadas.includes(value);

                    options += `<option value="${value}" ${
                        isSelected ? "selected" : ""
                    }>${text}</option>`;
                    });

                    $("#prioridadeSelect").html(options);
                    $("#prioridadeSelect").selectpicker("refresh");
                    prioridadesLoaded = true;
                } else {
                    console.error(
                    "Erro ao carregar Prioridades:",
                    response ? response.message : "Erro desconhecido"
                    );
                }
                },
                error: function (xhr, status, error) {
                console.error("Erro na requisição:", error);
                console.log("Resposta do servidor:", xhr.responseText);
                },
            });
        }

        function carregarSelectUsuarios() {
            if (usuariosLoaded) return;

            $("#atribuido_para").empty().append('<option value="-1">Carregando...</option>');
            $("#atribuido_para").selectpicker("refresh");
            
            $.ajax({
                url: base_url + 'atribuir_grupo/ajax_get_usuarios_filtro',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const select = $('#atribuido_para');
                        select.empty();
                        
                        // Adiciona as opções principais
                        response.data.options.forEach(option => {
                            if (option.divider) {
                                select.append('<option data-divider="true"></option>');
                            }
                            select.append(`<option value="${option.value}">${option.text}</option>`);
                        });
                        
                        // Adiciona os optgroups
                        if (response.data.usuarios_ativos && response.data.usuarios_ativos.optgroups && response.data.usuarios_ativos.optgroups.length > 0) {
                            select.append(`
                                <option disabled style="margin-top: 5px; border-top: 1px solid #e5e5e5; padding-top: 5px;">
                                    <span class="label label-success" style="font-size: 12px; border-radius: 10px;">Usuários ativos</span>
                                </option>
                            `)

                            response.data.usuarios_ativos.optgroups.forEach(group => {
                                const optgroup = $('<optgroup>').attr('label', group.label);
                                
                                group.options.forEach(option => {
                                    optgroup.append(`
                                        <option style="padding-left: 44px;" value="${option.value}" data-subtext="${option.subtext}">
                                            ${option.text}
                                        </option>
                                    `);
                                });
                                
                                select.append(optgroup);
                            });
                        }

                        if (response.data.usuarios_inativos && response.data.usuarios_inativos.optgroups && response.data.usuarios_inativos.optgroups.length > 0) {
                            select.append(`
                                <option disabled>
                                    <span class="label label-danger" style="font-size: 12px; border-radius: 10px;">Usuários inativos</span>
                                </option>
                            `)
                            response.data.usuarios_inativos.optgroups.forEach(group => {
                                const optgroup = $('<optgroup>').attr('label', group.label);

                                group.options.forEach(option => {
                                    optgroup.append(`
                                        <option style="padding-left: 44px;" value="${option.value}" data-subtext="${option.subtext}">
                                            ${option.text}
                                        </option>
                                    `);
                                });

                                select.append(optgroup);
                            });
                        }

                        // Reinicializa o selectpicker
                        select.selectpicker('refresh');

                        $('.btn-group.bootstrap-select').find('.dropdown-header').css('margin-left', '12px');
                        $('.btn-group.bootstrap-select').find('li.divider').remove(); // Remove todos os li.divider do dropdown gerado

                        usuariosLoaded = true;
                    } else {
                        console.error('Erro ao carregar usuários:', response);
                        select.empty().append('<option value="-1">Erro ao carregar</option>');
                        select.selectpicker('refresh');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erro na requisição:', error);
                    const select = $('#atribuido_para');
                    select.empty().append('<option value="-1">Erro ao carregar</option>');
                    select.selectpicker('refresh');
                }
            });
        }

        inicializarEventosSelecionados();
        inicializarSistemasOrigemSelecionados();
        inicializarOwnersSelecionados();
        inicializarPrioridadesSelecionadas();
        
        $('#eventoPacote').on('show.bs.select', function() {
            carregarEventos();
        });

        $('#sistemaorigemSelect').on('show.bs.select', function() {
            carregarSistemasOrigem();
        });

        $('#owner').on('show.bs.select', function() {
            carregarOwners();
        });

        $('#prioridadeSelect').on('show.bs.select', function() {
            carregarPrioridades();
        });

        $('#atribuido_para').on('show.bs.select', function() {
            if(usuariosLoaded) return;
            
            carregarSelectUsuarios();
        });
     
    })

    function busca_automatica(){
        var  $busca_automatica = $("#busca_automatica").val();

        if($busca_automatica != ''){
            $("#item_input").val($busca_automatica);

            $('#search_itens').submit();
        }

    }
    const base_url = "<?php echo base_url() ?>";

    $('#modal_motivo').on('show.bs.modal', function(e) {
        let idx_grupo_value = $("#accordion_grupos input:checked").val();
        let trAttr = $("#tr-grupo-" + idx_grupo_value);
        let memoria_classificacao = trAttr.attr('data-memoria-classificacao');
        let subsidio = trAttr.attr('data-subsidio');
        let caracteristica = trAttr.attr('data-caracteristica');

        if (memoria_classificacao && memoria_classificacao != undefined && memoria_classificacao != '') $("#memoria_classificacao").val(memoria_classificacao);

        if (subsidio && subsidio != undefined && subsidio != '') $("#subsidio").val(subsidio);

        if (caracteristica && caracteristica != undefined && caracteristica != '') $("#caracteristica").val(caracteristica);
        var checked_itens = $('input[type="checkbox"][name="item[]"]:checked');

        let bt_multpaises = $('#modal-multi-paises-diana');
        
        let $alert = $('#modal_motivo .alert-multiple-selection');
        let values = $('.item_selected:checked').map(function() {
            return $(this).val();
        }).get();

        if (!$alert.hasClass('hide')) {
            $alert.addClass('hide');
        }
        if (bt_multpaises.hasClass('hide')) {
            bt_multpaises.removeClass('hide');
        }

        $('#modal_motivo #marca, #modal_motivo #funcao, #modal_motivo #aplicacao, \
                #modal_motivo #material_constitutivo').val('');

        if (values.length == 1) {
            const estabelecimento = $('.item_selected:checked').data('estabelecimento');
            const data = {
                'part_number': values.shift(),
                'estabelecimento': estabelecimento
            };

            $.post(base_url + 'atribuir_grupo/ajax_get_item', data, function(response) {
                const e = JSON.parse(response);

                let $elm = null;

                $elm = $('#modal_motivo #marca');
                if ($elm.length > 0) {
                    $elm.val(e.item.marca);
                }

                $elm = $('#modal_motivo #funcao');
                if ($elm.length > 0) {
                    $elm.val(e.item.funcao);
                }

                $elm = $('#modal_motivo #inf_adicionais');
                if ($elm.length > 0) {
                    $elm.val(e.item.inf_adicionais);
                }

                $elm = $('#modal_motivo #material_constitutivo');
                if ($elm.length > 0) {
                    $elm.val(e.item.material_constitutivo);
                }

                $elm = $('#modal_motivo #aplicacao');
                if ($elm.length > 0) {
                    $elm.val(e.item.aplicacao);
                }

                $elm = $('#modal_motivo #descricao_proposta_completa');
                if ($elm.length > 0) {
                    $elm.val(e.item.descricao_proposta_completa);
                }

                $elm = $('#modal_motivo #observacoes_mestre');
                if ($elm.length > 0) {
                    $elm.val(e.item.observacoes);
                }

                $elm = $('#modal_motivo #evento');
                if ($elm.length > 0) {
                    $elm.val(e.item.evento);
                }

                $elm = $('#modal_motivo #descricao');
                if ($elm.length > 0) {
                    $elm.val(e.item.descricao);
                }

                $elm = $('#modal_motivo #memoria_classificacao');
                if ($elm.length > 0) {
                    $elm.val(e.item.memoria_classificacao);
                }
            });
        } else if (values.length > 1) {
            if ($alert.hasClass('hide')) {
                $alert.removeClass('hide');
            }

            if (!bt_multpaises.hasClass('hide')) {
                bt_multpaises.addClass('hide');
            }
            
        }
    });

    function clearInputs() {
        $('#modal_motivo').on('hidden.bs.modal', function () {
				var modal = $(this);
				var selects = modal.find('select.selectpicker');
				selects.each(function () {
					$(this).selectpicker('val', '').selectpicker('refresh');
				});
				
				modal.find('input[type="radio"]').prop('checked', false);
				modal.find('input, textarea').each(function () {
					var name = $(this).attr('name');
					if (name !== 'raw_attr[]') {
						$(this).val('');
					}
				});
			});
    }

    function exportItems  () {
		var checked_items = [];
		$('input[type="checkbox"][name="item[]"]:checked').each(function () {
			var inputElement = $(this);
            var part_number = inputElement.attr('data-part-number');
            checked_items.push(part_number);
		});

		// Cria a query string com os atributos part_number usando o mesmo nome de parâmetro
        var queryString = checked_items.map(function(item) {
            return 'part_number[]=' + encodeURIComponent(item);
        }).join('&');

        return queryString;
	}

	$('#desvincularGrupo').on('click', function(e) {
		e.preventDefault();
		var queryString = exportItems();
		console.log(queryString);

		// Concatena a query string na URL da rota
		var baseUrl = "<?php echo site_url('atribuir_grupo/deletar'); ?>";
		var exportUrl = baseUrl + '?' + queryString;

		// Redireciona para a rota com a query string
		window.location.href = exportUrl;
	});
</script>

<style>   
    .grupo_tarifario_table {
      border: 1px solid #dddddd;
      text-align: left;
      padding: 8px;
    }
    
</style>

<script>
    const button = document.getElementById("send_search_itens");
    const textarea = document.getElementById("item_input");
    function setInitialTextareaHeight() {
        const buttonHeight = button.offsetHeight;
        textarea.style.height = `${buttonHeight}px`;
    }

    setInitialTextareaHeight();

    function removeTextareaHeightLimit() {
        textarea.style.height = '';
    }
</script>