<script type="text/javascript">
    var item = {

        edit: function() {
            checked = $('input[type="checkbox"][name="item[]"]:checked');

            if (checked.length == 0) {
                swal('Atenção', 'Selecione um item para realizar a edição', 'warning');
                return false;
            }

            if (checked.length > 1) {
                swal('Atenção', 'Só é possível editar um item por vez', 'warning');
                return false;
            }

            item_id = $(checked).val();
            location.href = '<?php echo base_url() . "monitor-ex/addedit/" ?>' + item_id;
        },

        remove: function() {
            checked = $('input[type=checkbox]:checked');

            if (checked.length == 0) {
                swal('Atenção', 'Primeiro você deve selecionar os pleitos que deseja excluir.', 'warning');
                return false;
            }

            var id_list = new Array();

            $('input[type="checkbox"][name="item[]"]:checked').each(function() {
                id_list.push($(this).val());
            });

            var post_data = {
                'id_list': id_list
            };

            swal({
                title: "Atenção!",
                text: "Você deseja excluir os registros selecionados?",
                type: "warning",
                confirmButtonText: "OK",
                cancelButtonText: "Cancelar",
                showConfirmButton: true,
                showCancelButton: true,
                allowOutsideClick: false
			}).then(function () {
                $.post('<?php echo site_url("monitor-ex/remove") ?>',
                    post_data,
                    function(data, status, xhr) {

                        // var json = $.parseJSON(data);
                        var json = data;

                        if (json.status == true) {
                            window.location.reload();
                        } else {
                            swal('Atenção', "Ops... um erro aconteceu, recarregue a página.", 'warning');
                        }
                    }
                );
            });
        }
    };

    jQuery(document).ready(function() {
        // $('tbody').on('click', 'tr',function(e) {
        //     if(!$(e.target).prop('href'))
        //     {   
        //         var self = $(this);
        //         var selectedLine = self.find('input[type="checkbox"]');
        //         selectedLine.trigger("click"); 
        //     }
        // });
        // $('tbody').on('click', 'input[type="checkbox"]', function(e)
        // {
        //     if (e.clientX > 0 || e.clientY > 0)
        //     {
        //         e.stopPropagation();
        //     }
        // });
        $('.click-select').on('click', function(e) {
            if (e.target.nodeName != 'INPUT') {
                $(this).find('input').click();
            }
        })
    });
</script>

<div class="page-header">
    <h2>
        Monitoramento de EX / FTA
        <div class="pull-right">
            <?php if (has_role('sysadmin') || has_role('consultor')) : ?>
                <a href="<?php echo site_url("monitor-ex/pacotes") ?>" class="btn btn-default"><i class="glyphicon glyphicon-list"></i> Pacotes</a>
            <?php endif; ?>
            <a href="<?php echo site_url("monitor-ex/addedit") ?>" class="btn btn-default"><i class="glyphicon glyphicon-plus"></i> Nova ação</a>
            <button onclick="item.edit()" class="btn btn-default"><i class="glyphicon glyphicon-edit"></i> Editar</button>
            <button onclick="item.remove()" class="btn btn-danger"><i class="glyphicon glyphicon-trash"></i> Excluir</button>
            <?php if (customer_has_role('exportar_monitor_ex', sess_user_id())) : ?>
                <a href="<?php echo site_url("monitor_ex/exporta_monitor_ex") ?>" target="_blank" class="btn btn-default">  Exportar  <i class="glyphicon glyphicon-cloud-download"></i></a>
            <?php endif; ?>
        </div>
    </h2>
</div>
<div class="row" style="margin-top: 20px;margin-bottom: 20px;">
    <?php echo form_open('', array('id' => 'form_filter_monitor_ex', 'method' => 'post', 'class' => 'form-horizontal')); ?>
    <div class="col-md-12">
        <div class="row">
            <?php if (!empty($filters['tipo'])) : ?>
                <div class="col-md-3">
                    <select title="TODOS" data-width="100%" class="selectpicker" multiple="multiple" data-selected-text-format="count > 0" data-count-selected-text="Tipos ({0})" data-actions-box="true" name="tipo[]" id="filter_tipo">
                        <?php foreach ($filters['tipo'] as $tipo) : ?>
                            <option <?php if ((empty($this->ctrl_ex_tarifario_model->get_state('filter.id_tipo'))) || (in_array($tipo->id_tipo, $this->ctrl_ex_tarifario_model->get_state('filter.id_tipo'))))  echo 'selected="selected"' ?> value="<?php echo $tipo->id_tipo ?>"><?php echo $tipo->descricao ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            <?php endif; ?>
            <?php if (!empty($filters['evento'])) : ?>
                <div class="col-md-3">
                    <select title="TODOS" data-width="100%" class="selectpicker" multiple="multiple" data-selected-text-format="count > 0" data-count-selected-text="Eventos ({0})" data-actions-box="true" name="evento[]" id="filter_evento">
                        <?php foreach ($filters['evento'] as $evento) : ?>
                            <option <?php if ((empty($this->ctrl_ex_tarifario_model->get_state('filter.id_evento'))) || (in_array($evento->id_evento, $this->ctrl_ex_tarifario_model->get_state('filter.id_evento'))))  echo 'selected="selected"' ?> value="<?php echo $evento->id_evento ?>"><?php echo $evento->descricao ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            <?php endif; ?>
            <?php if (!empty($filters['pacote'])) : ?>
                <div class="col-md-3">
                    <select title="TODOS" data-width="100%" class="selectpicker" multiple="multiple" data-selected-text-format="count > 0" data-count-selected-text="Pacotes ({0})" data-actions-box="true" name="pacote[]" id="filter_pacote">
                        <?php foreach ($filters['pacote'] as $pacote) : ?>
                            <option <?php if ((empty($this->ctrl_ex_tarifario_model->get_state('filter.id_pacote'))) || (in_array($pacote->id_pacote, $this->ctrl_ex_tarifario_model->get_state('filter.id_pacote'))))  echo 'selected="selected"' ?> value="<?php echo $pacote->id_pacote ?>"><?php echo $pacote->descricao ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            <?php endif; ?>
            <?php if (!empty($filters['status'])) : ?>
                <div class="col-md-3">
                    <select title="TODOS" data-width="100%" class="selectpicker" multiple="multiple" data-selected-text-format="count > 0" data-count-selected-text="Status ({0})" data-actions-box="true" name="status[]" id="filter_status">
                        <?php $first = true;
                        $optgroup = ''; ?>
                        <?php foreach ($filters['status'] as $status) : ?>
                            <?php if (strtolower($status->estagio) != strtolower($optgroup)) : $optgroup = $status->estagio;
                                $close_opt = true; ?>
                                <?php if (!$first) : ?> </optgroup> <?php endif;
                                                                $first = false; ?>
                                <optgroup label="<?php echo $optgroup; ?>"> ?>
                                <?php endif; ?>
                                <option <?php if ((empty($this->ctrl_ex_tarifario_model->get_state('filter.id_status'))) || (in_array($status->id_status, $this->ctrl_ex_tarifario_model->get_state('filter.id_status')))) echo 'selected="selected"' ?> value="<?php echo $status->id_status ?>"><?php echo $status->descricao ?></option>
                            <?php endforeach; ?>
                    </select>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <div class="row" style="margin-bottom: 10px;"></div>
    <div class="col-md-12">
        <textarea class="form-control" placeholder="PESQUISE POR NCM, PART NUMBER OU TEXTO..." rows="3" id="search" name="search"><?php echo !empty($this->ctrl_ex_tarifario_model->get_state('filter.search_view')) ? $this->ctrl_ex_tarifario_model->get_state('filter.search_view') : '' ?></textarea>
    </div>
    <div class="col-md-12">
        <strong><label class="radio-inline"><input type="radio" name="search_type" value="ncm" <?php echo $this->ctrl_ex_tarifario_model->get_state('filter.search_type') == 'ncm' || empty($this->ctrl_ex_tarifario_model->get_state('filter.search_type')) ? 'checked' : '' ?>>NCM</label></strong>
        <strong><label class="radio-inline"><input type="radio" name="search_type" value="part_number" <?php echo $this->ctrl_ex_tarifario_model->get_state('filter.search_type') == 'part_number' ? 'checked' : '' ?>>PART NUMBER</label></strong>
        <strong><label class="radio-inline"><input type="radio" name="search_type" value="text" <?php echo $this->ctrl_ex_tarifario_model->get_state('filter.search_type') == 'text' ? 'checked' : '' ?>>TEXTO</label></strong>
        <strong><label class="radio-inline"><input type="radio" name="search_type" value="sdci" <?php echo $this->ctrl_ex_tarifario_model->get_state('filter.search_type') == 'sdci' ? 'checked' : '' ?>>SDCI</label></strong>
        <a href="<?php echo site_url('monitor_ex?reset_filters=1'); ?>" class="btn btn-link" style="float: right !important;">Limpar Pesquisa</a>
    </div>
    <div class="col-md-12">
        <button class="btn btn-primary btn-block" value="1" name="submit" type="submit">Pesquisar</button>
    </div>
    <?php echo form_close(); ?>
</div>
<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead>
            <tr>
                <th width="30">
                    <input type="checkbox" id="toggle-checkbox" onclick="toggle_checkbox(this)">
                </th>
                <th width="350">Título / Num EX / FTA</th>
                <th>NCM</th>
                <th>SDCI</th>
                <th>Tipo</th>
                <th>Evento</th>
                <th>Pacote</th>
                <th>Status</th>
                <th width="70">

                </th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($entries)) : ?>
                <?php foreach ($entries as $entry) : ?>
                    <tr class="click-select">
                        <td>
                            <input type="checkbox" data-toggle="true" name="item[]" value="<?php echo $entry->id_ctrl_ex ?>">
                        </td>
                        <td class="text-uppercase">
                            <a href="<?php echo site_url('/monitor-ex/detalhes/' . $entry->id_ctrl_ex) ?>">
                                <?php echo !empty($entry->titulo_ex) ? $entry->titulo_ex : 'N/A' ?>
                                <br>
                                <small><?php echo $entry->num_ex ?></small>
                            </a>
                        </td>
                        <td class="text-uppercase"><?php echo !empty($entry->cod_ncm) ? $entry->cod_ncm : 'N/A' ?></td>
                        <td class="text-uppercase"><?php echo !empty($entry->cod_sdci) ? $entry->cod_sdci : 'N/A' ?></td>
                        <td class="text-uppercase"><?php echo !empty($entry->descricao_tipo) ? $entry->descricao_tipo : 'N/A' ?></td>
                        <td class="text-uppercase"><?php echo !empty($entry->descricao_evento) ? $entry->descricao_evento : 'N/A' ?></td>
                        <td class="text-uppercase"><?php echo !empty($entry->descricao_pacote) ? $entry->descricao_pacote : 'N/A' ?></td>
                        <td class="text-uppercase">
                            <?php echo !empty($entry->estagio_status) ? $entry->estagio_status : '' ?><br>
                            <div class="label label-default">
                                <?php echo !empty($entry->descricao_status) ? $entry->descricao_status : 'N/A' ?>
                            </div>
                        </td>
                        <td class="text-center">
                            <a href="<?php echo base_url('/monitor-ex/detalhes/' . $entry->id_ctrl_ex) ?>" class="btn btn-primary btn-inline btn-xs">
                                <i class="fa fa-eye"></i>
                            </a>
                            <a href="<?php echo base_url('/monitor-ex/addedit/' . $entry->id_ctrl_ex) ?>" class="btn btn-default btn-xs btn-inline">
                                <i class="glyphicon glyphicon-edit"></i>
                            </a>
                        </td>
                    </tr>
                <?php endforeach ?>
            <?php else : ?>
                <tr>
                    <td colspan="8" class="text-center">Nenhuma informação encontrada</td>
                </tr>
            <?php endif ?>
        </tbody>
    </table>
</div>
<?php echo !empty($pagination) ? $pagination : NULL ?>