<script type="text/javascript">
    $(function() {
        var base_url = '<?php echo base_url();  ?>';
        // $('#select-id_grupo_tarifario').change(function () {
        //     var ncm_recomendada = $(this).find('option:selected').data('ncm-recomendada');
        //     $('#input-cod_ncm').val(ncm_recomendada);
        // });

        $('.date').datetimepicker({
            locale: 'pt-BR'
        });

        $(".maskmoney").maskMoney({
            'thousands': '.',
            'decimal': ','
        });

        $('#ganho_variavel_ativo').on('change', function() {
            if ($(this).prop('checked') == true) {
                $('.periodo-ganho-variavel').show();
            } else {
                $('.periodo-ganho-variavel').hide();
            }
        }).trigger('change');

        CKEDITOR.config.height = '150px';

        var arrEvento = [];

        <?php $arr_evento_slug = array(); ?>
        <?php foreach ($lista_eventos as $key => $evento) : $arr_evento_slug[$evento->id_evento] = $evento->slug; ?>
            arrEvento['<?php echo $evento->id_evento; ?>'] = '<?php echo $evento->slug ?>';
        <?php endforeach; ?>

        $("#select-id_evento").on('change', function(ev) {
            var self = $(this);
            var forHtml = $("#for-input-num_ex span");
            if (arrEvento[this.value] != 'novo-ex-tarifario') {
                forHtml.css('display', 'initial');
            } else {
                forHtml.css('display', 'none');
            }
        });

        $('#select-id_grupo_tarifario').autocomplete({
            serviceUrl: '<?php echo base_url('monitor_ex/xhr_get_lista_grupos_tarif') ?>',
            minChars: 3,
            onSearchStart: function() {
                $("#button-search-grupo-tarifario").find('span').css('display', 'none');
                $("#button-search-grupo-tarifario").find('img').css('display', 'inline-block');
            },
            onSearchComplete: function() {
                $("#button-search-grupo-tarifario").find('span').css('display', 'inline-block');
                $("#button-search-grupo-tarifario").find('img').css('display', 'none');
            },
            onSelect: function(suggestion) {
                $('#select-id_grupo_tarifario_hidden').val(suggestion.data);
                $('#select-id_grupo_tarifario_hidden').trigger('change');
                $('#input-cod_ncm').val(suggestion.ncm);
            }
        });


        // Implementação tarefa 11500
        $("#select-id_grupo_tarifario_hidden, #input-num_ex, #input-cod_ncm").on("change keyup", function() {
            var selectGrupoTarifario = $("#select-id_grupo_tarifario_hidden");
            var numEx = $("#input-num_ex");

            if (selectGrupoTarifario.val() != "" &&
                numEx.val() != "" &&
                selectGrupoTarifario.val() != undefined &&
                numEx.val() != undefined &&
                selectGrupoTarifario.val() != null &&
                numEx.val() != null) {
                var data = {
                    cod_ncm: $("#input-cod_ncm").val(),
                    num_ex: numEx.val()
                };

                $.post(base_url + 'monitor_ex/ajax_get_ex_by_ncm_exnumber', data, function(data) {
                    if (data != undefined && data != null && data != '') {
                        var resp = JSON.parse(data);
                        $("#inicio_vigencia").val(resp.dat_vigencia_ini);
                        $("#fim_vigencia").val(resp.dat_vigencia_fim);
                        // $("#select-id_tipo_ex").val(resp.cod_tipo_ex);
                        // $("#select-id_tipo_ex").selectpicker('refresh');
                        CKEDITOR.instances['textarea-detalhes'].setData(resp.descricao_linha1);
                    }
                });
            }
        });
    });
</script>

<?php echo form_open_multipart('') ?>

<div class="row">
    <div class="col-md-4">
        <div class="form-group">
            <label for="select-id_evento" class="control-label">Evento <span class="required text-danger">*</span></label>
            <select data-live-search="true" data-title="--" class="selectpicker form-control" name="id_evento" id="select-id_evento">
                <?php if (!empty($lista_eventos)) : ?>
                    <?php foreach ($lista_eventos as $value) : ?>
                        <option <?php echo set_select('id_evento', $value->id_evento, !empty($entry) ? $entry->id_evento == $value->id_evento : NULL) ?> value="<?php echo $value->id_evento ?>"><?php echo $value->descricao ?></option>
                    <?php endforeach ?>
                <?php endif; ?>
            </select>
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            <label for="input-titulo_ex" class="control-label">Título do EX/FTA <span class="required text-danger">*</span></label>
            <input type="text" class="form-control" value="<?php echo set_value('titulo_ex', !empty($entry) ? $entry->titulo_ex : NULL) ?>" name="titulo_ex" id="input-titulo_ex" autofocus>
        </div>
    </div>
    <div class="col-md-2">
        <div class="form-group">
            <label for="input-num_ex" id="for-input-num_ex" class="control-label">Núm. EX/FTA <span <?php if ((!empty($entry_evento) && $entry_evento->slug == 'novo-ex-tarifario') || (isset($arr_evento_slug[set_value('id_evento', !empty($entry) ? $entry->id_evento : NULL)]) && $arr_evento_slug[set_value('id_evento', !empty($entry) ? $entry->id_evento : NULL)] == 'novo-ex-tarifario')) : ?> style="display:none;" <?php endif; ?> class="required text-danger">*</span></label>
            <input type="text" class="form-control" value="<?php echo set_value('num_ex', !empty($entry) ? $entry->num_ex : NULL) ?>" name="num_ex" id="input-num_ex">
        </div>
    </div>
    <div class="col-md-2">
        <div class="form-group">
            <label for="select-id_tipo_ex" class="control-label">Tipo do EX <span class="required text-danger">*</span></label>
            <select data-live-search="true" class="selectpicker form-control" data-title="--" name="id_tipo_ex" id="select-id_tipo_ex">
                <?php if (!empty($lista_tipos)) : ?>
                    <?php foreach ($lista_tipos as $value) : ?>
                        <option <?php echo set_select('id_tipo_ex', $value->id_tipo, !empty($entry) ? $entry->id_tipo_ex == $value->id_tipo : NULL) ?> value="<?php echo $value->id_tipo ?>"><?php echo $value->descricao ?></option>
                    <?php endforeach ?>
                <?php endif; ?>
            </select>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="form-group">
            <label for="select-id_pacote" class="control-label">Pacote <span class="required text-danger">*</span></label>
            <select data-live-search="true" class="selectpicker form-control" data-title="--" name="id_pacote" id="select-id_pacote">
                <?php if (!empty($lista_pacotes)) : ?>
                    <?php foreach ($lista_pacotes as $value) : ?>
                        <option <?php echo set_select('id_pacote', $value->id_pacote, !empty($entry) ? $entry->id_pacote == $value->id_pacote : NULL) ?> value="<?php echo $value->id_pacote ?>"><?php echo $value->descricao ?></option>
                    <?php endforeach ?>
                <?php endif; ?>
            </select>
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            <label for="select-id_responsavel_cliente" class="control-label">Responsável Cliente Pleito <span class="required text-danger">*</span></label>
            <select data-live-search="true" class="selectpicker form-control" data-title="--" name="id_responsavel_cliente" id="select-id_responsavel_cliente">
                <?php if (!empty($lista_responsaveis_cliente_ativos)) : ?>
                    <option disabled>
                        <span class="label label-success" style="font-size: 12px; border-radius: 10px;">Usuários ativos</span>
                    </option>
                    <?php foreach ($lista_responsaveis_cliente_ativos as $value) : ?>
                        <option style="padding-left: 32px;" <?php echo set_select('id_responsavel_cliente', $value->id_usuario, !empty($entry) ? $entry->id_responsavel_cliente == $value->id_usuario : NULL) ?> value="<?php echo $value->id_usuario ?>"><?php echo $value->nome ?></option>
                    <?php endforeach ?>
                <?php endif; ?>
                <?php if (!empty($lista_responsaveis_cliente_inativos)) : ?>
                    <option disabled>
                        <span class="label label-danger" style="font-size: 12px; border-radius: 10px;">Usuários inativos</span>
                    </option>
                    <?php foreach ($lista_responsaveis_cliente_inativos as $value) : ?>
                        <option style="padding-left: 32px;" <?php echo set_select('id_responsavel_cliente', $value->id_usuario, !empty($entry) ? $entry->id_responsavel_cliente == $value->id_usuario : NULL) ?> value="<?php echo $value->id_usuario ?>"><?php echo $value->nome ?></option>
                    <?php endforeach ?>
                <?php endif; ?>
            </select>
        </div>
    </div>
    <div class="col-md-2">
        <div class="form-group">
            <label for="inicio_vigencia" class="control-label">Início da vigência</label>
            <div class="input-group">
                <div class="input-group">
                    <input id="inicio_vigencia" value="<?php echo set_value('inicio_vigencia', !empty($entry) ? $entry->inicio_vigencia() : NULL) ?>" name="inicio_vigencia" class="form-control date" placeholder="Data inicial" data-date-format="DD/MM/YYYY" type="text" value="">
                    <label for="inicio_vigencia" class="input-group-addon">
                        <i data-time-icon="glyphicon-time" data-date-icon="glyphicon-calendar" class="glyphicon glyphicon-calendar"></i>
                    </label>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="form-group">
            <label for="fim_vigencia" class="control-label">Fim da vigência</label>
            <div class="input-group">
                <div class="input-group">
                    <input id="fim_vigencia" value="<?php echo set_value('fim_vigencia', !empty($entry) ? $entry->fim_vigencia() : NULL) ?>" name="fim_vigencia" class="form-control date" placeholder="Data final" data-date-format="DD/MM/YYYY" type="text">
                    <label for="fim_vigencia" class="input-group-addon">
                        <i data-time-icon="glyphicon-time" data-date-icon="glyphicon-calendar" class="glyphicon glyphicon-calendar"></i>
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-10">
        <div class="form-group">
            <label for="select-id_grupo_tarifario" class="control-label">Grupo Tarifário <span class="required text-danger">*</span></label>
            <div class="input-group">
                <input type="text" class="form-control" value="<?php echo set_value('descricao_gt', !empty($entry) ? $entry->descricao_gt : NULL) ?> " id="select-id_grupo_tarifario" placeholder="" autocomplete="off" />
                <input type="hidden" name="id_grupo_tarifario" id="select-id_grupo_tarifario_hidden" value="<?php echo set_value('id_grupo_tarifario', !empty($entry) ? $entry->id_grupo_tarifario : NULL) ?>" />
                <span class="input-group-btn">
                    <button class="btn btn-primary" type="button" id="button-search-grupo-tarifario">
                        <span class="glyphicon glyphicon-search"></span>
                        <img src="<?php echo site_url('/assets/img/loading.gif'); ?>" width="15" height="15" style="display: none;">
                    </button>
                </span>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="form-group">
            <label for="input-cod_ncm" class="control-label">NCM</label>
            <input type="text" class="form-control" value="<?php echo set_value("cod_ncm", !empty($entry) ? $entry->cod_ncm : NULL) ?>" name="cod_ncm" id="input-cod_ncm">
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-3">
        <div class="form-group">
            <label for="input-economia_estimada" class="control-label">Economia Estimada</label>
            <div class="input-group">
                <span class="input-group-addon">R$</span>
                <input type="text" value="<?php echo set_value("economia_estimada", !empty($entry) ? format_number($entry->economia_estimada) : NULL) ?>" class="form-control maskmoney" id="input-economia_estimada" name="economia_estimada">
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="form-group">
            <label style="margin-top: 5px" class="control-label" for="ganho_variavel_ativo">&nbsp;</label>
            <div>
                <input type="checkbox" id="ganho_variavel_ativo" name="ganho_variavel_ativo" value="1" <?php echo set_checkbox("ganho_variavel_ativo", 1, !empty($entry) ? $entry->ganho_variavel_ativo == 1 : NULL) ?> data-size="mini" data-toggle="toggle" data-style="ios" data-on="&nbsp;" data-off="&nbsp;" />
                <label onclick="$('#ganho_variavel_ativo').bootstrapToggle('toggle')">Possui Ganho Variável?</label>
            </div>
        </div>
    </div>
    <div class="col-md-3 periodo-ganho-variavel">
        <div class="form-group">
            <label for="inicio_apuracao_ganho_variavel" class="control-label">Período inicial ganho variável</label>
            <div class="input-group">
                <input value="<?php echo set_value('inicio_apuracao_ganho_variavel', !empty($entry) ? $entry->inicio_apuracao_ganho_variavel() : NULL) ?>" id="inicio_apuracao_ganho_variavel" name="inicio_apuracao_ganho_variavel" class="form-control date" placeholder="Data inicial" data-date-format="DD/MM/YYYY" type="text">
                <label for="inicio_apuracao_ganho_variavel" class="input-group-addon">
                    <i data-time-icon="glyphicon-time" data-date-icon="glyphicon-calendar" class="glyphicon glyphicon-calendar"></i>
                </label>
            </div>
        </div>
    </div>
    <div class="col-md-3 periodo-ganho-variavel">
        <div class="form-group">
            <label for="fim_apuracao_ganho_variavel" class="control-label">Período final ganho variável</label>
            <div class="input-group">
                <input value="<?php echo set_value('fim_apuracao_ganho_variavel', !empty($entry) ? $entry->fim_apuracao_ganho_variavel() : NULL) ?>" id="fim_apuracao_ganho_variavel" name="fim_apuracao_ganho_variavel" class="form-control date" placeholder="Data final" data-date-format="DD/MM/YYYY" type="text">
                <label for="fim_apuracao_ganho_variavel" class="input-group-addon">
                    <i data-time-icon="glyphicon-time" data-date-icon="glyphicon-calendar" class="glyphicon glyphicon-calendar"></i>
                </label>
            </div>
        </div>
    </div>
    <div class="col-md-3 codigo-sdci">
        <div class="form-group">
            <label for="input-cod_sdci">Código SDCI</label>
            <input type="text" value="<?php echo set_value("cod_sdci", !empty($entry) ? $entry->cod_sdci : NULL) ?>" name="cod_sdci" id="input-cod_sdci" class="form-control">
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="textarea-detalhes" class="control-label">Descrição Sugerida do Ex / Descrição do Acordo Econômico</label>
            <textarea style="height: 300px" type="text" class="form-control ckeditor" name="detalhes" id="textarea-detalhes"><?php
                                                                                                                                echo set_value('detalhes', !empty($entry) ? $entry->detalhes : NULL)
                                                                                                                                ?></textarea>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12 text-right">
        <a class="btn btn-link" href="<?php echo site_url('monitor-ex') ?>">Cancelar</a>

        <button class="btn btn-primary" type="submit" name="salvar" id="salvar" value="1">
            <span class="glyphicon glyphicon-floppy-disk"></span>
            Salvar
        </button>

    </div>
</div>

<?php echo form_close(); ?>

<style type="text/css">
    .form-adjust-max-size .bootstrap-select .dropdown-menu {
        max-width: 100% !important;
    }
</style>