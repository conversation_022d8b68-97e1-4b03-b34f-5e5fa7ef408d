<?php

/**
 * Partial para campo de filtro
 *
 * Parâmetros esperados:
 * - $type: 'select', 'checkbox', 'date_range', 'text'
 * - $name: nome do campo
 * - $label: rótulo do campo
 * - $options: array de opções para selects (formato: [['value' => '', 'label' => '']])
 * - $value: valor atual do campo
 * - $ajax_url: URL para carregamento dinâmico (opcional)
 * - $select_config: configurações específicas do Bootstrap Select (opcional)
 *
 * Exemplos de $select_config:
 * [
 *     'multiple' => false,           // Select simples (não múltiplo)
 *     'data-live-search' => 'true',  // Habilitar busca
 *     'data-actions-box' => 'false', // Desabilitar ações (Selecionar Todos/Nenhum)
 *     'data-max-options' => '3'      // Máximo de opções selecionáveis
 * ]
 */
?>
<div class="form-group <?= $col_class ?? '' ?> <?= $type === 'date_range' ? 'date-range-col' : '' ?>">
    <label for="<?= $name ?>"><?= $label ?></label>
    <?php if ($type === 'select'): ?>
        <?php
        // Configurações padrão do selectpicker
        $default_config = [
            'multiple' => true,
            'data-ajax-url' => $ajax_url ?? '',
            'data-loaded' => 'false',
            'data-depends-on' => $depends_on ?? '',
            'data-filter-options' => $filter_options ?? 'true',
            'title' => $title ?? 'Selecione...',
            'data-count-selected-text' => $label . ' ({0})',
            'data-selected-text-format' => 'count > 1',
            'data-deselect-all-text' => 'Nenhum',
            'data-select-all-text' => 'Todos',
            'data-actions-box' => 'true',
            'data-live-search' => 'true'
        ];

        // Mesclar com configurações específicas do filtro
        $select_config = $select_config ?? [];
        $final_config = array_merge($default_config, $select_config);

        // Remover configurações vazias
        $final_config = array_filter($final_config, function ($value) {
            return $value !== '' && $value !== null;
        });
        ?>
        <select
            id="<?= $name ?>"
            name="<?= $name ?><?= $final_config['multiple'] ? '[]' : '' ?>"
            class="selectpicker form-control"
            <?php foreach ($final_config as $attr => $value): ?>
            <?php if ($attr === 'multiple'): ?>
            <?= $value ? 'multiple' : '' ?>
            <?php else: ?>
            <?= $attr ?>="<?= htmlspecialchars($value) ?>"
            <?php endif; ?>
            <?php endforeach; ?>>
            <?php if (!empty($options)): ?>
                <?php foreach ($options as $opt): ?>
                    <option value="<?= $opt['value'] ?>" <?= in_array($opt['value'], (array)$value) ? 'selected' : '' ?>><?= $opt['label'] ?></option>
                <?php endforeach; ?>
            <?php elseif (!empty($ajax_url)): ?>
                <option value="">Carregando...</option>
            <?php else: ?>
                <option value="">Selecione...</option>
            <?php endif; ?>
        </select>
    <?php elseif ($type === 'checkbox'): ?>
        <?php if (strpos(strtolower($name), 'triagem_diana') !== false): ?>
            <div class="toggle-switch">
                <input type="hidden" name="<?= $name ?>" value="0">
                <input
                    type="checkbox"
                    id="<?= $name ?>"
                    name="<?= $name ?>"
                    value="1"
                    <?= $value ? 'checked' : '' ?>
                    class="toggle-checkbox">
                <label for="<?= $name ?>" class="toggle-label">
                    <span class="toggle-inner"></span>
                    <span class="toggle-switch-text"><?= $label ?></span>
                </label>
            </div>
        <?php else: ?>
            <div class="<?= $extra_class ?? 'form-check' ?>">
                <input type="checkbox" class="form-check-input" id="<?= $name ?>" name="<?= $name ?>" value="1" <?= $value ? 'checked' : '' ?>>
                <label class="form-check-label" for="<?= $name ?>"><?= $label ?></label>
            </div>
        <?php endif; ?>
    <?php elseif ($type === 'date_range'): ?>
        <div class="date-range-group">
            <!-- Campo De -->
            <div class="input-group date datetimepicker">
                <span class="input-group-addon">De</span>
                <input type="text" class="form-control datetimepicker"
                    placeholder="Data inicial"
                    id="<?= $name ?>_from"
                    name="<?= $name ?>_from"
                    value="<?= $value['from'] ?? '' ?>">
            </div>

            <!-- Campo a -->
            <div class="input-group date datetimepicker">
                <span class="input-group-addon">a</span>
                <input type="text" class="form-control datetimepicker"
                    placeholder="Data final"
                    id="<?= $name ?>_to"
                    name="<?= $name ?>_to"
                    value="<?= $value['to'] ?? '' ?>">
            </div>
        </div>
    <?php else: ?>
        <input type="text" class="form-control" id="<?= $name ?>" name="<?= $name ?>" value="<?= $value ?? '' ?>">
    <?php endif; ?>
</div>