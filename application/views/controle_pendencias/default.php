<?php echo form_open_multipart('', array('class' => 'form-horizontal')) ?>
<div class=" form-group row">
    <div class="col-md-12">

        <div class="page-header">
            <div class="row">
                <div class="col-md-12">
                    <h2>Controle de pendências</h2>


                    <div class="form-group">
                        <div class="" style="float: right;      margin-top: -43px; margin-right: 20px;">
                            <div class="fileupload fileupload-new" data-provides="fileupload">
                                <?php if (customer_has_role('exportar_controle_pendencias',sess_user_id())) : ?>
                                <button class="btn btn-primary btn-sm btn-baixar" name="baixar" value="1" type="submit"><i class="glyphicon glyphicon-cloud-download"></i> Baixar</button>
                                <?php endif; ?>
                                <span class="btn btn-success btn-file"><span class="fileupload-new">Importar</span>
                                    <span class="fileupload-exists">Modificar <span class="fileupload-preview"></span></span> <input type="file" name="arquivo" id="arquivo" /></span>
                                <a href="#" class="close fileupload-exists" data-dismiss="fileupload" style="float: none">×</a>
                            </div>
                        </div>
                        <div class="" style="padding-left: 0px;">
                            <button id="btn-enviar" class="btn-enviar btn btn-success btn-sm" name="send" value="1" type="submit"><i class="glyphicon glyphicon-upload"></i> Enviar</button>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
<?= form_close(); ?>
<?php echo form_open('', array('class' => 'form-inline', 'id' => 'pesquisa_form', 'method' => 'GET')) ?>
<div class="row">
    <div class="form-group col-md-6" style="margin-bottom: 15px">
        <label>Part Number / Descrição: </label>

        <div id="search_itens_holder">
            <div>
                <input value="<?php echo !empty($pesquisa) ? $pesquisa : ''; ?>" style="width:100%;" type="text" class="form-control" name="partnumbers" id="partnumbers" placeholder="Digite o(s) código(s) ou descrição do(s) item(ns)" />
            </div>
        </div>

    </div>

    <div class="form-group col-md-6" style="margin-bottom: 15px">
        <label>Status:</label>

        <select class="selectpicker" multiple="multiple" data-selected-text-format="count > 0" data-count-selected-text="Status ({0})" name="status[]" title="Selecionar" id="status">
            <option value="0" <?php echo (isset($status) && is_array($status) && in_array(0, $status)) ? 'selected' : ''; ?>>
                Respondidas
            </option>
            <option value="1" <?php echo $this->input->is_set("status") ? ((isset($status) && is_array($status) && in_array(1, $status)) ? 'selected' : '') : 'selected'; ?>>
                Pendentes
            </option>
        </select>
    </div>

</div>
<div class="row">

    <?php if ($hasOwner) : ?>
        <div class="form-group col-md-6" style="margin-bottom: 15px">
            <label>Tipo de Responsável:</label>
            <div>
                <input type="radio" name="responsible_type" id="user" value="user" <?php echo isset($responsible_type) && $responsible_type == 'user' ? 'checked' : '' ?> />
                <label for="user" class="radio-input">Usuário</label>
                <input type="radio" name="responsible_type" id="owner" value="owner" <?php echo ($responsible_type == 'owner' || $responsible_type == false) ? 'checked' : '' ?> />
                <label for="owner" class="radio-input">Owner</label>
            </div>
        </div>

        <div class="form-group col-md-6" id="userSelect" style="margin-bottom: 15px; <?php echo ($responsible_type == 'owner' || $responsible_type == false) ? 'display: none' : '' ?>">
            <label>Responsável</label>

            <select class="form-control selectpicker" multiple data-selected-text-format="count > 0" data-count-selected-text="Usuário Pergunta ({0})" data-live-search="true" data-actions-box="true" data-select-all-text="Todos" data-deselect-all-text="Nenhum" name="usuarioPergunta[]" title="Selecionar" id="usuarioPergunta">
                <?php if (count($usuariosFilterAtivos) > 0) : ?>
                    <option disabled>
                        <span class="label label-success" style="font-size: 12px; border-radius: 10px;">Usuários ativos</span>
                    </option>
                    <?php foreach ($usuariosFilterAtivos as $item) : ?>
                        <option style="padding-left: 32px;" value="<?php echo $item->id_usuario; ?>" <?php echo in_array($item->id_usuario, $usuarios) ? 'selected' : ''; ?>>
                            <?php echo $item->nome; ?>
                        </option>
                    <?php endforeach; ?>
                <?php endif; ?>
                <?php if (count($usuariosFilterInativos) > 0) : ?>
                    <option disabled>
                        <span class="label label-danger" style="font-size: 12px; border-radius: 10px;">Usuários inativos</span>
                    </option>
                    <?php foreach ($usuariosFilterInativos as $item) : ?>
                        <option style="padding-left: 32px;" value="<?php echo $item->id_usuario; ?>" <?php echo in_array($item->id_usuario, $usuarios) ? 'selected' : ''; ?>>
                            <?php echo $item->nome; ?>
                        </option>
                    <?php endforeach; ?>
                <?php endif; ?>
            </select>
        </div>

        <div class="form-group col-md-6" id="ownerSelect" style="<?php echo isset($responsible_type) && $responsible_type == 'user' ? 'display: none' : '' ?>">
            <label>Owner</label>

            <select class="form-control selectpicker" multiple data-selected-text-format="count > 0" data-count-selected-text="Owner ({0})" data-live-search="true" data-actions-box="true" data-select-all-text="Todos" data-deselect-all-text="Nenhum" name="ownerPergunta[]" title="Selecionar" id="ownerPergunta">
                <?php if (count($ownersFilter) > 0) : ?>
                    <?php foreach ($ownersFilter as $item) : ?>
                        <option value="<?php echo $item->codigo; ?>" <?php echo in_array($item->codigo, $owners) ? 'selected' : ''; ?>>
                            <?php echo $item->codigo; ?> - <?php echo $item->descricao; ?> - <?php echo $item->nomes; ?>
                        </option>
                    <?php endforeach; ?>
                <?php endif; ?>
            </select>

        </div>
    <?php else : ?>
        <div class="form-group col-md-6" id="userSelect" style="margin-bottom: 15px;">
            <label>Responsável</label>

            <select class="form-control selectpicker" multiple data-selected-text-format="count > 0" data-count-selected-text="Usuário Pergunta ({0})" data-live-search="true" data-actions-box="true" data-select-all-text="Todos" data-deselect-all-text="Nenhum" name="usuarioPergunta[]" title="Selecionar" id="usuarioPergunta">
                <?php if (!empty($usuariosFilter) && count($usuariosFilter) > 0) : ?>
                    <?php foreach ($usuariosFilter as $item) : ?>
                        <option value="<?php echo $item->id_usuario; ?>" <?php echo in_array($item->id_usuario, $usuarios) ? 'selected' : ''; ?>>
                            <?php echo $item->nome; ?>
                        </option>
                    <?php endforeach; ?>
                <?php endif; ?>
            </select>
        </div>
    <?php endif; ?>
</div>

<div class="row">

    <div class="col-md-3 form-group" style="margin-bottom: 15px">

        <label>Data de:</label>

        <div class="input-group date datetimepicker" id="periodoIni">
            <input style="width: 225px;" type="text" class="form-control" name="periodoIni" value="<?php echo $this->input->get("periodoIni"); ?>" placeholder="Data inicial" />
            <span class="input-group-addon">
                <span class="glyphicon glyphicon-calendar"></span>
            </span>
        </div>
    </div>


    <div class="col-md-3 form-group" style="margin-bottom: 10px">
        <label>Data até:</label>

        <div class="input-group date datetimepicker" id="periodoFim" style="float: right;">
            <input style="width: 225px;" type="text" class="form-control" name="periodoFim" value="<?php echo $this->input->get("periodoFim"); ?>" placeholder="Data final" />
            <span class="input-group-addon">
                <span class="glyphicon glyphicon-calendar"></span>
            </span>
        </div>
    </div>
    <?php if ($hasPrioridade) : ?>
        <div class="form-group col-md-6" id="prioridadeSelect">
            <label>Prioridade</label>

            <select class="form-control selectpicker" multiple data-selected-text-format="count > 0" data-count-selected-text="Prioridades ({0})" data-live-search="true" data-actions-box="true" data-select-all-text="Todos" data-deselect-all-text="Nenhum" name="prioridade[]" title="Selecionar" id="prioridade">

                <?php foreach ($prioridadesFilter as $prioridade) : ?>
                    <option <?php echo in_array($prioridade->id_prioridade, $prioridades) ? "selected" : ""; ?> value="<?php echo $prioridade->id_prioridade; ?>"><?php echo $prioridade->nome; ?></option>
                <?php endforeach; ?>
            </select>

        </div>
    <?php endif; ?>
</div>

<input type="hidden" name="search_questions" value="1" />
<a href="<?php echo site_url("controle_pendencias/index?unsetFilters=1") ?>" class="btn btn-link pull-right" style="padding-right: 0px">Limpar pesquisa</a>
<button class="btn btn-primary btn-block" value="1" type="button" id="pesquisaForm">Pesquisar</button>
</form>

<div class="alert bulk-selection-box text-center alert-warning" role="alert" <?php echo isset($bulk_selection['item']) && count($bulk_selection['item']) > 0 ? '' : 'style="display: none"' ?>>
    <div class="bulk-selection-loading"></div>

    <div class="bulk-info-actions">
        Você selecionou: <strong><span class="bulk-selection-count"><?php echo isset($bulk_selection['item']) ? count($bulk_selection['item']) : '' ?></span></strong> item(ns) &ndash;

        <a href="javascript: void(0)" onclick="bulk_clean()">Deseja limpar a seleção?</a>
    </div>
</div>


<?php if (isset($itens)) : ?>
    <hr />
    <?php if (count($itens) > 0) : ?>
        <div class="clearfix"></div>
        <div class="text-center">
            <?php echo $this->pagination->create_links() ?>
        </div>

        <form action="<?php echo base_url('controle_pendencias/responder') ?>" id="form-answer">
            <button type="submit" class="btn btn-success pull-right mb-5 btn-answer" style="display: none">
                <i class="glyphicon glyphicon-share"></i>
                Responder perguntas
            </button>
        </form>

        <?php if (customer_has_role('visualizar_transferir_perguntas', sess_user_id())) : ?>
            <button class="btn btn-success pull-right mb-5 btn-transfer" style=" margin-right: 20px;" data-toggle="modal" data-target="#transferirResponsavel" data-part-number="" data-estabelecimento="">
                <i class="glyphicon glyphicon-transfer"></i>Transferir Perguntas
            </button>
        <?php endif; ?>
        <table class="table table-striped table-hover" border="0" cellspacing="5" cellpadding="5">
            <thead>
                <tr>
                    <th width="1%">
                        <input type="checkbox" id="toggle-checkbox" onclick="toggle_checkbox(this)">
                    </th>
                    <th width="10%">PART NUMBER</th>
                    <th width="5%">Estabelecimento</th>
                    <?php if ($hasPrioridade) : ?>
                        <th width="10%">Descrição</th>
                        <th width="5%">Prioridade</th>
                    <?php else : ?>
                        <th width="15%">Descrição</th>
                    <?php endif ?>
                    <th width="5%">Peso</th>
                    <th width="20%">Info. Adicionais</th>
                    <th width="10%">Data última ação</th>
                    <th width="5%">Último a interagir</th>
                    <th width="5%">Perguntas pendentes</th>
                    <th width="9%"></th>
                </tr>
            </thead>
            <tbody>
                <?php

                foreach ($itens as $item) {
                ?>
                    <tr class="click-select">
                        <td>
                            <?php if ($item->total > 0) : ?>
                                <input class="check-box-item" type="checkbox" data-part_number="<?php echo $item->part_number ?>" data-estabelecimento="<?php echo $item->estabelecimento ?>" data-toggle="true" value='<?php echo  "$item->part_number" . '&' . "$item->estabelecimento" ?>' name="itens[]" form="form-answer">
                            <?php endif ?>
                        </td>
                        <td>
                            <?php if ($item->total > 0) : ?>
                                <a href="<?php echo base_url('controle_pendencias/responder?partnumber=' . urlencode($item->part_number) . '&estabelecimento=' . urlencode($item->estabelecimento) . '&unico=1') ?>" title="Responder">
                                    <strong><?php echo $item->part_number ?></strong>
                                </a>
                            <?php else : ?>
                                <strong><?php echo $item->part_number ?></strong>
                            <?php endif ?>
                        </td>
                        <td>
                            <strong><?php echo empty($item->estabelecimento) ? 'N/A' : $item->estabelecimento ?></strong>
                        </td>
                        <td>
                            <?php if ($hasOwner) : ?>
                                <strong><?php echo (!empty($item->descricao)) ? $item->descricao :
                                            $item->descricao_global . " (GLOBAL)"  ?></strong>
                            <?php else : ?>
                                <strong><?php echo $item->descricao ?></strong>
                            <?php endif ?>
                        </td>
                        <?php if ($hasPrioridade) : ?>
                            <td>
                                <strong><?php echo $item->empresa_prioridade ?></strong>
                            </td>
                        <?php endif ?>
                        <td>
                            <strong><?php echo $item->peso ?></strong>
                        </td>
                        <td>
                            <strong><?php echo $item->inf_adicionais ?></strong>
                        </td>
                        <td>
                            <?php echo date('d/m/Y H:i:s', strtotime($item->atualizado_em)); ?>
                        </td>
                        <td>
                            -
                        </td>
                        <td>
                            <?php echo $item->total ?>
                        </td>
                        <?php if ($item->total > 0) : ?>
                            <td class="text-right" style="display: flex;">

                                <a class="btn btn-default" href="<?php echo base_url('controle_pendencias/responder?partnumber=' . urlencode($item->part_number) . '&estabelecimento=' . urlencode($item->estabelecimento) . '&unico=1') ?>" title="Responder">
                                    <i class="glyphicon glyphicon-share"></i>
                                </a>
                                <?php if (customer_has_role('visualizar_transferir_perguntas', sess_user_id())) : ?>
                                    <button class="btn btn-primary btn-transfer" title="Transferir" data-toggle="modal" data-target="#transferirResponsavel" data-part-number="<?php echo $item->part_number ?>" data-estabelecimento="<?php echo $item->estabelecimento ?>">
                                        <i class="glyphicon glyphicon-transfer"></i>
                                    </button>
                                <?php endif; ?>
                            </td>
                        <?php endif; ?>
                    </tr>
                <?php } ?>
            </tbody>
        </table>

        <div class="text-center">
            <?php echo $this->pagination->create_links() ?>
        </div>

    <?php else : ?>
        <p class="lead">Nenhum resultado encontrado</p>
    <?php endif; ?>
<?php endif; ?>

<?php $this->load->view('controle_pendencias/modal-transferir-responsavel'); ?>

<style type="text/css">
    .btn-group.bootstrap-select.show-tick {
        width: 100% !important;
    }

    .mb-5 {
        margin-bottom: 15px;
    }
</style>

<script type="text/javascript">
    $(function() {
        let answerLength = $("input[type='checkbox']:checked").length;

        answerLength >= 1 ? $('.btn-answer').show() : $('.btn-answer').hide();

        $('.datetimepicker').datetimepicker({
            'format': 'DD/MM/YYYY',
            'locale': 'pt-BR'
        });

        $('.click-select').on('click', function(e) {
            if (e.target.nodeName != 'INPUT') {
                $(this).find('input').click();
            }
        });

        $(".click-select, input[type='checkbox']").on('click', function() {
            let answerLength = $("input[type='checkbox']:checked").length;

            answerLength >= 1 ? $('.btn-answer').show() : $('.btn-answer').hide();
        });
    });

    $("#status_implementacao").on('change', function() {
        if (!$(this).val()) {
            $(this).selectpicker('selectAll');
        }
    });

    $('#status_exportacao').on('change', function() {
        if (!$(this).val()) {
            $(this).selectpicker('selectAll');
        }
    })
</script>

<script type="text/javascript">
    ! function(e) {
        var t = function(t, n) {
            this.$element = e(t), this.type = this.$element.data("uploadtype") || (this.$element.find(".thumbnail").length > 0 ? "image" : "file"), this.$input = this.$element.find(":file");
            if (this.$input.length === 0) return;
            this.name = this.$input.attr("name") || n.name, this.$hidden = this.$element.find('input[type=hidden][name="' + this.name + '"]'), this.$hidden.length === 0 && (this.$hidden = e('<input type="hidden" />'), this.$element.prepend(this.$hidden)), this.$preview = this.$element.find(".fileupload-preview");
            var r = this.$preview.css("height");
            this.$preview.css("display") != "inline" && r != "0px" && r != "none" && this.$preview.css("line-height", r), this.original = {
                exists: this.$element.hasClass("fileupload-exists"),
                preview: this.$preview.html(),
                hiddenVal: this.$hidden.val()
            }, this.$remove = this.$element.find('[data-dismiss="fileupload"]'), this.$element.find('[data-trigger="fileupload"]').on("click.fileupload", e.proxy(this.trigger, this)), this.listen()
        };
        t.prototype = {
            listen: function() {
                this.$input.on("change.fileupload", e.proxy(this.change, this)), e(this.$input[0].form).on("reset.fileupload", e.proxy(this.reset, this)), this.$remove && this.$remove.on("click.fileupload", e.proxy(this.clear, this))
            },
            change: function(e, t) {
                if (t === "clear") return;
                var n = e.target.files !== undefined ? e.target.files[0] : e.target.value ? {
                    name: e.target.value.replace(/^.+\\/, "")
                } : null;
                if (!n) {
                    this.clear();
                    return
                }
                this.$hidden.val(""), this.$hidden.attr("name", ""), this.$input.attr("name", this.name);
                if (this.type === "image" && this.$preview.length > 0 && (typeof n.type != "undefined" ? n.type.match("image.*") : n.name.match(/\.(gif|png|jpe?g)$/i)) && typeof FileReader != "undefined") {
                    var r = new FileReader,
                        i = this.$preview,
                        s = this.$element;
                    r.onload = function(e) {
                        i.html('<img src="' + e.target.result + '" ' + (i.css("max-height") != "none" ? 'style="max-height: ' + i.css("max-height") + ';"' : "") + " />"), s.addClass("fileupload-exists").removeClass("fileupload-new")
                    }, r.readAsDataURL(n)
                } else this.$preview.text(n.name), this.$element.addClass("fileupload-exists").removeClass("fileupload-new");
                $('#btn-enviar').removeClass("btn-enviar");
            },
            clear: function(e) {
                this.$hidden.val(""), this.$hidden.attr("name", this.name), this.$input.attr("name", "");
                if (navigator.userAgent.match(/msie/i)) {
                    var t = this.$input.clone(!0);
                    this.$input.after(t), this.$input.remove(), this.$input = t
                } else this.$input.val("");
                this.$preview.html(""), this.$element.addClass("fileupload-new").removeClass("fileupload-exists"), e && (this.$input.trigger("change", ["clear"]), e.preventDefault())
            },
            reset: function(e) {
                this.clear(), this.$hidden.val(this.original.hiddenVal), this.$preview.html(this.original.preview), this.original.exists ? this.$element.addClass("fileupload-exists").removeClass("fileupload-new") : this.$element.addClass("fileupload-new").removeClass("fileupload-exists")
            },
            trigger: function(e) {
                this.$input.trigger("click"), e.preventDefault()
            }
        }, e.fn.fileupload = function(n) {
            return this.each(function() {
                var r = e(this),
                    i = r.data("fileupload");
                i || r.data("fileupload", i = new t(this, n)), typeof n == "string" && i[n]()
            })
        }, e.fn.fileupload.Constructor = t, e(document).on("click.fileupload.data-api", '[data-provides="fileupload"]', function(t) {
            var n = e(this);
            if (n.data("fileupload")) return;
            n.fileupload(n.data());
            var r = e(t.target).closest('[data-dismiss="fileupload"],[data-trigger="fileupload"]');
            r.length > 0 && (r.trigger("click.fileupload"), t.preventDefault())
        })
    }(window.jQuery)
</script>
<style type="text/css" media="screen">
    .clearfix {
        *zoom: 1;
    }

    #btn-enviar {
        width: 100px;
        float: right;
    }

    .btn-enviar {
        display: none;
    }

    .clearfix:before,
    .clearfix:after {
        display: table;
        content: "";
        line-height: 0;
    }

    .clearfix:after {
        clear: both;
    }

    .hide-text {
        font: 0/0 a;
        color: transparent;
        text-shadow: none;
        background-color: transparent;
        border: 0;
    }

    .input-block-level {
        display: block;
        width: 100%;
        min-height: 30px;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
    }

    .btn-file {
        overflow: hidden;
        position: relative;
        vertical-align: middle;
    }

    .btn-file>input {
        position: absolute;
        top: 0;
        right: 0;
        margin: 0;
        opacity: 0;
        filter: alpha(opacity=0);
        transform: translate(-300px, 0) scale(4);
        font-size: 23px;
        direction: ltr;
        cursor: pointer;
    }

    .btn-baixar {
        overflow: hidden;
        position: relative;
        vertical-align: middle;
        font-size: 15px;
        -webkit-border-radius: 0 3px 3px 0;
        -moz-border-radius: 0 3px 3px 0;
        border-radius: 0 3px 3px 0;
    }

    .fileupload .uneditable-input {
        display: inline-block;
        margin-bottom: 0px;
        vertical-align: middle;
        cursor: text;
    }

    .fileupload .thumbnail {
        overflow: hidden;
        display: inline-block;
        margin-bottom: 5px;
        vertical-align: middle;
        text-align: center;
    }

    .fileupload .thumbnail>img {
        display: inline-block;
        vertical-align: middle;
        max-height: 100%;
    }

    .fileupload .btn {
        vertical-align: middle;
    }

    .fileupload-exists .fileupload-new,
    .fileupload-new .fileupload-exists {
        display: none;
    }

    .fileupload-inline .fileupload-controls {
        display: inline;
    }

    .fileupload-new .input-append .btn-file {
        -webkit-border-radius: 0 3px 3px 0;
        -moz-border-radius: 0 3px 3px 0;
        border-radius: 0 3px 3px 0;
    }

    .thumbnail-borderless .thumbnail {
        border: none;
        padding: 0;
        -webkit-border-radius: 0;
        -moz-border-radius: 0;
        border-radius: 0;
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none;
    }

    .fileupload-new.thumbnail-borderless .thumbnail {
        border: 1px solid #ddd;
    }

    .control-group.warning .fileupload .uneditable-input {
        color: #a47e3c;
        border-color: #a47e3c;
    }

    .control-group.warning .fileupload .fileupload-preview {
        color: #a47e3c;
    }

    .control-group.warning .fileupload .thumbnail {
        border-color: #a47e3c;
    }

    .control-group.error .fileupload .uneditable-input {
        color: #b94a48;
        border-color: #b94a48;
    }

    .control-group.error .fileupload .fileupload-preview {
        color: #b94a48;
    }

    .control-group.error .fileupload .thumbnail {
        border-color: #b94a48;
    }

    .control-group.success .fileupload .uneditable-input {
        color: #468847;
        border-color: #468847;
    }

    .control-group.success .fileupload .fileupload-preview {
        color: #468847;
    }

    .control-group.success .fileupload .thumbnail {
        border-color: #468847;
    }

    .radio-input {
        margin-right: 25px;
    }
</style>

<script>
    $(function() {

        function ativarCheckbox(el) {
            $(el).prop('checked', true);
        }

        let bulk_itens = <?php echo json_encode($bulk_selection); ?>;
        let item = [];
        let retorno;
        let texto;

        $(".check-box-item").each(function(index) {
            let _this = $(this);

            var estab = $(this).attr('data-estabelecimento');
            var part_number = $(this).attr('data-part_number');

            if (bulk_itens) {
                for (i in bulk_itens.item) {
                    texto = bulk_itens.item[i].toString();
                    retorno = texto.split("&");
                    if (retorno[0] == part_number && retorno[1] == estab) {

                        ativarCheckbox(_this)
                    }
                };
            }
        });

        $(".check-box-item").click(function() {

            var estab = $(this).attr('data-estabelecimento');
            var part_number = $(this).attr('data-part_number');
            var checked;
            if ($(this).is(':checked')) {
                checked = 1;
            } else {
                checked = 0;
            }
            $.ajax({
                type: 'POST',
                url: "<?php echo site_url('controle_pendencias/bulk') ?>",
                data: {
                    'estabelecimento': estab,
                    'part_number': part_number,
                    'checked': checked
                },
                success: function(value) {
                    console.log(value);
                    if (value.item_result) {
                        let total_items;
                        total_items = Object.keys(value.item_result).length;
                        console.log(total_items);
                        $('.bulk-selection-count').html(total_items);
                        $(".bulk-selection-box").css("display", "block");
                        // location.reload();

                    } else {
                        $(".bulk-selection-box").css("display", "none");
                    }
                }
            });
        });
    });

    function bulk_clean() {

        $.ajax({
            type: 'POST',
            url: "<?php echo site_url('controle_pendencias/bulk_clean') ?>",
            data: true,
            success: function(value) {
                location.reload();
            }
        });
    }

    $("#toggle-checkbox").click(function() {
        $('.check-box-item').each(function() {
            var estab = $(this).attr('data-estabelecimento');
            var part_number = $(this).attr('data-part_number');
            var checked;
            if ($(this).is(':checked')) {
                checked = 1;
            } else {
                checked = 0;
            }
            $.ajax({
                type: 'POST',
                url: "<?php echo site_url('controle_pendencias/bulk') ?>",
                data: {
                    'estabelecimento': estab,
                    'part_number': part_number,
                    'checked': checked
                },
                success: function(value) {
                    if (value.item_result) {
                        let total_items;
                        total_items = Object.keys(value.item_result).length;
                        $('.bulk-selection-count').html(total_items);
                        $(".bulk-selection-box").css("display", "block");

                    } else {
                        $(".bulk-selection-box").css("display", "none");
                    }
                }
            });
        });
    });
</script>

<script>
    $(document).ready(function() {

        function clearSelection(selectId) {
            $('#' + selectId).val([]).selectpicker('refresh');
        }

        if ($('#ownerPergunta option:selected').length === 0) {
            if ($('#ownerPergunta option:selected').length === 0) {
                $('#ownerPergunta option').prop('selected', true);
                $('#ownerPergunta').selectpicker('refresh');
            }
        }

        if ($('#usuarioPergunta option:selected').length === 0) {
            if ($('#usuarioPergunta option:selected').length === 0) {
                $('#usuarioPergunta option').prop('selected', true);
                $('#usuarioPergunta').selectpicker('refresh');
            }
        }
        // Escuta o evento de mudança nos inputs radio
        $('input[name="responsible_type"]').on('change', function() {
            // Verifica qual opção foi selecionada
            var selecionado = $(this).val();

            // Mostra o select correspondente e esconde o outro
            if (selecionado === 'user') {
                $('#userSelect').show();
                $('#ownerSelect').hide();
                $('#usuarioPergunta').prop('selectedIndex', -1);

                if ($('#usuarioPergunta option:selected').length === 0) {
                    $('#usuarioPergunta option').prop('selected', true);
                    $('#usuarioPergunta').selectpicker('refresh');
                }
                // clearSelection('usuarioPergunta');
                clearSelection('ownerPergunta');
            } else if (selecionado === 'owner') {
                $('#ownerSelect').show();
                $('#userSelect').hide();
                $('#ownerPergunta').prop('selectedIndex', -1);

                if ($('#ownerPergunta option:selected').length === 0) {
                    $('#ownerPergunta option').prop('selected', true);
                    $('#ownerPergunta').selectpicker('refresh');
                }
                // clearSelection('ownerPergunta');
                clearSelection('usuarioPergunta');
            }
        });
    });

    $(document).ready(function() {
        $('#usuarioPergunta').on('change', function() {
            checkResponsibleType();
        });
        $('#ownerPergunta').on('change', function() {
            checkResponsibleType();
        });

        function checkResponsibleType() {
            if ($('#user').is(':checked')) {
                $('#ownerPergunta option').prop('selected', false);
                $('#ownerPergunta').selectpicker('refresh');
            } else if ($('#owner').is(':checked')) {
                $('#usuarioPergunta option').prop('selected', false);
                $('#usuarioPergunta').selectpicker('refresh');
            }
        }

        $('#pesquisaForm').click(function() {
            checkResponsibleType();
            $('#pesquisa_form').submit();
        });
    });
</script>