<script type="text/javascript">
	var item = {

		edit: function() {
			checked = $('input[type="checkbox"][name="item[]"]:checked');

			if (checked.length == 0) {
				swal('Atenção', 'Selecione um item para realizar a edição', 'warning');
				return false;
			}

			if (checked.length > 1) {
				swal('Atenção', 'Só é possível editar um item por vez', 'warning');
				return false;
			}

			item_id = $(checked).val();
			location.href = '<?php echo base_url() . "cadastros/prioridades/editar/" ?>' + item_id;
		},

		remove: function() {
			checked = $('input[type=checkbox]:checked');

			if (checked.length == 0) {
				swal('Atenção', 'Primeiro você deve selecionar as prioridadess que deseja excluir.', 'warning');
				return false;
			}

			var id_list = new Array();

			$('input[type="checkbox"][name="item[]"]:checked').each(function() {
				id_list.push($(this).val());
			});

			var post_data = {
				'id_list': id_list
			};

			swal({
                title: "Atenção!",
                text: "Você deseja excluir os registros selecionados?",
                type: "warning",
                confirmButtonText: "OK",
                cancelButtonText: "Cancelar",
                showConfirmButton: true,
                showCancelButton: true,
                allowOutsideClick: false
			}).then(function () {
				$.post('<?php echo site_url("cadastros/prioridades/excluir") ?>',
					post_data,
					function(data, status, xhr) {
						var json = $.parseJSON(data);

						if (json.status == true) {
							window.location.reload();
						} else {
							swal('Atenção', "Ops... um erro aconteceu, recarregue a página.", 'warning');
						}
					}
				);
			});
		}
	};
</script>
<?php 
	$has_permission = customer_has_role('alterar_criticidade', sess_user_id());
?>
<div class="page-header">
	<h2>
		Prioridades

		<div class="col-md-10 pull-right">
			<div class="row">
				<form action="<?php echo site_url("cadastros/prioridades") ?>" accept-charset="utf-8" class="form-horizontal" method="get" name="search_form" style="padding-top: 2px;">
					<div class="form-group col-md-2" style="padding: 0; margin-right: 15px;">
						<input type="text" name="prioridade" placeholder="Prioridade" class="form-control" value="<?php echo (!empty($this->input->get('prioridade'))) ? $this->input->get('prioridade') : null  ?>">
					</div>

 
					<div class="form-group col-md-1" style="margin-top: -2px">
						<button class="btn btn-primary" type="submit"><i class="glyphicon glyphicon-search"></i></button>
					</div>

					<div class="form-group col-md-1" style="margin-top: -2px">
						<a href="<?php echo site_url("cadastros/prioridades?reset_filter=1") ?>" class="btn btn-default">Limpar</a>
					</div>

				</form>
				<div class="form-group col-md-5 pull-right text-right" style="margin-top: -2px; padding-right: 0px;">
					<a href="<?php echo site_url("cadastros/prioridades/novo") ?>" class="btn btn-default"><i class="glyphicon glyphicon-plus"></i>Nova prioridade</a>
					<button onclick="item.edit()" class="btn btn-default"><i class="glyphicon glyphicon-edit"></i> Editar</button>
					<button onclick="item.remove()" class="btn btn-danger"><i class="glyphicon glyphicon-trash"></i> Excluir</button>
				</div>
			</div>
		</div>
	</h2>
</div>

<?php if (isset($pagination)) { ?>
	<div class="controls">
		<div class="pull-right">
			<?php echo $pagination ?>
		</div>
	</div>
<?php } ?>

<table class="table table-striped table-hover" border="0">
	<thead>
		<tr>
			<th><input type="checkbox" id="toggle-checkbox" onclick="toggle_checkbox(this)" /></th>
			<th>Ordem</th>
			<th>Nome</th>
			<th>Horas</th>
		</tr>
	</thead>
	<tbody>
		<?php

		if (isset($list) && count($list)) {
			foreach ($list as $item) {  
		?>
				<tr class="click-select">
					<td><input type="checkbox" data-toggle="true" name="item[]" value="<?php echo $item->id_prioridade ?>" /></td>
					<td>
						<?php echo $item->ordem ?> <br />
						<small> </small>
					</td>
					<td> <?php echo $item->nome ?> <br /></td>
					<td> <?php echo $item->qdt_horas_uteis ?> <br /></td>
				</tr>
		<?php
			}
		}

		?>
	</tbody>
</table>

<?php if (isset($pagination)) { ?>
	<div class="controls">
		<div class="pull-right">
			<?php echo $pagination ?>
		</div>
	</div>
<?php } ?>

<script>
	jQuery(document).ready(function() {
		$('.click-select').on('click', function(e) {
			if (e.target.nodeName != 'INPUT') {
				$(this).find('input').click();
			}
		})
	});
</script>