<?php echo form_open_multipart('', array('class' => 'form-horizontal')) ?>

    <div class="row">

        <div class="col-md-12">

            <legend>
                <h2>Logs de Aprovação
                    <a href="<?php echo site_url('cadastros/logs?restore_search=1'); ?>" class="btn btn-default pull-right">
                        <i class="glyphicon glyphicon-arrow-left"></i> Voltar
                    </a>
                </h2>
            </legend>

            <h4>Part Number: <?php echo $part_number ?>
            <?php if (isset($estabelecimento)) { ?>
            <br>
            <small>Estabelecimento: <?php echo $estabelecimento; ?></small>
            <?php } ?>
            </h4>

            <table class="table table-striped">
                <tr>
                    <th>Data</th>
                    <th>Usuário</th>
                    <th>Ação</th>
                    <th>Motivo</th>
                    <th>Tipo de Homologação</th>
                </tr>

                <?php if ( count($entries) == 0 ) { ?>
                <tr>
                    <td colspan="4">Nenhum log cadastrado para este item</td>
                </tr>
                <?php } else {
                foreach ($entries as $entry) {
                    switch ($entry->titulo) {
                        case "envio":
                            $titulo = "Envio para aprovação";
                            break;

                        case "reenvio":
                            $titulo = "Reenvio para aprovação";
                            break;

                        case "aprovado":
                            $titulo = "Aprovado";
                            break;

                        case "reprovado":
                            $titulo = "Reprovado";
                            break;

                        case "obsoleto":
                            $titulo = "Inativo";
                            break;

                        case "pendenciacadastrada":
                            $titulo = "Pendência cadastrada";
                            break;

                        case "pendenciarespondida":
                            $titulo = "Pendência respondida";
                            break;

                        case "pendenciatransferida":
                            $titulo = "Pendência Transferida";
                            break;

                        case "atribuicaogrupo":
                            $titulo = "Atribuição de grupo";
                            break;

                        case "desvinculargrupo":
                            $titulo = "Desvincular grupo";
                            break;

                        case "alterartag":
                            $titulo = "Alteração Pré-agrupamento";
                            break;

                        case "vinculacao_ex":
                            $titulo = "Vinculação de EX Tarifário";
                            break;

                        case "vinculacao_cest":
                            $titulo = "Vinculação de CEST";
                            break;

                        case "vinculacao_cest_novo":
                            $titulo = "Vinculação de novo CEST";
                            break;

                        case "vinculacao_cest_remove":
                            $titulo = "Remoção vinculação de CEST";
                            break;

                        case "vinculacao_nve":
                            $titulo = "Vinculação de NVE";
                            break;

                        case "simplus":
                            $titulo = "Integração Simplus";
                            break;

                        case "criacao":
                            $titulo = "Criação";
                            break;

                        case "exclusao":
                            $titulo = "Exclusão";
                            break;

                        case "atualizacao":
                            $titulo = "Atualização";
                            break;

                        case "reanalise":
                            $titulo = "Reanálise";
                            break;

                        case "envio":
                            $titulo = "Envio para aprovação";
                            break;
                        
                        case "atualizarowner":
                            $titulo = "Atualização de Owner";
                            break;

                        case "inativacao":
                            $titulo = "Inativação do item";
                            break;

                        case "ativacao":
                            $titulo = "Ativação do item";
                            break;

                        default:
                            $titulo = "Desconhecido";
                    } // end switch
                ?>
                <tr>
                    <td width="15%"><?php echo date("d/m/Y H:i:s", strtotime($entry->criado_em)) ?></td>
                    <td width="20%"><?php echo $entry->nome_usuario ? $entry->nome_usuario : '<em>Indefinido</em>' ?></td>
                    <td width="10%"><?php echo $titulo ?></td>
                    <td width="40%"><?php echo $entry->motivo ? $entry->motivo : '-' ?></td>
                    <?php
                        if (!empty($entry->tipo_homologacao))
                        {
                            if ($entry->tipo_homologacao == 'Engenharia')
                            {
                                $tipo_homologacao = 'Responsável Técnico';
                            }else
                            {
                                $tipo_homologacao = $entry->tipo_homologacao;
                            }
                        } else {
                            $tipo_homologacao = ' - ';
                        }
                    ?>
                    <td width="15%"><?php echo $tipo_homologacao; ?></td>
                </tr>
                <?php
                    } // end foreach
                } // end else
                ?>
            </table>

        </div>

    </div>