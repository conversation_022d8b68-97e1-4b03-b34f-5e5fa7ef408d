<style>
    /* Toggle Switch para Status */
    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 100%;
    }

    .toggle-checkbox {
        display: none;
    }

    .toggle-label {
        display: flex;
        align-items: center;
        cursor: pointer;
        margin-bottom: 0;
        font-weight: normal;
        padding-left: 0 !important;
    }

    .toggle-inner {
        position: relative;
        display: inline-block;
        width: 45px;
        height: 20px;
        background-color: #ccc;
        border-radius: 14px;
        transition: background-color 0.3s;
        margin-right: 10px;
        flex-shrink: 0;
    }

    .toggle-inner:before {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 16px;
        height: 16px;
        background-color: white;
        border-radius: 50%;
        transition: transform 0.3s;
    }

    .toggle-checkbox:checked+.toggle-label .toggle-inner {
        background-color: #337ab7;
    }

    .toggle-checkbox:checked+.toggle-label .toggle-inner:before {
        transform: translateX(25px);
    }

    .toggle-switch-text {
        font-size: 14px;
        color: #333;
        user-select: none;
    }
</style>

<?php echo form_open('', array('class' => 'form-horizontal')) ?>

<div class="page-header">
	<h2>
		Editar usuário
	</h2>
</div>

<div class="form-group">
	<label for="input-nome" class="col-sm-2 control-label">Nome</label>
	<div class="col-sm-10">
		<input type="text" class="form-control" name="nome" id="input-nome" value="<?php echo set_value('nome', $entry->nome) ?>" placeholder="Nome">
	</div>
</div>

<div class="form-group">
	<label for="input-email" class="col-sm-2 control-label">E-mail</label>
	<div class="col-sm-10">
		<input type="email" class="form-control" name="email" id="input-email" placeholder="Email" value="<?php echo set_value('email', $entry->email) ?>">
	</div>
</div>

<div class="form-group">
	<label for="select-perfil" class="col-sm-2 control-label">Perfil</label>
	<div class="col-sm-10">
		<select class="form-control custom" name="id_perfil" id="select-perfil">
			<?php
			foreach ($perfis as $perfil) {
				if (!has_role('sysadmin') && !has_role('visualizar_usuarios_adm') && !in_array($perfil->id_perfil, array(2, 3))) continue;
			?>
				<option <?php echo set_select('id_perfil', $perfil->id_perfil, $perfil->id_perfil == $entry->id_perfil); ?> value="<?php echo $perfil->id_perfil ?>"><?php echo $perfil->descricao ?></option>
			<?php } ?>
		</select>
	</div>
</div>

<div id="validate-empresas" class="col-md-offset-2 alert alert-warning small hide">
	<strong>Atenção!</strong> Apenas uma empresa será vinculada. Múltiplas empresas apenas são permitidas para os perfis Cliente-Engenharia e Cliente-Fiscal.
</div>

<div class="form-group" style="display: block;">
	<label for="select-empresa" class="col-sm-2 control-label">Empresa</label>
	<div class="col-sm-10">
		<?php
		if (customer_has_role('engenheiro', $entry->id_usuario) || customer_has_role('fiscal', $entry->id_usuario) || customer_has_role('permitir_multiplas_empresas', $entry->id_usuario)) {
			$show_multiple = TRUE;
		} else {
			$show_multiple = FALSE;
		}

		?>
		<select class="form-control custom selectpicker" name="id_empresa[]" id="select-empresa" multiple data-live-search="true" data-count-selected-text="{0} empresas selecionadas" data-selected-text-format="count > 1" title="Selecione as empresas do usuário" data-hide-select="<?php echo $show_multiple === FALSE ? 'true' : 'false'; ?>">
			<?php foreach ($empresas as $empresa) { ?>
				<option <?php echo set_select('id_empresa', $empresa->id_empresa, in_array($empresa->id_empresa, $usuario_empresas)); ?> value="<?php echo $empresa->id_empresa ?>"><?php echo $empresa->razao_social ?></option>
			<?php } ?>
		</select>
		<select class="form-control custom" name="id_empresa[]" id="select-empresa-unique" style="display: <?php echo $show_multiple === TRUE ? 'none' : 'block'; ?>">
			<option value="">[Selecione]</option>
			<?php foreach ($empresas as $empresa) { ?>
				<option data-empresa-recebe-email="<?php echo $empresa->recebe_email_pendencias; ?>" <?php echo set_select('id_empresa', $empresa->id_empresa, in_array($empresa->id_empresa, $usuario_empresas)); ?> value="<?php echo $empresa->id_empresa ?>"><?php echo $empresa->razao_social ?></option>
			<?php } ?>
		</select>
		<label class="control-label <?php echo $entry->id_perfil != 5 ? 'hide' : NULL; ?>" id="checkbox-gp">
			<input type="checkbox" name="gerente_de_projetos" <?php echo $id_gerente_de_projetos == $entry->id_usuario ? 'checked' : NULL; ?>> Definir como gerente de projetos da empresa
		</label>
	</div>
</div>

<div class="form-group" style="display: block;">
	<label for="select-empresa" class="col-sm-2 control-label">Data do Cadastro:</label>
	<div class="col-sm-10">
		 
		<label class="control-label"  ><?php 
		if (!empty($entry->criado_em)) {
 
			$data = new DateTime($entry->criado_em);
		
			$data_formatada = $data->format('d/m/Y');
		} else {
			$data_formatada = '-';
		} 
		
		echo $data_formatada;
		?>
			 
		</label>
	</div>
</div>

<div class="form-group" style="display: block;">
	<label for="select-empresa" class="col-sm-2 control-label">Data do último login:</label>
	<div class="col-sm-10">
		 
		<label class="control-label"  ><?php 
		if (!empty($entry->ultimo_login)) {
 
			$data = new DateTime($entry->ultimo_login);
		
			$data_formatada = $data->format('d/m/Y');
		} else {
			$data_formatada = '-';
		} 
		
		echo $data_formatada;
		?> 
			 
		</label>
	</div>
</div>

<div class="form-group">
	<div class="checkbox col-sm-10 col-sm-offset-2">
		<label>
			<input type="checkbox" <?php echo $entry->recebe_email_pendencias ? 'checked' : ''; ?> name="recebe_email_pendencias" id="recebe_email_pendencias" value="1"> Recebe Email Pendências
		</label>
	</div>
	<div class="checkbox col-sm-10 col-sm-offset-2">
		<label>
			<input type="checkbox" <?php echo $entry->recebe_email_alteracao_status ? 'checked' : ''; ?> name="recebe_email_alteracao_status" id="recebe_email_alteracao_status" value="1"> Recebe Email de Alteração de Status
		</label>
	</div>
</div>

<div class="form-group">
    <div class="checkbox col-sm-10 col-sm-offset-2">
        <div class="toggle-switch">
            <!-- O input hidden é necessário para garantir que o valor '0' seja enviado quando o checkbox estiver desmarcado -->
            <input type="hidden" name="status" value="0">
            <input
                type="checkbox"
                id="status"
                name="status"
                value="1"
                <?php echo $entry->status === '1' ? 'checked' : null; ?>
                class="toggle-checkbox">
            <label for="status" class="toggle-label">
                <span class="toggle-inner"></span>
                <span class="toggle-switch-text">Ativo</span>
            </label>
        </div>
    </div>
</div>

<div class="form-group">
	<div class="col-sm-offset-2 col-sm-10">
		<button type="submit" class="btn btn-primary" value="1" name="submit"><i class="glyphicon glyphicon-floppy-disk"></i> Salvar</button>
		<a href="<?php echo site_url("cadastros/usuario") ?>" class="btn">Cancelar</a>
	</div>
</div>

</form>

<script type="text/javascript">
	var $checkbox = $('#recebe_email_pendencias');

	$(function() {

		if ($('#select-empresa').attr('data-hide-select') == 'true') {
			$('.selectpicker').selectpicker('hide');
			$('#select-empresa').attr('disabled', 'disabled');
			$('.selectpicker').selectpicker('refresh');
		} else {
			$('#select-empresa-unique').attr('disabled', 'disabled');
		}

		$('#select-perfil').on('change', function(e) {''
            //Se Cliente-PMO selecionado
            if ($(this).val() == 5) {
                $('#checkbox-gp').removeClass('hide');
            } else {
                $('#checkbox-gp').addClass('hide');
            }

            $.ajax({
                url: "<?php echo site_url('cadastros/usuario/verificarPermissaoPerfil') ?>",
                data: {
                    'idPerfil': $(this).val(),
                    'permissao': ['engenheiro', 'fiscal', 'permitir_multiplas_empresas'],
                },
                method: 'POST',
                success: function(res) {
                    var data = JSON.parse(res);
                    
                    if (data.hasPermissao) {
                        $('#validate-empresas').addClass('hide');

                        $('#select-empresa').removeAttr('disabled');
                        $('.selectpicker').selectpicker('refresh');

                        $('#select-empresa-unique').attr('disabled', 'disabled');

                        $('#select-empresa-unique').css('display', 'none');
                        $('.selectpicker').selectpicker('show');
                    } else {
                        $checkbox.removeAttr('disabled');
                        $checkbox.prop('checked', true);
                        $checkbox.attr('checked', 'checked');

                        $('#validate-empresas').removeClass('hide');

                        $('.selectpicker').selectpicker('hide');
                        $('#select-empresa').attr('disabled', 'disabled');
                        $('.selectpicker').selectpicker('refresh');

                        $('#select-empresa-unique').removeAttr('disabled');
                        $('#select-empresa-unique').css('display', 'block');

                    }
                }
            })
        });
	})

	$('#select-empresa-unique').on('change', function() {
		var self = $(this);
		var option = self.find('option:selected');
		if (option.attr('data-empresa-recebe-email')) {
			$checkbox.removeAttr('disabled');
			$checkbox.prop('checked', true);
			$checkbox.attr('checked', 'checked');
		} else {
			$checkbox.attr('disabled', 'disabled');
			$checkbox.prop('checked', false);
			$checkbox.removeAttr('checked');
		}
	})
</script>