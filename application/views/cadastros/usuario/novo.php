<style>
    /* Toggle Switch para Status */
    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 100%;
    }

    .toggle-checkbox {
        display: none;
    }

    .toggle-label {
        display: flex;
        align-items: center;
        cursor: pointer;
        margin-bottom: 0;
        font-weight: normal;
        padding-left: 0 !important;
    }

    .toggle-inner {
        position: relative;
        display: inline-block;
        width: 45px;
        height: 20px;
        background-color: #ccc;
        border-radius: 14px;
        transition: background-color 0.3s;
        margin-right: 10px;
        flex-shrink: 0;
    }

    .toggle-inner:before {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 16px;
        height: 16px;
        background-color: white;
        border-radius: 50%;
        transition: transform 0.3s;
    }

    .toggle-checkbox:checked+.toggle-label .toggle-inner {
        background-color: #337ab7;
    }

    .toggle-checkbox:checked+.toggle-label .toggle-inner:before {
        transform: translateX(25px);
    }

    .toggle-switch-text {
        font-size: 14px;
        color: #333;
        user-select: none;
    }
</style>

<?php echo form_open('', array('class' => 'form-horizontal')) ?>

<div class="page-header">
    <h2>
        Novo usuário
    </h2>
</div>

<div class="form-group">
    <label for="input-nome" class="col-sm-2 control-label">Nome</label>
    <div class="col-sm-10">
        <input type="text" class="form-control" name="nome" id="input-nome" value="<?php echo set_value('nome') ?>" placeholder="Nome">
    </div>
</div>

<div class="form-group">
    <label for="input-email" class="col-sm-2 control-label">E-mail</label>
    <div class="col-sm-10">
        <input type="email" class="form-control" name="email" id="input-email" placeholder="Email" value="<?php echo set_value('email') ?>">
    </div>
</div>

<div class="form-group">
    <label for="select-perfil" class="col-sm-2 control-label">Perfil</label>
    <div class="col-sm-10">
        <select class="form-control custom" name="id_perfil" id="select-perfil">
            <option value="">[Selecione]</option>
            <?php
            foreach ($perfis as $perfil) {
				if (!has_role('sysadmin') && !has_role('visualizar_usuarios_adm') && !in_array($perfil->id_perfil, array(2, 3))) continue;
            ?>
                <option <?php echo set_select('id_perfil', $perfil->id_perfil); ?> value="<?php echo $perfil->id_perfil ?>"><?php echo $perfil->descricao ?></option>
            <?php } ?>
        </select>
    </div>
</div>

<div id="validate-empresas" class="col-md-offset-2 alert alert-warning small hide">
    <strong>Atenção!</strong> Apenas uma empresa será vinculada. Múltiplas empresas apenas são permitidas para os perfis Cliente-Engenharia e Cliente-Fiscal.
</div>

<div class="form-group">
    <label for="select-empresa" class="col-sm-2 control-label">Empresa</label>
    <div class="col-sm-10">
        <select class="form-control custom selectpicker" name="id_empresa[]" id="select-empresa" multiple data-live-search="true" data-count-selected-text="{0} empresas selecionadas" data-selected-text-format="count > 1" title="Selecione as empresas do usuário" data-hide-select="true">
            <?php foreach ($empresas as $empresa) { ?>
                <option <?php echo set_select('id_empresa', $empresa->id_empresa); ?> value="<?php echo $empresa->id_empresa ?>"><?php echo $empresa->razao_social ?></option>
            <?php } ?>
        </select>
        <select class="form-control custom" name="id_empresa" id="select-empresa-unique">
            <option value="">[Selecione]</option>
            <?php foreach ($empresas as $empresa) { ?>
                <option data-empresa-recebe-email="<?php echo $empresa->recebe_email_pendencias; ?>" <?php echo set_select('id_empresa', $empresa->id_empresa); ?> value="<?php echo $empresa->id_empresa ?>"><?php echo $empresa->razao_social ?></option>
            <?php } ?>
        </select>
        <label class="control-label hide" id="checkbox-gp">
            <input type="checkbox" name="gerente_de_projetos"> Definir como gerente de projetos da empresa
        </label>
    </div>
</div>

<div class="form-group">
    <div class="checkbox col-sm-10 col-sm-offset-2">
        <label>
            <input type="checkbox" name="recebe_email_pendencias" id="recebe_email_pendencias" value="1"> Recebe Email Pendências
        </label>
    </div>
</div>

<div class="form-group">
    <div class="checkbox col-sm-10 col-sm-offset-2">
        <div class="toggle-switch">
            <!-- O input hidden é necessário para garantir que o valor '0' seja enviado quando o checkbox estiver desmarcado -->
            <input type="hidden" name="status" value="0">
            <input
                type="checkbox"
                id="status"
                name="status"
                value="1"
                checked
                class="toggle-checkbox">
            <label for="status" class="toggle-label">
                <span class="toggle-inner"></span>
                <span class="toggle-switch-text">Ativo</span>
            </label>
        </div>
    </div>
</div>

<div class="form-group">
    <div class="col-sm-offset-2 col-sm-10">
        <button type="submit" class="btn btn-primary" value="1" name="submit"><i class="glyphicon glyphicon-floppy-disk"></i> Salvar</button>
        <a href="<?php echo site_url("cadastros/usuario") ?>" class="btn">Cancelar</a>
    </div>
</div>

</form>

<script type="text/javascript">
    var $checkbox = $('#recebe_email_pendencias');
    var permissoes = [];

    function verificarPermissao(idPerfil, permissao) {
        $.ajax({
            url: "<?php echo site_url('cadastros/usuario/verificarPermissaoPerfil') ?>",
            data: {
                'idPerfil': idPerfil,
                'permissao': permissao,
            },
            success: function(res) {
            }
        })
    }
    
    $(function() {
        if ($('#select-empresa').attr('data-hide-select') == 'true') {
            $('.selectpicker').selectpicker('hide');
            $('#select-empresa').attr('disabled', 'disabled');
            $('.selectpicker').selectpicker('refresh');
        } else {
            $('#select-empresa-unique').attr('disabled', 'disabled');
        }

        $('#select-perfil').on('change', function(e) {''
            //Se Cliente-PMO selecionado
            if ($(this).val() == 5) {
                $('#checkbox-gp').removeClass('hide');
            } else {
                $('#checkbox-gp').addClass('hide');
            }

            $.ajax({
                url: "<?php echo site_url('cadastros/usuario/verificarPermissaoPerfil') ?>",
                data: {
                    'idPerfil': $(this).val(),
                    'permissao': ['engenheiro', 'fiscal', 'permitir_multiplas_empresas'],
                },
                method: 'POST',
                success: function(res) {
                    var data = JSON.parse(res);
                    
                    if (data.hasPermissao) {
                        $('#validate-empresas').addClass('hide');

                        $('#select-empresa').removeAttr('disabled');
                        $('.selectpicker').selectpicker('refresh');

                        $('#select-empresa-unique').attr('disabled', 'disabled');

                        $('#select-empresa-unique').css('display', 'none');
                        $('.selectpicker').selectpicker('show');
                    } else {
                        $checkbox.removeAttr('disabled');
                        $checkbox.prop('checked', true);
                        $checkbox.attr('checked', 'checked');

                        $('#validate-empresas').removeClass('hide');

                        $('.selectpicker').selectpicker('hide');
                        $('#select-empresa').attr('disabled', 'disabled');
                        $('.selectpicker').selectpicker('refresh');

                        $('#select-empresa-unique').removeAttr('disabled');
                        $('#select-empresa-unique').css('display', 'block');

                    }
                }
            })
        });
    })

    $('#select-empresa-unique').on('change', function() {
        var self = $(this);
        var option = self.find('option:selected');
        if (option.attr('data-empresa-recebe-email')) {
            $checkbox.removeAttr('disabled');
            $checkbox.prop('checked', true);
            $checkbox.attr('checked', 'checked');
        } else {
            $checkbox.attr('disabled', 'disabled');
            $checkbox.prop('checked', false);
            $checkbox.removeAttr('checked');
        }
    })

    $(document).ready(function() {
        $('#select-empresa-unique').trigger('change');
    })
</script>