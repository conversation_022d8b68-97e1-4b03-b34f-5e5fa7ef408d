<?php echo form_open('', array('class' => 'form-horizontal')) ?>
	
	<div class="page-header">
		<h2>
			Novo Perfil
		</h2>
	</div>

	<div class="form-group">
		<label for="input-razao_social" class="col-sm-2 control-label">Descrição</label>
		<div class="col-sm-10">
			<input type="text" class="form-control" name="descricao" id="input-descricao" value="<?php echo set_value('descricao') ?>" placeholder="Descrição">
		</div>
	</div>
	
	<div class="form-group">
		<label class="col-sm-2 control-label">Permissões</label>
		<div class="col-sm-10">
			<div class="row">
		
			<?php foreach ($permissoes as $permissao) { ?>
				<?php if ($permissao->permissao_especial == 0 && $permissao->permissao_exportacao == 0) : ?>

					<div class="col-xs-12 col-sm-6"><input type="checkbox" id="permissao<?php echo $permissao->id_permissao ?>" name="permissao[]" value="<?php echo $permissao->id_permissao ?>"> <label for="permissao<?php echo $permissao->id_permissao ?>"><?php echo $permissao->descricao ?></label></div>

				<?php endif; ?>
				
			<?php } ?>

			<!-- <?php //foreach ($permissoes as $permissao) { ?>
				<div class="col-xs-12 col-sm-6"><input type="checkbox" id="permissao<?php //echo $permissao->id_permissao ?>" name="permissao[]" value="<?php //echo $permissao->id_permissao ?>"> <label for="permissao<?php //echo $permissao->id_permissao ?>"><?php //echo $permissao->descricao ?></label></div>
			<?php //} ?> -->
	  
			</div>
		</div>
	</div>
<br>
	<div class="form-group">
		<label class="col-sm-2 text-right">Permissões especiais</label>
		<div class="col-sm-10">
			<div class="row">
		
				<?php foreach ($permissoes as $permissao) { ?>
					<?php if ($permissao->permissao_especial == 1 && $permissao->permissao_exportacao == 0) : ?>

						<div class="col-xs-12 col-sm-6"><input type="checkbox" id="permissao<?php echo $permissao->id_permissao ?>" name="permissao[]" value="<?php echo $permissao->id_permissao ?>"> <label for="permissao<?php echo $permissao->id_permissao ?>"><?php echo $permissao->descricao ?></label></div>

					<?php endif; ?>
					
				<?php } ?>
	  
			</div>
		</div>
	</div>
	<br>
	<div class="form-group">
		<label class="col-sm-2 text-right">Permissões de exportação</label>
		<div class="col-sm-10">
			<div class="row">
		
				<?php foreach ($permissoes as $permissao) { ?>
					<?php if ($permissao->permissao_exportacao == 1) : ?>

						<div class="col-xs-12 col-sm-6"><input type="checkbox" id="permissao<?php echo $permissao->id_permissao ?>" name="permissao[]" value="<?php echo $permissao->id_permissao ?>"> <label for="permissao<?php echo $permissao->id_permissao ?>"><?php echo $permissao->descricao ?></label></div>

					<?php endif; ?>
					
				<?php } ?>
	  
			</div>
		</div>
	</div>
	<div class="form-group">
		<label class="col-sm-2 text-right">Páginas</label>
		<div class="col-sm-10">
			<div class="row">
		
			<?php foreach ($permissoes as $permissao) { ?>
				<?php if($permissao->pagina) : ?>
					<div class="col-xs-12 col-sm-6"><input type="checkbox" id="permissao<?php echo $permissao->id_permissao ?>" name="permissao[]" value="<?php echo $permissao->id_permissao ?>"> <label for="permissao<?php echo $permissao->id_permissao ?>"><?php echo $permissao->descricao ?></label></div>
				<?php endif; ?>
			<?php } ?>
	  
			</div>
		</div>
	</div>

		
	<div class="form-group">
		<div class="col-sm-offset-2 col-sm-10">
			<button type="submit" class="btn btn-primary" value="1" name="submit"><i class="glyphicon glyphicon-floppy-disk"></i> Salvar</button>
			<a href="<?php echo site_url("cadastros/perfil") ?>" class="btn">Cancelar</a>
		</div>
	</div>

</form>
