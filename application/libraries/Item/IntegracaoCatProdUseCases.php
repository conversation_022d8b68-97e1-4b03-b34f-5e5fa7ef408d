<?php 

class IntegracaoCatProdUseCases
{
    private $CI = null;

    public function __construct()
    {
        $this->CI = & get_instance();
    }

    public static function generate_xlsx($xlsx_writer, $itens)
    {
        $xlsx_writer->setActiveSheetIndex(0);
        $xlsx_writer->getActiveSheet()->setTitle('Integracao_Cat_Prod');

        $xlsx_writer->getActiveSheet()->setCellValue('A1', 'PART-NUMBER');
        $xlsx_writer->getActiveSheet()->setCellValue('B1', 'DESCRIÇÃO');
        $xlsx_writer->getActiveSheet()->setCellValue('C1', 'NCM');
        $xlsx_writer->getActiveSheet()->setCellValue('D1', 'CÓDIGO RECEITA');
        $xlsx_writer->getActiveSheet()->setCellValue('E1', 'VERSÃO');
        $xlsx_writer->getActiveSheet()->setCellValue('F1', 'MODALIDADE');

        $xlsx_writer->getActiveSheet()->getStyle("A1:F1")
            ->getFont()
            ->setBold(true);
        
        $xlsx_writer->getActiveSheet()->getStyle("A1:F1")
            ->getFill()
            ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
            ->getStartColor()->setARGB('F9FF00');
        
        $xlsx_writer->getActiveSheet()->getStyle("A1:F1")
            ->getAlignment()
            ->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

        foreach (range('A', "F") as $columnID) {
            $xlsx_writer->getActiveSheet()
                ->getColumnDimension($columnID
                )->setAutoSize(true);
            
            $xlsx_writer->getActiveSheet()
                ->getStyle($columnID)
                ->getAlignment()
                ->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);
        }

        if (!empty($itens)) {
            $horizontal_left = array(
                'alignment' => array(
                    'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_LEFT,
                ),
            );

            $i = count($itens) + 1;

            $xlsx_writer->getActiveSheet()->getStyle('A1:A' . $i)->applyFromArray($horizontal_left);

            foreach ($itens as $key => $item) {

                $i = $key + 2;

                $xlsx_writer->getActiveSheet()->setCellValueExplicit('A' . $i, $item->part_number, PHPExcel_Cell_DataType::TYPE_STRING);
                $xlsx_writer->getActiveSheet()->setCellValueExplicit('B' . $i, $item->descricao_mercado_local, PHPExcel_Cell_DataType::TYPE_STRING);
                $xlsx_writer->getActiveSheet()->setCellValueExplicit('C' . $i, $item->ncm_proposto, PHPExcel_Cell_DataType::TYPE_STRING);
                $xlsx_writer->getActiveSheet()->setCellValueExplicit('D' . $i, $item->codigo_receita, PHPExcel_Cell_DataType::TYPE_STRING);
                $xlsx_writer->getActiveSheet()->setCellValueExplicit('E' . $i, $item->versao, PHPExcel_Cell_DataType::TYPE_STRING);
                $xlsx_writer->getActiveSheet()->setCellValueExplicit('F' . $i, $item->modalidade, PHPExcel_Cell_DataType::TYPE_STRING);
            }
        }

        return $xlsx_writer;
    }
}