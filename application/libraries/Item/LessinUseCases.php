<?php 

class LessinUseCases
{
    private $CI = null;

    public function __construct()
    {
        $this->CI = & get_instance();

        $this->CI->load->model(array(
            'lessin_model'
        ));
    }

    public function apply_filters($filters = array())
    {
        if (!empty($filters["id"])) {
            $this->CI->lessin_model->set_state("filter.id", $filters["id"]);
        }

        if (!empty($filters["ncm"])) {
            $this->CI->lessin_model->set_state("filter.ncm", $filters["ncm"]);
        }
    }

    public function unapply_filters()
    {
        $this->CI->lessin_model->unset_state("filter.id");
        $this->CI->lessin_model->unset_state("filter.ncm");
    }

    public function get_item()    
    {
        return $this->CI->lessin_model->get_item();
    }

    public function save_item($data, $id = NULL)
    {
        if (!empty($id)) {
            return $this->CI->lessin_model->save($data, array("id" => $id));
        }

        return $this->CI->lessin_model->save($data);
    }

    public function get_items($limit = NULL, $offset = NULL)    
    {
        return $this->CI->lessin_model->get_items($limit, $offset);
    }

    public function get_total_items()    
    {
        return $this->CI->lessin_model->get_total_items();
    }

    public function get_related_items($id_lessin) 
    {
        return $this->CI->lessin_model->get_related_items($id_lessin);
    }

    public function generate_xlsx($xlsx_writer, $items)
    {
        $xlsx_writer->setActiveSheetIndex(0);
        $xlsx_writer->getActiveSheet()->setTitle('Lessin');

        $xlsx_writer->getActiveSheet()->setCellValue('A1', 'NCM');
        $xlsx_writer->getActiveSheet()->setCellValue('B1', 'FUNDAMENTO');
        $xlsx_writer->getActiveSheet()->setCellValue('C1', 'DESTAQUES INCLUÍDOS');
        $xlsx_writer->getActiveSheet()->setCellValue('D1', 'DESTAQUES EXCLUÍDOS');
        $xlsx_writer->getActiveSheet()->setCellValue('E1', 'VALIDADE');
        $xlsx_writer->getActiveSheet()->setCellValue('F1', 'CRIADO EM');
        $xlsx_writer->getActiveSheet()->setCellValue('G1', 'VERSÃO');
        // $xlsx_writer->getActiveSheet()->setCellValue('C1', 'TIPO ARQUIVO');
        // $xlsx_writer->getActiveSheet()->setCellValue('I1', 'LOTE IMPORTAÇÃO');

        $xlsx_writer->getActiveSheet()->getStyle("A1:G1")
            ->getFont()
            ->setBold(true);
        
        $xlsx_writer->getActiveSheet()->getStyle("A1:G1")
            ->getFill()
            ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
            ->getStartColor()->setARGB('F9FF00');
        
        $xlsx_writer->getActiveSheet()->getStyle("A1:G1")
            ->getAlignment()
            ->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

        foreach (range('A', "I") as $columnID) {
            $xlsx_writer->getActiveSheet()
                ->getColumnDimension($columnID
                )->setAutoSize(true);
            
            $xlsx_writer->getActiveSheet()
                ->getStyle($columnID)
                ->getAlignment()
                ->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);
        }

        if (!empty($items)) {
            $horizontal_left = array(
                'alignment' => array(
                    'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_LEFT,
                ),
            );

            $i = count($items) + 1;

            $xlsx_writer->getActiveSheet()->getStyle('A1:A' . $i)->applyFromArray($horizontal_left);

            foreach ($items as $key => $item) {
                $i = $key + 2;

                $xlsx_writer->getActiveSheet()->setCellValueExplicit('A' . $i, $item->get_ncm(), PHPExcel_Cell_DataType::TYPE_STRING);
                // $xlsx_writer->getActiveSheet()->setCellValueExplicit('C' . $i, !empty($item->get_tipo_arquivo()) ? $item->get_tipo_arquivo() : "", PHPExcel_Cell_DataType::TYPE_STRING);
                $xlsx_writer->getActiveSheet()->setCellValueExplicit('B' . $i, !empty($item->get_fundamento()) ? $item->get_fundamento() : "", PHPExcel_Cell_DataType::TYPE_STRING);
                $xlsx_writer->getActiveSheet()->setCellValueExplicit('C' . $i, !empty($item->get_destaques_incluidos()) ? $item->get_destaques_incluidos() : "", PHPExcel_Cell_DataType::TYPE_STRING);
                $xlsx_writer->getActiveSheet()->setCellValueExplicit('D' . $i, !empty($item->get_destaques_excluidos()) ? $item->get_destaques_excluidos() : "", PHPExcel_Cell_DataType::TYPE_STRING);
                // $xlsx_writer->getActiveSheet()->setCellValueExplicit('I' . $i, !empty($item->get_lote_importacao()) ? $item->get_lote_importacao() : "", PHPExcel_Cell_DataType::TYPE_STRING);
                $xlsx_writer->getActiveSheet()->setCellValueExplicit('E' . $i, $item->get_validade(true), PHPExcel_Cell_DataType::TYPE_STRING);
                $xlsx_writer->getActiveSheet()->setCellValueExplicit('F' . $i, $item->get_criado_em(true), PHPExcel_Cell_DataType::TYPE_STRING);
                $xlsx_writer->getActiveSheet()->setCellValueExplicit('G' . $i, $item->get_versao(), PHPExcel_Cell_DataType::TYPE_STRING);
            }
        }

        return $xlsx_writer;
    }

    public function upload_xlsx()
    {
        $upload_path = config_item('upload_tmp_path');
        $id_empresa  = sess_user_company();
        $type        = '';
        $msg         = '';

        $config['allowed_types'] = 'xls|xlsx';
        $config['upload_path']   = $upload_path;
        $config['max_size']      = 2147483648;

        $this->CI->load->library('upload', $config);

        if (!$this->CI->upload->do_upload('arquivo')) {
            return array(
                'type' => "error",
                'msg'  => $this->CI->upload->display_errors('<p>', '</p>')
            );
        } else {
            $log_atualizados = array();
            $log_inseridos = array();
            $log_com_erro = array();

            $upload_data = $this->CI->upload->data();

            $file_ext = strtolower($upload_data['file_ext']);

            if (in_array($file_ext, array(".xlsx", ".xls"))) {
                include APPPATH . 'libraries/xlsxreader.php';

                $xlsx        = new XLSXReader($upload_data['full_path']);
                $sheetNames  = $xlsx->getSheetNames();
                $sheetActive = current($sheetNames);
                $sheet       = $xlsx->getSheet($sheetActive);

                $i = 0;

                $colunas = array(
                    'ncm'                 => array('NCM', 'ncm', 'Ncm'),
                    'criado_em'           => array('CRIADO EM', 'criado em'),
                    'destaques_incluidos' => array('DESTAQUES INCLUIDOS', 'destaques incluidos', 'Destaque(s) Incluido(s) na Lessin', 'Destaque(s) Incluído(s) na Lessin'),
                    'destaques_excluidos' => array('DESTAQUES EXCLUIDOS', 'destaques excluidos', 'Destaque(s) Excluido(s) da Lessin', 'Destaque(s) Excluído(s) da Lessin'),
                    'fundamento'          => array('FUNDAMENTO', 'fundamento'),
                    'validade'            => array('VALIDADE', 'validade'),
                    'lote_importacao'     => array('LOTE IMPORTACAO', 'lote importacao')
                );

                $this->CI->load->helper('text');
                $this->CI->load->helper('formatador');

                foreach ($sheet->getData() as $row) {
                    if ($i == 0) {
                        $idx = array();

                        $cols = array_map(function ($item) {
                            return convert_accented_characters($item);
                        }, array_values($row));

                        foreach ($cols as $k => $row_col) {
                            foreach ($colunas as $ck => $regras) {
                                $preg = '/^(' . implode("|", $regras) . ')$/i';

                                if (preg_match($preg, $row_col)) {
                                    if (array_key_exists($ck, $idx)) {
                                        continue 2;
                                    }

                                    unset($cols[$k]);
                                    $idx[$ck] = $k;

                                    continue 2;
                                }
                            }
                        }

                        // Colunas inválidas/duplicadas
                        $erro_proc_col = FALSE;

                        if (count($cols) > 0) {
                            $lista_colunas = '';

                            foreach ($cols as $kv => $colv) {
                                $alpha = num2alpha($kv);
                                $col_info = (empty($row[$kv]) ? '<em>Sem título</em>' : $row[$kv]);
                                $lista_colunas .= '<li>' . $col_info . ' (<b>' . $alpha . '</b>)</li>';
                            }

                            $msg_err  = "<li>Colunas inválidas ou duplicadas: <b>" . count($cols) . "</b></li>";
                            $msg_err .= "<ul>" . $lista_colunas . "</ul>";

                            $erro_proc_col = TRUE;
                        }

                        // Campos Obrigatórios
                        if (!isset($idx['ncm'])) {
                            $msg_err .= '<li>Coluna <b>NCM</b> não encontrada.</li>';
                            $erro_proc_col = TRUE;
                        }

                        if (!isset($idx['validade'])) {
                            $msg_err .= '<li>Coluna <b>VALIDADE</b> não encontrada.</li>';
                            $erro_proc_col = TRUE;
                        }

                        if ($erro_proc_col === TRUE) {
                            $message = '<h4>Ops.. Alguns erros foram encontrados na planilha enviada:</h4><ul>' . $msg_err . '</ul>';
                            
                            return array(
                                'type' => "error",
                                'msg'  => $message
                            );
                        }
                        
                    } else {
                        // Limpeza de Caracteres Especiais
                        array_walk($row, function (&$v) {
                            $v = clean_str($v);
                        });

                        $ncm                 = str_replace('.', '', trim($row[$idx['ncm']]));
                        $criado_em           = trim($row[$idx['criado_em']]);
                        $destaques_incluidos = trim($row[$idx['destaques_incluidos']]);
                        $destaques_excluidos = trim($row[$idx['destaques_excluidos']]);
                        $fundamento          = trim($row[$idx['fundamento']]);
                        $validade            = trim($row[$idx['validade']]);
                        $lote_importacao     = trim($row[$idx['lote_importacao']]);

                        if (
                            empty($ncm) &&
                            empty($criado_em) &&
                            empty($destaques_incluidos) &&
                            empty($destaques_excluidos) &&
                            empty($fundamento) &&
                            empty($validade) &&
                            empty($lote_importacao)
                        ) {
                            $log_com_erro[] = 'Não há registros na linha <b>' . $i . '</b>';
                            continue;
                        }

                        if (empty($ncm)) {
                            $log_com_erro[] = 'NCM não informado na linha <b>' . $i . '</b>';
                            continue;
                        }

                        if (empty($validade)) {
                            $log_com_erro[] = 'VALIDADE não informada na linha <b>' . $i . '</b>';
                            continue;
                        }

                        $item = null;

                        try {
                            $this->apply_filters(array("ncm" => $ncm));

                            $item = $this->get_item();
                        } catch (Exception $e) {
                            $item = null;
                        }

                        $criado_em_timestamp = (new DateTime())->getTimestamp();

                        if (!empty($criado_em)) {
                            $criado_em_datetime = DateTime::createFromFormat("d/m/Y H:i", $criado_em);
                            $criado_em_timestamp = $criado_em_datetime->getTimestamp();
                        }

                        $validade_datetime = DateTime::createFromFormat("d/m/Y", $validade);
                        $validade_timestamp = $validade_datetime->getTimestamp();

                        $dbdata['ncm']                 = $ncm;
                        $dbdata['id_empresa']          = $id_empresa;
                        $dbdata['tipo_arquivo']        = $file_ext;
                        $dbdata['versao']              = 0;
                        $dbdata['criado_em']           = isset($criado_em) ? date("Y-m-d H:i", $criado_em_timestamp) : date("Y-m-d H:i");
                        $dbdata['destaques_incluidos'] = $destaques_incluidos;
                        $dbdata['destaques_excluidos'] = $destaques_excluidos;
                        $dbdata['fundamento']          = $fundamento;
                        $dbdata['validade']            = date("Y-m-d H:i", $validade_timestamp);
                        $dbdata['lote_importacao']     = $lote_importacao;

                        if (!empty($item)) {
                            $this->apply_filters(array('ncm' => $dbdata['ncm']));
                            
                            $itens_replicados = $this->get_items();

                            foreach ($itens_replicados as $item) {
                                $data = $dbdata;
                                $data['versao'] = $item->versao + 1;

                                $this->save_item($data, $item->id);
                            }
                            
                            $this->save_item($dbdata);

                            $log_atualizados[] = 'Item [<b>' . $ncm . '</b>] atualizado';
                        } else {
                            $this->save_item($dbdata);

                            $log_inseridos[] = 'Item [<b>' . $ncm . '</b>] foi inserido';
                        }
                    }

                    $i++;
                }

                $msg  = '';
                $type = '';

                if (!empty($log_com_erro)) {
                    $type = 'error';

                    $msg .= '<h4>Erro</h4>';
                    $msg .= 'Arquivo XLSX <b>' . $upload_data['orig_name'] . '</b> recebido, mas ocorreram alguns erros.';
                } else {
                    $type = 'success';

                    $msg .= '<h4>Sucesso</h4>';
                    $msg .= 'Arquivo XLSX <b>' . $upload_data['orig_name'] . '</b> recebido e processado com sucesso!';
                }

                $msg .= '<div id="log-accordion">';
                $msg .= '<ul>';

                // Itens inseridos
                $msg .= '<li>';

                if (!empty($log_inseridos)) {
                    $msg .= '<a data-toggle="collapse" data-parent="#log-accordion" href="#inseridos">';
                    $msg .= 'Itens inseridos: <b>' . count($log_inseridos) . '</b></a>';
                    $msg .= '<div id="inseridos" class="collapse">';
                    $msg .= format_log_as_html($log_inseridos);
                    $msg .= '</div>';
                } else {
                    $msg .= 'Itens inseridos: <b>0</b>';
                }

                $msg .= '</li>';

                // Itens atualizados
                $msg .= '<li>';

                if (!empty($log_atualizados)) {
                    $msg .= '<a data-toggle="collapse" data-parent="#log-accordion" href="#atualizados">';
                    $msg .= 'Itens atualizados: <b>' . count($log_atualizados) . '</b></a>';
                    $msg .= '<div id="atualizados" class="collapse">';
                    $msg .= format_log_as_html($log_atualizados);
                    $msg .= '</div>';
                } else {
                    $msg .= 'Itens atualizados: <b>0</b>';
                }

                $msg .= '</li>';

                // Itens com erro
                $msg .= '<li>';

                if (!empty($log_com_erro)) {
                    $msg .= '<a data-toggle="collapse" data-parent="#log-accordion" href="#com-erro">';
                    $msg .= 'Itens com erro: <b>' . count($log_com_erro) . '</b></a>';
                    $msg .= '<div id="com-erro" class="collapse">';
                    $msg .= format_log_as_html($log_com_erro);
                    $msg .= '</div>';
                } else {
                    $msg .= 'Itens com erro: <b>0</b>';
                }
                
                $msg .= '</li>';

                $msg .= '</ul>';
                $msg .= '</div>';

            }

            unlink($upload_data['full_path']);
        }
        
        return array(
            "type" => $type,
            "msg"  => $msg
        ) ;
    }
}