<?php 

class Status
{
    private $CI = null;
    private $id_status = null;

    public function __construct()
    {
        $this->CI = &get_instance();

        $this->CI->load->model(array(
            'item_model',
            'empresa_model',
            'item_log_model'
        ));
    }

    public function set_status($status)
    {
        if (empty($status)) {
            throw new Exception("Não é possível atualizar o item sem o status!");
        }

        switch ($status) {
            case 'homologar':
                $this->id_status = 1;
                break; 
            case 'homologado':
                $this->id_status = 2;
                break; 
            case 'nao_homologado':
                $this->id_status = 3;
                break; 
            case 'obsoleto':
                $this->id_status = 4;
                break; 
            case 'revisao':
                $this->id_status = 5;
                break; 
            case 'em_analise':
                $this->id_status = 6;
                break; 
            case 'pendente_duvidas':
                $this->id_status = 7;
                break; 
            case 'respondido':
                $this->id_status = 8;
                break;
            case 'revisar_informacoes_erp':
                $this->id_status = 9;
                break;
            case 'homologado_em_revisao':
                $this->id_status = 10;
                break; 
            case 'revisar_informacoes_tecnicas':
                $this->id_status = 11;
                break;
            case 'informacoes_erp_revisadas':
                $this->id_status = 12;
                break;
            case 'aguardando_definicao_responsavel':
                $this->id_status = 13;
                break; 
            case 'aguardando_descricao':
                $this->id_status = 14;
                break;
            case 'perguntas_respondidas_novas':
                $this->id_status = 15;
                break;
        }

        return $this->id_status;
    }

    public function check_homologations($id_empresa, $part_number, $estabelecimento, $is_homologado) 
    {
        $empresa = $this->CI->empresa_model->get_entry(!empty($id_empresa) ? $id_empresa : sess_user_company());

        $funcoes_adicionais = explode('|', $empresa->funcoes_adicionais);
        
        $this->CI->db->where("ci.id_empresa", $id_empresa);
        $this->CI->db->where("ci.part_number", $part_number);
        $this->CI->db->where("ci.estabelecimento", $estabelecimento);

        if (in_array('homologacao_fiscal', $funcoes_adicionais) && in_array('homologacao_engenharia', $funcoes_adicionais)) {
           
            if ($is_homologado == 0)
            {
                $this->CI->db->join("cad_item_homologacao cih", "ci.id_item = cih.id_item AND (cih.tipo_homologacao = 'Fiscal' OR cih.tipo_homologacao = 'Engenharia') AND cih.homologado = {$is_homologado}", "inner");
            } else {
                $this->CI->db->join("cad_item_homologacao cih", "ci.id_item = cih.id_item AND cih.tipo_homologacao = 'Fiscal' AND cih.homologado = {$is_homologado}", "inner");
                $this->CI->db->join("cad_item_homologacao cih2", "ci.id_item = cih2.id_item AND cih2.tipo_homologacao = 'Engenharia' AND cih2.homologado = {$is_homologado}", "inner");    
            }
            
        } else if (in_array('homologacao_fiscal', $funcoes_adicionais)) {
            $this->CI->db->join("cad_item_homologacao cih", "ci.id_item = cih.id_item AND cih.tipo_homologacao = 'Fiscal' AND cih.homologado = {$is_homologado}", "inner");
            
        } else if (in_array('homologacao_engenharia', $funcoes_adicionais)) {
            $this->CI->db->join("cad_item_homologacao cih", "ci.id_item = cih.id_item AND cih.tipo_homologacao = 'Engenharia' AND cih.homologado = {$is_homologado}", "inner");
        }

        $query = $this->CI->db->get("cad_item ci");
        $row = $query->row();

        return !empty($row);
    }

    public function get_status($id)
    {

        $this->CI->db->where('id', $id);
        $query = $this->CI->db->get('status');

        $result = $query->row();

        if ($result->slug == 'homologar')
        {
            return 'Pendente de Homologação';
        }

        return $result->status;
    }

    public function get_slug($id)
    {
        $this->CI->db->where('id', $id);
        $query = $this->CI->db->get('status');

        return $query->row();
    }

    public function update_item($part_number, $estabelecimento, $id_empresa = null)
    {
        if (empty($part_number) || empty($this->id_status)) {
            throw new Exception("Não é possível atualizar o item uma das informações Part Number e ID Status!");
        }

        if (empty($id_empresa)) {
            $id_empresa = sess_user_company();
        }

        $item = $this->CI->item_model->get_entry($part_number, $id_empresa, $estabelecimento);

        $status_anterior = $this->get_status($item->id_status);
        $status_atual = $this->get_status($this->id_status);

        // Verifica se o item está sendo homologado e altera a flag de item_ja_homologado
        if ($this->id_status == 2) {
            $item->item_ja_homologado = 1;
        }

        $motivo = "Alteração do Status: <em>{$status_anterior}</em> &rarr; <strong>{$status_atual}</strong>";
        
        if ($item->id_status == $this->id_status) {
            $motivo = false;
        }
        
        $data = array(
            "id_status" => $this->id_status,
            "item_ja_homologado" => $item->item_ja_homologado
            );

        $check_change = array(
            'part_number'                  => $part_number,
            'estabelecimento'              => $estabelecimento,
            'descricao'                    => $item->descricao,
            'id_empresa'                   => $id_empresa,
            'status_atual'                 => $this->id_status,
            'responsavel_fiscal_atual'     => $item->id_resp_fiscal,
            'responsavel_engenharia_atual' => $item->id_resp_engenharia                         
        );

        $this->CI->item_model->set_change_send_mail($check_change);

        $empresa = $this->CI->empresa_model->get_entry($id_empresa);
        $campos_adicionais = explode("|", $empresa->campos_adicionais);
        $hasOwner = in_array('owner', $campos_adicionais);

        if ($item->recebe_email_alteracao_status == 1 && $hasOwner && !is_null($item->criado_por) && is_numeric($item->criado_por)) {
            $this->send_notification_for_user_status_changed(
                $part_number,
                $estabelecimento,
                $status_anterior,
                $status_atual,
                $item,
                $id_empresa
            );
        }

        // Envia email de notificação para os seguidores do item
        $this->send_notification_email_usuarios_seguidores($part_number, $estabelecimento, $status_anterior, $status_atual, $item);

        return $this->CI->item_model->update_item($part_number, $id_empresa, $data, $motivo, $estabelecimento);
    }

    private function send_notification_email_usuarios_seguidores($part_number, $estabelecimento, $status_anterior, $status_atual, $item)
    {
        $usuarios_seguidores = $this->CI->item_model->get_email_usuarios_seguidores($part_number, sess_user_company(), $estabelecimento);

        $owners = $this->CI->item_model->get_owners($part_number, sess_user_company(), $estabelecimento);

        // var_dump($owners);
        // die();

        if (empty($owners)) {
            $owners = array();
        }

        $body = $this->CI->load->view('templates/notificacao_mudanca_status_item', array('part_number' => $part_number, 'status_anterior' => $status_anterior, 'status_atual' => $status_atual, 'owners' => $owners, 'item' => $item), true);
        
        if(!empty($usuarios_seguidores)) {
            $this->CI->load->library('email');
            foreach ($usuarios_seguidores as $key => $usuario_seguidor) {
                $this->CI->email->from(config_item('mail_from_addr'));
                $this->CI->email->to($usuario_seguidor->email_seguidor);
                $this->CI->email->subject("[Gestao Tarifária] Alteracao de Status do Item {$part_number}");
                $this->CI->email->message($body);
                $this->CI->email->send();
            }

            // var_dump($this->CI->email->print_debugger());
            // die();
        }
    }

    public function get_perguntas_pendentes($pendente,$part_number, $estabelecimento, $id_empresa,$joinResponsavel = FALSE)
    {
        $this->CI->db->select("count(*) as perguntas");
        $this->CI->db->join('item i', 'i.part_number = pp.part_number AND i.id_empresa = pp.id_empresa AND i.estabelecimento = pp.estabelecimento', 'inner');
        
        if ($joinResponsavel == TRUE)
           $this->CI->db->join('rel_perguntas_responsavel rel', 'rel.id_ctr_pendencias = pp.id', 'inner');                     
        
           $this->CI->db->where("pp.id_empresa", $id_empresa);
       
        if ($pendente == true)
            $this->CI->db->where("pp.pendente", '1');

        if (empty($estabelecimento))
        {
            $this->CI->db->where("(pp.estabelecimento = '' OR pp.estabelecimento is null)",null,false);
        } else {
            $this->CI->db->where("pp.estabelecimento", $estabelecimento);
        }
        
        $this->CI->db->where("pp.part_number", $part_number);
        $query = $this->CI->db->get('ctr_pendencias_pergunta pp');

        return $query->row();
    }

    public function check_status_item_perguntas(
                        $part_number,
                        $estabelecimento,
                        $status_anterior,
                        $exclusao = false
                    )
    {
        $id_empresa = sess_user_company();

        $item = $this->CI->item_model->get_entry($part_number, $id_empresa, $estabelecimento);

        $empresa = $this->CI->empresa_model->get_entry(sess_user_company());
        $campos_adicionais = explode("|", $empresa->campos_adicionais);
        $hasOwner =  in_array('owner', $campos_adicionais);
       
        $hasDescricaoGlobal = in_array("descricao_global", $campos_adicionais);

        $respostas_pendentes = $this->get_perguntas_pendentes(TRUE, $part_number, $estabelecimento,$id_empresa );
        $perguntas_do_item   = $this->get_perguntas_pendentes(FALSE, $part_number, $estabelecimento,$id_empresa,FALSE );

        // se for homologado/não homologado/por homologar/em revisão/inativo o status não deve mudar
        // regra informada por Matheus da Becomex
        if (in_array($status_anterior, [1, 2, 3, 4, 5])) {
            return;
        }
        
        if (($status_anterior == 7 || $status_anterior == 11) 
            && $respostas_pendentes->perguntas == 0 
            && $perguntas_do_item->perguntas > 0 
            && $hasDescricaoGlobal 
            && ($item->descricao == '' || $item->descricao == null)
        ) {

            $this->set_status("aguardando_descricao");
            $this->update_item($part_number, $estabelecimento, $id_empresa);

        } elseif ($status_anterior == 7 &&
                    $respostas_pendentes->perguntas == 0 &&
                    $perguntas_do_item->perguntas > 0 &&
                    empty($item->id_grupo_tarifario) &&
                    !empty($item->descricao)
                ) {

            $this->set_status("respondido");
            $this->update_item($part_number, $estabelecimento, $id_empresa);

        } elseif ($hasOwner &&
                    $status_anterior == 11 &&
                    $respostas_pendentes->perguntas == 0 &&
                    $perguntas_do_item->perguntas > 0
                ) {

            // Verifica se a atualização foi devido a uma exclusão de pergunta, conforme regra enviada pelo Jhonatan no chamado 25016
            if ($exclusao) {
                $this->set_status("respondido");
            } else {
                $this->set_status("perguntas_respondidas_novas");
            }

            $this->update_item($part_number, $estabelecimento, $id_empresa);
        
        } elseif ($respostas_pendentes->perguntas > 0 &&
                    $status_anterior != 7 &&
                    ($hasOwner && $status_anterior != 11)
                ) {

            $this->set_status("pendente_duvidas");
            $this->update_item($part_number, $estabelecimento, $id_empresa);

        } elseif ($perguntas_do_item->perguntas > 0 &&
                    $respostas_pendentes->perguntas == 0 &&
                    empty($item->id_grupo_tarifario)
                ) {

            $this->set_status("respondido");
            $this->update_item($part_number, $estabelecimento, $id_empresa);

        } elseif ($perguntas_do_item->perguntas == 0) {

            $this->set_status("em_analise");
            $this->update_item($part_number, $estabelecimento, $id_empresa);
            
        }
    }

    private function send_notification_for_user_status_changed(
        $part_number, 
        $estabelecimento, 
        $status_anterior, 
        $status_atual, 
        $item,
        $id_empresa
        )
    {
        $this->CI->load->library('email');
        $this->CI->load->model('usuario_model');
        $this->CI->load->model('log_notificacao_usuario_model');

        $empresa = $this->CI->empresa_model->get_entry($id_empresa);
        $campos_adicionais = explode("|", $empresa->campos_adicionais);

        $email_usuario = $this->CI->usuario_model->get_email_user($item->criado_por);

        if (is_null($email_usuario) || empty($email_usuario)) {
            return;
        }

        $owners = $this->CI->item_model->get_owners($part_number, sess_user_company(), $estabelecimento);

        if (empty($owners)) {
            $owners = array();
        }

        $body = $this->CI->load->view('templates/notificacao_responsavel_item_mudanca_status', array('part_number' => $part_number, 'status_anterior' => $status_anterior, 'status_atual' => $status_atual, 'owners' => $owners, 'item' => $item,
        'hasDescricaoGlobal' => in_array("descricao_global", $campos_adicionais)), true);

        if (!empty($email_usuario)) {
            $this->CI->email->from(config_item('mail_from_addr'));
            $this->CI->email->to($email_usuario);
            $this->CI->email->subject("[Gestao Tarifária] Alteracao de Status do Item {$part_number}");
            $this->CI->email->message($body);
            $this->CI->email->send();
        }

        try
        {
            $log_data['id_empresa'] = sess_user_company();
            $log_data['part_number'] = $part_number;
            $log_data['estabelecimento'] = $estabelecimento;
            $log_data['id_usuario_origem'] = sess_user_id();
            $log_data['id_usuario_notificado'] = $item->criado_por;
            $log_data['tipo_notificacao'] = 'email';
            $log_data['modelo'] = 'notificacao_responsavel_item_mudanca_status';
            $log_data['motivo'] = 'Alteracao de Status do Item';
            $log_data['status_anterior'] = $status_anterior;
            $log_data['status_atual'] = $status_atual;
            $log_data['data_notificacao'] = date('Y-m-d H:i:s');

            $this->CI->log_notificacao_usuario_model->save($log_data);

        } catch (Exception $e) {
            log_message('error', $e->getMessage());
        }
    }
}