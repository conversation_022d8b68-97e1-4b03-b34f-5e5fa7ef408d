<?php 

class LogItem
{
    private $logs = [];
    private $CI = null;
    private $log = null;

    public function __construct()
    {
        $this->CI = & get_instance();
    }

    public function setLog($label, $descricao)
    {
        $this->logs[$label] = $descricao;
    }

    public function setLogs($logs = array())
    {
        $this->logs = $logs;
    }

    public function getLogs()
    {
        return $this->logs;
    }

    public function getLogsFormatted()
    {
        $text = '';

        foreach($this->logs as $label => $value) {
            $text .= "<strong>$label</strong>: $value <br>";
        }

        return $text;
    }

    public function setup($args = array())
    {
        $this->CI->load->model('item_log_model');

        $this->log = new ItemLog($args, true);
    }

    public function write()
    {
        $this->log->setMotivo($this->getLogsFormatted());
        $this->CI->item_log_model->save($this->log->toSaveArray());
    }
}