<?php

class ItemDirect
{
    private $CI = NULL;

    public function __construct()
    {
        $this->CI = & get_instance();

        $this->CI->load->model(array(
            'empresa_model',
            'item/item_direct_model',
            'item_model',
            'cad_item_model'
        ));
    }

    public function isDirectItem($item = array(), $cnpj = null, $tipo = null)
    {
        try {
            if (empty($item)) {
                return false;
            }
    
            $directItens = $this->CI->item_direct_model->getItemsByCnpj($cnpj);
    
            $listDirect = new ListItemDirect($directItens);
    
            if ($listDirect->isDirectItemByPN($item['part_number'])) {
                $this->moveItemToCadItem($item,$tipo);
                
                return true;
            }
        } catch (Exception $e) {
            if (!empty($tipo) && isset($tipo['retornar_erro']) && $tipo['retornar_erro'] == false)
            {
                return false;
            } else {
                throw new Exception($e->getMessage());
            }
        }
    }

    private function moveItemToCadItem($item,$tipo = null)
    {

        $this->CI->load->library('Item/CadItem');

        if (!empty($tipo) && isset($tipo['atualizar']) && $tipo['atualizar'] == true)
        {
            $this->CI->caditem->atualizar($item);
        } else {
            $this->CI->caditem->atribuir($item);
        }

    }
}