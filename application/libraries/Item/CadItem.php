<?php 

class CadItem
{

    private $CI = null;

    public function __construct()
    {
        $this->CI = & get_instance();
    }

    public function atribuir($item)
    {
        if (empty($item)) {
            return false;
        }

        if (empty($item['ncm'])) {
            throw new Exception("O item (<strong>" . $item['part_number'] . "</strong>) está relacionado para ser atribuido diretamente, porém não foi informado nenhuma NCM.");
        }

        $this->CI->load->model(array(
            'item_log_model', 'cad_item_model',
            'grupo_tarifario_model'            
        ));

        $grupo = $this->CI->grupo_tarifario_model->get_entry_by_ncm_recomendado($item['ncm']);

        if (empty($grupo)) {
            throw new Exception("Não foi encontrado nenhum grupo tarifário relacionado a NCM informada ({$item['ncm']}), o item (<strong>" . $item['part_number'] . "</strong>) não será atribuido diretamente");
        }

        if (!$this->CI->cad_item_model->check_item_exists($item['part_number'], $item['id_empresa'], $item['estabelecimento'])) {
            $itemRow = $this->CI->item_model->get_entry($item['part_number'], $item['id_empresa'], $item['estabelecimento']);

            $cadItem = array(
                'part_number'                 => $item['part_number'],
                'id_grupo_tarifario'          => $grupo->id_grupo_tarifario,
                'ncm_proposto'                => $grupo->ncm_recomendada,
                'id_empresa'                  => $itemRow->id_empresa,
                'origem'                      => 'atribuicao_via_importacao_txt',
                'estabelecimento'             => $itemRow->estabelecimento,
                'id_resp_fiscal'              => $itemRow->id_resp_fiscal,
                'id_resp_engenharia'          => $itemRow->id_resp_engenharia,
                'inf_adicionais'              => $itemRow->inf_adicionais,
                'caracteristicas'             => $grupo->caracteristica,
                'subsidio'                    => $grupo->subsidio,
                'dispositivo_legal'           => $grupo->dispositivo_legal,
                'solucao_consulta'            => $grupo->solucao_consulta,
                'memoria_classificacao'       => $grupo->memoria_classificacao,
                'descricao_mercado_local'     => strtoupper($itemRow->descricao),
            );

            if (!empty($itemRow->funcao)) {
                $cadItem['funcao'] = $itemRow->funcao;
            }

            if (!empty($itemRow->inf_adicionais)) {
                $cadItem['inf_adicionais'] = $itemRow->inf_adicionais;
            }

            if (!empty($itemRow->aplicacao)) {
                $cadItem['aplicacao'] = $itemRow->aplicacao;
            }

            if (!empty($itemRow->material_constitutivo)) {
                $cadItem['material_constitutivo'] = $itemRow->material_constitutivo;
            }

            if (!empty($itemRow->marca)) {
                $cadItem['marca'] = $itemRow->marca;
            }

            if ($this->CI->cad_item_model->save($cadItem)) {
                $this->gerarLog($cadItem, array(
                    'Subsídio' => $cadItem['subsidio'],
                    'Dispositivo Legal' => $cadItem['dispositivo_legal'],
                    'Solução de Consulta' => $cadItem['solucao_consulta'],
                    'Memória de Classificação' => $cadItem['memoria_classificacao'],
                    'Descrição proposta resumida' => $cadItem['descricao_mercado_local'],
                    'Função' => $cadItem['funcao'],
                    'Informações Adicionais' => $cadItem['inf_adicionais'],
                    'Aplicação' => $cadItem['aplicacao'],
                    'Material Constitutivo' => $cadItem['material_constituvio'],
                    'Marca' => $cadItem['marca'],
                    'Grupo Tarifário' => $grupo->descricao
                ));
            }
        } else {
            throw new Exception("O item (<strong>" . $item['part_number'] . "</strong>) está relacionado para ser atribuido diretamente, porém já foi atribuido.");
        }
    }

    public function atualizar($item)
    {
        if (empty($item)) {
            return false;
        }

        if (empty($item['ncm'])) {
            throw new Exception("O item (<strong>" . $item['part_number'] . "</strong>) está relacionado para ser atribuido diretamente, porém não foi informado nenhuma NCM.");
        }

        $this->CI->load->model(array(
            'item_log_model', 'cad_item_model',
            'grupo_tarifario_model'            
        ));

        $grupo = $this->CI->grupo_tarifario_model->get_entry_by_ncm_recomendado($item['ncm']);

        if (empty($grupo)) {
            throw new Exception("Não foi encontrado nenhum grupo tarifário relacionado a NCM informada ({$item['ncm']}), o item (<strong>" . $item['part_number'] . "</strong>) não será atribuido diretamente");
        }
            $itemRow = $this->CI->item_model->get_entry($item['part_number'], $item['id_empresa'], $item['estabelecimento']);
            
            $cadItem = array(
                'part_number'                 => $item['part_number'],
                'id_grupo_tarifario'          => $grupo->id_grupo_tarifario,
                'ncm_proposto'                => $grupo->ncm_recomendada,
                'id_empresa'                  => $itemRow->id_empresa,
                'origem'                      => 'atribuicao_via_importacao_txt',
                'estabelecimento'             => $itemRow->estabelecimento,
                'id_resp_fiscal'              => $itemRow->id_resp_fiscal,
                'id_resp_engenharia'          => $itemRow->id_resp_engenharia,
                'inf_adicionais'              => $itemRow->inf_adicionais,
                'caracteristicas'             => $grupo->caracteristica,
                'subsidio'                    => $grupo->subsidio,
                'dispositivo_legal'           => $grupo->dispositivo_legal,
                'solucao_consulta'            => $grupo->solucao_consulta,
                'memoria_classificacao'       => $grupo->memoria_classificacao,
                'descricao_mercado_local'     => strtoupper($itemRow->descricao),
            );

            if (!empty($itemRow->funcao)) {
                $cadItem['funcao'] = $itemRow->funcao;
            }

            if (!empty($itemRow->inf_adicionais)) {
                $cadItem['inf_adicionais'] = $itemRow->inf_adicionais;
            }

            if (!empty($itemRow->aplicacao)) {
                $cadItem['aplicacao'] = $itemRow->aplicacao;
            }

            if (!empty($itemRow->material_constitutivo)) {
                $cadItem['material_constitutivo'] = $itemRow->material_constitutivo;
            }

            if (!empty($itemRow->marca)) {
                $cadItem['marca'] = $itemRow->marca;
            }

            if ($this->CI->cad_item_model->atualiza_cad_item($item['part_number'],$itemRow->id_empresa,$cadItem,$itemRow->estabelecimento)) {
                $this->gerarLog($cadItem, array(
                    'Subsídio' => $cadItem['subsidio'],
                    'Dispositivo Legal' => $cadItem['dispositivo_legal'],
                    'Solução de Consulta' => $cadItem['solucao_consulta'],
                    'Memória de Classificação' => $cadItem['memoria_classificacao'],
                    'Descrição proposta resumida' => $cadItem['descricao_mercado_local'],
                    'Função' => $cadItem['funcao'],
                    'Informações Adicionais' => $cadItem['inf_adicionais'],
                    'Aplicação' => $cadItem['aplicacao'],
                    'Material Constitutivo' => $cadItem['material_constituvio'],
                    'Marca' => $cadItem['marca'],
                    'Grupo Tarifário' => $grupo->descricao
                ));

                return true;
            }
    }

    private function gerarLog($item, $logs)
    {
        $this->CI->load->library('Item/LogItem');
        $this->CI->logitem->setup(array(
            'titulo' => 'atribuicaogrupoviatxt',
            'criado_em' => date('Y-m-d H:i:s'),
            'id_empresa' => $item['id_empresa'],
            'id_usuario' => sess_user_id(),
            'part_number' => $item['part_number'],
            'estabelecimento' => $item['estabelecimento']
        ));
        
        $this->CI->logitem->setLog('Part Number', $item['part_number']);
        $this->CI->logitem->setLog('Estabelecimento', $item['estabelecimento']);
        
        foreach($logs as $label => $log) {
            if (!empty($log)) {
                $this->CI->logitem->setLog($label, $log);
            }
        }

        $this->CI->logitem->write();
    }
}