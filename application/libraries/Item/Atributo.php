<?php 

class Atributo
{
    private $CI = null;

    public function __construct()
    {
        $this->CI = &get_instance();

    }

    public function get_status_by_desc($status_desc)
    {
        $this->CI->db->where("status", $status_desc);
        $query = $this->CI->db->get('status_wf_atributos');

        return $query->row();
    }

    public function get_all_status()
    {
        $query = $this->CI->db->get('status_wf_atributos');

        return $query->result();
    }

    public function get_all_status_integracao()
    {
        $query = $this->CI->db->get('status_wf_atributos_integracao');

        return $query->result();
    }


    public function get_status($id,$all_result = false)
    {
        if (is_numeric($id))
        {
            $this->CI->db->where('id', $id);
        } else {
            $this->CI->db->where('slug', $id);
        }

        $query = $this->CI->db->get('status_wf_atributos');

        $result = $query->row();

        if ($result->slug == 'homologar')
        {
            return 'Pendente de Homologação';
        }
        if ($all_result)
        {
            return $result;
        }
        return $result->status;
    }

    public function get_status_integracao($id)
    {

        $this->CI->db->where('id', $id);
        $query = $this->CI->db->get('status_wf_atributos_integracao');

        $result = $query->row();

        if ($result->slug == 'pendente_integracao')
        {
            return 'Pendente de Integração';
        }

        return $result->status;
    }
}