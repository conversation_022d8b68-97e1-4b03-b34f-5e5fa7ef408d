<?php
defined('BASEPATH') || exit('No direct script access allowed');

require_once 'vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class PlanilhaGenerator {
    protected $CI;
    private $apiUrl;

    public function __construct() {

        $this->CI =& get_instance();
        $this->CI->load->model('empresa_model');
        $this->CI->load->model('usuario_homolog_bulk_model');
        $this->CI->load->model('cad_item_model');
        $this->apiUrl = config_item('api_url_planilhas_becomex');

    }

    public function gerarExportacaoItens() {
        $filePath = $this->gerarPlanilhaInicial();

        if (is_array($filePath) && $filePath['success'] === false) {
            return $filePath;
        }
        
        $processId = $this->enviarParaProcessamento($filePath);
    
        if (!$processId) {
            return ['success' => false, 'error' => 'Falha ao enviar para processamento'];
        }
    
        $resultado = $this->aguardarProcessamento($processId);
    
        if ($resultado['status'] === 'ready') {
            $planilhaProcessada = $this->baixarPlanilhaProcessada($processId);
            
            if ($planilhaProcessada) {
                $resultadoEnvio = $this->enviarPlanilhaParaUsuario($planilhaProcessada);
                if ($resultadoEnvio['success']) {
                    return ['success' => true, 'message' => 'Planilha gerada e enviada com sucesso', 'file_path' => $resultadoEnvio['file_path']];
                } else {
                    return $resultadoEnvio;
                }
            } else {
                return ['success' => false, 'error' => 'Falha ao baixar a planilha processada'];
            }
        } else {
            return ['success' => false, 'error' => 'Falha no processamento da planilha'];
        }
    }

    private function gerarPlanilhaInicial() {
        
        error_reporting(0);
        set_time_limit(0);
        ini_set('memory_limit', -1);

        $id_empresa = sess_user_company();
        $id_usuario = sess_user_id();

        $empresa = $this->CI->empresa_model->get_entry($id_empresa);
        $campos_adicionais = explode('|', $empresa->campos_adicionais);

        if ($this->CI->cad_item_model->get_state('filter.list_opt') === 'todos') {
            $hasOwner =  in_array('owner', $campos_adicionais);
            if (!$hasOwner)
            {
                $this->CI->cad_item_model->set_state('filter.use_index_helper', TRUE);
            }
        }

        $result = $this->CI->usuario_homolog_bulk_model->get_entry($id_usuario, $id_empresa);

        $idItens = array();

        if ($result) {
            $idItens = unserialize($result->data);
        }

        $itens = $this->CI->cad_item_model->get_itens_to_export_diana($idItens);

        if ($itens->num_rows() > 15000) {
            return ['success' => false, 'error' => 'O número de itens selecionados excede o limite de 15.000 itens'];
        }

        $headerRow = array(
            array(
                'label' => 'CÓDIGO DO PRODUTO',
                'field' => 'codigo_produto',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'NCM PROPOSTO',
                'field' => 'ncm_proposto',
                'col_format' => 'texto',
                'col_width' => 20
            ),
            array(
                'label' => 'DESCRIÇÃO PROPOSTA COMPLETA',
                'field' => 'descricao_completa',
                'col_format' => 'texto',
                'col_width' => 60
            ),
        );

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Adicionar cabeçalho
        $col = 1;
        foreach ($headerRow as $header) {
            $cell = $sheet->getCellByColumnAndRow($col, 1);
            $cell->setValue($header['label']);
            
            // Estilo do cabeçalho
            $cell->getStyle()
                ->getFont()
                ->setBold(true)
                ->setName('Arial')
                ->setSize(10);
            
            $cell->getStyle()
                ->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()
                ->setARGB('FFF9FF00');
            
            $cell->getStyle()
                ->getAlignment()
                ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER)
                ->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);
            
            $sheet->getColumnDimension($cell->getColumn())->setWidth($header['col_width']);
            
            $col++;
        }

        // Adicionar dados
        $row = 2;
        while ($item = $itens->unbuffered_row()) {
            $sheet->setCellValueByColumnAndRow(1, $row, $item->part_number);
            $sheet->setCellValueByColumnAndRow(2, $row, $item->ncm_proposto);
            $sheet->setCellValueByColumnAndRow(3, $row, $item->descricao_proposta_completa);
            
            // Estilo padrão para as células de dados
            $sheet->getStyle($row)->getFont()->setName('Arial')->setSize(11);
            
            $row++;
        }

        // Salvar o arquivo
        $writer = new Xlsx($spreadsheet);
        $fileName = 'Homologacao' . date('Y-m-d_H-i-s') . '.xlsx';
        $filePath = config_item('upload_anexos_path') . $fileName;
        $writer->save($filePath);

        return $filePath;
    }

    private function enviarParaProcessamento($filePath) {
        $ch = curl_init($this->apiUrl . '/criar_planilha');
        $cfile = new CURLFile($filePath, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', basename($filePath));
        $data = [
            'file' => $cfile,
            'portal' => 'true',
            'gpt' => 'true'
        ];
        
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $response = curl_exec($ch);
        curl_close($ch);

        if ($response === false) {
            return null;
        }

        $responseData = json_decode($response, true);
        return $responseData['process_id'] ?? null;
    }

    private function aguardarProcessamento($processId) {
        $maxTentativas = 300; // Cada tentativa leva 3 segundos, totalizando 15 minutos
        $tentativa = 0;
    
        do {
            $status = $this->verificarStatus($processId);
            log_message('debug', "Tentativa $tentativa: " . json_encode($status));
    
            if ($status['status'] === 'ready' || $status['status'] === 'error') {
                log_message('info', "Processamento concluído com status: " . $status['status']);
                return $status;
            }
    
            if ($tentativa % 5 == 0) { // Log a cada 5 tentativas
                log_message('info', "Aguardando processamento. Tentativa: $tentativa");
            }
    
            sleep(2);
            $tentativa++;
        } while ($tentativa < $maxTentativas);
    
        log_message('error', "Tempo limite excedido após $maxTentativas tentativas");
        return ['status' => 'timeout', 'error' => 'Tempo limite excedido'];
    }
    
    private function verificarStatus($processId) {
        $ch = curl_init($this->apiUrl . "/id/$processId/status");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10); // Adiciona um timeout de 10 segundos
    
        $response = curl_exec($ch);
    
        if(curl_errno($ch)) {
            log_message('error', 'Curl error: ' . curl_error($ch));
            curl_close($ch);
            return ['status' => 'error', 'error' => 'Falha na comunicação com o servidor'];
        }
    
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
    
        if ($httpCode != 200) {
            log_message('error', "HTTP error: $httpCode");
            return ['status' => 'error', 'error' => "HTTP error: $httpCode"];
        }
    
        $decodedResponse = json_decode($response, true);
    
        if (json_last_error() !== JSON_ERROR_NONE) {
            log_message('error', 'JSON decode error: ' . json_last_error_msg());
            return ['status' => 'error', 'error' => 'Resposta inválida do servidor'];
        }
    
        return $decodedResponse;
    }

    private function baixarPlanilhaProcessada($processId) {
        $ch = curl_init($this->apiUrl . "/id/$processId/download");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $planilha = curl_exec($ch);
        curl_close($ch);

        if ($planilha === false) {
            return null;
        }

        $fileName = date('Y-m-d_H-i-s') . '_planilha_atributos_diana.xlsx';
        $tempFile = config_item('upload_anexos_path') . $fileName;
        file_put_contents($tempFile, $planilha);
        return $tempFile;
    }

    private function enviarPlanilhaParaUsuario($planilhaPath) {
        if (!file_exists($planilhaPath)) {
            log_message('error', "Arquivo não encontrado: $planilhaPath");
            return ['success' => false, 'error' => 'Arquivo não encontrado'];
        }

        $fileName = basename($planilhaPath);

        $fileUrl = base_url('assets/anexos/' . $fileName);
    
        return ['success' => true, 'file_path' => $fileUrl];
    }
    

}
