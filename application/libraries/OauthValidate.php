<?php

declare(strict_types=1);

use GuzzleHttp\Client;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Builder;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Configuration;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Rsa\Sha256;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Key\InMemory;
use League\OAuth2\Client\Provider\Exception\ClientNotMigratedException;
use League\OAuth2\Client\Provider\GenericProvider;
use League\OAuth2\Client\Provider\GenericResourceOwner;
use League\OAuth2\Client\Token\AccessToken;

if (!defined('OAUTH2_ROOT')) {
    define('OAUTH2_ROOT', dirname(__FILE__) . '/');
    require_once OAUTH2_ROOT . 'oauth/vendorlco/autoload.php';
    require_once OAUTH2_ROOT . 'oauth/vendoroauth/autoload.php';
}
class OauthValidate
{
    private $clientId;
    private $clientSecret;
    private $redirectUrl;
    private $urlAuthorize;
    private $urlAccessToken;
    private $urlRefreshToken;
    private $urlResource;
    private $redirectUrlRedirect;
    private $logoutUrl;
    private $urlPortal;

    private $provider;
    private $accessToken;
    private $refreshToken;
    private $state;
    private $code;
    private $tokenUrl;
    private $tokenEnv;
    private $tokenConsultaUrl;
    private $tokenClientId;
    private $tokenClientSecret;

    public function __construct($type = false)
    {
        if ($type == 'env') {
            $this->clientId = $_ENV['OAUTH_CLIENT_ID'];
            $this->clientSecret = $_ENV['OAUTH_CLIENT_SECRET'];
            $this->redirectUrl = $_ENV['OAUTH_REDIRECT_URI'];
            $this->urlAuthorize = $_ENV['OAUTH_URL_AUTHORIZE'];
            $this->urlAccessToken = $_ENV['OAUTH_URL_ACCESS_TOKEN'];
            $this->urlRefreshToken = $_ENV['OAUTH_URL_REFRESH_TOKEN'];
            $this->urlResource = $_ENV['OAUTH_URL_RESOURCE_OWNER_DETAILS'];
            $this->redirectUrlRedirect = $_ENV['OAUTH_REDIRECT_URI_LOGOUT'];
            $this->logoutUrl = $_ENV['OAUTH_URL_LOGOUT'];
            $this->urlPortal = $_ENV['url_portal'];
            $this->tokenEnv = $_ENV['TOKEN_AMBIENTE'];
            $this->tokenUrl = $_ENV['TOKEN_URL'];
            $this->tokenConsultaUrl = $_ENV['TOKEN_CONSULTA_URL'];
            $this->tokenClientId = $_ENV['CLIENT_ID_' . $this->tokenEnv];
            $this->tokenClientSecret = $_ENV['CLIENT_PASS_' . $this->tokenEnv];
        } elseif ($type == 'config') {
            $this->clientId = config_item('OAUTH_CLIENT_ID');
            $this->clientSecret = config_item('OAUTH_CLIENT_SECRET');
            $this->redirectUrl = config_item('OAUTH_REDIRECT_URI');
            $this->urlAuthorize = config_item('OAUTH_URL_AUTHORIZE');
            $this->urlAccessToken = config_item('OAUTH_URL_ACCESS_TOKEN');
            $this->urlRefreshToken = config_item('OAUTH_URL_REFRESH_TOKEN');
            $this->urlResource = config_item('OAUTH_URL_RESOURCE_OWNER_DETAILS');
            $this->redirectUrlRedirect = config_item('OAUTH_REDIRECT_URI_LOGOUT');
            $this->logoutUrl = config_item('OAUTH_URL_LOGOUT');
            $this->urlPortal = url_portal;
            $this->tokenEnv =  config_item('TOKEN_AMBIENTE');
            $this->tokenUrl =  config_item('TOKEN_URL');
            $this->tokenConsultaUrl =  config_item('TOKEN_CONSULTA_URL');
            $this->tokenClientId =  config_item('CLIENT_ID_' . $this->tokenEnv);
            $this->tokenClientSecret =  config_item('CLIENT_PASS_' . $this->tokenEnv);
        } else {
            $this->clientId = 'XXX';
            $this->clientSecret = 'XXX';
            $this->redirectUrl = 'XXX';
            $this->urlAuthorize = 'XXX';
            $this->urlAccessToken = 'XXX';
            $this->urlRefreshToken = 'XXX';
            $this->urlResource = 'XXX';
            $this->redirectUrlRedirect = 'XXX';
        }

        if (
            $this->isNan($this->clientId) ||
            $this->isNan($this->clientSecret) ||
            $this->isNan($this->redirectUrl) ||
            $this->isNan($this->urlAuthorize) ||
            $this->isNan($this->urlAccessToken) ||
            $this->isNan($this->urlResource) ||
            $this->isNan($this->redirectUrlRedirect)
        ) {
            throw new Exception('Missing parameters, check data in .env');
        }
        $CI = &get_instance();
        $CI->load->library('session');
        if ($this->is_session_started() === FALSE) @session_start();
        $this->provider = new \League\OAuth2\Client\Provider\GenericProvider([
            'clientId' => $this->clientId, // The client ID assigned to you by the provider
            'clientSecret' => $this->clientSecret, // The client password assigned to you by the provider
            'redirectUri' => $this->redirectUrl,
            'urlAuthorize' => $this->urlAuthorize,
            'urlAccessToken' => $this->urlAccessToken,
            'urlResourceOwnerDetails' => $this->urlResource
        ]);
    }

    private function is_session_started()
    {
        if (php_sapi_name() !== 'cli') {
            if (version_compare(phpversion(), '5.4.0', '>=')) {
                return session_status() === PHP_SESSION_ACTIVE ? TRUE : FALSE;
            } else {
                return session_id() === '' ? FALSE : TRUE;
            }
        }
        return FALSE;
    }
    private function isNan($value)
    {
        if (is_array($value)) {
            $valuesArr = array_map('trim', $value);
            $valuesArr = array_filter($valuesArr, 'strlen');
            $valuesArr = array_filter($valuesArr, '_arrayFilters');

            if (count($valuesArr) == 0) {
                return true;
            }
        } elseif (empty(trim($value)) || is_null($value)) {
            return true;
        }

        return false;
    }
    public function redirectToAuthentication()
    {
        $authorizationUrl = $this->provider->getAuthorizationUrl(['scope' => 'openid profile email tenants']);
        $_SESSION['state'] = $this->provider->getState();

        // Redirect the user to the authorization URL.
        header('Location: ' . $authorizationUrl);
        exit();
    }

    public function validateReturnData()
    {
        $response = [
            'status' => 'error',
            'message' => '',
            'data' => ''
        ];

        try {
            if (!isset($_GET['code']) || $this->isNan($_GET['code'])) {
                if (
                    isset($_GET['error'])
                    && ($_GET['error'] == 'disabled_user' || $_GET['error'] == 'interaction_required')
                    && isset($_GET['preferred_username'])
                ) {

                    throw new ClientNotMigratedException($_GET['error'], '400', $_GET['error'], $_GET['preferred_username']);
                }
                throw new Exception('Code not Found');
            }

            $this->code = $_GET['code'];

            $accessToken = $this->provider->getAccessToken(
                'authorization_code',
                [
                    'code' => $this->code,
                    'scope' => 'openid profile email tenants'
                ]
            );

            if (!$accessToken instanceof AccessToken) {
                throw new Exception('Error on generate token');
            }

            $resourceOwner = $this->provider->getResourceOwner($accessToken);

            if (!$resourceOwner instanceof GenericResourceOwner) {
                throw new Exception('Error on generate token');
            }

            $decodedToken = $this->decode($accessToken->getToken(), null, false);

            $_SESSION['accessTokenOauth'] = $accessToken->getToken();
            $_SESSION['idToken'] = $accessToken->getIdToken();
            $_SESSION['tenants'] = $decodedToken->tenants ?? null;
            $_SESSION['email'] = $decodedToken->email ?? null;


            $CI = &get_instance();
            $data = [
                'ultimo_login' => date('Y-m-d H:i:s')
            ];
            
            $CI->db->set($data);
            $CI->db->where('email', $decodedToken->email);
            $CI->db->update('usuario');


            $response['status'] = 'success';
            $response['data'] = [
                'accessToken' => $accessToken->getToken(),
                'refreshToken' => $accessToken->getRefreshToken(),
                'expires' => $accessToken->getExpires(),
                $resourceOwner->toArray()
            ];
        } catch (ClientNotMigratedException $e) {
            $product = config_item('product_slug') ? config_item('product_slug') : 'ges_tar';
            $date = new DateTime();
            $date->add(new DateInterval('PT15M'));
            $encriptedToken = base64_encode($date->format('Y-m-d H:i:s') . '||' . $product . '||' . $e->getExtra());
            $urlRedirect = sprintf($this->urlPortal, $encriptedToken);
            header('Location: ' . $urlRedirect);
            exit;
        } catch (Exception $e) {
            // Failed to get the access token or user details.
            $response['message'] = $e->getMessage();
        }

        return $response;
    }
    public function logout($sess)
    {
        $logout = sprintf(
            $this->logoutUrl,
            $sess['idToken'],
            $this->redirectUrlRedirect
        );

        header('Location: ' . $logout);
    }

    public function decode($jwt, $key = null, $verify = true)
    {
        $tks = explode('.', $jwt);
        if (count($tks) != 3) {
            throw new UnexpectedValueException('Wrong number of segments');
        }
        list($headb64, $payloadb64, $cryptob64) = $tks;
        if (
            null === ($header = $this->jsonDecode($this->urlsafeB64Decode($headb64)))
        ) {
            throw new UnexpectedValueException('Invalid segment encoding');
        }
        if (
            null === $payload = $this->jsonDecode($this->urlsafeB64Decode($payloadb64))
        ) {
            throw new UnexpectedValueException('Invalid segment encoding');
        }
        $sig = $this->urlsafeB64Decode($cryptob64);
        if ($verify) {
            if (empty($header->alg)) {
                throw new DomainException('Empty algorithm');
            }
            if ($sig != $this->sign("$headb64.$payloadb64", $key, $header->alg)) {
                throw new UnexpectedValueException('Signature verification failed');
            }
        }
        return $payload;
    }

    public function jsonDecode($input)
    {
        $obj = json_decode($input);
        if (function_exists('json_last_error') && $errno = json_last_error()) {
            $this->handleJsonError($errno);
        } elseif ($obj === null && $input !== 'null') {
            throw new DomainException('Null result with non-null input');
        }
        return $obj;
    }

    private function handleJsonError($errno)
    {
        $messages = array(
            JSON_ERROR_DEPTH => 'Maximum stack depth exceeded',
            JSON_ERROR_CTRL_CHAR => 'Unexpected control character found',
            JSON_ERROR_SYNTAX => 'Syntax error, malformed JSON'
        );
        throw new DomainException(
            isset($messages[$errno])
                ? $messages[$errno]
                : 'Unknown JSON error: ' . $errno
        );
    }

    public function sign($msg, $key, $method = 'HS256')
    {
        $methods = array(
            'HS256' => 'sha256',
            'HS384' => 'sha384',
            'HS512' => 'sha512',
        );
        if (empty($methods[$method])) {
            throw new DomainException('Algorithm not supported');
        }
        return hash_hmac($methods[$method], $msg, $key, true);
    }

    public function urlsafeB64Decode($input)
    {
        $remainder = strlen($input) % 4;
        if ($remainder) {
            $padlen = 4 - $remainder;
            $input .= str_repeat('=', $padlen);
        }
        return base64_decode(strtr($input, '-_', '+/'));
    }
    private function client_credentials()
    {
        $clientId = $this->tokenClientId;
        $tokenUrl = $this->tokenUrl;
        $audience = $tokenUrl;
        $now = new DateTimeImmutable();
        $expiration = $now->modify('+120 seconds');
        sleep(1);

        $privateKeyPath = OAUTH2_ROOT . 'oauth/chaves/svc-portal-gt-' . $this->tokenEnv . '.pem';
        $privateKeyContent = file_get_contents($privateKeyPath);

        // Carregar a chave privada e descriptografá-la
        $privateKey = openssl_pkey_get_private($privateKeyContent, $this->tokenClientSecret);

        if ($privateKey === false) {
            throw new Exception('Não foi possível carregar a chave privada. Verifique a senha e o formato da chave.');
        }
        $privateKeyPem = InMemory::plainText($privateKey); // Carregar a chave PEM

        // Configuração do JWT com o algoritmo de assinatura RS256
        $config = Configuration::forAsymmetricSigner(
            new Sha256(),
            $privateKeyPem,
            InMemory::plainText('') // Use a chave privada para assinatura
        );

        $token = (new Builder())
            ->issuedBy($clientId)    // iss
            ->permittedFor($audience) // aud
            ->relatedTo($clientId)   // sub
            ->issuedAt($now)         // iat
            ->expiresAt($expiration) // exp
            ->identifiedBy(uniqid()) // jti
            ->getToken($config->signer(), $config->signingKey());

        $provider = new GenericProvider([
            'clientId'                => $clientId,
            'clientSecret'            => '',  // Não usamos client_secret aqui
            'urlAuthorize'            => '',
            'urlAccessToken'          => $tokenUrl,
            'urlResourceOwnerDetails' => '',
        ]);

        $accessToken = $provider->getAccessToken('client_credentials', [
            'client_assertion_type' => 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',
            'client_assertion'      => $token->toString(),
        ]);

        return $accessToken;
    }

    public function getDadosEmpresas()
    {

        $response = ['status' => 'error', 'data' => [], 'message' => 'Nenhuma empresa associada a esta conta'];
        if (
            !isset($_SESSION['tenants'])
            || empty($_SESSION['tenants'])
            || !is_array($_SESSION['tenants'])
            || count($_SESSION['tenants']) < 1
        ) {
            return $response;
        }
        $tenants = [];
        try {
            foreach ($_SESSION['tenants'] as $tenant) {
                $accessToken = $this->client_credentials();

                $client = new Client();

                $response = $client->request('GET', $this->tokenConsultaUrl . '?IdGrupoEmpresarial=' . $tenant, [
                    'headers' => [
                        'Authorization' => "Bearer {$accessToken->getToken()}", // Adiciona o token de acesso no cabeçalho
                        'Accept'        => 'text/plain', // Indica que você aceita uma resposta em texto
                    ],
                ]);

                // Obtém o corpo da resposta e o decodifica
                $data = json_decode($response->getBody()->getContents(), true);

                if (json_last_error() === JSON_ERROR_NONE) {
                    // Percorre os itens e imprime o CNPJ
                    if (isset($data['totalCount']) && $data['totalCount'] > 0 && isset($data['items'])) {
                        foreach ($data['items'] as $item) {
                            $tenants[$item['cnpj']] = $item;
                        }
                    }
                } else {
                    throw new Exception('Erro ao consultar o tenant. Erro na decodificação JSON: ' . json_last_error_msg());
                }
            }
            if (count($tenants) > 0) {
                return [
                    'status' => 'success',
                    'data' => $tenants
                ];
            }
        } catch (Exception $e) {
            $response['message'] = $e->getMessage();
        }

        return $response;
    }
}

function _arrayFilters($value)
{
    return !is_null($value) && $value !== '';
}
