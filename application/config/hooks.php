<?php  if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
| -------------------------------------------------------------------------
| Hooks
| -------------------------------------------------------------------------
| This file lets you define "hooks" to extend CI without hacking the core
| files.  Please see the user guide for info:
|
|	http://codeigniter.com/user_guide/general/hooks.html
|
*/

$hook['pre_system'] = array(
    'class'    => 'Db_check_hook',
    'function' => 'check_db_connection',
    'filename' => 'db_check.php',
    'filepath' => 'hooks',
    'params'   => array()
);

$hook['post_controller_constructor'][] = array(
    'class'    => 'User_status_check_hook',
    'function' => 'check_user_status',
    'filename' => 'user_status_check.php',
    'filepath' => 'hooks',
    'params'   => array()
);

// $hook['post_controller_constructor'][] = array(
//     'class'    => 'MaintenanceMode',
//     'function' => 'check_maintenance',
//     'filename' => 'MaintenanceMode.php',
//     'filepath' => 'hooks'
// );


/* End of file hooks.php */
/* Location: ./application/config/hooks.php */