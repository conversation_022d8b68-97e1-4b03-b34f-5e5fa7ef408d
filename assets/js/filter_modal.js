// JS genérico para modal de filtros com selects AJAX
$(function () {
  $(document).on("show.bs.select", "select[data-ajax-url]", function () {
    const $select = $(this);
    if ($select.data("loaded")) return;
    const url = $select.data("ajax-url");
    $select
      .empty()
      .append("<option>Carregando...</option>")
      .selectpicker("refresh");
    $.get(url, function (response) {
      $select.empty();
      let hasOptions = false;
      // Caso seja array simples de opções
      if (Array.isArray(response)) {
        response.forEach((opt) => {
          const value = opt.value || opt.codigo;
          const text = opt.text || opt.label || opt.descricao;
          if (opt.divider) {
            $select.append('<option data-divider="true"></option>');
          } else {
            $select.append(
              '<option value="' + value + '">' + text + "</option>"
            );
          }
          hasOptions = true;
        });
      } else if (response && response.data) {
        // Caso seja objeto com data.options/data.optgroups
        const data = response.data;
        if (Array.isArray(data.options)) {
          data.options.forEach((opt) => {
            if (opt.divider) {
              $select.append('<option data-divider="true"></option>');
            } else {
              $select.append(
                '<option value="' + opt.value + '">' + opt.text + "</option>"
              );
            }
            hasOptions = true;
          });
        }
        if (Array.isArray(data.optgroups)) {
          data.optgroups.forEach((group) => {
            const $optgroup = $(
              '<optgroup label="' + group.label + '"></optgroup>'
            );
            group.options.forEach((opt) => {
              const subtext = opt.subtext
                ? ' data-subtext="' + opt.subtext + '"'
                : "";
              $optgroup.append(
                '<option value="' +
                  opt.value +
                  '"' +
                  subtext +
                  ">" +
                  opt.text +
                  "</option>"
              );
              hasOptions = true;
            });
            $select.append($optgroup);
          });
        }
      } else if (response && Array.isArray(response.options)) {
        // Caso seja objeto com options/optgroups direto
        response.options.forEach((opt) => {
          if (opt.divider) {
            $select.append('<option data-divider="true"></option>');
          } else {
            $select.append(
              '<option value="' + opt.value + '">' + opt.text + "</option>"
            );
          }
          hasOptions = true;
        });
        if (Array.isArray(response.optgroups)) {
          response.optgroups.forEach((group) => {
            const $optgroup = $(
              '<optgroup label="' + group.label + '"></optgroup>'
            );
            group.options.forEach((opt) => {
              const subtext = opt.subtext
                ? ' data-subtext="' + opt.subtext + '"'
                : "";
              $optgroup.append(
                '<option value="' +
                  opt.value +
                  '"' +
                  subtext +
                  ">" +
                  opt.text +
                  "</option>"
              );
              hasOptions = true;
            });
            $select.append($optgroup);
          });
        }
      }
      // Caso não tenha opções
      if (!hasOptions) {
        $select.append('<option value="">Nenhuma opção encontrada</option>');
      }
      $select.data("loaded", true).selectpicker("refresh");
    }).fail(function () {
      $select
        .empty()
        .append('<option value="">Erro ao carregar</option>')
        .selectpicker("refresh");
    });
  });

  // Reset do carregamento ao abrir o modal
  $("#filterModal").on("show.bs.modal", function () {
    $("select[data-ajax-url]").data("loaded", false);
  });
});
