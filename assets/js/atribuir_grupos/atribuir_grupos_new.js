/**
 * Extensões e adaptações do atribuir_grupos.js para a nova interface (index_new)
 *
 * Este arquivo complementa o atribuir_grupos.js original, adicionando:
 * - Compatibilidade com a nova estrutura HTML
 * - Funcionalidades específicas da nova interface
 * - Adaptações para trabalhar com dados server-side renderizados
 *
 * Dependências:
 * - jQuery
 * - SweetAlert
 * - atribuir_grupos.js (deve ser carregado antes)
 */

const AtribuirGruposNew = {
  // Configurações
  config: {
    base_url: "",
    id_usuario_logado: null,
    usuario_desbloqueador: 0,
  },

  /**
   * Inicialização do módulo
   * @param {string} base_url - URL base da aplicação
   * @param {number} id_usuario_logado - ID do usuário logado
   * @param {number} usuario_desbloqueador - Flag se usuário pode desbloquear (0 ou 1)
   */
  init: function (base_url, id_usuario_logado, usuario_desbloqueador) {
    this.config.base_url = base_url;
    this.config.id_usuario_logado = id_usuario_logado;
    this.config.usuario_desbloqueador = usuario_desbloqueador;

    // Aguardar um pouco para garantir que todos os scripts foram carregados
    let self = this;
    setTimeout(function () {
      self.initEventHandlers();
      self.initTooltips();
      self.populateItensFound();

      // Inicializar o módulo original se ainda não foi
      if (
        typeof atribuir_grupos !== "undefined" &&
        typeof atribuir_grupos.init === "function"
      ) {
        atribuir_grupos.init(base_url);
      }
    }, 100);
  },

  /**
   * Inicializar event handlers específicos da nova interface
   */
  initEventHandlers: function () {
    let self = this;

    // Comportamento de clique nas linhas da tabela
    $(document).on("click", ".click-select", function (e) {
      if (e.target.nodeName != "INPUT") {
        $(this).find("input").click();
      }
    });

    // Lógica para checkboxes bloqueados
    $(document).on("click", ".item_selected:checked", function (e) {
      self.handleCheckboxSelection(this);
    });

    // Remover travamento quando desmarca checkbox
    $(document).on("click", ".item_selected:not(:checked)", function (e) {
      self.handleCheckboxDeselection(this);
    });

    // Modal de alteração de pré-agrupamento
    $("#alterar_pre_agrupamento").click(function (e) {
      e.preventDefault();
      self.openModalChangeTag();
    });

    // Sallet alteração de tag
    $("#send_change_tag").click(function (e) {
      e.preventDefault();
      if (
        typeof atribuir_grupos !== "undefined" &&
        typeof atribuir_grupos.save_tag === "function"
      ) {
        atribuir_grupos.save_tag();
      }
    });

    // Funcionalidade de desbloqueio
    $("#desbloquear_item").click(function (e) {
      e.preventDefault();
      self.handleDesbloquearItem();
    });

    // Funcionalidade de desvincular grupo
    $("#desvincularGrupo").on("click", function (e) {
      e.preventDefault();
      self.handleDesvincularGrupo();
    });

    // Desabilitar event handlers originais para evitar conflitos
    $("body").off("click", ".part_number_inf");
    $("body").off("click", ".part_number_click");

    // Event handlers para cliques nos part numbers (substituindo os originais)
    $(document).on("click", ".part_number_click", function (e) {
      e.preventDefault();
      e.stopPropagation();
      self.handlePartNumberClick(this, true); // true = integração Simplus
    });

    $(document).on("click", ".part_number_inf", function (e) {
      e.preventDefault();
      e.stopPropagation();
      self.handlePartNumberClick(this, false); // false = sem integração Simplus
    });

    // Funcionalidade de transferir owner
    $("#btn-transferencia-owner").click(function (e) {
      e.preventDefault();
      let checked_itens_to_owners = $(
        'input[type="checkbox"][name="item[]"]:checked'
      );
      if (checked_itens_to_owners.length == 0) {
        swal(
          "Atenção",
          "Selecione no mínimo um item para transferir",
          "warning"
        );
        return false;
      }
      self.handleTransferirOwner();
    });

    // Funcionalidade de transferir responsável
    $("#btn-transferir").click(function (e) {
      e.preventDefault();
      let checked_itens_to_owners = $(
        'input[type="checkbox"][name="item[]"]:checked'
      );
      if (checked_itens_to_owners.length == 0) {
        swal(
          "Atenção",
          "Selecione no mínimo um item para transferir",
          "warning"
        );
        return false;
      }
      self.handleTransferirResponsavel();
    });

    // Chamada para handler para salvar transferência de responsável
    $("#form_transfer_submit").click(function (e) {
      e.preventDefault();
      self.handleTransferirResponsavelSubmit();
    });

    // Botão Salvar no modal de motivo
    $(document).on('click', '#save_association', function(e) {
        e.preventDefault();
        if (typeof atribuir_grupos !== 'undefined' && typeof atribuir_grupos.save === 'function') {
            atribuir_grupos.save();
        } else {
            console.error('atribuir_grupos.save() is not defined.');
            swal('Erro', 'A função para salvar não foi encontrada. Contacte o suporte.', 'error');
        }
    });

    // Botão Fechar no modal de motivo
    $(document).on('click', '.desbloqueia_item', function(e) {
        e.preventDefault();
        self.handleDesbloquearTodosItens();
    });
  },

  /**
   * Inicializar tooltips
   */
  initTooltips: function () {
    $('[data-toggle="tooltip"]').tooltip();
  },

  /**
   * Popular itens_found para compatibilidade com métodos existentes
   */
  populateItensFound: function () {
    if (typeof atribuir_grupos === "undefined") {
      window.atribuir_grupos = {};
    }

    if (typeof atribuir_grupos.itens_found === "undefined") {
      atribuir_grupos.itens_found = {};
    }

    $(".item_selected").each(function () {
      let partNumber = $(this).val();
      if (partNumber && !atribuir_grupos.itens_found[partNumber]) {
        atribuir_grupos.itens_found[partNumber] = {
          part_number: $(this).data("part-number") || partNumber,
          estabelecimento: $(this).data("estabelecimento") || "",
          descricao: $(this).data("descricao") || "",
          tag: $(this).data("tag") || "",
          ncm: $(this).data("ncm_item") || "",
          usuario_bloqueador: $(this).data("bloqueador") || null,
          nome_usuario_bloqueador:
            $(this).closest("tr").find(".glyphicon-lock").attr("title") || "",
        };
      }
    });
  },

  /**
   * Manipular seleção de checkbox
   * @param {HTMLElement} checkbox - Elemento checkbox selecionado
   */
  handleCheckboxSelection: function (checkbox) {
    let $checkbox = $(checkbox);
    let bloqueador = $checkbox.data("bloqueador");

    if (
      bloqueador != "" &&
      bloqueador != null &&
      bloqueador != this.config.id_usuario_logado
    ) {
      $checkbox.addClass("travado");

      // Trava os demais botões para permitir somente o desbloqueio
      $(".btn-success").attr("disabled", "disabled");
      $(".btn-primary").attr("disabled", "disabled");
      $("#desvincularGrupo").attr("disabled", "disabled");
    }
  },

  /**
   * Manipular desseleção de checkbox
   * @param {HTMLElement} checkbox - Elemento checkbox desmarcado
   */
  handleCheckboxDeselection: function (checkbox) {
    let $checkbox = $(checkbox);
    $checkbox.removeClass("travado");

    // Verificar se ainda há itens travados
    if ($(".item_selected.travado").length === 0) {
      $(".btn-success").removeAttr("disabled");
      $(".btn-primary").removeAttr("disabled");
      $("#desvincularGrupo").removeAttr("disabled");
    }
  },

  /**
   * Abrir modal de alteração de tag
   */
  openModalChangeTag: function () {
    // Garantir que os dados estão atualizados antes de abrir o modal
    this.populateItensFound();

    // Chamar método original se disponível
    if (
      typeof atribuir_grupos !== "undefined" &&
      typeof atribuir_grupos.open_modal_change_tag === "function"
    ) {
      atribuir_grupos.open_modal_change_tag();
    }
  },

  /**
   * Manipular desbloqueio de todos os itens quando usuário fecha modal de Atribuição de Grupo
   * @returns {Promise}
   */
  handleDesbloquearTodosItens: function () {
    return $.ajax({
        url: base_url + "pr/perguntas/unsetUsuarioBloqueadorItens",
        method: 'POST'
    });
  },

  /**
   * Manipular desbloqueio de itens
   */
  handleDesbloquearItem: function () {
    let itens_checked = [];
    let itens = $(".item_selected:checked").length;
    let checked_itens = $(".item_selected:checked");

    $(checked_itens).each(function () {
      let item = {
        part_number: $(this).val(),
        estabelecimento: $(this).attr("data-estabelecimento"),
      };
      itens_checked.push(item);
    });

    if (!itens) {
      swal("Atenção!", "Nenhum item foi selecionado!", "warning");
      return;
    }

    swal({
      title: "Atenção!",
      text: "Você deseja realmente Desbloquear este(s) item(s)?",
      type: "warning",
      confirmButtonText: "OK",
      cancelButtonText: "Cancelar",
      showConfirmButton: true,
      showCancelButton: true,
      allowOutsideClick: false,
    }).then(
      function () {
        // Ajax de Desbloqueio dos items
        $.ajax({
          url:
            AtribuirGruposNew.config.base_url +
            "atribuir_grupo/ajax_desbloqueia_item",
          data: {
            itens_checked: itens_checked,
          },
          async: false,
          method: "POST",
          success: function (data) {
            // Recarregar a página para atualizar os dados
            location.reload();
          },
          error: function (xhr, status, error) {
            console.error("Erro ao desbloquear itens:", error);
            swal("Erro!", "Ocorreu um erro ao desbloquear os itens.", "error");
          },
        });
      },
      function (dismiss) {
        // Cancelado - não fazer nada
      }
    );
  },

  /**
   * Manipular clique no part number
   * @param {HTMLElement} element - Elemento clicado
   * @param {boolean} isSimplus - Se é integração Simplus ou não
   */
  handlePartNumberClick: function (element, isSimplus) {
    let $element = $(element);
    let partNumber = $element.data("part-number");
    let estabelecimento = isSimplus
      ? $element.data("estabelecimento-simplus")
      : $element.data("estabelecimento");

    if (!partNumber) {
      console.error("Part number não encontrado no elemento clicado");
      return;
    }

    // Implementar a lógica baseada no código original, mas com melhor tratamento
    if (isSimplus) {
      // Lógica para integração Simplus
      $("#detalhes-simplus").modal();
      let post_action = {
        part_number: partNumber,
        estabelecimento: estabelecimento,
      };

      $.ajax({
        url:
          this.config.base_url + "atribuir_grupo/ajax_get_item_by_pn_company",
        method: "POST",
        data: post_action,
        dataType: "html",
        beforeSend: function () {
          $("#detalhes-simplus").html(
            '<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Carregando...</div>'
          );
        },
        success: function (data) {
          try {
            // Usar uma abordagem mais segura para inserir o conteúdo
            let tempDiv = document.createElement("div");
            tempDiv.innerHTML = data;

            // Remover todos os scripts para evitar conflitos
            let scripts = tempDiv.querySelectorAll("script");
            for (let i = 0; i < scripts.length; i++) {
              scripts[i].remove();
            }

            $("#detalhes-simplus").html(tempDiv.innerHTML);
          } catch (e) {
            console.error("Erro ao processar conteúdo Simplus:", e);
            $("#detalhes-simplus").html(
              '<div class="alert alert-danger">Erro ao processar conteúdo do item.</div>'
            );
          }
        },
        error: function (xhr, status, error) {
          console.error("Erro ao carregar detalhes Simplus:", error);
          $("#detalhes-simplus").html(
            '<div class="alert alert-danger">Erro ao carregar detalhes do item.</div>'
          );
        },
      });
    } else {
      // Lógica para integração padrão
      $("#detalhes-part-number").modal();
      let post_action = {
        part_number: partNumber,
        estabelecimento: estabelecimento,
      };

      $.ajax({
        url: this.config.base_url + "atribuir_grupo/ajax_get_item_by_pn",
        method: "POST",
        data: post_action,
        dataType: "html",
        beforeSend: function () {
          $("#detalhes-part-number").html(
            '<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Carregando...</div>'
          );
        },
        success: function (data) {
          try {
            // Usar uma abordagem mais segura para inserir o conteúdo
            let tempDiv = document.createElement("div");
            tempDiv.innerHTML = data;

            // Remover todos os scripts para evitar conflitos
            // Não usar esta regra por enquanto. Pode causar problemas com o modal e os componentes dentro dele
            // var scripts = tempDiv.querySelectorAll("script");
            // for (var i = 0; i < scripts.length; i++) {
            //   scripts[i].remove();
            // }

            $("#detalhes-part-number").html(tempDiv.innerHTML);
          } catch (e) {
            console.error("Erro ao processar conteúdo do part number:", e);
            $("#detalhes-part-number").html(
              '<div class="alert alert-danger">Erro ao processar conteúdo do item.</div>'
            );
          }
        },
        error: function (xhr, status, error) {
          console.error("Erro ao carregar detalhes do part number:", error);
          $("#detalhes-part-number").html(
            '<div class="alert alert-danger">Erro ao carregar detalhes do item.</div>'
          );
        },
      });
    }
  },

  /**
   * Manipular desvincular grupo
   */
  handleDesvincularGrupo: function () {
    let checked_items = [];
    $('input[type="checkbox"][name="item[]"]:checked').each(function () {
      let inputElement = $(this);
      let part_number = inputElement.attr("data-part-number");
      if (part_number) {
        checked_items.push(part_number);
      }
    });

    if (checked_items.length === 0) {
      swal("Atenção!", "Nenhum item foi selecionado!", "warning");
      return;
    }

    // Cria a query string com os atributos part_number
    let queryString = checked_items
      .map(function (item) {
        return "part_number[]=" + encodeURIComponent(item);
      })
      .join("&");

    // Concatena a query string na URL da rota
    let baseUrl = this.config.base_url + "atribuir_grupo/deletar";
    let exportUrl = baseUrl + "?" + queryString;

    // Redireciona para a rota com a query string
    window.location.href = exportUrl;
  },

  /**
   * Transferir owner
   */
  handleTransferirOwner: function () {
    $("#message_user_transfer_owner").empty();
    let checked_itens_to_owners = $(
      'input[type="checkbox"][name="item[]"]:checked'
    );

    if (checked_itens_to_owners.length == 0) {
      // Avisa e fecha o modal
      $("#modal-transferencia-owner").modal("hide");
      swal("Atenção", "Selecione no mínimo um item para transferir", "warning");
      return false;
    }

    $("#total_itens_owners").text(checked_itens_to_owners.length);
  },

  /**
   * Transferir responsável
   */
  handleTransferirResponsavel: function () {
    $("#message_user_transfer").empty();
    let checked_itens_to_owners = $(
      'input[type="checkbox"][name="item[]"]:checked'
    );

    if (checked_itens_to_owners.length == 0) {
      // Avisa e fecha o modal
      $("#transferencia-modal").modal("hide");
      swal("Atenção", "Selecione no mínimo um item para transferir", "warning");
      return false;
    }

    $("#total_itens").text(checked_itens_to_owners.length);
  },

  /**
   * Salvar transferência de responsável
   */
  handleTransferirResponsavelSubmit: function () {
    let btn = $("#form_transfer_submit").button("loading");

    let itens = [];
    let checked_itens = $('input[type="checkbox"][name="item[]"]:checked');

    $(checked_itens).each(function () {
      let item = {
        part_number: $(this).val(),
        estabelecimento: $(this).attr("data-estabelecimento"),
      };

      itens.push(item);
    });

    let tipo_responsavel = $(
      "#form_transferir_usuario #tipo_responsavel"
    ).val();
    let id_usuario = $("#form_transferir_usuario #id_usuario").val();
    let motivo = $("#form_transferir_usuario #motivo").val();

    let post_data = {
      itens: itens,
      id_usuario: id_usuario,
      motivo: motivo,
      tipo_responsavel: tipo_responsavel,
    };

    const _this = this;

    $.post(
      this.config.base_url + "atribuir_grupo/transferir_usuario",
      post_data,
      function (response) {
        let responseAjax = JSON.parse(response);

        if (responseAjax.error) {
          $("#message_user_transfer").append(responseAjax.error);
          btn.button("reset");
        } else if (responseAjax.success) {
          // _this.refresh_users_filter();
          btn.button("reset");
          $("#transferencia-modal").modal("hide");

          swal({
            title: "Sucesso",
            text: "Transferência de responsável concluída com sucesso.",
            type: "success",
          }).then(function () {
            location.reload();
          });
        }
      }
    );
  },

  /**
   * Utilitários
   */
  utils: {
    /**
     * Codificar HTML para evitar XSS
     * @param {string} str - String para codificar
     * @returns {string} String codificada
     */
    encodeHTML: function (str) {
      if (!str) return "";
      return str
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#39;");
    },

    /**
     * Limitar texto com reticências
     * @param {string} texto - Texto para limitar
     * @param {number} limite - Limite de caracteres
     * @returns {string} Texto limitado
     */
    limitarTexto: function (texto, limite) {
      if (texto && texto.length > limite) {
        return texto.substring(0, limite) + "(...)";
      }
      return texto || "";
    },
  },
};

// Compatibilidade global
window.AtribuirGruposNew = AtribuirGruposNew;

// --- START: Modal Atribuir Grupo Tarifario Logic --- //

// Adicionando o handler do modal ao init principal
const originalAtribuirGruposNewInit = AtribuirGruposNew.init;
AtribuirGruposNew.init = function(...args) {
    originalAtribuirGruposNewInit.apply(this, args);
    
    // Botão principal que abre o modal
    $(document).on("click", "#btn-abrir-modal-atribuir", (e) => {
        e.preventDefault();
        this.openAtribuirGrupoTarifarioModal();
    });

    // Botão de busca de grupos DENTRO do modal
    $(document).on("submit", "#search_grupos_tarifarios_modal", (e) => {
        e.preventDefault();
        this.get_grupos_modal(true);
    });

    // Botão final de atribuição DENTRO do modal (que abre o #modal_motivo)
    $(document).on("click", "#btn-confirmar-atribuicao-grupo-modal", (e) => {
        e.preventDefault();
        const checked_grupo = $('#accordion_grupos_modal input[name="grupo[]"]:checked, #diana-modal input[name="grupo[]"]:checked');
        if (checked_grupo.length === 0) {
            swal("Atenção!", "Selecione um grupo tarifário para associar.", "warning");
            return;
        }

        // fechar o modal de atribuição de grupo tarifário e abrir o de motivo
        $('#modal-atribuir-grupo-tarifario').modal('hide');
        $('#modal_motivo').modal('show');
    });
    
    // Adapta o evento 'show' do modal de motivo para ler os dados do nosso novo modal
    $('#modal_motivo').on('show.bs.modal', (e) => {
        const id_grupo = $("#accordion_grupos_modal input[name='grupo[]']:checked, #diana-modal input[name='grupo[]']:checked").val();
        if (!id_grupo) {
            // Se o grupo não foi selecionado no novo modal, tenta pegar do #accordion_grupos antigo, por segurança
            const id_grupo_antigo = $("#accordion_grupos input[name='grupo[]']:checked").val();
            if(!id_grupo_antigo) return;
        } else {
            const tr = $("#accordion_grupos_modal").find("#tr-grupo-" + id_grupo);
            const memoria = tr.data('memoria-classificacao');
            const subsidio = tr.data('subsidio');
            const caracteristica = tr.data('caracteristica');

            $('#modal_motivo #memoria_classificacao').val(memoria || '');
            $('#modal_motivo #subsidio').val(subsidio || '');
            $('#modal_motivo #caracteristica').val(caracteristica || '');
        }

        // Lógica para preencher dados do item (quando apenas 1 está selecionado)
        let values = $('.item_selected:checked').map(function() {
            return $(this).val();
        }).get();

        $('#modal_motivo #marca, #modal_motivo #funcao, #modal_motivo #aplicacao, #modal_motivo #material_constitutivo').val('');

        if (values.length == 1) {
            const estabelecimento = $('.item_selected:checked').data('estabelecimento');
            const data = {
                'part_number': values.shift(),
                'estabelecimento': estabelecimento
            };

            $.post(AtribuirGruposNew.config.base_url + 'atribuir_grupo/ajax_get_item', data, function(response) {
                const itemData = response;
                if(itemData && itemData.item) {
                    const item = itemData.item;
                    $('#modal_motivo #marca').val(item.marca);
                    $('#modal_motivo #funcao').val(item.funcao);
                    $('#modal_motivo #inf_adicionais').val(item.inf_adicionais);
                    $('#modal_motivo #material_constitutivo').val(item.material_constitutivo);
                    $('#modal_motivo #aplicacao').val(item.aplicacao);
                    $('#modal_motivo #descricao_proposta_completa').val(item.descricao_proposta_completa);
                    $('#modal_motivo #observacoes_mestre').val(item.observacoes);
                    $('#modal_motivo #evento').val(item.evento);
                    $('#modal_motivo #descricao').val(item.descricao);
                    $('#modal_motivo #memoria_classificacao').val(item.memoria_classificacao);
                }
            });
        }
    });
};

AtribuirGruposNew.openAtribuirGrupoTarifarioModal = function() {
    const checked_itens = $('.item_selected:checked');
    if (checked_itens.length === 0) {
        swal("Atenção!", "Selecione pelo menos um item para atribuir um grupo.", "warning");
        return;
    }

    const modal = $('#modal-atribuir-grupo-tarifario');
    modal.modal('show');
    
    modal.one('shown.bs.modal', () => { // Use .one() para evitar re-binding
        $('#modal-atribuir-loading').show();
        $('#modal-atribuir-content').hide();
        
        $('#search_grupos_tarifarios_modal input[name="grupo_input"]').val('');
        $('#accordion_grupos_modal').empty();
        $('#grupos_ocultos_holder_modal').empty();

        // Ativa a aba Diana se ela existir
        if ($('#tab-diana-modal').length > 0) {
            $('#tab-diana-modal').tab('show');
        }

        setTimeout(() => {
            this.get_grupos_modal(true);
            this.refresh_hidden_modal();
            
            $('#modal-atribuir-loading').hide();
            $('#modal-atribuir-content').show();

            if ($('#filter_selected_modal').data('selectpicker') == null) {
                $('#filter_selected_modal').selectpicker();
            }
        }, 300);
    });
};

AtribuirGruposNew.get_grupos_modal = function(new_request) {
    const _this = this;
    let tag = null;

    if (!$("#disable_filter_by_tag_modal:checked").val()) {
        tag = atribuir_grupos.selected_tags.length > 0 ? atribuir_grupos.selected_tags : atribuir_grupos.tag;
    }

    const post_data = {
        grupo_input: $('#search_grupos_tarifarios_modal input[name="grupo_input"]').val(),
        tag: tag,
        order: { order: 'ncm_recomendada', by: 'asc' },
        group_by: 'caracteristica'
    };

    $("#accordion_grupos_modal").empty();

    const btn = $("#send_search_grupos_tarifarios_modal").button("loading");
    $("#response-ajax-modal").show();

    $.post(`${this.config.base_url}atribuir_grupo/ajax_get_grupos_tarifarios`, post_data, function (data) {
        const result = data
        if (result.grupos == null) {
            $("#accordion_grupos_modal").html('<h5 class="text-center">Nenhum grupo encontrado.</h5>');
        } else {
            $.each(result.grupos, function (i, grupo_data) {
                _this.create_group_structure_modal(grupo_data.caracteristica, i, grupo_data);
            });
        }
        btn.button("reset");
        $("#response-ajax-modal").hide();
    });
};

AtribuirGruposNew.create_group_structure_modal = function(caracteristica, idx, data) {
    const _this = this;
    const accordion = $("#accordion_grupos_modal");
    const panelId = `collapse-modal-${idx}`;
    const panel = `
        <div class="panel panel-default">
            <div class="panel-heading" role="tab">
                <h4 class="panel-title">
                    <a role="button" data-toggle="collapse" data-parent="#accordion_grupos_modal" href="#${panelId}" aria-expanded="false" class="collapsed">
                        ${caracteristica}
                    </a>
                </h4>
            </div>
            <div id="${panelId}" data-caracteristica="${caracteristica}" class="panel-collapse collapse" role="tabpanel">
                <div class="panel-body">Carregando...</div>
            </div>
        </div>`;
    accordion.append(panel);

    // Adiciona o listener para o evento de "mostrar" o painel (lazy loading)
    $(`#${panelId}`).on('show.bs.collapse', function () {
        // Apenas carrega se o conteúdo for o placeholder "Carregando..."
        if ($(this).find('table').length === 0) {
             _this.fill_groups_modal(caracteristica);
        }
    });

    // Listener para atualizar o NCM oculto quando um grupo é selecionado no modal
    $('#modal-atribuir-content').on('click', 'input[type="radio"][name="grupo[]"]', function() {
        const radio = $(this);
        const tr = radio.closest('tr');
        let ncmRecomendada = '';

        // Tenta pegar da aba "Atribuir"
        let ncmLinkAtribuir = tr.find('.display-ncm-table');
        if (ncmLinkAtribuir.length > 0) {
            ncmRecomendada = ncmLinkAtribuir.data('ncm-recomendada');
        }

        // Tenta pegar da aba "Diana" (Vue component)
        if (!ncmRecomendada) {
            let ncmLinkDiana = tr.find('.ncm_info');
            if (ncmLinkDiana.length > 0) {
                ncmRecomendada = ncmLinkDiana.attr('data-attr-ncm');
            }
        }
        
        // Atualiza o campo hidden que é usado pelas chamadas AJAX
        $("#suframa-produto-ncm").val(ncmRecomendada);
    });
};

AtribuirGruposNew.fill_groups_modal = function(caracteristica) {
    const _this = this;
    let tag = null;
    if (!$("#disable_filter_by_tag_modal:checked").val()) {
        tag = atribuir_grupos.selected_tags.length > 0 ? atribuir_grupos.selected_tags : atribuir_grupos.tag;
    }
    const post_data = {
        grupo_input: $('#search_grupos_tarifarios_modal input[name="grupo_input"]').val(),
        tag: tag,
        order: { order: 'ncm_recomendada', by: 'asc' },
        caracteristica: caracteristica
    };

    const panel_body = $(`#accordion_grupos_modal .panel-collapse[data-caracteristica="${caracteristica}"] .panel-body`);

    $.post(`${this.config.base_url}atribuir_grupo/ajax_get_grupos_tarifarios`, post_data, function (data) {
        const result = data;
        panel_body.empty();
        const table = $('<table class="table table-striped table-hover"><tbody></tbody></table>');
        panel_body.append(table);
        const table_body = table.find('tbody');

        if (result.grupos == null) {
            table_body.html('<tr><td colspan="2" class="text-center">Nenhum grupo para esta característica.</td></tr>');
        } else {
            $.each(result.grupos, function (i, grupo) {
                const ncm_html = grupo.ncm_recomendada
                    ? `<a class="display-ncm-table" href="javascript:void(0);" data-ncm-recomendada="${grupo.ncm_recomendada}">
                         <i class="glyphicon glyphicon-info-sign"></i> ${grupo.ncm_recomendada}
                       </a>`
                    : 'N/A';

                const row = `
                    <tr id="tr-grupo-${grupo.id_grupo_tarifario}" 
                        data-caracteristica="${grupo.caracteristica}" 
                        data-subsidio="${grupo.subsidio}" 
                        data-memoria-classificacao="${grupo.memoria_classificacao}">
                        <td width="5%"><input type="radio" id="grupo_selected" name="grupo[]" value="${grupo.id_grupo_tarifario}"></td>
                        <td>
                            ${grupo.descricao}
                            <div class="info-grupo"><small><strong>NCM Recomendada:</strong> ${ncm_html}</small></div>
                        </td>
                    </tr>`;
                table_body.append(row);
            });
        }
    });
};

AtribuirGruposNew.refresh_hidden_modal = function() {
    // Implementação simplificada para grupos ocultos
    console.log("Refreshing hidden groups for modal...");
};
