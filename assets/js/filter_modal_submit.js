// JS para submissão do modal de filtros e atualização da listagem
$(function () {
  // Submissão dos filtros ao clicar no botão Pesquisar
  $("#btn-pesquisar").on("click", function (e) {
    e.preventDefault();
    var $form = $("#filterForm");
    var formData = $form.serializeArray();
    var data = {};

    // Adicionar filtros salvos ao formData
    var savedFilters =
      typeof FilterModalIntegration !== "undefined" &&
      FilterModalIntegration.getSavedFilters
        ? FilterModalIntegration.getSavedFilters()
        : null;
    if (savedFilters) {
      Object.keys(savedFilters).forEach(function (key) {
        if (
          savedFilters[key] !== null &&
          savedFilters[key] !== undefined &&
          savedFilters[key] !== ""
        ) {
          if (Array.isArray(savedFilters[key])) {
            savedFilters[key].forEach(function (value) {
              formData.push({ name: key, value: value });
            });
          } else {
            formData.push({ name: key, value: savedFilters[key] });
          }
        }
      });
    }

    formData.forEach(function (item) {
      if (data[item.name]) {
        if (!Array.isArray(data[item.name]))
          data[item.name] = [data[item.name]];
        data[item.name].push(item.value);
      } else {
        data[item.name] = item.value;
      }
    });
    // Adiciona ordenação padrão (ajuste se necessário)
    data.order = { order: "part_number", by: "asc" };
    var endpoint = $form.data("endpoint");
    if (!endpoint) {
      alert("Endpoint do filtro não configurado.");
      return;
    }
    $.ajax({
      url: endpoint,
      type: "POST",
      data: data,
      dataType: "json",
      success: function (response) {
        atualizarTabelaItens(response.itens);
        if (response.sla_data) {
          atualizarSLA(response.sla_data);
        }
        if (typeof renderBadges === "function") {
          renderBadges();
        }
      },
      error: function (xhr) {
        alert("Erro ao pesquisar itens.");
      },
    });
  });
  $("#filterForm").on("submit", function (e) {
    e.preventDefault();
    var $form = $(this);
    var formData = $form.serializeArray();
    var data = {};

    // Adicionar filtros salvos ao formData
    var savedFilters =
      typeof FilterModalIntegration !== "undefined" &&
      FilterModalIntegration.getSavedFilters
        ? FilterModalIntegration.getSavedFilters()
        : null;
    if (savedFilters) {
      Object.keys(savedFilters).forEach(function (key) {
        if (
          savedFilters[key] !== null &&
          savedFilters[key] !== undefined &&
          savedFilters[key] !== ""
        ) {
          if (Array.isArray(savedFilters[key])) {
            savedFilters[key].forEach(function (value) {
              formData.push({ name: key, value: value });
            });
          } else {
            formData.push({ name: key, value: savedFilters[key] });
          }
        }
      });
    }

    formData.forEach(function (item) {
      // Agrupa múltiplos valores em array
      if (data[item.name]) {
        if (!Array.isArray(data[item.name]))
          data[item.name] = [data[item.name]];
        data[item.name].push(item.value);
      } else {
        data[item.name] = item.value;
      }
    });
    // Adiciona ordenação padrão (pode ser ajustado conforme necessário)
    data.order = { order: "part_number", by: "asc" };
    var endpoint = $form.data("endpoint");
    if (!endpoint) {
      alert("Endpoint do filtro não configurado.");
      return;
    }
    $.ajax({
      url: endpoint,
      type: "POST",
      data: data,
      dataType: "json",
      success: function (response) {
        // Atualiza a tabela de resultados
        atualizarTabelaItens(response.itens);
        // Atualiza dados de SLA se necessário
        if (response.sla_data) {
          atualizarSLA(response.sla_data);
        }
        // Fecha o modal
        $("#filterModal").modal("hide");
        // Atualiza badges dos filtros selecionados
        if (typeof renderBadges === "function") {
          renderBadges();
        }
      },
      error: function (xhr) {
        alert("Erro ao filtrar itens.");
      },
    });
  });

  // Função para atualizar a tabela de itens (exemplo, adapte conforme sua view)
  function atualizarTabelaItens(itens) {
    var $tbody = $("table.table tbody");
    $tbody.empty();
    if (!itens || itens.length === 0) {
      $tbody.append('<tr><td colspan="9">Nenhum item encontrado.</td></tr>');
      return;
    }
    itens.forEach(function (item) {
      $tbody.append(
        "<tr>" +
          '<td><input type="checkbox" /></td>' +
          "<td>" +
          (item.part_number || "") +
          "</td>" +
          "<td>" +
          (item.owner || "") +
          "</td>" +
          "<td>" +
          (item.estabelecimento || "") +
          "</td>" +
          "<td>" +
          (item.peso || "") +
          "</td>" +
          "<td>" +
          (item.prioridade || "") +
          "</td>" +
          "<td>" +
          (item.sla_hrs_restantes || "") +
          "</td>" +
          "<td>" +
          (item.farol || "") +
          "</td>" +
          "<td>" +
          (item.descricao || "") +
          "</td>" +
          "</tr>"
      );
    });
  }

  // Função para atualizar dados de SLA (implemente conforme necessário)
  function atualizarSLA(sla_data) {
    // ...
  }
});
