/**
 * Funções auxiliares para campos do modal de filtros
 * Contém utilitários para manipulação de campos específicos
 */

var FilterModalFields = {
  init: function () {
    this.bindEvents();
  },

  bindEvents: function () {
    var self = this;

    // Evento para campos de data range
    $(document).on("change", '#filterModal input[type="date"]', function () {
      console.log(
        "Data field changed:",
        $(this).attr("id"),
        "value:",
        $(this).val()
      );
      self.validateDateRange($(this));
    });

    // Evento para checkboxes
    $(document).on(
      "change",
      '#filterModal input[type="checkbox"]',
      function () {
        self.handleCheckboxChange($(this));
      }
    );

    // Evento para selects múltiplos
    $(document).on(
      "changed.bs.select",
      "#filterModal select[multiple]",
      function () {
        self.handleMultiSelectChange($(this));
      }
    );
  },

  // Validar range de datas
  validateDateRange: function (dateInput) {
    const fieldId = dateInput.attr("id");
    const value = dateInput.val();

    console.log("Validating date range for:", fieldId, "value:", value);

    // Se for campo "from", verificar se é menor que "to"
    if (fieldId.includes("_from")) {
      const toFieldId = fieldId.replace("_from", "_to");
      const toValue = $(`#${toFieldId}`).val();

      console.log(
        "Checking from field:",
        fieldId,
        "against to field:",
        toFieldId,
        "toValue:",
        toValue
      );

      if (value && toValue && value > toValue) {
        alert("Data inicial não pode ser maior que a data final");
        dateInput.val("");
        return false;
      }
    }

    // Se for campo "to", verificar se é maior que "from"
    if (fieldId.includes("_to")) {
      const fromFieldId = fieldId.replace("_to", "_from");
      const fromValue = $(`#${fromFieldId}`).val();

      console.log(
        "Checking to field:",
        fieldId,
        "against from field:",
        fromFieldId,
        "fromValue:",
        fromValue
      );

      if (value && fromValue && value < fromValue) {
        alert("Data final não pode ser menor que a data inicial");
        dateInput.val("");
        return false;
      }
    }

    console.log("Date validation passed for:", fieldId);
    return true;
  },

  // Manipular mudança de checkbox
  handleCheckboxChange: function (checkbox) {
    const fieldName = checkbox.attr("name");
    const isChecked = checkbox.is(":checked");

    console.log(`Checkbox ${fieldName} alterado para: ${isChecked}`);

    // Aqui pode ser adicionada lógica específica para cada checkbox
    // Por exemplo, mostrar/ocultar campos relacionados
  },

  // Manipular mudança de select múltiplo
  handleMultiSelectChange: function (select) {
    const fieldName = select.attr("name");
    const selectedValues = select.val() || [];

    console.log(`Select múltiplo ${fieldName} alterado para:`, selectedValues);

    // Aqui pode ser adicionada lógica específica para selects múltiplos
    // Por exemplo, atualizar contadores ou badges
  },

  // Função para resetar todos os campos do modal
  resetAllFields: function () {
    // Resetar formulário
    $("#filterForm")[0].reset();

    // Resetar selectpickers
    $("#filterModal .selectpicker").selectpicker("refresh");

    // Resetar campos de data específicos se necessário
    $('#filterModal input[type="date"]').val("");

    console.log("Todos os campos do modal foram resetados");
  },

  // Função para obter valores atuais de todos os campos
  getAllFieldValues: function () {
    const formData = $("#filterForm").serializeArray();
    const values = {};

    formData.forEach(function (item) {
      if (values[item.name]) {
        if (!Array.isArray(values[item.name])) {
          values[item.name] = [values[item.name]];
        }
        values[item.name].push(item.value);
      } else {
        values[item.name] = item.value;
      }
    });

    // Adicionar valores de checkboxes não marcados
    $('#filterForm input[type="checkbox"]:not(:checked)').each(function () {
      const name = $(this).attr("name");
      if (!values[name]) {
        values[name] = "0";
      }
    });

    return values;
  },

  // Função para validar campos obrigatórios antes da submissão
  validateRequiredFields: function () {
    let isValid = true;
    const errors = [];

    // Verificar campos obrigatórios (se houver)
    $("#filterForm [required]").each(function () {
      const field = $(this);
      const value = field.val();

      if (!value || value.trim() === "") {
        isValid = false;
        errors.push(`Campo ${field.attr("name")} é obrigatório`);
        field.addClass("is-invalid");
      } else {
        field.removeClass("is-invalid");
      }
    });

    if (!isValid) {
      alert("Por favor, corrija os seguintes erros:\n" + errors.join("\n"));
    }

    return isValid;
  },
};

// Inicializar quando o documento estiver pronto
$(document).ready(function () {
  FilterModalFields.init();
});
