/**
 * Integração do modal de filtros com valores salvos no backend
 * Responsável por inicializar filtros salvos e manter estado
 */

var FilterModalIntegration = {
  savedFilters: {},

  init: function () {
    this.loadSavedFilters();
    this.bindEvents();
    this.fetchOptionsForActiveFilters(); // Força o carregamento de opções para filtros ativos
  },

  // Carrega filtros salvos do backend (através de variáveis PHP)
  loadSavedFilters: function () {
    // Valores injetados pelo PHP através do método get_state do model
    this.savedFilters = {
      pacotes_eventos:
        typeof window.eventosSelecionados !== "undefined"
          ? window.eventosSelecionados
          : [],
      sistema_origem:
        typeof window.sistemasOrigemSelecionados !== "undefined"
          ? window.sistemasOrigemSelecionados
          : [],
      owner:
        typeof window.ownersSelecionados !== "undefined"
          ? window.ownersSelecionados
          : [],
      prioridade:
        typeof window.prioridadesSelecionadas !== "undefined"
          ? window.prioridadesSelecionadas
          : [],
      atribuido_para:
        typeof window.usuariosSelecionados !== "undefined"
          ? window.usuariosSelecionados
          : "",
      status_classificacao_fiscal:
        typeof window.statusSelecionados !== "undefined"
          ? window.statusSelecionados
          : [],
      triagem_diana_falha:
        typeof window.triagemDianaFalhaSelecionado !== "undefined"
          ? window.triagemDianaFalhaSelecionado
          : null,
      novo_material:
        typeof window.novoMaterialSelecionado !== "undefined"
          ? window.novoMaterialSelecionado
          : [],
      estabelecimento:
        typeof window.estabelecimentoSelecionado !== "undefined"
          ? window.estabelecimentoSelecionado
          : [],
      importado:
        typeof window.importadoSelecionado !== "undefined"
          ? window.importadoSelecionado
          : [],
      farol_sla:
        typeof window.farolSlaSelecionado !== "undefined"
          ? window.farolSlaSelecionado
          : [],
      data_criacao_from:
        typeof window.dataCriacaoFrom !== "undefined"
          ? window.dataCriacaoFrom
          : "",
      data_criacao_to:
        typeof window.dataCriacaoTo !== "undefined" ? window.dataCriacaoTo : "",
      data_modificacao_from:
        typeof window.dataModificacaoFrom !== "undefined"
          ? window.dataModificacaoFrom
          : "",
      data_modificacao_to:
        typeof window.dataModificacaoTo !== "undefined"
          ? window.dataModificacaoTo
          : "",
      data_importado_from:
        typeof window.dataImportadoFrom !== "undefined"
          ? window.dataImportadoFrom
          : "",
      data_importado_to:
        typeof window.dataImportadoTo !== "undefined"
          ? window.dataImportadoTo
          : "",
    };
  },

  // Vincula eventos necessários
  bindEvents: function () {
    var self = this;

    // Quando o modal é aberto, aplica os filtros salvos
    $(document)
      .off("show.bs.modal.filterIntegration")
      .on("show.bs.modal.filterIntegration", "#filterModal", function () {
        // Recarregar savedFilters antes de aplicar para garantir sincronização
        self.loadSavedFilters();
        self.applySavedFiltersToModal();
      });

    // Quando o modal é totalmente carregado, garantir que os filtros sejam aplicados
    $(document)
      .off("shown.bs.modal.filterIntegration")
      .on("shown.bs.modal.filterIntegration", "#filterModal", function () {
        // Forçar carregamento de selects AJAX que têm valores salvos
        self.preloadRequiredSelects();

        // Pequeno delay para garantir que todos os selects estejam inicializados
        setTimeout(function () {
          self.applySavedFiltersToModal();
        }, 500);
      });
  },

  /**
   * Itera sobre os filtros salvos na sessão e, se um filtro dinâmico (com ajax_url)
   * tiver um valor, força o carregamento de suas opções sem esperar a interação do usuário.
   */
  fetchOptionsForActiveFilters: function() {
    var self = this;
    setTimeout(function() {
      console.log('[Integration] Verificando filtros ativos para pré-carregar opções.');

      Object.keys(self.savedFilters).forEach(function(filterName) {
        var value = self.savedFilters[filterName];
        
        var hasValidValue = Array.isArray(value)
          ? value.filter(v => v && v !== "" && v !== "-1").length > 0
          : (value && value !== "" && value !== null && value !== false && value !== "0");

        if (hasValidValue) {
          var fieldSelector = self.getFieldSelector(filterName);
          var $field = $(fieldSelector);

          if ($field.length && $field.is('select') && $field.data('ajax-url') && !$field.data('loaded')) {
            console.log(`[Integration] Filtro ativo "${filterName}" encontrado. Carregando opções...`);
            FilterModal.loadSelectOptions($field, $field.data('ajax-url'));
          }
        }
      });
    }, 250); // Delay para garantir que FilterModal.js foi inicializado.
  },

  // Força o carregamento de selects AJAX que têm valores salvos
  preloadRequiredSelects: function () {
    var self = this;

    Object.keys(self.savedFilters).forEach(function (filterName) {
      var value = self.savedFilters[filterName];
      var fieldSelector = self.getFieldSelector(filterName);

      if (!fieldSelector || !$(fieldSelector).length) {
        return;
      }

      var $field = $(fieldSelector);

      // Verificar se tem valor válido e se é um select AJAX
      var hasValidValue = false;
      if (Array.isArray(value)) {
        const nonEmptyValues = value.filter((v) => v && v !== "" && v !== "-1");
        hasValidValue = nonEmptyValues.length > 0;
      } else if (
        value &&
        value !== "" &&
        value !== null &&
        value !== false &&
        value !== "0"
      ) {
        hasValidValue = true;
      }

      if (hasValidValue && $field.data("ajax-url") && !$field.data("loaded")) {
        // Simular clique para forçar carregamento
        $field.trigger("show.bs.select");
      }
    });
  },

  // Aplica filtros salvos aos campos do modal
  applySavedFiltersToModal: function () {
    var self = this;

    // Aplica valores salvos aos campos do modal
    Object.keys(self.savedFilters).forEach(function (filterName) {
      var value = self.savedFilters[filterName];
      var fieldSelector = self.getFieldSelector(filterName);

      if (!fieldSelector || !$(fieldSelector).length) {
        return;
      }

      // Verificar se o valor é válido antes de aplicar
      var hasValidValue = false;
      if (Array.isArray(value) && value.length > 0) {
        const nonEmptyValues = value.filter((v) => v && v !== "" && v !== "-1");
        if (nonEmptyValues.length > 0) {
          hasValidValue = true;
          value = nonEmptyValues; // Usar apenas valores válidos
        }
      } else if (
        value &&
        value !== "" &&
        value !== null &&
        value !== false &&
        value !== "0"
      ) {
        hasValidValue = true;
      }

      if (hasValidValue) {
        self.setFieldValue(fieldSelector, value, filterName);
      }
    });

    // Atualiza selectpickers após aplicar todos os valores
    setTimeout(function () {
      $("#filterModal .selectpicker").selectpicker("refresh");
    }, 50);
  },

  // Obtém seletor do campo baseado no nome do filtro
  getFieldSelector: function (filterName) {
    var selectors = {
      pacotes_eventos: "#filterModal #pacotes_eventos",
      sistema_origem: "#filterModal #sistema_origem",
      owner: "#filterModal #owner",
      prioridade: "#filterModal #prioridade",
      atribuido_para: "#filterModal #atribuido_para",
      status_classificacao_fiscal: "#filterModal #status_classificacao_fiscal",
      triagem_diana_falha: "#filterModal #triagem_diana_falha",
      novo_material: "#filterModal #novo_material",
      estabelecimento: "#filterModal #estabelecimento",
      importado: "#filterModal #importado",
      farol_sla: "#filterModal #farol_sla",
      data_criacao_from: "#filterModal #data_criacao_from",
      data_criacao_to: "#filterModal #data_criacao_to",
      data_modificacao_from: "#filterModal #data_modificacao_from",
      data_modificacao_to: "#filterModal #data_modificacao_to",
      data_importado_from: "#filterModal #data_importado_from",
      data_importado_to: "#filterModal #data_importado_to",
    };

    return selectors[filterName] || null;
  },

  // Define valor do campo
  setFieldValue: function (selector, value, filterName) {
    var $field = $(selector);

    if ($field.is("select")) {
      // Para selects com AJAX, pode ser necessário aguardar o carregamento
      if ($field.data("ajax-url")) {
        // Se o select tem AJAX mas ainda não foi carregado, aguardar
        var checkAndSet = function (attempts) {
          if (attempts <= 0) return;

          var hasOptions = $field.find("option").length > 1; // Mais que apenas "Carregando..."
          if (hasOptions) {
            if (Array.isArray(value) && value.length > 0) {
              $field.selectpicker("val", value);
            } else if (value && value !== "") {
              $field.selectpicker("val", [value]);
            }
            $field.selectpicker("refresh");
          } else {
            // Tentar novamente após um delay
            setTimeout(function () {
              checkAndSet(attempts - 1);
            }, 100);
          }
        };
        checkAndSet(10); // Tentar por até 1 segundo
      } else {
        // Select normal sem AJAX
        if (Array.isArray(value) && value.length > 0) {
          $field.selectpicker("val", value);
        } else if (value && value !== "") {
          $field.selectpicker("val", [value]);
        }
        $field.selectpicker("refresh");
      }
    } else if ($field.is(":checkbox")) {
      $field.prop("checked", value === "1" || value === true);
    } else if ($field.is('input[type="date"]')) {
      $field.val(value);
    } else {
      $field.val(value);
    }
  },

  // Método para salvar filtros no backend (chamado após aplicação bem-sucedida)
  saveFiltersToBackend: function (appliedFilters) {
    // Atualiza os filtros salvos localmente
    this.savedFilters = Object.assign(this.savedFilters, appliedFilters);

    // IMPORTANTE: Atualizar também as variáveis window.* para sincronizar
    // com o que será usado na próxima inicialização do modal
    this.updateWindowVariables(appliedFilters);

    // Os filtros são salvos automaticamente no backend quando a pesquisa é executada
    // através do método ajax_get_itens que aplica os filtros via apply_default_filters
  },

  // Atualiza as variáveis window.* para manter sincronização
  updateWindowVariables: function (appliedFilters) {
    // Para arrays, garantir que são arrays mesmo quando vazio
    if (appliedFilters.pacotes_eventos !== undefined) {
      window.eventosSelecionados = Array.isArray(appliedFilters.pacotes_eventos)
        ? appliedFilters.pacotes_eventos
        : [appliedFilters.pacotes_eventos];
    }
    if (appliedFilters.sistema_origem !== undefined) {
      window.sistemasOrigemSelecionados = Array.isArray(
        appliedFilters.sistema_origem
      )
        ? appliedFilters.sistema_origem
        : [appliedFilters.sistema_origem];
    }
    if (appliedFilters.owner !== undefined) {
      window.ownersSelecionados = Array.isArray(appliedFilters.owner)
        ? appliedFilters.owner
        : [appliedFilters.owner];
    }
    if (appliedFilters.prioridade !== undefined) {
      window.prioridadesSelecionadas = Array.isArray(appliedFilters.prioridade)
        ? appliedFilters.prioridade
        : [appliedFilters.prioridade];
    }
    if (appliedFilters.atribuido_para !== undefined) {
      window.usuariosSelecionados = appliedFilters.atribuido_para;
    }
    if (appliedFilters.status_classificacao_fiscal !== undefined) {
      window.statusSelecionados = Array.isArray(
        appliedFilters.status_classificacao_fiscal
      )
        ? appliedFilters.status_classificacao_fiscal
        : [appliedFilters.status_classificacao_fiscal];
    }
    if (appliedFilters.novo_material !== undefined) {
      window.novoMaterialSelecionado = Array.isArray(
        appliedFilters.novo_material
      )
        ? appliedFilters.novo_material
        : [appliedFilters.novo_material];
    }
    if (appliedFilters.estabelecimento !== undefined) {
      window.estabelecimentoSelecionado = Array.isArray(
        appliedFilters.estabelecimento
      )
        ? appliedFilters.estabelecimento
        : [appliedFilters.estabelecimento];
    }
    if (appliedFilters.importado !== undefined) {
      window.importadoSelecionado = Array.isArray(appliedFilters.importado)
        ? appliedFilters.importado
        : [appliedFilters.importado];
    }
    if (appliedFilters.farol_sla !== undefined) {
      window.farolSlaSelecionado = Array.isArray(appliedFilters.farol_sla)
        ? appliedFilters.farol_sla
        : [appliedFilters.farol_sla];
    }
    if (appliedFilters.triagem_diana_falha !== undefined) {
      window.triagemDianaFalhaSelecionado = appliedFilters.triagem_diana_falha;
    }
    if (appliedFilters.data_criacao_from !== undefined) {
      window.dataCriacaoFrom = appliedFilters.data_criacao_from;
    }
    if (appliedFilters.data_criacao_to !== undefined) {
      window.dataCriacaoTo = appliedFilters.data_criacao_to;
    }
    if (appliedFilters.data_modificacao_from !== undefined) {
      window.dataModificacaoFrom = appliedFilters.data_modificacao_from;
    }
    if (appliedFilters.data_modificacao_to !== undefined) {
      window.dataModificacaoTo = appliedFilters.data_modificacao_to;
    }
    if (appliedFilters.data_importado_from !== undefined) {
      window.dataImportadoFrom = appliedFilters.data_importado_from;
    }
    if (appliedFilters.data_importado_to !== undefined) {
      window.dataImportadoTo = appliedFilters.data_importado_to;
    }
  },

  // Método para obter filtros salvos (usado pelo filter_modal_submit.js)
  getSavedFilters: function () {
    return this.savedFilters;
  },

  // Método para atualizar filtros salvos após aplicação via AJAX
  // Deve ser chamado após submissão bem-sucedida dos filtros
  updateFiltersAfterSubmit: function (appliedFilters) {
    // IMPORTANTE: Primeiro atualizar as variáveis window.* que são a fonte da verdade
    this.updateWindowVariables(appliedFilters);

    // Depois atualizar savedFilters local diretamente com os valores aplicados
    this.savedFilters = Object.assign({}, this.savedFilters, appliedFilters);
  },

  // Método para limpar filtros salvos
  clearSavedFilters: function () {
    // Resetar todos os filtros salvos para valores vazios
    this.savedFilters = {
      pacotes_eventos: [],
      sistema_origem: [],
      owner: [],
      prioridade: [],
      atribuido_para: "",
      status_classificacao_fiscal: [],
      novo_material: [],
      estabelecimento: [],
      importado: [],
      farol_sla: [],
      triagem_diana_falha: null,
      data_criacao_from: "",
      data_criacao_to: "",
      data_modificacao_from: "",
      data_modificacao_to: "",
      data_importado_from: "",
      data_importado_to: "",
    };
  },
};

// Inicializa quando o documento estiver pronto
$(document).ready(function () {
  FilterModalIntegration.init();
});
