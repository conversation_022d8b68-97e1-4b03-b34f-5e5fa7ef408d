// Função de busca recursiva
function searchRecursive(items, searchTerm, path = []) {
    let results = [];
    const lowerCaseSearchTerm = searchTerm.toLowerCase();

    items.forEach(item => {
        // Garante que temos um label para buscar e corrige aspas não fechadas
        const label = item.label || item.title || ""; // Corrigido: aspas vazias
        const currentPath = [...path, { key: item.key, label: label }];

        // Verifica se o item atual corresponde ao termo de busca
        if (label.toLowerCase().includes(lowerCaseSearchTerm)) {
            // Adiciona o item com seu caminho (breadcrumbs)
            results.push({ ...item, breadcrumbs: currentPath });
        }

        // Busca recursiva nos subníveis
        if (item.col2 && Array.isArray(item.col2)) {
            results = results.concat(searchRecursive(item.col2, searchTerm, currentPath));
        }
        if (item.col3 && Array.isArray(item.col3)) {
            results = results.concat(searchRecursive(item.col3, searchTerm, currentPath));
        }
        if (item.col4 && Array.isArray(item.col4)) {
            results = results.concat(searchRecursive(item.col4, searchTerm, currentPath));
        }
        // Considera "sections" como um nível aninhado também
        if (item.sections && typeof item.sections === "object") {
            const sectionItems = Object.keys(item.sections).map(key => ({
                ...item.sections[key],
                key: key,
                label: item.sections[key].title
            }));
            results = results.concat(searchRecursive(sectionItems, searchTerm, currentPath));
        }
    });
    return results;
}

// Função para exibir resultados da busca
function displaySearchResults(results, helpers) {
    // const { createIconHtml, createColActionsHtml } = helpers; // REMOVIDO - Usar funções diretamente ou a nova unificada
    const { createIconHtml, createRightActionIconsHtml } = helpers; // Usar a nova função unificada
    const $col1List = $("#nav-list-col1");
    const $col2 = $("#nav-col-2"), $col3 = $("#nav-col-3"), $col4 = $("#nav-col-4");
    $col1List.empty();
    // Garante que colunas 2, 3 e 4 estejam escondidas durante a busca
    $col2.hide();
    $col3.hide();
    $col4.hide();

    if (results.length === 0) {
        $col1List.append("<li class=\"nav-list-item text-muted\" style=\"justify-content:center; padding:20px;\">Nenhum resultado encontrado.</li>");
        return;
    }

    const uniqueResults = results.reduce((acc, current) => {
        const uniqueId = current.url || current.key;
        if (!acc[uniqueId]) {
            acc[uniqueId] = current;
        }
        return acc;
    }, {});

    Object.values(uniqueResults).forEach(item => {
        let iconHtml = createIconHtml(item);
        // let colActionsHtml = createColActionsHtml(item); // REMOVIDO
        let rightActionsHtml = createRightActionIconsHtml(item, "nav-list-col1"); // Usar a nova função, passando o ID da coluna
        let breadcrumbHtml = "";
        if (item.breadcrumbs && item.breadcrumbs.length > 1) {
            breadcrumbHtml = `<span class="search-breadcrumb">${item.breadcrumbs.slice(0, -1).map(b => b.label).join(" > ")}</span>`;
        }

        // A estrutura do link <a> deve ser mantida, mas o conteúdo interno ajustado
        const listItemHtml = `
            <li class="nav-list-item nav-link-item search-result-item" data-url="${item.url || "#"}" ${item.new_tab ? "data-new-tab=\"true\"" : ""}>
                <a href="${item.url || "#"}" ${item.new_tab ? "target=\"_blank\" rel=\"noopener noreferrer\"" : ""}>
                    ${iconHtml}
                    <span class="item-label-container">
                        <span class="item-label">${item.label || item.title}</span>
                        ${breadcrumbHtml}
                    </span>
                    ${rightActionsHtml}  
                </a>
            </li>`;
        $col1List.append(listItemHtml);
    });
}

$(document).ready(function () {
    // Constantes e Variáveis Globais do Modal
    const BASE_URL = (typeof $("#base_url").val() !== "undefined") ? $("#base_url").val() : "/";
    const SITE_URL = (typeof $("#site_url").val() !== "undefined") ? $("#site_url").val() : BASE_URL;
    const SESS_USER_COMPANY_ID = (typeof $("#sess_user_company_id").val() !== "undefined") ? $("#sess_user_company_id").val() : "";
    const CURRENT_PAGE_URI = (typeof $("#current_page_uri").val() !== "undefined") ? $("#current_page_uri").val() : "";
    const iconPath = (typeof window.iconPath !== "undefined") ? window.iconPath : "/assets/icon/seta_direita.svg";
    const menuConfig = (typeof generatedMenuConfig !== "undefined") ? generatedMenuConfig : {};
    const empresasGlobalData = (typeof empresasDataFromPHP !== "undefined") ? empresasDataFromPHP : [];
    let activeCol1ItemKey = null;
    let activeCol2ItemKey = null;
    let activeCol3ItemKey = null;

    // --- Funções Auxiliares --- (Definidas dentro do ready para encapsulamento)
    function createIconHtml(item) {
        let iconContent = "";
        let iconClass = "";
        if (item.hasCheckIcon) {
            iconClass = item.icon ? `glyphicon ${item.icon}` : (item.icon_fa ? `fa ${item.icon_fa}` : "");
            if (iconClass) {
                iconContent = `<i class="${iconClass} icon-base icon-is-checked"></i> `;
            }
        } else if (item.icon) {
            iconContent = `<i class="glyphicon ${item.icon} icon-base"></i> `;
        } else if (item.icon_fa) {
            iconContent = `<i class="fa ${item.icon_fa} icon-base"></i> `;
        } else if (item.icon_img) {
            iconContent = `<img src="${item.icon_img}" class="custom-list-icon icon-base" alt=""> `;
            //             iconContent = `<span class="custom-list-icon icon-base" style="-webkit-mask-image: url('${item.icon_img}'); mask-image: url('${item.icon_img}'); -webkit-mask-repeat: no-repeat; mask-repeat: no-repeat; background-color: #296292; width: 24px; height: 24px; display: inline-block;"></span>`;

        }
        return iconContent;
    }

    function createRightActionIconsHtml(item, colId) {
        let starHtml = "";
        let newTabHtml = "";
        let arrowHtml = "";

        // Ícone de Estrela (apenas colunas 2, 3, 4)
        if (colId !== "nav-list-col1" && typeof item.star !== "undefined") {
            starHtml = `<i class="glyphicon ${item.star ? "glyphicon-star" : "glyphicon-star-empty"} item-action-star" title="${item.star ? "Favorito" : "Adicionar aos favoritos"}"></i>`;
        }

        // Ícone de Nova Aba (Colunas 2, 3, 4)
        if (colId !== "nav-list-col1" && item.new_tab && item.url && item.url !== "#") {
            newTabHtml = `<span class="item-action-icon" title="Abrir em nova aba"><img src="/assets/icon/link_abrir.svg" class="custom-list-icon icon-base" alt="Abrir em nova aba"></span>`;
        }
        // Ícone para Open Tab (Colunas 2, 3, 4) - Mesmo ícone que new_tab, mas com data-attribute diferente
        else if (colId !== "nav-list-col1" && item.open_tab && item.url && item.url !== "#") {
            newTabHtml = `<span class="item-action-icon item-action-open-tab" title="Abrir link" data-url="${item.url}"><img src="/assets/icon/link_abrir.svg" class="custom-list-icon icon-base" alt="Abrir link"></span>`;
        }
        // Ícone de Nova Aba (Coluna 1 - caso especial)
        else if (colId === "nav-list-col1" && item.showCol1NewTabIcon && item.url_col1_new_tab && item.url_col1_new_tab !== "#") {
            newTabHtml = `<span class="item-action-icon-col1" title="Abrir em nova aba"><img src="/assets/icon/link_abrir.svg" class="custom-list-icon icon-base" alt="Abrir em nova aba"></span>`;
        }
        else if (colId === "nav-list-col1" && item.showCol1NewTabIcon && item.url_col1 && item.url_col1 !== "#") {
            newTabHtml = `<span class="item-action-icon-col1" title="Abrir em nova aba"><img src="/assets/icon/link_abrir.svg" class="custom-list-icon icon-base" alt="Abrir em nova aba"></span>`;
        }
        // Ícone de Seta (Próxima Coluna)
        if ((colId === "nav-list-col1" && (item.col2 || item.sections || item.col2IsSections || item.type === "company_search")) ||
            (colId === "nav-list-col2" && item.col3 && item.col3.length > 0) ||
            (colId === "nav-list-col3" && item.col4 && item.col4.length > 0)) {
            arrowHtml = `<img style="width: 24px; height: 24px;" src="${iconPath}" class="custom-svg-icon item-arrow" alt="Seta">`;
        }

        // Agrupa os ícones se houver algum
        if (starHtml || newTabHtml || arrowHtml) {
            return `<span class="item-actions-right">${starHtml}${newTabHtml}${arrowHtml}</span>`;
        }
        return "";
    }

    function populateNavColumn(colId, items, parentKey = "") {
        const $listGroup = $(`#${colId}`);
        $listGroup.empty();
        if (!items || items.length === 0) {
            // Evita mostrar "Nenhum item" em colunas que devem ficar vazias propositalmente
            if ((colId === "nav-list-col3" && $("#nav-col-2").hasClass("force-empty-col3-active")) ||
                (colId === "nav-list-col4" && $("#nav-col-3").hasClass("force-empty-col4-active"))) {
                // Não faz nada, a coluna deve ficar vazia
            } else {
                $listGroup.append("<li class=\"nav-list-item text-muted\" style=\"justify-content:center; padding:20px;\">Nenhum item disponível.</li>");
            }
            return;
        }

        items.forEach(item => {
            if (typeof item.is_visible !== "undefined" && !item.is_visible) {
                return;
            }
            // Garante uma chave única para cada item, mesmo que não venha do config
            let itemUniqueKey = item.key || (item.label ? item.label.replace(/\W+/g, "-").toLowerCase() + "-" + Math.random().toString(16).slice(2) : Math.random().toString(36).substring(7));
            item.key = itemUniqueKey; // Atualiza o item com a chave gerada se necessário

            let iconHtml = createIconHtml(item);
            // let col1ActionIconHtml = (colId === "nav-list-col1") ? createCol1ActionIconHtml(item) : ""; 
            // let colActionsHtml = (colId !== "nav-list-col1") ? createColActionsHtml(item) : ""; 
            let rightActionsHtml = createRightActionIconsHtml(item, colId); // Nova função unificada
            // let arrowHtml = ""; 

            // Verifica se deve mostrar a seta para próxima coluna
            if ((colId === "nav-list-col1" && (item.col2 || item.sections || item.col2IsSections || item.type === "company_search")) ||
                (colId === "nav-list-col2" && item.col3 && item.col3.length > 0) ||
                (colId === "nav-list-col3" && item.col4 && item.col4.length > 0)) {
                arrowHtml = `<img style="width: 24px; height: 24px;" src="${iconPath}" class="custom-svg-icon item-arrow" alt="Seta">`;
            }

            const dataAttrs = `
                data-parent-key="${parentKey}"
                data-item-key="${itemUniqueKey}"
                ${(colId === "nav-list-col1" && item.col2IsSections) ? `data-is-section="true"` : ""}
                ${item.url ? `data-url="${item.url}"` : ""}
                ${item.new_tab || (colId === "nav-list-col1" && item.new_tab_on_item_click) ? `data-new-tab="true"` : ""}
            `;

            let listItemHtml;
            // Item clicável que é um link direto (sem subníveis)
            if ((colId === "nav-list-col1" && item.url && item.url !== "#" && !item.col2 && !item.sections && !item.col2IsSections && item.type !== "company_search") ||
                (colId !== "nav-list-col1" && item.url && item.url !== "#" &&
                    !((colId === "nav-list-col2" && item.col3 && item.col3.length > 0) || (colId === "nav-list-col3" && item.col4 && item.col4.length > 0)))) {
                listItemHtml = `
                    <li class="nav-list-item nav-link-item ${colId === "nav-list-col1" ? "nav-link-item-col1" : ""}" ${dataAttrs}>
                        <a href="${item.url}" ${(item.new_tab || (colId === "nav-list-col1" && item.new_tab_on_item_click)) ? " target=\"_blank\" rel=\"noopener noreferrer\"" : ""}>
                            ${iconHtml}
                            <span class="item-label">${item.label}</span>
                            
                        </a>
                        ${rightActionsHtml} 
                    </li>`;
            }
            // Item clicável que abre a próxima coluna
            else {
                listItemHtml = `
                    <li class="nav-list-item" ${dataAttrs}>
                        ${iconHtml}
                        <span class="item-label">${item.label}</span>
                        ${rightActionsHtml}  
                        
                    </li>`;
            }
            $listGroup.append(listItemHtml);
        });
    }

    // Função para inicializar/resetar o modal para o estado inicial (Coluna 1)
    function initializeModalNav() {
        const col1Items = Object.keys(menuConfig)
            .filter(key => menuConfig[key] && !menuConfig[key].directUrl && (typeof menuConfig[key].is_visible === "undefined" || menuConfig[key].is_visible))
            .map(key => {
                const configEntry = menuConfig[key];
                return {
                    ...configEntry,
                    key: key,
                    label: configEntry.title, // Usa title como label padrão para Col 1
                    is_visible: typeof configEntry.is_visible === "undefined" ? true : configEntry.is_visible
                };
            });
        populateNavColumn("nav-list-col1", col1Items);
        // Esconde e limpa colunas 2, 3, 4 e seus títulos
        $("#nav-col-2, #nav-col-3, #nav-col-4").hide();
        $("#nav-list-col2, #nav-list-col3, #nav-list-col4").empty();
        $("#nav-col-title2, #nav-col-title3, #nav-col-title4").text("").hide();
        // Reseta classes de controle e chaves ativas
        $("#nav-col-2").removeClass("has-col3 force-empty-col3-active");
        $("#nav-col-3").removeClass("has-col4 force-empty-col4-active");
        activeCol1ItemKey = null;
        activeCol2ItemKey = null;
        activeCol3ItemKey = null;
        // Limpa o campo de busca correto
        $("#nav-modal-filter").val(""); // CORRIGIDO AQUI
        // Remove a classe de modo de busca se existir
        $("#mainNavigationModal").removeClass("search-mode");
    }

    // --- Handlers de Eventos --- 

    // Handler de busca no input principal - USA O ID CORRETO
    $("#nav-modal-filter").on("keyup", function () { // CORRIGIDO AQUI
        const searchTerm = $(this).val();
        const $modal = $("#mainNavigationModal");

        if (searchTerm.length < 2) {
            // Se o termo for curto, volta ao estado normal
            if ($modal.hasClass("search-mode")) { // Só reinicializa se estava em modo busca
                initializeModalNav();
            }
            return;
        }

        // Entra em modo de busca
        $modal.addClass("search-mode");
        const topLevelItems = Object.keys(menuConfig)
            .filter(key => menuConfig[key] && !menuConfig[key].directUrl && (typeof menuConfig[key].is_visible === "undefined" || menuConfig[key].is_visible))
            .map(key => ({
                ...menuConfig[key],
                key: key,
                label: menuConfig[key].title,
                is_visible: typeof menuConfig[key].is_visible === "undefined" ? true : menuConfig[key].is_visible
            }));
        const searchResults = searchRecursive(topLevelItems, searchTerm);
        // Passar as funções corretas como helpers
        displaySearchResults(searchResults, { createIconHtml, createRightActionIconsHtml });
    });

    // Handler para cliques nos resultados da busca (renderizados na Coluna 1)
    $("#nav-list-col1").on("click", ".search-result-item a", function (e) {
        const url = $(this).attr("href");
        const newTab = $(this).closest(".search-result-item").data("new-tab");
        if (url && url !== "#") {
            if (!newTab) {
                // Fecha o modal apenas se não for abrir em nova aba
                $("#mainNavigationModal").modal("hide");
            }
            // Permite a ação padrão do link (navegar ou abrir nova aba)
        } else {
            e.preventDefault(); // Impede ação se não houver URL válida
        }
    });

    // Handler para clique em Ação "Nova Aba" na Coluna 1 (funciona na busca e navegação)
    // Usa delegação no container do modal para garantir que funcione mesmo após re-renderizações
    $("#mainNavigationModal").on("click", "#nav-list-col1 .item-action-icon-col1", function (e) {
        e.preventDefault();
        e.stopPropagation(); // Impede que o clique propague para o LI
        const parentLi = $(this).closest(".nav-list-item");
        const itemKey = parentLi.data("item-key");
        const config = menuConfig[itemKey]; // Assume que a chave é do nível 1
        if (config && config.url_col1_new_tab && config.url_col1_new_tab !== "#") {
            window.open(config.url_col1_new_tab, "_blank");
            $("#mainNavigationModal").modal("hide");
        }
        if (config && config.url_col1 && config.url_col1 !== "#") {
            window.location.href = config.url_col1;
            // $("#mainNavigationModal").modal("hide");
        }
    });

    // Handler para clique em ícones de ação "open_tab" nas colunas 2, 3, 4
    // Usa delegação no container do modal para garantir que funcione mesmo após re-renderizações
    $("#mainNavigationModal").on("click", ".nav-col-2 .item-action-open-tab, .nav-col-3 .item-action-open-tab, .nav-col-4 .item-action-open-tab", function (e) {
        e.preventDefault();
        e.stopPropagation(); // Impede que o clique propague para o LI

        const url = $(this).data("url");
        if (url && url !== "#") {
            $("#mainNavigationModal").modal("hide");
            window.open(url, '_blank');
            //window.location.href = url; // Redireciona usando window.location.href
        }
    });

    // --- Handlers para Navegação entre Colunas (só funcionam se não estiver em modo busca) ---

    // Coluna 1 -> Coluna 2
    $("#nav-list-col1").on("click", ".nav-list-item:not(.nav-link-item):not(.search-result-item)", function (e) {
        // Verifica se está em modo de busca
        if ($("#mainNavigationModal").hasClass("search-mode")) {
            return; // Não faz nada se estiver buscando
        }

        const $this = $(this);
        const itemKey = $this.data("item-key");
        const originalConfig = menuConfig[itemKey];
        if (!originalConfig) return;

        // Verifica se realmente há conteúdo para a coluna 2
        const hasCol2Content = originalConfig.col2 || originalConfig.sections || originalConfig.col2IsSections || originalConfig.type === "company_search";
        if (!hasCol2Content) return; // Não faz nada se não houver conteúdo para col2

        const config = { ...originalConfig, label: originalConfig.title }; // Adiciona label para consistência

        activeCol1ItemKey = itemKey;
        activeCol2ItemKey = null;
        activeCol3ItemKey = null;
        $("#nav-list-col1 .nav-list-item").removeClass("active");
        $this.addClass("active");
        const $navCol2 = $("#nav-col-2"), $navCol3 = $("#nav-col-3"), $navCol4 = $("#nav-col-4");
        const $navListCol2 = $("#nav-list-col2"), $navListCol3 = $("#nav-list-col3"), $navListCol4 = $("#nav-list-col4");
        const $navColTitle2 = $("#nav-col-title2"), $navColTitle3 = $("#nav-col-title3"), $navColTitle4 = $("#nav-col-title4");

        // Reseta colunas 2, 3, 4
        $navCol2.hide().removeClass("has-col3 force-empty-col3-active");
        $navCol3.hide().removeClass("has-col4 force-empty-col4-active");
        $navCol4.hide();
        $navListCol2.empty(); $navListCol3.empty(); $navListCol4.empty();
        $navColTitle2.text("").hide(); $navColTitle3.text("").hide(); $navColTitle4.text("").hide();

        const colTitle = config.title || "";
        let col2Items = [];

        if (activeCol1ItemKey === "empresa_selecao") {

            $navColTitle2.html(colTitle).show();

            $navListCol2.html(`<div class="nav-col-search-container" style="padding:12px 15px 0 15px; border-bottom: none;"><input type="text" id="modal-company-filter-input" class="form-control nav-filter-input" placeholder="Digitar o nome da empresa..."></div><div id="modal-empresas-list-container" class="nav-list-group" style="padding:10px 15px 15px 15px;"></div>`);
            loadCompaniesIntoModalList();
            $("#modal-company-filter-input").off("keyup").on("keyup", filterCompaniesInModal);
            $navCol2.show();
            return;
        } else if (config.col2IsSections && config.sections) {
            $navColTitle2.text(colTitle).show();
            col2Items = Object.keys(config.sections)
                .filter(key => typeof config.sections[key].is_visible === "undefined" || config.sections[key].is_visible)
                .map(sectionKey => ({ ...config.sections[sectionKey], key: sectionKey, label: config.sections[sectionKey].title }));
        } else if (config.col2) {
            $navColTitle2.text(colTitle).show();
            col2Items = config.col2
                .filter(item => typeof item.is_visible === "undefined" || item.is_visible)
                .map(item => ({ ...item, label: item.label || item.title })); // Garante label
        }

        if (col2Items.length > 0) {
            populateNavColumn("nav-list-col2", col2Items, activeCol1ItemKey);
            $navCol2.show();
            if (config.forceEmptyCol3 === true) {
                $navCol2.addClass("force-empty-col3-active");
                $navCol3.show();
            }
        } else {
            $navCol2.show();
        }
    });

    // Coluna 2 -> Coluna 3
    $("#nav-list-col2").on("click", ".nav-list-item:not(.nav-link-item)", function (e) {
        if ($("#mainNavigationModal").hasClass("search-mode")) return;

        const $this = $(this);
        const itemKeyCol2 = $this.data("item-key");
        const parentKeyCol1 = $this.data("parent-key"); // activeCol1ItemKey
        if (!parentKeyCol1 || !menuConfig[parentKeyCol1]) return;

        const configCol1 = menuConfig[parentKeyCol1];
        let configCol2;
        if (configCol1.col2IsSections && configCol1.sections) {
            configCol2 = configCol1.sections[itemKeyCol2];
        } else if (configCol1.col2) {
            configCol2 = configCol1.col2.find(item => item.key === itemKeyCol2);
        }
        if (!configCol2 || !configCol2.col3 || configCol2.col3.length === 0) return; // Não faz nada se não houver col3

        activeCol2ItemKey = itemKeyCol2;
        activeCol3ItemKey = null;
        $("#nav-list-col2 .nav-list-item").removeClass("active");
        $this.addClass("active");
        const $navCol2 = $("#nav-col-2"), $navCol3 = $("#nav-col-3"), $navCol4 = $("#nav-col-4");
        const $navListCol3 = $("#nav-list-col3"), $navListCol4 = $("#nav-list-col4");
        const $navColTitle3 = $("#nav-col-title3"), $navColTitle4 = $("#nav-col-title4");

        // Reseta colunas 3, 4
        $navCol3.hide().removeClass("has-col4 force-empty-col4-active");
        $navCol4.hide();
        $navListCol3.empty(); $navListCol4.empty();
        $navColTitle3.text("").hide(); $navColTitle4.text("").hide();
        $navCol2.removeClass("has-col3"); // Remove classe da col2

        const colTitle = configCol2.label || configCol2.title || "";
        const col3Items = configCol2.col3
            .filter(item => typeof item.is_visible === "undefined" || item.is_visible)
            .map(item => ({ ...item, label: item.label || item.title })); // Garante label

        if (col3Items.length > 0) {
            $navColTitle3.text(colTitle).show();
            populateNavColumn("nav-list-col3", col3Items, activeCol2ItemKey);
            $navCol3.show();
            $navCol2.addClass("has-col3"); // Adiciona classe na col2
            if (configCol2.forceEmptyCol4 === true) {
                $navCol3.addClass("force-empty-col4-active");
                $navCol4.show(); // Mostra col4 vazia
            }
        } else {
            $navCol3.show(); // Mostra col3 vazia
            $navCol2.addClass("has-col3");
        }
    });

    // Coluna 3 -> Coluna 4
    $("#nav-list-col3").on("click", ".nav-list-item:not(.nav-link-item)", function (e) {
        if ($("#mainNavigationModal").hasClass("search-mode")) return;

        const $this = $(this);
        const itemKeyCol3 = $this.data("item-key");
        // Precisa das chaves pai para encontrar a config
        const parentKeyCol1 = activeCol1ItemKey;
        const parentKeyCol2 = activeCol2ItemKey;
        if (!parentKeyCol1 || !parentKeyCol2 || !menuConfig[parentKeyCol1]) return;

        const configCol1 = menuConfig[parentKeyCol1];
        let configCol2;
        if (configCol1.col2IsSections && configCol1.sections) {
            configCol2 = configCol1.sections[parentKeyCol2];
        } else if (configCol1.col2) {
            configCol2 = configCol1.col2.find(item => item.key === parentKeyCol2);
        }
        if (!configCol2 || !configCol2.col3) return;

        let configCol3 = configCol2.col3.find(item => item.key === itemKeyCol3);
        if (!configCol3 || !configCol3.col4 || configCol3.col4.length === 0) return; // Não faz nada se não houver col4

        activeCol3ItemKey = itemKeyCol3;
        $("#nav-list-col3 .nav-list-item").removeClass("active");
        $this.addClass("active");
        const $navCol3 = $("#nav-col-3"), $navCol4 = $("#nav-col-4");
        const $navListCol4 = $("#nav-list-col4");
        const $navColTitle4 = $("#nav-col-title4");

        // Reseta coluna 4
        $navCol4.hide();
        $navListCol4.empty();
        $navColTitle4.text("").hide();
        $navCol3.removeClass("has-col4"); // Remove classe da col3

        const colTitle = configCol3.label || configCol3.title || "";
        const col4Items = configCol3.col4
            .filter(item => typeof item.is_visible === "undefined" || item.is_visible)
            .map(item => ({ ...item, label: item.label || item.title })); // Garante label

        if (col4Items.length > 0) {
            $navColTitle4.text(colTitle).show();
            populateNavColumn("nav-list-col4", col4Items, activeCol3ItemKey);
            $navCol4.show();
            $navCol3.addClass("has-col4"); // Adiciona classe na col3
        } else {
            $navCol4.show(); // Mostra col4 vazia
            $navCol3.addClass("has-col4");
        }
    });

    // Handler para links diretos clicáveis (em qualquer coluna, exceto busca)
    $("#mainNavigationModal").on("click", ".nav-link-item a", function (e) {
        // Não interfere com resultados da busca, pois eles têm seu próprio handler
        if ($(this).closest(".search-result-item").length > 0) {
            return;
        }
        // Fecha modal se não for abrir em nova aba
        if ($(this).attr("target") !== "_blank") {
            $("#mainNavigationModal").modal("hide");
        }
        // Permite a ação padrão do link
    });

    // --- Handlers do Modal Bootstrap --- 

    // Handler para o botão de fechar o modal
    $("#mainNavigationModal .close").on("click", function () {
        $("#mainNavigationModal").modal("hide");
    });

    // Limpa e reinicializa o modal quando ele é fechado (evento hidden.bs.modal)
    $("#mainNavigationModal").on("hidden.bs.modal", function () {
        initializeModalNav(); // Garante retorno ao estado inicial
    });

    // Inicializa a navegação na primeira vez que o modal é mostrado
    $("#mainNavigationModal").one("show.bs.modal", function () {
        initializeModalNav();
    });

    // Foco no input de busca ao abrir (evento shown.bs.modal)
    $("#mainNavigationModal").on("shown.bs.modal", function () {
        // Garante que não está em modo busca ao focar
        if (!$("#mainNavigationModal").hasClass("search-mode")) {
            setTimeout(function () {
                $("#nav-modal-filter").focus(); // CORRIGIDO AQUI
            }, 100);
        }
    });

    // --- Funções de Seleção de Empresa (Manter como estavam) ---
    function loadCompaniesIntoModalList() {
        const $listContainer = $("#modal-empresas-list-container");
        if (!$listContainer.length) return; // Sai se o container não existe
        $listContainer.empty();
        if (!empresasGlobalData || empresasGlobalData.length === 0) {
            $listContainer.append("<p class=\"text-muted\">Nenhuma empresa disponível.</p>");
            return;
        }
        empresasGlobalData.forEach(empresa => {

            const isActive = SESS_USER_COMPANY_ID == empresa.id;
            const companyName = empresa.nome || (empresa.id ? `${empresa.nome}` : 'Empresa sem nome'); // Added fallback logic
            const checkIconHtml = isActive ? '<i class="fa fa-check company-active-check"></i>' : ''; // Corrected check icon HTML
            const itemHtml = `
            <div class="nav-list-group ${isActive ? "active" : ""}" data-company-id="${empresa.id}">
                <span class="company-name">${companyName}</span>
                ${checkIconHtml}
            </div>`;
            $listContainer.append(itemHtml);
        });
    }

    function filterCompaniesInModal() {
        const searchTerm = $("#modal-company-filter-input").val().toLowerCase(); // Este é o filtro DENTRO da lista de empresas, ID diferente
        $("#modal-empresas-list-container .nav-list-group").each(function () {
            const companyName = $(this).find(".company-name").text().toLowerCase();
            if (companyName.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    }
    // Handler para clique na lista de empresas (se existir)
    $("#mainNavigationModal").on("click", "#modal-empresas-list-container .nav-list-group", function () {
        const companyId = $(this).data("company-id");
        selectCompany(companyId);
    });

    function selectCompany(companyId, event) {

        $.ajax({
            url: SITE_URL + "home/change_company/" + companyId,
            type: "GET",
            dataType: 'json',

        });
        setTimeout(function() {
            window.location.reload();
        }, 1000); 
    }


    // --- Lógica da Sidebar (Manter como estava) ---
    const currentPath = window.location.pathname;
    $(".sidebar-nav-icons li a").each(function () {
        const linkPath = $(this).attr("href");
        // Lógica de ativação do item atual
        if (linkPath && linkPath !== "#" && currentPath.includes(linkPath.replace(BASE_URL, "/"))) {
            if (linkPath !== BASE_URL || currentPath === "/") { // Evita ativar tudo com "/"
                $(this).addClass("active-sidebar-link");
            }
        }
        // Tratamento especial para modal de perguntas (exemplo)
        if ($(this).data("target") === "#perguntasRespostas" && currentPath.includes("controle_pendencias")) {
            // $(this).addClass("active-sidebar-link"); // Decidir se o gatilho do modal deve ficar ativo
        }
    });
    // Ativa "home" se nenhum outro item estiver ativo
    if ($(".sidebar-nav-icons li a.active-sidebar-link").length === 0) {
        $(".sidebar-nav-icons li a[data-menu-key=\"home\"]").addClass("active-sidebar-link");
    }



    $('#app-sidebar .sidebar-nav-icons li a').on('mouseenter', function (e) {
        var $tooltip = $(this).find('.sidebar-tooltip');

        // Obter a posição do ícone
        var iconPos = $(this).offset();
        var iconHeight = $(this).outerHeight();

        // Posicionar o tooltip ao lado do ícone
        $tooltip.css({
            'top': (iconPos.top + (iconHeight / 2) - ($tooltip.outerHeight() / 2)) + 'px',
            'left': '70px', // Posição fixa à direita da sidebar
            'opacity': '1',
            'visibility': 'visible',
            'display': 'block'
        });
    });

    // Adicionar evento de mouseout para esconder o tooltip
    $('#app-sidebar .sidebar-nav-icons li a').on('mouseleave', function () {
        var $tooltip = $(this).find('.sidebar-tooltip');
        $tooltip.css({
            'opacity': '0',
            'visibility': 'hidden'
        });
    });



}); // Fim do $(document).ready()


// document.addEventListener('DOMContentLoaded', function () {

//     let navItems = document.querySelectorAll('#nav-list-col1 .nav-list-item');

//     navItems.forEach(item => {
//         let link = item.querySelector('a');
//         let icons = item.querySelectorAll('img');
//         let url = item.getAttribute('data-url');
//         let openInNewTab = item.getAttribute('data-new-tab') === 'true';

//         item.addEventListener('click', function (event) {
//             // Ignora cliques nos ícones
//             if ([...icons].includes(event.target)) return;

//             // Prioriza <a href> se existir
//             if (link) {
//                 link.click();
//                 return;
//             }

//             // Se não há <a>, mas há data-url, navega
//             if (url && url !== '#') {
//                 if (openInNewTab) {
//                     window.open(url, '_blank');
//                 } else {
//                     window.location.href = url;
//                 }
//             }
//         });
//     });



//     let navItems_3 = document.querySelectorAll('#nav-list-col3 .nav-list-item');

//     navItems_3.forEach(item => {
//         let link = item.querySelector('a');
//         let icons = item.querySelectorAll('img');
//         let url = item.getAttribute('data-url');
//         let openInNewTab = item.getAttribute('data-new-tab') === 'true';

//         item.addEventListener('click', function (event) {
//             // Ignora cliques nos ícones
//             if ([...icons].includes(event.target)) return;

//             // Prioriza <a href> se existir
//             if (link) {
//                 link.click();
//                 return;
//             }

//             // Se não há <a>, mas há data-url, navega
//             if (url && url !== '#') {
//                 if (openInNewTab) {
//                     window.open(url, '_blank');
//                 } else {
//                     window.location.href = url;
//                 }
//             }
//         });

//     });



// });