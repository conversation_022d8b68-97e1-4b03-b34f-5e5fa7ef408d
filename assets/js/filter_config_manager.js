/**
 * Gerenciador de configurações de filtros dinâmico
 * Responsável por gerenciar labels, mapeamentos e validações baseados na configuração PHP
 */

var FilterConfigManager = {
  configs: {
    basic_filters: [],
    advanced_filters: [],
  },
  labelMap: {},

  /**
   * Inicializa o gerenciador com as configurações vindas do PHP
   */
  init: function () {
    console.log("FilterConfigManager.init() chamado");
    console.log("window.filterConfigs disponível?", typeof window.filterConfigs !== "undefined");
    
    if (typeof window.filterConfigs !== "undefined") {
      this.configs = window.filterConfigs;
      this.buildLabelMap();
      console.log("FilterConfigManager inicializado com sucesso:", this.configs);
      console.log("Label map criado:", this.labelMap);
    } else {
      console.warn("window.filterConfigs não encontrado. Usando configurações padrão.");
    }
  },

  /**
   * Constrói o mapa de labels baseado nas configurações PHP
   */
  buildLabelMap: function () {
    var self = this;

    // Processa filtros básicos
    this.configs.basic_filters.forEach(function (filter) {
      if (filter.type === "date_range") {
        // Para campos de data range, criar entradas separadas para _from e _to
        self.labelMap[filter.name + "_from"] = filter.label + " - De";
        self.labelMap[filter.name + "_to"] = filter.label + " - Até";
      } else {
        self.labelMap[filter.name] = filter.label;
      }
    });

    // Processa filtros avançados
    this.configs.advanced_filters.forEach(function (filter) {
      if (filter.type === "date_range") {
        // Para campos de data range, criar entradas separadas para _from e _to
        self.labelMap[filter.name + "_from"] = filter.label + " - De";
        self.labelMap[filter.name + "_to"] = filter.label + " - Até";
      } else {
        self.labelMap[filter.name] = filter.label;
      }
    });

    console.log("Label map construído:", this.labelMap);
  },

  /**
   * Obtém o label de um filtro pelo nome
   */
  getLabel: function (fieldName) {
    return this.labelMap[fieldName] || fieldName;
  },

  /**
   * Verifica se um campo é um filtro avançado
   */
  isAdvancedFilter: function (fieldName) {
    // Remove sufixos _from e _to para verificação
    var baseName = fieldName.replace(/_from$|_to$/, "");

    return this.configs.advanced_filters.some(function (filter) {
      return filter.name === baseName;
    });
  },

  /**
   * Obtém todas as configurações de filtros (básicos + avançados)
   */
  getAllFilters: function () {
    return this.configs.basic_filters.concat(this.configs.advanced_filters);
  },

  /**
   * Obtém configuração específica de um filtro pelo nome
   */
  getFilterConfig: function (fieldName) {
    // Remove sufixos _from e _to para encontrar a configuração base
    var baseName = fieldName.replace(/_from$|_to$/, "");

    var allFilters = this.getAllFilters();
    return allFilters.find(function (filter) {
      return filter.name === baseName;
    });
  },

  /**
   * Verifica se um campo tem valores válidos para exibir badge
   */
  hasValidValue: function (fieldName, value) {
    // Para checkboxes
    if (typeof value === "boolean") {
      return value === true;
    }

    // Para strings (datas, etc)
    if (typeof value === "string") {
      return value.trim() !== "";
    }

    // Para arrays (selects múltiplos)
    if (Array.isArray(value)) {
      return (
        value.length > 0 &&
        value.some(function (v) {
          return v !== null && v !== undefined && v !== "" && v !== "-1";
        })
      );
    }

    // Para valores únicos
    return (
      value !== null && value !== undefined && value !== "" && value !== "-1"
    );
  },

  /**
   * Obtém o valor de exibição formatado para um filtro
   */
  getDisplayValue: function (fieldName, value) {
    var config = this.getFilterConfig(fieldName);

    if (!config) {
      return value;
    }

    // Para checkboxes
    if (config.type === "checkbox" && value === true) {
      return "Sim";
    }

    // Para selects com options fixas, tentar encontrar o label
    if (
      config.type === "select" &&
      config.options &&
      config.options.length > 0
    ) {
      if (Array.isArray(value)) {
        return value
          .map(function (v) {
            var option = config.options.find(function (opt) {
              return opt.value == v;
            });
            return option ? option.label : v;
          })
          .join(", ");
      } else {
        var option = config.options.find(function (opt) {
          return opt.value == value;
        });
        return option ? option.label : value;
      }
    }

    // Para arrays em geral
    if (Array.isArray(value)) {
      return value.join(", ");
    }

    return value;
  },
};

// Inicializar automaticamente quando o documento estiver pronto
$(document).ready(function () {
  console.log("DOM ready - iniciando FilterConfigManager...");
  FilterConfigManager.init();
});
