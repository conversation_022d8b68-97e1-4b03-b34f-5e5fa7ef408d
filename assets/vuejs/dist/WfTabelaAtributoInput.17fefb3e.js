// modules are defined as an array
// [ module function, map of requires ]
//
// map of requires is short require name -> numeric require
//
// anything defined in a previous bundle is accessed via the
// orig method which is the require for previous bundles
parcelRequire = (function (modules, cache, entry, globalName) {
  // Save the require from previous bundle to this closure if any
  var previousRequire = typeof parcelRequire === 'function' && parcelRequire;
  var nodeRequire = typeof require === 'function' && require;

  function newRequire(name, jumped) {
    if (!cache[name]) {
      if (!modules[name]) {
        // if we cannot find the module within our internal map or
        // cache jump to the current global require ie. the last bundle
        // that was added to the page.
        var currentRequire = typeof parcelRequire === 'function' && parcelRequire;
        if (!jumped && currentRequire) {
          return currentRequire(name, true);
        }

        // If there are other bundles on this page the require from the
        // previous one is saved to 'previousRequire'. Repeat this as
        // many times as there are bundles until the module is found or
        // we exhaust the require chain.
        if (previousRequire) {
          return previousRequire(name, true);
        }

        // Try the node require function if it exists.
        if (nodeRequire && typeof name === 'string') {
          return nodeRequire(name);
        }

        var err = new Error('Cannot find module \'' + name + '\'');
        err.code = 'MODULE_NOT_FOUND';
        throw err;
      }

      localRequire.resolve = resolve;
      localRequire.cache = {};

      var module = cache[name] = new newRequire.Module(name);

      modules[name][0].call(module.exports, localRequire, module, module.exports, this);
    }

    return cache[name].exports;

    function localRequire(x){
      return newRequire(localRequire.resolve(x));
    }

    function resolve(x){
      return modules[name][1][x] || x;
    }
  }

  function Module(moduleName) {
    this.id = moduleName;
    this.bundle = newRequire;
    this.exports = {};
  }

  newRequire.isParcelRequire = true;
  newRequire.Module = Module;
  newRequire.modules = modules;
  newRequire.cache = cache;
  newRequire.parent = previousRequire;
  newRequire.register = function (id, exports) {
    modules[id] = [function (require, module) {
      module.exports = exports;
    }, {}];
  };

  var error;
  for (var i = 0; i < entry.length; i++) {
    try {
      newRequire(entry[i]);
    } catch (e) {
      // Save first error but execute all entries
      if (!error) {
        error = e;
      }
    }
  }

  if (entry.length) {
    // Expose entry point to Node, AMD or browser globals
    // Based on https://github.com/ForbesLindesay/umd/blob/master/template.js
    var mainExports = newRequire(entry[entry.length - 1]);

    // CommonJS
    if (typeof exports === "object" && typeof module !== "undefined") {
      module.exports = mainExports;

    // RequireJS
    } else if (typeof define === "function" && define.amd) {
     define(function () {
       return mainExports;
     });

    // <script>
    } else if (globalName) {
      this[globalName] = mainExports;
    }
  }

  // Override the current require with this new one
  parcelRequire = newRequire;

  if (error) {
    // throw error from earlier, _after updating parcelRequire_
    throw error;
  }

  return newRequire;
})({"8K5j":[function(require,module,exports) {
"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

var _lodash = _interopRequireDefault(require("lodash"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

var VisionViewEnum = Object.freeze({
  HOMOLOG_VIEW: 'homolog_view',
  FILL_VIEW: 'fill_view',
  FILL_HOMOLOG_VIEW: 'fill_homolog_view'
});
var _default = {
  name: 'WfTabelaAtributoInput',
  props: {
    addClass: {
      required: false,
      type: String
    },
    initialAttr: {
      required: true,
      type: [Array, Object]
    },
    initialValue: {
      required: true,
      type: String
    },
    idItem: {
      required: true,
      type: Number
    },
    permissionType: {
      required: true,
      default: String
    },
    homologar_atributos: {
      required: true,
      type: Boolean
    },
    preencher_atributos: {
      required: true,
      type: Boolean
    },
    diana_atributos: {
      required: true,
      type: Boolean
    },
    movimentar_itens: {
      required: true,
      type: Boolean
    }
  },
  data: function data() {
    return {
      attr: null,
      value: '',
      currentLength: 0,
      visionViewEnum: VisionViewEnum,
      preencherAtributos: null,
      homologarAtributos: null,
      movimentarItens: null,
      diana_atributos: null
    };
  },
  computed: {
    totalDigits: function totalDigits() {
      return this.attr && this.attr.tamanhoMaximo;
    },
    // Adicionado check
    inputType: function inputType() {
      return this.attr && this.attr.formaPreenchimento ? this.attr.formaPreenchimento.toUpperCase() : 'TEXTO';
    },
    // Adicionado check
    isMultiSelect: function isMultiSelect() {
      return this.attr && this.attr.multivalorado === true;
    },
    // Adicionado check
    decimalPlaces: function decimalPlaces() {
      return this.attr && this.attr.casasDecimais || 0;
    },
    // Adicionado check
    preencherAtributos: function preencherAtributos() {
      return this.preencher_atributos;
    } // Usa prop diretamente

  },
  watch: {
    initialValue: {
      // REMOVIDO immediate: true
      handler: function handler(newValue, oldValue) {
        var _this = this;

        // --- ADICIONAR GUARDA ---
        if (!this.attr) {
          console.warn("WATCH initialValue ".concat(this.idItem, ": Attr n\xE3o definido ainda, pulando."));
          return; // Não executa se attr não está pronto
        } // ------------------------


        var processedValue = this.processValueForType(newValue);

        if (!_lodash.default.isEqual(this.value, processedValue)) {
          this.value = processedValue;
          this.$nextTick(function () {
            _this.refreshSelectPicker();
          });
          this.$nextTick(function () {
            _this.$emit("changedValues", {
              value: _this.value,
              attrCodigo: _this.attr.codigo,
              itemId: _this.idItem
            });

            _this.handleNewColumns(_this.value);
          });
        }
      } // immediate: true // REMOVIDO

    },
    initialAttr: {
      // REMOVIDO immediate: true
      handler: function handler(newAttr, oldAttr) {
        var _this2 = this;

        if (!_lodash.default.isEqual(newAttr, oldAttr)) {
          this.attr = _lodash.default.cloneDeep(newAttr);
          this.value = this.processValueForType(this.initialValue);
          this.updateCurrentLength();
          this.$nextTick(function () {
            _this2.refreshSelectPicker();
          });
        }
      },
      deep: true // immediate: true // REMOVIDO

    }
  },
  // Fim watch
  methods: {
    processValueForType: function processValueForType(val) {
      var processed = val;
      var inputType = this.attr && this.attr.formaPreenchimento ? this.attr.formaPreenchimento.toUpperCase() : 'TEXTO'; // Usa computed property com validação

      var isMulti = this.isMultiSelect; // Usa computed property
      // Lógica de conversão (Booleano para '1'/'0'/'', Multivalor para Array, etc.)

      if (isMulti && typeof processed === 'string' && processed !== '') processed = processed.split(",");else if (isMulti && !processed) processed = [];else if (inputType === 'BOOLEANO') {
        if (processed === true || processed === '1' || processed === 1) processed = '1';else if (processed === false || processed === '0' || processed === 0) processed = '0';else processed = '';
      } else if (processed === null || processed === undefined || processed === '') processed = '';
      if (!Array.isArray(processed)) processed = String(processed); // Garante string ou array

      return processed;
    },
    updateCurrentLength: function updateCurrentLength() {
      if (this.inputType === 'TEXTO' && this.totalDigits) {
        this.currentLength = this.totalDigits - String(this.value || '').length;
      } else {
        this.currentLength = 0;
      }
    },
    refreshSelectPicker: function refreshSelectPicker() {
      if (this.inputType === 'LISTA_ESTATICA' || this.inputType === 'BOOLEANO') {
        if (typeof $ !== 'undefined' && $.fn.selectpicker) {
          var $select = $(this.$el).find('select.selectpicker');

          if ($select.length > 0 && $select.data('selectpicker')) {
            try {
              $select.selectpicker('refresh');
            } catch (e) {
              console.warn("Erro refresh selectpicker:", e);
            }
          }
        }
      }
    },
    checkAndRemoveHighlight: function checkAndRemoveHighlight(event) {
      if (event && event.target) {
        var element = event.target;

        if ($(element).hasClass('valor-atualizado')) {
          console.log("Removendo 'valor-atualizado' de ".concat(element.id || element.name, " (dentro de WfTabelaAtributoInput)"));
          $(element).removeClass('valor-atualizado'); // this.$emit('highlight-removed', element.id || element.name);
        }
      }
    },
    findValueSelectedById: function findValueSelectedById(id, list) {
      var findValue = list.find(function (item) {
        return item.codigo == id;
      });
      return findValue != undefined && findValue != '' && findValue != null ? findValue.descricao : '(vazio)';
    },
    fillValue: function fillValue(_ref) {
      var initialValue = _ref.initialValue,
          currentValue = _ref.currentValue;
      var event = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;

      if (event) {
        var valoresSelecionados = Array.from(event.target.selectedOptions).map(function (opt) {
          return opt.value;
        });
      }

      if (!this.value) {
        this.handleDeleteColumns('');
      }

      if (this.currentValue != currentValue) {
        this.currentValue = currentValue;
      }

      if (this.attr.dbdata[this.idItem]) {
        this.attr.dbdata[this.idItem].codigo = currentValue;
      }

      if (this.attr.listaSubatributos) {
        this.handleNewColumns(this.value);
      }

      if (this.attr.atributoCondicionante) {
        this.handleNewColumns(this.value);
      }

      this.$emit('changedValues', {
        attr: this.attr,
        itemId: this.idItem
      });
    },
    preventInvalidInput: function preventInvalidInput(event) {
      this.checkAndRemoveHighlight(event);
      var allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '-', '.', ','];
      var isCopyPaste = event.ctrlKey || event.metaKey && event.key === 'v';
      var inputValue = this.value || '';

      if (event.key === '-') {
        // Permitir '-' apenas no início e se não houver outro '-'
        if (inputValue.includes('-')) {
          // Removido a condição inputValue.length > 0
          event.preventDefault();
          return;
        }
      } else if (event.key === ',') {
        // Permitir apenas um '.' ou ',' e substituir ',' por '.'
        if (inputValue.includes('.') || inputValue.includes(',')) {
          event.preventDefault();
          return;
        }
      } else if (!allowedKeys.includes(event.key) && !isCopyPaste) {
        // Impedir qualquer outra tecla que não seja permitida
        event.preventDefault();
        return;
      }
    },
    onHandleInputNumberReal: function onHandleInputNumberReal(event) {
      this.checkAndRemoveHighlight(event);
      var str = this.value; // Remover todos os sinais negativos, exceto o primeiro

      var firstHyphenIndex = str.indexOf('-');

      if (firstHyphenIndex !== -1) {
        str = str.substring(0, firstHyphenIndex + 1) + str.substring(firstHyphenIndex + 1).replace(/-/g, '');
      } // Expressão regular para validar números com um sinal negativo opcional no início


      var regex = /^-?\d*(\.\d*)?$/; // Se a string não corresponder à regex, ela é processada

      if (!regex.test(str)) {
        // Remover caracteres inválidos, exceto o primeiro '-'
        str = str.replace(/[^0-9.,-]/g, '');
        var firstHyphen = str.indexOf('-');

        if (firstHyphen > 0) {
          str = str.replace(/-/g, ''); // Remove todos os sinais negativos se não for o primeiro
        } // Substituir vírgula por ponto e garantir que haja apenas um ponto decimal


        str = str.replace(/,/g, '.').replace(/(\..*)\./g, '$1');
      } // Permitir valores iniciais básicos


      if (str === '' || str === '-' || str === '.') {
        this.value = str;
        return;
      } // Ajustar casos como "-." e "."


      if (str.startsWith('-') && str.length > 1 && str[1] === '.') {
        str = '-0' + str.substring(1);
      } else if (str.startsWith('.')) {
        str = '0' + str;
      } // Separar parte inteira e decimal


      var _str$split = str.split('.'),
          _str$split2 = _slicedToArray(_str$split, 2),
          integerPart = _str$split2[0],
          decimalPart = _str$split2[1]; // Verificar se o número é negativo


      var isNegative = integerPart.startsWith('-'); // Calcular o tamanho máximo da parte inteira

      var maxIntegerLength = this.attr.tamanhoMaximo - this.attr.casasDecimais; // Se houver parte decimal, subtrair 1 do tamanho máximo para a vírgula

      if (this.attr.casasDecimais > 0) {
        maxIntegerLength -= 1;
      } // Se o número for negativo, adicionar 1 ao tamanho máximo (não contabilizar o '-')


      if (isNegative) {
        maxIntegerLength++;
      } // Limitar a parte inteira


      integerPart = integerPart.slice(0, maxIntegerLength); // Limitar a parte decimal

      var maxDecimalLength = this.attr.casasDecimais;

      if (decimalPart && maxDecimalLength !== null) {
        decimalPart = decimalPart.slice(0, maxDecimalLength);
      } // Recompor a string


      str = decimalPart !== undefined ? "".concat(integerPart, ".").concat(decimalPart) : integerPart;
      this.value = str; // Atualiza o valor diretamente

      this.$emit('onHandleInputNumber', {
        currentValue: this.value
      });
    },
    onHandleChange: function onHandleChange(tipo) {
      if (tipo == 'NUMERO_INTEIRO') {
        var num = Math.round(parseFloat(this.value));

        if (!isNaN(num)) {
          this.value = num.toString();
        }
      }

      if (tipo == 'NUMERO_REAL') {// if (this.value !== '' && this.value !== '-' && this.value !== '.') {
        //   let num = parseFloat(this.value);
        //   if (!isNaN(num)) {
        //     this.value = num.toFixed(this.attr.casasDecimais);
        //   }
        // }
      }

      this.fillValue({
        currentValue: this.value
      });
    },
    onHandleKeypress: function onHandleKeypress(event) {
      this.checkAndRemoveHighlight(event);
      this.currentLength = this.attr.tamanhoMaximo - this.value.length;
      this.fillValue({
        currentValue: this.value
      }); // this.$emit('onHandleKeypress', {
      //   currentValue: this.value,
      //   currentLength: this.currentLength,
      // });
    },
    process: function process(value, attr, event) {
      this.checkAndRemoveHighlight(event);

      if (value == 'LISTA_ESTATICA') {
        this.onHandleSelect(event);
      }

      if (value == 'TEXTO') {
        this.onHandleChange(attr.formaPreenchimento && attr.formaPreenchimento.toUpperCase());
      }

      if (value == 'NUMERO_INTEIRO') {
        this.onHandleChange(attr.formaPreenchimento && attr.formaPreenchimento.toUpperCase());
      }

      if (value == 'NUMERO_REAL') {
        this.onHandleChange(attr.formaPreenchimento && attr.formaPreenchimento.toUpperCase());
      }

      if (value == 'BOOLEANO') {
        this.onHandleSelectBoolean(event);
      }

      this.$emit('dataEdited', true);
    },
    onHandleSelect: function onHandleSelect(event) {
      this.fillValue({
        currentValue: this.value
      }, event);
    },
    onHandleSelectBoolean: function onHandleSelectBoolean(event) {
      this.attr.descricao = this.value == 1 ? 'Sim' : 'Não';
      this.fillValue({
        currentValue: this.value
      }, event);
    },
    onHandleInputNumber: function onHandleInputNumber(event) {
      this.checkAndRemoveHighlight(event);
      var str = this.value;

      if (str === '' || str === '-' || str === '.') {
        this.displayValue = str;
        return;
      } // Se não há casas decimais definidas, permite apenas números inteiros


      if (/[^\d-]/.test(str)) {
        str = this.value.slice(0, -1);
      }

      if (str.length > this.totalDigits) {
        str = str.substring(0, this.totalDigits);
      }

      this.value = str;
      var num = Number(str);

      if (isNaN(num)) {
        this.displayValue = '';
        return;
      }

      this.displayValue = str;
      this.$emit('onHandleInputNumber', {
        currentValue: this.value
      }); // this.emitValueChange();
    },
    // onHandleInputNumber() {
    //   const sanatizedVal = this.value.replace(/\-/g, '');
    //   if (sanatizedVal.length > this.attr.tamanhoMaximo) {
    //     this.value = this.value.slice(0, this.attr.tamanhoMaximo);
    //   }
    //   this.$emit('onHandleInputNumber', {
    //     currentValue: this.value,
    //   });
    //   this.emitValueChange();
    // },
    // handleNewColumns(newValue) {
    //   const newColumns = [];
    //   const deleteColumns = [];
    //   const attIsConditions = {};
    //   if (!this.attr || !this.attr.codigo) {
    //     console.warn('Atributos necessários não disponíveis');
    //     return;
    //   }
    //   if (this.attr.listaSubatributos && this.attr.listaSubatributos.length > 0) {
    //     this.attr.listaSubatributos.forEach((item) => {
    //       if (item && item.codigo) {
    //         newColumns.push({
    //           ...item,
    //           codigo_pai: this.attr.codigo,
    //           dbdata: item.dbdata || {}
    //         });
    //       }
    //     });
    //   } else if (this.attr.condicionados && this.attr.condicionados.length > 0) {
    //     this.attr.condicionados.forEach((item) => {
    //       const isCondition = this.handleCondition(newValue, item.condicao);
    //       if (isCondition && item.atributo && item.atributo.codigo) {
    //         newColumns.push({
    //           ...item.atributo,
    //           codigo_pai: this.attr.codigo,
    //           dbdata: item.atributo.dbdata || {}
    //         });
    //       } else if (item.atributo && item.atributo.codigo) {
    //         deleteColumns.push({
    //           ...item.atributo,
    //           codigo_pai: this.attr.codigo
    //         });
    //         if (this.attr.dbdata) {
    //           Object.values(this.attr.dbdata).forEach((cond) => {
    //             const isConditionDbdata = this.handleCondition(
    //               cond.codigo,
    //               item.condicao
    //             );
    //             if (!attIsConditions[item.atributo.codigo]) {
    //               attIsConditions[item.atributo.codigo] = [];
    //             }
    //             attIsConditions[item.atributo.codigo].push(isConditionDbdata);
    //           });
    //         }
    //       }
    //     });
    //   }
    //   console.log('conditions: ', {
    //     newItens: newColumns,
    //     deleteItens: deleteColumns
    //   });
    //   if (newColumns.length > 0 || deleteColumns.length > 0) {
    //     this.$nextTick(() => {
    //       this.$emit('handleNewColumns', {
    //         newItens: newColumns,
    //         deleteItens: deleteColumns,
    //         arrayIsConditions: attIsConditions,
    //         idItem: this.idItem
    //       });
    //     });
    //   }
    // },
    handleDeleteColumns: function handleDeleteColumns(newValue) {
      var _this3 = this;

      var deleteColumns = [];
      var attIsConditions = {};

      if (!this.attr || !this.attr.codigo) {
        console.warn('Atributos necessários não disponíveis');
        return;
      } // Função auxiliar para registrar uma coluna para deleção


      var markForDeletion = function markForDeletion(item) {
        if (item && item.codigo) {
          deleteColumns.push(item.codigo);
        }
      }; // Caso 1: Lista de subatributos


      if (this.attr.listaSubatributos && this.attr.listaSubatributos.length > 0) {
        if (!newValue) {
          this.attr.listaSubatributos.forEach(markForDeletion);
        }
      } // Caso 2: Atributos condicionados
      else if (this.attr.condicionados && this.attr.condicionados.length > 0) {
          this.attr.condicionados.forEach(function (item) {
            if (!item.atributo || !item.atributo.codigo) return;

            var isCondition = _this3.handleCondition(newValue, item.condicao);

            if (!isCondition) {
              // Armazenar a informação de condição
              if (!attIsConditions[item.atributo.codigo]) {
                attIsConditions[item.atributo.codigo] = [];
              }

              var shouldKeepColumn = false; // Verificar se algum outro item ainda precisa desta coluna

              if (_this3.attr.dbdata) {
                Object.keys(_this3.attr.dbdata).forEach(function (key) {
                  var cond = _this3.attr.dbdata[key];

                  var isConditionDbdata = _this3.handleCondition(cond.codigo, item.condicao);

                  attIsConditions[item.atributo.codigo].push(isConditionDbdata);

                  if (isConditionDbdata) {
                    shouldKeepColumn = true;
                  }
                });
              } // Se nenhum item precisa da coluna, marca para exclusão


              if (!shouldKeepColumn) {
                markForDeletion(item.atributo);
              }
            }
          });
        } // Emitir apenas se houver colunas a excluir ou condições aplicadas


      if (deleteColumns.length > 0 || Object.keys(attIsConditions).length > 0) {
        this.$nextTick(function () {
          _this3.$emit('handleNewColumns', {
            newItens: [],
            // apenas remoção agora
            deleteItens: deleteColumns,
            arrayIsConditions: attIsConditions,
            idItem: _this3.idItem
          });
        });
      }
    },
    handleNewColumns: function handleNewColumns(newValue) {
      var _this4 = this;

      var newColumns = [];
      var deleteColumns = [];
      var attIsConditions = {};

      if (!this.attr || !this.attr.codigo) {
        console.warn('Atributos necessários não disponíveis');
        return;
      } // Função auxiliar para adicionar uma coluna


      var addColumn = function addColumn(item, parentCode) {
        if (item && item.codigo) {
          return _objectSpread(_objectSpread({}, item), {}, {
            codigo_pai: parentCode,
            dbdata: item.dbdata || {}
          });
        }

        return null;
      }; // Caso 1: Lista de subatributos


      if (this.attr.listaSubatributos && this.attr.listaSubatributos.length > 0) {
        this.attr.listaSubatributos.forEach(function (item) {
          var column = addColumn(item, _this4.attr.codigo);

          if (column) {
            if (newValue) {
              newColumns.push(column);
            } else {
              deleteColumns.push(column.codigo);
            }
          }
        });
      } // Caso 2: Atributos condicionados
      else if (this.attr.condicionados && this.attr.condicionados.length > 0) {
          this.attr.condicionados.forEach(function (item) {
            if (!item.atributo || !item.atributo.codigo) return;

            var isCondition = _this4.handleCondition(newValue, item.condicao);

            if (isCondition) {
              var column = addColumn(item.atributo, _this4.attr.codigo);
              if (column) newColumns.push(column);
            } else {
              // Armazenar a informação de condição
              if (!attIsConditions[item.atributo.codigo]) {
                attIsConditions[item.atributo.codigo] = [];
              }

              var shouldKeepColumn = false; // Verificar se algum outro item precisa desta coluna

              if (_this4.attr.dbdata) {
                Object.keys(_this4.attr.dbdata).forEach(function (key) {
                  var cond = _this4.attr.dbdata[key];

                  var isConditionDbdata = _this4.handleCondition(cond.codigo, item.condicao);

                  attIsConditions[item.atributo.codigo].push(isConditionDbdata);

                  if (isConditionDbdata) {
                    shouldKeepColumn = true;
                  }
                });
              } // Se nenhum item precisa da coluna, adiciona à lista de deleção


              if (!shouldKeepColumn) {
                deleteColumns.push(item.atributo.codigo);
              }
            }
          });
        } // Emitir o evento com as informações de novas colunas e deleções


      if (newColumns.length > 0 || deleteColumns.length > 0 || Object.keys(attIsConditions).length > 0) {
        this.$nextTick(function () {
          _this4.$emit('handleNewColumns', {
            newItens: newColumns ? newColumns : [],
            deleteItens: deleteColumns,
            arrayIsConditions: attIsConditions,
            idItem: _this4.idItem
          });
        });
      }
    },
    handleCondition: function handleCondition(valueSelected, condition) {
      if (!valueSelected) {
        return false;
      }

      var evaluateCondition = function evaluateCondition(value, cond) {
        var operator = cond.operador;
        var targetValue = cond.valor;

        switch (operator) {
          case '==':
            return value == targetValue;

          case '===':
            return value === targetValue;

          case '!=':
            return value != targetValue;

          case '!==':
            return value !== targetValue;

          case '>':
            return value > targetValue;

          case '<':
            return value < targetValue;

          case '>=':
            return value >= targetValue;

          case '<=':
            return value <= targetValue;

          default:
            throw new Error('Operador desconhecido: ' + operator);
        }
      };

      var evaluateNestedCondition = function evaluateNestedCondition(value, cond) {
        var currentResult = evaluateCondition(value, cond);

        if (cond.condicao) {
          var nextResult = evaluateNestedCondition(value, cond.condicao);
          var composition = cond.composicao || '&&';

          if (composition === '||') {
            return currentResult || nextResult;
          } else if (composition === '&&') {
            return currentResult && nextResult;
          } else {
            throw new Error('Composição desconhecida: ' + composition);
          }
        }

        return currentResult;
      };

      var valueSelectedFormat = valueSelected;

      if (this.attr.formaPreenchimento && this.attr.formaPreenchimento.toUpperCase() === 'COMPOSTO') {
        return true;
      }

      if (this.attr.formaPreenchimento && this.attr.formaPreenchimento.toUpperCase() === 'BOOLEANO' && (valueSelectedFormat > 0 || valueSelectedFormat == 'Sim' || valueSelectedFormat == 'SIM' || valueSelectedFormat == 'sim')) {
        valueSelectedFormat = 'true';
      }

      if (this.attr.atributoCondicionante) {
        if (this.attr.formaPreenchimento && this.attr.formaPreenchimento.toUpperCase() === 'BOOLEANO' && valueSelected == '0') {
          valueSelectedFormat = 'false';
        }
      }

      return evaluateNestedCondition(valueSelectedFormat, condition);
    },
    formatInputInteger: function formatInputInteger() {
      var str = String(this.value || '');
      str = str.replace(/[^\d-]/g, '');
      if (str.indexOf('-') > 0 || str.split('-').length - 1 > 1) str = (str.startsWith('-') ? '-' : '') + str.replace(/-/g, '');
      if (this.totalDigits && str.length > this.totalDigits) str = str.substring(0, this.totalDigits);
      if (this.value !== str) this.value = str;
    },
    formatInputReal: function formatInputReal() {
      var str = String(this.value || '');
      str = str.replace(/,/g, '.').replace(/[^\d.-]/g, '');
      var parts = str.split('.');
      if (parts.length > 2) str = parts[0] + '.' + parts.slice(1).join('');
      var hyphens = str.split('-').length - 1;
      if (hyphens > 1 || hyphens === 1 && str.indexOf('-') !== 0) str = (str.startsWith('-') ? '-' : '') + str.replace(/-/g, '');
      var decimalParts = str.split('.');

      if (decimalParts.length > 1 && this.decimalPlaces >= 0) {
        decimalParts[1] = decimalParts[1].slice(0, this.decimalPlaces);
        str = decimalParts.join('.');
      }

      if (this.value !== str) this.value = str;
    },
    // --- Handlers de Eventos do Template (Emitindo @changedValues) ---
    handleIntermediateInput: function handleIntermediateInput(event) {
      // Chamado por @input
      this.checkAndRemoveHighlight(event);

      if (this.inputType === 'NUMERO_INTEIRO') {
        this.formatInputInteger();
      } else if (this.inputType === 'NUMERO_REAL') {
        this.formatInputReal();
      } // Emite valor intermediário


      this.$emit('changedValues', {
        value: this.value,
        attrCodigo: this.attr.codigo,
        itemId: this.idItem
      });
      this.$emit('dataEdited', true); // Informa edição
    },
    handleTextKeyup: function handleTextKeyup(event) {
      // Chamado por @keyup de texto
      this.checkAndRemoveHighlight(event);
      this.updateCurrentLength(); // Emite valor intermediário

      this.$emit('changedValues', {
        value: this.value,
        attrCodigo: this.attr.codigo,
        itemId: this.idItem
      });
      this.$emit('dataEdited', true);
    },
    handleFinalChange: function handleFinalChange(event) {
      // Chamado por @change de TODOS
      this.checkAndRemoveHighlight(event); // Aplica formatação final, se necessário (Ex: Inteiro)

      if (this.inputType === 'NUMERO_INTEIRO') {
        var numVal = parseFloat(String(this.value || '').replace(',', '.'));
        if (!isNaN(numVal)) this.value = String(Math.round(numVal));else this.value = '';
      } // Adicione aqui formatação final para NUMERO_REAL se precisar (ex: casas decimais)
      // Emite o valor FINAL para o pai


      this.$emit('changedValues', {
        value: this.value,
        attrCodigo: this.attr.codigo,
        itemId: this.idItem
      });
      this.$emit('dataEdited', true);
    }
  },
  beforeMount: function beforeMount() {
    var _this5 = this;

    // Inicializa attr e value aqui
    this.attr = _lodash.default.cloneDeep(this.initialAttr);
    this.value = this.processValueForType(this.initialValue);
    this.updateCurrentLength();
    this.preencherAtributos = this.preencher_atributos;
    this.attr = this.initialAttr; // Verifica se o atributo pai está presente e se a forma de preenchimento é 'COMPOSTO'

    if (this.attr.codigo_pai && this.initialAttr.formaPreenchimento && this.initialAttr.formaPreenchimento.toUpperCase() === 'COMPOSTO') {
      // Emite o evento para adicionar colunas com o atributo atual
      this.$nextTick(function () {
        _this5.$emit('handleNewColumns', {
          newItens: [_this5.attr],
          deleteItens: [],
          arrayIsConditions: {},
          idItem: _this5.idItem
        });
      });
    }

    if (!_lodash.default.isEmpty(this.initialValue)) {
      this.value = this.initialValue;
    } else if (this.attr.formaPreenchimento && this.attr.formaPreenchimento.toUpperCase() == 'BOOLEANO') {
      this.value = '';
    }

    if (this.attr.multivalorado && typeof this.value === 'string') {
      this.value = this.initialValue.split(',');
    }
  },
  mounted: function mounted() {
    if (this.attr.atributoCondicionante) {
      this.onHandleSelect();
    }

    if (this.attr.listaSubatributos) {
      this.onHandleSelect();
    } // Verificar a existência da propriedade 'formaPreenchimento' antes de acessá-la


    if (this.attr.formaPreenchimento && this.attr.formaPreenchimento.toUpperCase() == 'TEXTO') {
      this.onHandleKeypress();
    }

    $('.selectpicker').selectpicker();
    $('.selectpicker').selectpicker('refresh');
    this.preencherAtributos = this.preencher_atributos;
    this.homologarAtributos = this.homologar_atributos;
    this.movimentarItens = this.movimentar_itens;
    this.diana_atributos = this.diana_atributos;
  }
};
exports.default = _default;
$(document).ready(function () {
  $(document).on('show.bs.select', '.selectpicker', function () {
    $('.table-atribute').css('min-height', '500px');
  });
  $(document).on('hide.bs.select', '.selectpicker', function () {
    $('.table-atribute').css('min-height', ''); // Remove o min-height inline
  });
});
        var $aea742 = exports.default || module.exports;
      
      if (typeof $aea742 === 'function') {
        $aea742 = $aea742.options;
      }
    
        /* template */
        Object.assign($aea742, (function () {
          var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:_vm.addClass},[_c('div',{directives:[{name:"show",rawName:"v-show",value:(_vm.preencherAtributos == true),expression:"preencherAtributos == true"}]},[(
            _vm.attr.formaPreenchimento && _vm.attr.formaPreenchimento.toUpperCase() == 'LISTA_ESTATICA' &&
            !_vm.attr.multivalorado
        )?_c('select',{directives:[{name:"model",rawName:"v-model",value:(_vm.value),expression:"value"}],staticClass:"form-control btn-input",attrs:{"name":_vm.attr.codigo,"data-apresentacao":_vm.attr.nomeApresentacao,"data-live-search":"true","required":_vm.attr.obrigatorio},on:{"change":[function($event){var $$selectedVal = Array.prototype.filter.call($event.target.options,function(o){return o.selected}).map(function(o){var val = "_value" in o ? o._value : o.value;return val}); _vm.value=$event.target.multiple ? $$selectedVal : $$selectedVal[0]},function($event){return _vm.process('LISTA_ESTATICA', _vm.attr, $event)}]}},[_c('option',{key:'',attrs:{"value":""}},[_vm._v("SELECIONE")]),_vm._v(" "),_vm._l((_vm.attr.dominio),function(dominio){return _c('option',{key:dominio.codigo,domProps:{"value":dominio.codigo}},[_vm._v("\n                "+_vm._s(dominio.descricao || '')+"\n            ")])})],2):_vm._e(),_vm._v(" "),(
            _vm.attr.formaPreenchimento && _vm.attr.formaPreenchimento.toUpperCase() == 'LISTA_ESTATICA' &&
            _vm.attr.multivalorado
        )?_c('select',{directives:[{name:"model",rawName:"v-model",value:(_vm.value),expression:"value"}],staticClass:"form-control btn-input selectpicker",attrs:{"name":_vm.attr.codigo,"data-apresentacao":_vm.attr.nomeApresentacao,"data-actions-box":"true","multiple":"","data-live-search":"true","title":"SELECIONE","required":_vm.attr.obrigatorio},on:{"change":[function($event){var $$selectedVal = Array.prototype.filter.call($event.target.options,function(o){return o.selected}).map(function(o){var val = "_value" in o ? o._value : o.value;return val}); _vm.value=$event.target.multiple ? $$selectedVal : $$selectedVal[0]},function($event){return _vm.process('LISTA_ESTATICA', _vm.attr, $event)}]}},_vm._l((_vm.attr.dominio),function(dominio){return _c('option',{key:dominio.codigo,domProps:{"value":dominio.codigo}},[_vm._v("\n                "+_vm._s(dominio.descricao || '')+"\n            ")])}),0):_vm._e(),_vm._v(" "),(_vm.attr.formaPreenchimento && _vm.attr.formaPreenchimento.toUpperCase() == 'TEXTO')?_c('input',{directives:[{name:"model",rawName:"v-model",value:(_vm.value),expression:"value"}],staticClass:"form-control btn-input",attrs:{"placeholder":_vm.attr.nomeApresentacao,"name":_vm.attr.codigo,"data-apresentacao":_vm.attr.nomeApresentacao,"maxlength":_vm.attr.tamanhoMaximo,"type":"text","required":_vm.attr.obrigatorio},domProps:{"value":(_vm.value)},on:{"change":function($event){return _vm.process('TEXTO', _vm.attr, $event)},"keyup":function($event){return _vm.onHandleKeypress($event)},"input":function($event){if($event.target.composing){ return; }_vm.value=$event.target.value}}}):_vm._e(),_vm._v(" "),(_vm.attr.formaPreenchimento && _vm.attr.formaPreenchimento.toUpperCase() == 'NUMERO_INTEIRO')?_c('input',{directives:[{name:"model",rawName:"v-model",value:(_vm.value),expression:"value"}],staticClass:"form-control btn-input",attrs:{"placeholder":_vm.attr.nomeApresentacao,"name":_vm.attr.codigo,"data-apresentacao":_vm.attr.nomeApresentacao,"maxlength":_vm.attr.tamanhoMaximo,"type":"number","required":_vm.attr.obrigatorio},domProps:{"value":(_vm.value)},on:{"change":function($event){return _vm.process('NUMERO_INTEIRO', _vm.attr, $event)},"input":[function($event){if($event.target.composing){ return; }_vm.value=$event.target.value},function($event){return _vm.onHandleInputNumber($event)}]}}):_vm._e(),_vm._v(" "),(_vm.attr.formaPreenchimento && _vm.attr.formaPreenchimento.toUpperCase() == 'NUMERO_REAL')?_c('input',{directives:[{name:"model",rawName:"v-model",value:(_vm.value),expression:"value"}],staticClass:"form-control btn-input",attrs:{"placeholder":_vm.attr.nomeApresentacao,"name":_vm.attr.codigo,"data-apresentacao":_vm.attr.nomeApresentacao,"maxlength":_vm.attr.tamanhoMaximo,"type":"text","required":_vm.attr.obrigatorio},domProps:{"value":(_vm.value)},on:{"change":function($event){return _vm.process('NUMERO_REAL', _vm.attr, $event)},"input":[function($event){if($event.target.composing){ return; }_vm.value=$event.target.value},function($event){return _vm.onHandleInputNumberReal($event)}],"keydown":function($event){return _vm.preventInvalidInput($event)}}}):_vm._e(),_vm._v(" "),(
            _vm.attr.formaPreenchimento && _vm.attr.formaPreenchimento.toUpperCase() == 'BOOLEANO' &&
            !_vm.attr.atributoCondicionante
        )?_c('select',{directives:[{name:"model",rawName:"v-model",value:(_vm.value),expression:"value"}],staticClass:"form-control btn-input",attrs:{"name":_vm.attr.codigo,"data-apresentacao":_vm.attr.nomeApresentacao,"data-live-search":"true","placeholder":"SELECIONE","required":_vm.attr.obrigatorio},on:{"change":[function($event){var $$selectedVal = Array.prototype.filter.call($event.target.options,function(o){return o.selected}).map(function(o){var val = "_value" in o ? o._value : o.value;return val}); _vm.value=$event.target.multiple ? $$selectedVal : $$selectedVal[0]},function($event){return _vm.process('BOOLEANO', _vm.attr, $event)}]}},[_c('option',{attrs:{"value":""}},[_vm._v("SELECIONE")]),_vm._v(" "),_c('option',{attrs:{"value":"0"}},[_vm._v("Não")]),_vm._v(" "),_c('option',{attrs:{"value":"1"}},[_vm._v("Sim")])]):_vm._e(),_vm._v(" "),(
            _vm.attr.formaPreenchimento && _vm.attr.formaPreenchimento.toUpperCase() == 'BOOLEANO' &&
            _vm.attr.atributoCondicionante
        )?_c('select',{directives:[{name:"model",rawName:"v-model",value:(_vm.value),expression:"value"}],staticClass:"form-control btn-input",attrs:{"name":_vm.attr.codigo,"data-apresentacao":_vm.attr.nomeApresentacao,"data-live-search":"true","placeholder":"SELECIONE","required":_vm.attr.obrigatorio},on:{"change":[function($event){var $$selectedVal = Array.prototype.filter.call($event.target.options,function(o){return o.selected}).map(function(o){var val = "_value" in o ? o._value : o.value;return val}); _vm.value=$event.target.multiple ? $$selectedVal : $$selectedVal[0]},function($event){return _vm.process('BOOLEANO', _vm.attr, $event)}]}},[_c('option',{attrs:{"value":""}},[_vm._v("SELECIONE")]),_vm._v(" "),_c('option',{attrs:{"value":"0"}},[_vm._v("Não")]),_vm._v(" "),_c('option',{attrs:{"value":"1"}},[_vm._v("Sim")])]):_vm._e()]),_vm._v(" "),(_vm.preencherAtributos == false)?_c('div',[(
            _vm.attr.formaPreenchimento && _vm.attr.formaPreenchimento.toUpperCase() == 'LISTA_ESTATICA' &&
            !_vm.attr.multivalorado
        )?_c('div',[_vm._v("\n            "+_vm._s(_vm.findValueSelectedById(_vm.value, _vm.attr.dominio))+"\n        ")]):_vm._e(),_vm._v(" "),(
            _vm.attr.formaPreenchimento && _vm.attr.formaPreenchimento.toUpperCase() == 'LISTA_ESTATICA' &&
            _vm.attr.multivalorado
        )?_c('div',[_vm._v("\n            "+_vm._s(_vm.value || '(vazio)')+"\n        ")]):_vm._e(),_vm._v(" "),(
            _vm.attr.formaPreenchimento && (_vm.attr.formaPreenchimento.toUpperCase() == 'TEXTO' ||
                _vm.attr.formaPreenchimento.toUpperCase() == 'NUMERO_INTEIRO' ||
                _vm.attr.formaPreenchimento.toUpperCase() == 'NUMERO_REAL')
        )?_c('div',[_vm._v("\n            "+_vm._s(_vm.value || '(vazio)')+"\n        ")]):_vm._e(),_vm._v(" "),(_vm.attr.formaPreenchimento && _vm.attr.formaPreenchimento.toUpperCase() == 'BOOLEANO')?_c('div',[_vm._v("\n            "+_vm._s(String(_vm.value) == '1'
                    ? 'Sim'
                    : String(_vm.value) == '0'
                        ? 'Não'
                        : '(vazio)')+"\n        ")]):_vm._e()]):_vm._e()])}
var staticRenderFns = []

          return {
            render: render,
            staticRenderFns: staticRenderFns,
            _compiled: true,
            _scopeId: null,
            functional: undefined
          };
        })());
      
},{"lodash":"uin+"}]},{},[], null)