.vld-shown {
  overflow: hidden;
}

.vld-overlay {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  align-items: center;
  display: none;
  justify-content: center;
  overflow: hidden;
  z-index: 9999;
}

.vld-overlay.is-active {
  display: flex;
}

.vld-overlay.is-full-page {
  z-index: 9999;
  position: fixed;
}

.vld-overlay .vld-background {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  background: #fff;
  opacity: 0.5;
}

.vld-overlay .vld-icon, .vld-parent {
  position: relative;
}

.list-group-item[data-v-f435ba]:hover {
    background-color: #F9F9F9;
    cursor: pointer;
}
.fa.fa-grip[data-v-f435ba]:before {
    letter-spacing: 0.1em;
    content: "\f142 \f142";
}
.fa.fa-grip[data-v-f435ba] {
    font-size: 16px;
    color: lightgray;
    margin-right: 10px;
}
.grabbable[data-v-f435ba] {
    cursor: move;
    cursor: grab;
    cursor: -moz-grab;
    cursor: -webkit-grab;
}
.grabbable[data-v-f435ba]:active {
    cursor: grabbing;
    cursor: -moz-grabbing;
    cursor: -webkit-grabbing;
}.mb-2[data-v-870ff0] {
    margin-bottom: 10px;
}