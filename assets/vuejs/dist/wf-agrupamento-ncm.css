.vld-shown {
  overflow: hidden;
}

.vld-overlay {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  align-items: center;
  display: none;
  justify-content: center;
  overflow: hidden;
  z-index: 9999;
}

.vld-overlay.is-active {
  display: flex;
}

.vld-overlay.is-full-page {
  z-index: 9999;
  position: fixed;
}

.vld-overlay .vld-background {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  background: #fff;
  opacity: 0.5;
}

.vld-overlay .vld-icon, .vld-parent {
  position: relative;
}

.header[data-v-ae731c] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}
.body[data-v-ae731c] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  /* padding: 25px 15px; */
}
.icon[data-v-ae731c] {
  font-size: smaller;
  margin-right: 10px;
}
.text-title[data-v-ae731c] {
  color: #8d9296;
  font-weight: 600;
}
.text-body[data-v-ae731c] {
  font-size: 20px;
  font-weight: 500;
  color: #4d545b;
  margin: 0;
}
.footer[data-v-ae731c] {
  border-top: 1px solid #e5e5e5;
  padding: 15px;
  display: flex;
  justify-content: space-between;
}
.list[data-v-ae731c],
.form-move-selected[data-v-ae731c] {
  display: flex;
  width: 100%;
  margin: 0 0 10px;
}
.table-movement[data-v-ae731c] {
  width: 100%;
  border: 1px solid;
  border-radius: 5px;
}
.table-movement td[data-v-ae731c] {
  background: white;
}
.table-movement th[data-v-ae731c],
.table-movement td[data-v-ae731c] {
  padding: 10px;
}
.table-movement-thead[data-v-ae731c] {
  color: white;
  background-color: #337ab7;
}
.table-movement-tbody[data-v-ae731c] {
  display: block;
  max-height: 200px;
  overflow-y: auto;
}
.table-movement-thead[data-v-ae731c],
.table-movement-tbody tr[data-v-ae731c] {
  display: table;
  width: 100%;
  table-layout: fixed;
}
.odd-item td[data-v-ae731c] {
  background: #e2e3e5;
}
.move-selected-input[data-v-ae731c] {
  width: 100%;
}
.header[data-v-564fac] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}
.body[data-v-564fac] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  /* padding: 25px 15px; */
}
.icon[data-v-564fac] {
  font-size: smaller;
  margin-right: 10px;
}
.text-title[data-v-564fac] {
  color: #8d9296;
  font-weight: 600;
}
.text-body[data-v-564fac] {
  font-size: 20px;
  font-weight: 500;
  color: #4d545b;
  margin: 0;
}
.footer[data-v-564fac] {
  border-top: 1px solid #e5e5e5;
  padding: 15px;
  display: flex;
  justify-content: space-between;
}
.list[data-v-564fac],
.form-fill-in-lot[data-v-564fac] {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin: 0 0 10px;
}
.form-fill-in-lot[data-v-564fac] {
  gap: 15px;
}
.table-movement[data-v-564fac] {
  width: 100%;
  border: 1px solid;
}
.table-movement td[data-v-564fac] {
  background: white;
}
.table-movement th[data-v-564fac],
.table-movement td[data-v-564fac] {
  padding: 10px;
}
.table-movement-thead[data-v-564fac] {
  color: white;
  background-color: #337ab7;
}
.table-movement-tbody[data-v-564fac] {
  display: block;
  max-height: 200px;
  overflow-y: auto;
}
.table-movement-thead[data-v-564fac],
.table-movement-tbody tr[data-v-564fac] {
  display: table;
  width: 100%;
  table-layout: fixed;
}
.odd-item td[data-v-564fac] {
  background: #e2e3e5;
}
.fill-in-lot-input[data-v-564fac] {
  width: 100%;
  border: 1px solid #ddd;
}
.input-container[data-v-564fac],
.label-container[data-v-564fac] {
  padding: 10px;
}
.label-container[data-v-564fac] {
  background-color: #e2e3e5;
  margin-bottom: 1px solid #ddd;
}
.label-container label[data-v-564fac] {
  margin: 0;
}
.input-container[data-v-564fac] {
  /* border-radius: 0 0 5px 5px; */
}
.card-history[data-v-ad7421] {
  border: 1px solid #ddd;
  border-radius: 5px;
  margin: 10px 0;
}
.card-history-header[data-v-ad7421] {
  background-color: #e2e3e5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 5px 5px 0 0;
  padding: 10px;
}
.card-history-header h4[data-v-ad7421],
.card-history-header h5[data-v-ad7421] {
  margin: 0;
}
.card-history-header h4[data-v-ad7421] {
  font-weight: 700;
}
.card-history-body[data-v-ad7421] {
  padding: 10px;
}
.strong[data-v-ad7421] {
  font-weight: 700;
}
.radio-container[data-v-71ffb1] {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}
.custom-radio[data-v-71ffb1]:checked {
  accent-color: #007bff;
}
.card-container[data-v-71ffb1] {
  border: 1px solid #ddd;
  padding: 10px;
  box-shadow: rgba(0, 0, 0, 0.15) 1.95px 1.95px 2.6px;
  border-radius: 5px;
}
.form-check-inline[data-v-71ffb1] {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
.form-check-inline input[data-v-71ffb1] {
  margin: 0;
}
.radio-label-fw-normal[data-v-71ffb1] {
  font-weight: 400;
}
.form-check-label[data-v-71ffb1] {
  margin: 0;
}
.form-input[data-v-71ffb1] {
  position: relative;
  margin-bottom: 10px;
}
.error[data-v-71ffb1] {
  position: absolute;
  bottom: -18px;
  color: red;
  font-size: 0.9em;
}.header[data-v-ac6bf7] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}
.body[data-v-ac6bf7] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  padding: 25px 15px;
}
.icon[data-v-ac6bf7] {
  font-size: smaller;
  margin-right: 10px;
}
.text-title[data-v-ac6bf7] {
  color: #8d9296;
  font-weight: 600;
}
.text-body[data-v-ac6bf7] {
  font-size: 20px;
  font-weight: 500;
  color: #4d545b;
  margin: 0;
}
.footer[data-v-ac6bf7] {
  border-top: 1px solid #e5e5e5;
  padding: 15px;
  display: flex;
  justify-content: space-between;
}
.modal-content-custom[data-v-ac6bf7] {
  width: 100%;
}
.btn-danger[data-v-ac6bf7] {
  background-color: #b00302 !important;
}
.tab-pane[data-v-ac6bf7] {
  padding: 10px;
}
.cursor-pointer[data-v-ac6bf7] {
  cursor: pointer;
}
@media (min-width: 992px) {
.modal-lg[data-v-ac6bf7] {
    width: 80%;
}
}
@media (min-width: 1500px) {
.modal-lg[data-v-ac6bf7] {
    width: 50%;
}
}.header[data-v-66a87f] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}
.body[data-v-66a87f] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  /* padding: 25px 15px; */
}
.icon[data-v-66a87f] {
  font-size: smaller;
  margin-right: 10px;
}
.text-title[data-v-66a87f] {
  color: #8d9296;
  font-weight: 600;
}
.text-body[data-v-66a87f] {
  font-size: 20px;
  font-weight: 500;
  color: #4d545b;
  margin: 0;
}
.footer[data-v-66a87f] {
  border-top: 1px solid #e5e5e5;
  padding: 15px;
  display: flex;
  justify-content: flex-end;
}
.list[data-v-66a87f],
.form-modal[data-v-66a87f] {
  display: flex;
  width: 100%;
  margin: 0 0 10px;
}
.form-modal[data-v-66a87f] {
  flex-direction: column;
}
.table-movement[data-v-66a87f] {
  width: 100%;
  border: 1px solid;
  border-radius: 5px;
}
.table-movement td[data-v-66a87f] {
  background: white;
}
.table-movement th[data-v-66a87f],
.table-movement td[data-v-66a87f] {
  padding: 10px;
}
.table-movement-thead[data-v-66a87f] {
  color: white;
  background-color: #337ab7;
}
.table-movement-tbody[data-v-66a87f] {
  display: block;
  max-height: 200px;
  overflow-y: auto;
}
.table-movement-thead[data-v-66a87f],
.table-movement-tbody tr[data-v-66a87f] {
  display: table;
  width: 100%;
  table-layout: fixed;
}
.odd-item td[data-v-66a87f] {
  background: #e2e3e5;
}
.move-selected-input[data-v-66a87f] {
  width: 100%;
}
.radio-container[data-v-66a87f] {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}
.custom-radio[data-v-66a87f]:checked {
  accent-color: #007bff;
}
.form-check-inline[data-v-66a87f] {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
.form-check-inline input[data-v-66a87f] {
  margin: 0;
}
.radio-label-fw-normal[data-v-66a87f] {
  font-weight: 400;
}
.form-check-label[data-v-66a87f] {
  margin: 0;
}
.form-input[data-v-66a87f] {
  position: relative;
}
.error[data-v-66a87f] {
  position: absolute;
  bottom: -18px;
  color: red;
  font-size: 0.9em;
}
.color-scale[data-v-54ec8d] {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}
.color-segment[data-v-54ec8d] {
  width: 100%;
}.tooltip-container[data-v-73f2a1] {
  position: relative;
  display: inline-block;
}
.tooltiptext[data-v-73f2a1] {
  visibility: visible;
  background-color: black;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px 0;
  width: 250px;
}
.tooltip-container .tooltiptext[data-v-73f2a1]::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 5px;
  border-style: solid;
  border-color: black transparent transparent transparent;
}
.tooltip-container:hover .tooltiptext[data-v-73f2a1] {
  visibility: visible;
}.bulk-selection-footer[data-v-8343aa] {
  position: fixed;
  bottom: 0px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #3276b1;
  color: white;
  padding: 10px 25px;
  z-index: 1000;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 6px 6px 0 0;
  white-space: nowrap;
  transition: bottom 0.2s;
}
.bulk-selection-footer.above-main-footer[data-v-8343aa] {
  bottom: 50px; /* altura do footer do layout */
}
.footer-content[data-v-8343aa] {
  display: flex;
  align-items: center;
  gap: 20px;
  font-size: 14px;
}
.selection-summary[data-v-8343aa] {
  font-weight: 500;
}
.footer-actions[data-v-8343aa] {
  display: flex;
  align-items: center;
  gap: 12px;
}
.footer-action-link[data-v-8343aa] {
  color: white;
  text-decoration: underline;
  cursor: pointer;
  font-weight: 400;
  font-size: 14px;
}
.footer-action-link[data-v-8343aa]:hover {
  color: #e9ecef;
  text-decoration: none;
}
.footer-action-link.loading[data-v-8343aa] {
  opacity: 0.7;
  cursor: wait;
}
.separator[data-v-8343aa] {
  color: #e9ecef;
  font-weight: 300;
}
.small[data-v-8343aa] {
  font-size: 0.875em;
}

/* Responsividade */
@media (max-width: 768px) {
.footer-content[data-v-8343aa] {
    flex-direction: column;
    gap: 10px;
    text-align: center;
}
.footer-actions[data-v-8343aa] {
    justify-content: center;
}
.bulk-selection-footer[data-v-8343aa] {
    white-space: normal;
    padding: 15px 20px;
}
}/* Estilos para links desabilitados no footer */
.footer-action-link.disabled[data-v-4b02f8] {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}button[data-v-4b02f8]:focus {
  outline: none;
  box-shadow: none;
}
.table-responsive[data-v-4b02f8] {
  overflow-x: auto;
}
.table-atribute[data-v-4b02f8] {
  padding: 0;
  overflow-x: auto;
  min-height: 250px;
  background-color: #eaeef2;
}
.tooltip-trigger[data-v-4b02f8] {
  display: inline-block;
  position: relative;
}
.tooltip[data-v-4b02f8] {
  position: absolute !important;
  z-index: 9999 !important;
  pointer-events: none;
}
.tooltip-inner[data-v-4b02f8] {
  max-width: 300px;
  background-color: rgba(0, 0, 0, 0.85) !important;
  color: yellow !important;
  padding: 8px 12px !important;
  border-radius: 4px !important;
  text-align: left !important;
}
.table[data-v-4b02f8] {
  margin: 0 !important;
  border-spacing: 0;
  border-collapse: separate;
}
.table thead[data-v-4b02f8] {
  background: #337ab7;
  color: white;
}
.odd-item tr td[data-v-4b02f8] {
  background: #f5f8fb;
}
.table-atribute th[data-v-4b02f8] {
  background: #337ab7;
}
.table-atribute td[data-v-4b02f8] {
  background: white;
}
.table-atribute th[data-v-4b02f8],
.table-atribute td[data-v-4b02f8] {
  border: none;
  vertical-align: middle !important;
}
.table-atribute tr th[data-v-4b02f8]:nth-child(1),
.table-atribute tr td[data-v-4b02f8]:nth-child(1) {
  position: sticky;
  left: 0px;
  width: 50px;
  min-width: 50px;
  max-width: 50px;
  z-index: 1000;
  border-left: 1px solid #ddd;
}
.table-atribute th[data-v-4b02f8]:nth-child(2),
.table-atribute td[data-v-4b02f8]:nth-child(2) {
  position: sticky;
  left: 50px;
  width: 150px;
  min-width: 150px;
  max-width: 150px;
  z-index: 1000;
}
.table-atribute th[data-v-4b02f8]:nth-child(3),
.table-atribute td[data-v-4b02f8]:nth-child(3) {
  position: sticky;
  left: 200px;
  width: 230px;
  min-width: 230px;
  max-width: 230px;
  z-index: 1000;
}
.table-atribute th[data-v-4b02f8]:nth-child(4),
.table-atribute td[data-v-4b02f8]:nth-child(4) {
  position: sticky;
  left: 430px;
  width: 30px;
  min-width: 30px;
  max-width: 30px;
  z-index: 1000;
  background-color: #337ab7;
  border-right: 1px solid #ddd;
}
.table-atribute td[data-v-4b02f8]:nth-child(4) {
  background-color: white;
}
.odd-item tr td[data-v-4b02f8]:nth-child(4) {
  background: #f5f8fb;
}
.table-atribute th[data-v-4b02f8]:nth-child(5),
.table-atribute td[data-v-4b02f8]:nth-child(5) {
  border-left: 1px solid #ddd;
}
.sticky-actions[data-v-4b02f8] {
  position: sticky;
  right: 0;
  background-color: white;
  z-index: 1000;
  border-left: 1px solid #ddd !important;
  border-right: 1px solid #ddd !important;
  padding: 0 15px;
}
.dynamic-column[data-v-4b02f8] {
  background-color: #296292 !important;
  min-width: 200px;
  width: 200px;
}
.dynamic-column-th[data-v-4b02f8] {
  display: flex;
  align-items: center;
  gap: 10px;
}
.dynamic-column-description[data-v-4b02f8] {
  background-color: #337ab7 !important;
}
.dynamic-column-actions[data-v-4b02f8] {
  background-color: #337ab7 !important;
}
.dynamic-column-actions-th[data-v-4b02f8] {
  display: flex;
  align-items: center;
  gap: 10px;
}
.col-description[data-v-4b02f8] {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.centralize[data-v-4b02f8] {
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-icon[data-v-4b02f8] {
  background-color: white;
  color: black;
  padding: 8px;
  border: 1px solid #cccccc;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-icon[data-v-4b02f8]:focus {
  outline: none;
  box-shadow: none;
}
.btn-icon-fill-lot[data-v-4b02f8] {
  background-color: #337ab7;
  color: white;
  padding: 4px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-icon-fill-lot[data-v-4b02f8]:focus {
  outline: none;
  box-shadow: none;
}
hr[data-v-4b02f8] {
  margin: 0px 0 5px 0;
  border-top: 1px solid #000;
}
.complete-description[data-v-4b02f8] {
  margin: 0 0 10px 0 !important;
}
.btn-load-more[data-v-4b02f8] {
  background-color: white;
  border: 1px solid #cccccc;
  font-weight: 600;
  font-size: 16px;
}
.btn-load-more[data-v-4b02f8]:hover {
  background-color: rgba(0, 0, 0, 0.1);
}
.buttons-actions-container[data-v-4b02f8] {
  display: flex;
  justify-content: space-between;
}
.buttons-actions-container div[data-v-4b02f8] {
  display: flex;
  gap: 15px;
}
.form-check-input[data-v-4b02f8] {
  accent-color: #007bff;
}

/* Estilos do tooltip */
.tooltip[data-v-4b02f8] {
  position: absolute !important;
  z-index: 9999 !important;
  pointer-events: none;
  max-width: 300px;
}
.tooltip-inner[data-v-4b02f8] {
  background-color: rgba(0, 0, 0, 0.85) !important;
  color: yellow !important;
  padding: 8px 12px !important;
  border-radius: 4px !important;
  white-space: normal !important;
  text-align: left !important;
}
.tooltip-arrow[data-v-4b02f8] {
  display: none;
}.header[data-v-dd87f1] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}
.body[data-v-dd87f1] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  padding: 25px 15px;
}
.icon[data-v-dd87f1] {
  font-size: smaller;
  margin-right: 10px;
}
.text-title[data-v-dd87f1] {
  color: #8d9296;
  font-weight: 600;
}
.text-body[data-v-dd87f1] {
  font-size: 20px;
  font-weight: 500;
  color: #4d545b;
  margin: 0;
}
.footer[data-v-dd87f1] {
  border-top: 1px solid #e5e5e5;
  padding: 15px;
  display: flex;
  justify-content: space-between;
}
.modal-dialog-centered[data-v-dd87f1] {
  height: 100vh;
  overflow-y: hidden;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-content-custom[data-v-dd87f1] {
  width: 100%;
}
.btn-success[data-v-dd87f1] {
  background-color: #1d8856 !important;
}
.btn-danger[data-v-dd87f1] {
  background-color: #b00302 !important;
}@charset "UTF-8";
.header[data-v-178347] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}
.body[data-v-178347] {
  padding: 20px;
  background-color: #f8f9fa;
}
.text-title[data-v-178347] {
  color: #8d9296;
  font-weight: 600;
  margin: 0;
}
.footer[data-v-178347] {
  border-top: 1px solid #e5e5e5;
  padding: 15px;
  display: flex;
  justify-content: flex-end;
}
.icon[data-v-178347] {
  font-size: smaller;
  margin-right: 10px;
}
.btn-content[data-v-178347] {
  display: flex;
  align-items: center;
}
.btn-content i[data-v-178347] {
  margin-right: 5px;
  /* ajuste o espaçamento entre o ícone e o texto */
}

/* Resumo da seleção */
.selection-summary[data-v-178347] {
  margin-bottom: 25px;
}
.summary-card[data-v-178347] {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.summary-item[data-v-178347] {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}
.summary-label[data-v-178347] {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 5px;
}
.summary-value[data-v-178347] {
  font-size: 24px;
  font-weight: bold;
  color: #337ab7;
}

/* Lista de NCMs */
.ncms-list[data-v-178347] {
  margin-bottom: 25px;
}
.ncms-list h5[data-v-178347] {
  color: #495057;
  margin-bottom: 15px;
  font-weight: 600;
}
.ncms-grid[data-v-178347] {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
  max-height: 200px;
  overflow-y: auto;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 15px;
}
.ncm-item[data-v-178347] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}
.ncm-code[data-v-178347] {
  font-weight: 600;
  color: #495057;
}
.ncm-count[data-v-178347] {
  font-size: 12px;
  color: #6c757d;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
}

/* Opções de homologação */
.homologation-options[data-v-178347] {
  margin-bottom: 25px;
}
.homologation-options h5[data-v-178347] {
  color: #495057;
  margin-bottom: 15px;
  font-weight: 600;
}
.radio-container[data-v-178347] {
  display: flex;
  gap: 20px;
  margin-bottom: 10px;
}
.custom-radio[data-v-178347]:checked {
  accent-color: #007bff;
}
.form-check-inline[data-v-178347] {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
.form-check-inline input[data-v-178347] {
  margin: 0;
}
.radio-label-fw-normal[data-v-178347] {
  font-weight: 400;
}
.form-check-label[data-v-178347] {
  margin: 0;
}

/* Campo de justificativa */
.form-input[data-v-178347] {
  margin-bottom: 25px;
  position: relative;
}
.form-input label[data-v-178347] {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}
.form-control[data-v-178347] {
  width: 100%;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 10px;
  font-size: 14px;
}
.error[data-v-178347] {
  position: absolute;
  bottom: -18px;
  color: #dc3545;
  font-size: 0.9em;
}

/* Avisos */
.warnings[data-v-178347] {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 15px;
}
.warning-item[data-v-178347] {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-bottom: 10px;
  color: #856404;
  font-size: 14px;
}
.warning-item[data-v-178347]:last-child {
  margin-bottom: 0;
}
.warning-item i[data-v-178347] {
  margin-top: 2px;
  color: #f39c12;
}

/* Spinner para processamento */
.spinning[data-v-178347] {
  animation: spin-data-v-178347 1s linear infinite;
}
@keyframes spin-data-v-178347 {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}

/* Responsividade */
@media (max-width: 768px) {
.summary-card[data-v-178347] {
    flex-direction: column;
    gap: 15px;
}
.ncms-grid[data-v-178347] {
    grid-template-columns: 1fr;
}
.radio-container[data-v-178347] {
    flex-direction: column;
    gap: 10px;
}
}

/* Estilos específicos para o modal Bootstrap */
.modal-content-custom[data-v-178347] {
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}
.modal-dialog-centered[data-v-178347] {
  display: flex;
  align-items: center;
  justify-content: center;
  /* align horizontally */
  margin: 0 auto;
  /* auto margins to center */
  min-height: calc(100% - 1rem);
}

/* Ajustes para botões */
.btn[data-v-178347] {
  margin-left: 10px;
}
.btn[data-v-178347]:first-child {
  margin-left: 0;
}

/* Scrollbar customizada para a grid de NCMs */
.ncms-grid[data-v-178347]::-webkit-scrollbar {
  width: 6px;
}
.ncms-grid[data-v-178347]::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}
.ncms-grid[data-v-178347]::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}
.ncms-grid[data-v-178347]::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
/* Estilos existentes */
.card-header[data-v-9d1e9e] {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    border: 1px solid #e2e3e5;
    margin: 10px 0;
    padding: 10px;
&:hover {
        box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
}
&.selected {
        background: #337ab7;
        color: white;
}
}
.btn-link[data-v-9d1e9e] {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    text-decoration: none;
    color: inherit;
&:focus {
        outline: none;
        box-shadow: none;
}
}
.ncm-title-container[data-v-9d1e9e],
.ncm-arrow-container[data-v-9d1e9e] {
    display: flex;
    gap: 20px;
    align-items: center;
}
.ncm-title[data-v-9d1e9e] {
    font-size: 18px;
    font-weight: 500;
}
.circle[data-v-9d1e9e] {
    display: inline-block;
    width: 10px;
    height: 10px;
    background-color: transparent;
    border-radius: 50%;
    border: 1px solid #337ab7;
&.selected {
        border: 1px solid white;
}
}
.blue-circle[data-v-9d1e9e] {
    background-color: #337ab7;
&.selected {
        background-color: white;
}
}

/* Novos estilos para seleção em massa */
.bulk-selection-header[data-v-9d1e9e] {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    /* background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 5px; */
    margin: 10px 0;
}
.select-all-container[data-v-9d1e9e] {
    display: flex;
    align-items: center;
    gap: 10px;
}
.select-all-label[data-v-9d1e9e] {
    margin: auto;
    font-weight: 500;
    color: #495057;
    cursor: pointer;
}
.bulk-homologate-btn[data-v-9d1e9e] {
    background-color: #337ab7;
    border-color: #2e6da4;
&:hover {
        background-color: #286090;
        border-color: #204d74;
}
&:disabled {
        background-color: #6c757d;
        border-color: #6c757d;
        cursor: not-allowed;
}
}
.ncm-checkbox-container[data-v-9d1e9e] {
    display: flex;
    align-items: center;
    margin-right: 15px;
    z-index: 10;
}
.ncm-checkbox[data-v-9d1e9e] {
    margin: 0;
    cursor: pointer;
}

/* Estilos do footer movidos para BulkSelectionFooter.vue */

/* Ajuste para o conteúdo principal quando o footer está visível */
#accordion[data-v-9d1e9e] {
    margin-bottom: 80px;
}

/* Input check box específico dessa tela, para retirar o margin que o bootstrap aplica */
.form-check-input[data-v-9d1e9e] {
    margin: 0;
}

/* Estilo para checkbox indeterminado */
.form-check-input[data-v-9d1e9e]:indeterminate {
    background-color: #007bff;
    border-color: #007bff;
}

/* Estilos para seleção de itens por página */
.items-per-page-container[data-v-9d1e9e] {
    display: flex;
    justify-content: flex-end;
    margin: 10px 0;
    padding: 0 10px;
}
.items-per-page-wrapper[data-v-9d1e9e] {
    display: flex;
    align-items: center;
    gap: 10px;
}
.items-per-page-label[data-v-9d1e9e] {
    margin: 0;
    font-weight: 500;
    color: #495057;
    white-space: nowrap;
}
.items-per-page-select[data-v-9d1e9e] {
    width: auto;
    min-width: 120px;
    padding: 5px 10px;
    font-size: 14px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background-color: white;
}