<style scoped>
.mx-2 {
    margin-left: 1.5rem;
    margin-right: 1.5rem;
}

.mb-2 {
    margin-bottom: 1.5rem;
}

/* Temporariamente SEM scoped e SEM :deep() */
.api-filled-border {
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 1.20rem rgba(13, 110, 253, 0.25) !important;
    transition: border-color .5s ease-out, box-shadow .5s ease-out;
}

.api-filled-border+.bootstrap-select>.dropdown-toggle {
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 1.20rem rgba(13, 110, 253, 0.25) !important;
    transition: border-color .5s ease-out, box-shadow .5s ease-out;
}

:deep(.api-filled-border) {
    /* Ou só .api-filled-border se global */
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 1.20rem rgba(13, 110, 253, 0.25) !important;
}
</style>

<script>
import Loading from 'vue-loading-overlay';
import CadItemAttr from './CadItemAttr.vue';

import 'vue-loading-overlay/dist/vue-loading.css';
import _ from "lodash";
import axios from "axios";

export default {
    name: 'CadItemAttrGroup',
    data() {
        return {
            key: 0,
            isLoading: false,
            fullPage: true,
            attrs: [],
            integra_diana_company: false,
            email_usuario: '',
            local_vue: this.local,
            alerts: false,
            registro_item: '',
            errorsOccurred : '',
            attrs_processados_diana: [],
        }
    },
    props: {
        addClass: {
            required: false,
            type: String
        },
        isEditable: {
            required: true,
            type: Boolean
        },
        local: {
            required: true,
            type: String
        },
        params: {
            required: true,
            type: Object
        },
    },
    methods: {
        reload() {
            // this.key++;
            this.load(true);
        },
        load(reload = false) {
            try {
                this.isLoading = true;

                if (!this.params) {
                    throw "Dados invalidos para exibicao dos atributos do item";
                }

                axios
                    .get(`${$base_url}cad_item_attr/ajax_get_attrs`, {
                        params: this.params
                    })
                    .then((response) => {
                        return response.data;
                    })
                    .then((data) => {
                        return data.data;
                    })
                    .then((data) => {
                        if (_.isEmpty(data)) {
                            throw "Não foi possivel receber uma resposta";
                        }

                        this.attrs = data.assocAttrs;

                        this.key++;

                        if (reload) {
                            swal("Sucesso!", "Os dados foram atualizados.", "success");
                        }


                        // Botão salvar externo
                        $('#save_association').prop("disabled", false);

                        // Animação loading externa
                        $('.loading').hide();

                        this.isLoading = false;

                    })
                    .catch((err) => {
                        const data = "";

                        if (err.length && err.response.length && err.response.data.length) {
                            data = err.response.data;
                        }

                        this.isLoading = false;
                    });

            } catch (err) {


                this.isLoading = false;
            }
        },

        processaCondicionadosDiana(item, idx, key) {
 
            let currentValue = this.attrs[key].condicionados[idx].atributo.dbdata.codigo;
            const isEmpty = currentValue === null || currentValue === undefined || currentValue === '' || (Array.isArray(currentValue) && currentValue.length === 0);
            if (!isEmpty) { return; }

            this.isDianaLoading = true;
            this.dangerMessage = false;
            const codigoParaEnviar = this.attrs[key].condicionados[idx].atributo.codigo;
            if (this.attrs_processados_diana.includes(`${codigoParaEnviar}`)) {
                return;
            }
            const integracaoApiUrl = `/wf/atributos/requestDiana`;

            let descricaoPayload = this.itemDescription || "N/A";
            if (descricaoPayload === "N/A" && typeof $ === 'function') { descricaoPayload = $('#ficha table tbody tr:eq(3) td').text().trim() || "N/A"; }
          //  console.log(`processaCondicionadosDiana 2 ${codigoParaEnviar} `);
            const payload = {
                "descricao":  this.registro_item,
                "codigo": codigoParaEnviar,
                "checar_info_na_desc": false,
                "busca_trechos": false,
                "busca_razao": false,
                "request_email": this.email_usuario
            };

            try {
                axios.post(integracaoApiUrl, payload).then((response) => {
                    this.attrs_processados_diana.push(`${codigoParaEnviar}`);
                    if (response.data.hasOwnProperty('valor')) {
                        let valorRecebido = response.data.valor;

                        if (valorRecebido !== null && valorRecebido !== undefined && String(valorRecebido).trim() !== '') {
    
                            if (valorRecebido == true || valorRecebido == "true" || valorRecebido == "1") {
                                valorRecebido = 1;
                            } else if (valorRecebido == false || valorRecebido == "false" || valorRecebido == "0") {
                                valorRecebido = 0;
                            }

                            if (!this.attrs[key].condicionados[idx].atributo.dbdata) this.$set(this.attrs[key].condicionados[idx].atributo, 'dbdata', {});
                            // $(`#${codigoParaEnviar}`).removeClass('api-filled-border').addClass('api-filled-border');

                            this.$set(this.attrs[key].condicionados[idx].atributo.dbdata, 'codigo', String(valorRecebido));
                            this.$set(this.attrs[key].condicionados[idx].atributo.dbdata, 'filledByApi', true);

                            this.changed = true;

                        } else {   }
                    } else {  }
                });

            } catch (error) {
                const errorMsg = error.response.data.erro || error.message || "Erro API Diana.";
                
            } finally {


                this.isDianaLoading = false;
                return;
            }
        },
        integrarDiana(isSecondRun = false) {
            console.log("acessando integrarDiana");
            let output = '';
            $('#aba-perguntas-respostas table tbody tr').each(function (index) {
                const pergunta = $(this).find('td').eq(2).text().trim();
                const resposta = $(this).find('td').eq(4).text().trim();
                const respostaFinal = (resposta === '-' ? '' : resposta);

                output += (index + 1) + ' - ' + pergunta + '.\nR: ' + respostaFinal + '\n\n';
            });

            let marca = ($('#marca').val() || '').trim();
            let material_constitutivo = ($('#material_constitutivo').val() || '').trim();
            let descricao_curta = ($('#descricao').val() || '').trim();
            let descricao_mercado_local = ($('#descricao_mercado_local').val() || '').trim();

            let dataSelectedCheckboxes = $('input.item_selected:checked');
            let dataPartNumber = "";

            if (dataSelectedCheckboxes && dataSelectedCheckboxes.length === 1) {
                dataPartNumber = dataSelectedCheckboxes.data('part-number');
            }

            let descricao_proposta_completa = ($('#descricao_proposta_completa').val() || '').trim();

            this.registro_item = descricao_proposta_completa + output + ' ' + 
                (marca ? `Marca: ${marca}. ` : '') +
                (dataPartNumber ? `Part Number: ${dataPartNumber}. ` : '') +
                (material_constitutivo ? `Material: ${material_constitutivo}. ` : '') +
                (descricao_curta ? `Descrição curta: ${descricao_curta}. ` : '') +
                (descricao_mercado_local ? `Descrição resumida: ${descricao_mercado_local}. ` : '');

            this.attrs_processados_diana = [];
            if (!this.attrs || this.attrs.length === 0) {
                if (typeof swal === 'function') swal("Atenção!", "Não há atributos carregados.", "warning");
                else alert("Não há atributos carregados.");
                return;
            }

            let attrsToRequest = this.attrs.filter(attr => {
                const value = attr.dbdata ? attr.dbdata.codigo : undefined;
                const isEmpty = value === null || value === undefined || value === '' || (Array.isArray(value) && value.length === 0);
                return isEmpty;
            });


            this.attrs.forEach((attr1, key) => {
                            let val_db = attr1.dbdata ? attr1.dbdata.codigo : null;
                            if (attr1.condicionados)
                            {
                                attr1.condicionados.forEach((item, idx) => {
                                    let val_cond = item.condicao.valor;

                                    if (val_cond == true || val_cond == "true" || val_cond == "1") {
                                        val_cond = 1;
                                    } else if (val_cond == false || val_cond == "false" || val_cond == "0") {
                                        val_cond = 0;
                                    }
                                   // console.log(`val_cond ${val_cond} val_db ${val_db} `);
                                    if (val_cond == val_db) {
                                        let containerId = `${item.atributo.codigo}`;
                                        let $inputElement = $(`#${containerId} input`);
                                        let $selectElement = $(`#${containerId} select`);

                                        if (($inputElement.val() != null && $inputElement.val() != undefined && $inputElement.val() != '') ||
                                        ($selectElement.val() != null && $selectElement.val != undefined && $selectElement.val() != '') 
                                        ){
                            
                                            attrsToRequest.push(item.atributo.codigo);
                                        }
 
                                
                                    } 

                    
       
                                });

                            }
                        });
            // this.attrs.forEach((attr1, key) => {
            //                 let val_db = attr1.dbdata.codigo;
            // if (attrsToRequest.length === 0) {
            //     if (this.attrs.condicionados && this.attrs.condicionados.length > 0) {
            //         this.attrs.condicionados.forEach((item, idx) => {
            //             let val_cond = item.condicao.valor;
            //             if (val_cond == true || val_cond == "true" || val_cond == "1") {
            //                 val_cond = 1;
            //             } else if (val_cond == false || val_cond == "false" || val_cond == "0") {
            //                 val_cond = 0;
            //             }
            //             attrsToRequest = attrsToRequest.filter(attr => attr.codigo != item.atributo.codigo);
            //         });
            //     }
            // }
            console.log(this.attrs);


            if (attrsToRequest.length === 0) {
                if (typeof swal === 'function') swal("Informação", "Todos os atributos já possuem um valor.", "info");
                else alert("Todos os atributos já possuem um valor.");
                return;
            }

            this.isLoading = true;

            const requestPromises = attrsToRequest.map(attr => {
                const codigoParaEnviar = attr.codigo;
                if (this.attrs_processados_diana.includes(`${codigoParaEnviar}`)) {
                return;
            }
                const integracaoApiUrl = `/wf/atributos/requestDiana`;

                const payload = {
                    "descricao":  this.registro_item,
                    "codigo": codigoParaEnviar,
                    "checar_info_na_desc": false,
                    "busca_trechos": false,
                    "busca_razao": false,
                    "request_email": this.email_usuario
                };
                
                if (codigoParaEnviar == undefined || 
                    codigoParaEnviar == null || 
                    codigoParaEnviar == '' || 
                    (Array.isArray(codigoParaEnviar) && codigoParaEnviar.length === 0)) {
                    return Promise.resolve({
                        codigo: codigoParaEnviar,
                        valor: null,
                        success: false,
                        error: "Código inválido"
                    });
                }

                return axios.post(integracaoApiUrl, payload)
                    .then(response => {
                        this.attrs_processados_diana.push(`${codigoParaEnviar}`);
                        if (response && response.data && response.data.hasOwnProperty('valor')) {
                            return {
                                codigo: codigoParaEnviar,
                                valor: response.data.valor,
                                success: true
                            };
                        } else {
                            return {
                                codigo: codigoParaEnviar,
                                valor: null,
                                success: false,
                                error: "Formato de resposta inválido (campo 'valor' ausente)"
                            };
                        }
                    })
                    .catch(error => {
                        return {
                            codigo: codigoParaEnviar,
                            valor: null,
                            success: false,
                            error: "Erro desconhecido na requisição"
                        };
                    });
            });

            Promise.all(requestPromises)
                .then(results => {
                    let updated = false;

                    results.forEach(result => {
                        if (result.success && result.valor !== null && result.valor !== undefined && String(result.valor).trim() !== '') {
                            try {
                                const codigoAttr = result.codigo;
                                let valorRecebido = String(result.valor);

                                if (valorRecebido == true || valorRecebido == "true" || valorRecebido == "1") {
                                    valorRecebido = 1;
                                } else if (valorRecebido == false || valorRecebido == "false" || valorRecebido == "0") {
                                    valorRecebido = 0;
                                }
                                
                                const containerSelector = '#' + CSS.escape(codigoAttr);
                                const $container = $(containerSelector);

                                const originalAttr = this.attrs.find(attr => attr.codigo === codigoAttr);
                                // if (originalAttr && originalAttr.dbdata) {
                                //     originalAttr.dbdata.codigo = valorRecebido;
                                // }

                                if (originalAttr) {
                                    if (!originalAttr.dbdata) {
                                        this.$set(originalAttr, 'dbdata', {});
                                    }

                                    this.$set(originalAttr.dbdata, 'codigo', valorRecebido);
                                    this.$set(originalAttr.dbdata, 'filledByApi', true); // se quiser manter o mesmo padrão
                                }


                                if ($container.length > 0) {
                                    let $targetElement = $container.find('select.form-control');
                                    let isSelect = false;

                                    if ($targetElement.length > 0) {
                                        isSelect = true;
                                    } else {
                                        $targetElement = $container.find('input.form-control');
                                        if ($targetElement.length > 0) {
                                            isSelect = false;
                                        }
                                    }

                                    if ($targetElement.length > 0) {
                                        const valorAtual = $targetElement.val();
                                        const isEmpty = valorAtual === null || valorAtual === undefined || String(valorAtual).trim() === '' || (Array.isArray(valorAtual) && valorAtual.length === 0);

                                        if (isEmpty) {
                                            let canUpdate = false;
                                            if (isSelect) {
                                                const optionExists = $targetElement.find(`option[value="${valorRecebido}"]`).length > 0;
                                                if (optionExists) canUpdate = true;
                                            } else {
                                                canUpdate = true;
                                            }

                                            if (canUpdate) {
                                                let valorDefinidoComSucesso = false;

                                                if (isSelect) {
                                                    if (typeof $targetElement.selectpicker === 'function') {
                                                        try {
                                                            $targetElement.selectpicker('val', valorRecebido);
                                                            valorDefinidoComSucesso = true;
                                                        } catch (e) {
                                                            $targetElement.val(valorRecebido).trigger('change');
                                                            if ($targetElement.val() == valorRecebido) valorDefinidoComSucesso = true;
                                                        }
                                                    } else {
                                                        $targetElement.val(valorRecebido).trigger('change');
                                                        if ($targetElement.val() == valorRecebido) valorDefinidoComSucesso = true;
                                                    }
                                                } else {
                                                    $targetElement.val(valorRecebido).trigger('input').trigger('change');
                                                    valorDefinidoComSucesso = true;
                                                }

                                                if (valorDefinidoComSucesso) {
                                                    if (!isSecondRun) {
                                                        $(`#${codigoAttr}`).removeClass('api-filled-border').addClass('api-filled-border');

                                                        if (isSelect) {
                                                            const $button = $targetElement.next('.bootstrap-select').find('button.dropdown-toggle');
                                                            if ($button.length > 0) {
                                                                $(`#${codigoAttr}`).removeClass('api-filled-border').addClass('api-filled-border');
                                                            }
                                                        }
                                                    }

                                                    updated = true;
                                                }
                                            }
                                        } else {
                                            if (!isSecondRun) {
                                                $(`#${codigoAttr}`).removeClass('api-filled-border');
                                            }
                                        }
                                    }
                                }
                            } catch (e) { console.warn(e); }
                        } else {
                            try {
                                $(`#${CSS.escape(result.codigo)}`).find('.form-control').removeClass('api-filled-border');
                            } catch (e) { console.warn(e); }
                        }
                    });

 
                    //     if (typeof swal === 'function') swal("Atenção!", `Integração concluída com erros parciais.`, "warning");
                    //     else alert(`Integração concluída com erros parciais.`);
                    // } else if (updated) {
                    //     if (!isSecondRun) {
                    //         if (typeof swal === 'function') swal("Sucesso!", "Sugestão Diana aplicada.", "success");
                    //         else alert("Sugestão Diana aplicada aos campos vazios.");
                    //     }
                    // } else {
                    //     if (!isSecondRun) {
                    //         if (typeof swal === 'function') swal("Informação", "Nenhum campo vazio precisou ser atualizado pela sugestão Diana.", "info");
                    //         else alert("Nenhum campo vazio precisou ser atualizado pela sugestão Diana.");
                    //     }
                    // }

                    this.isLoading = false;
                    if (!isSecondRun) {


                        // if (this.attr.condicionados) {
                        // // 
                        //     let val_db = this.attr.dbdata.codigo;
                
                        //     // console.log('this.attr',this.attr);
                        //     // console.log('this.attr.condicionados',this.attr.condicionados);
                        //     this.attr.condicionados.forEach((item, idx) => {
                        //         let val_cond = item.condicao.valor;

                        //         if (val_cond == true || val_cond == "true" || val_cond == "1") {
                        //             val_cond = 1;
                        //         } else if (val_cond == false || val_cond == "false" || val_cond == "0") {
                        //             val_cond = 0;
                        //         }
                        //         console.log(`val_cond ${val_cond} val_db ${val_db} item ${item}`);
                        //         if (val_cond == val_db) {
                        //             console.log(`val_cond ${val_cond} val_db ${val_db} item ${item}`);
                        //             this.processaCondicionadosDiana(item, idx);
                        //         } 

                        //     });

                        // }


                        this.attrs.forEach((attr1, key) => {
                            let val_db = attr1.dbdata.codigo;
                            if (attr1.condicionados)
                            {
                                attr1.condicionados.forEach((item, idx) => {
                                    let val_cond = item.condicao.valor;

                                    if (val_cond == true || val_cond == "true" || val_cond == "1") {
                                        val_cond = 1;
                                    } else if (val_cond == false || val_cond == "false" || val_cond == "0") {
                                        val_cond = 0;
                                    }
                                   // console.log(`val_cond ${val_cond} val_db ${val_db} `);
                                    if (val_cond == val_db) {
                              
                                        this.processaCondicionadosDiana(item, idx,key);
                                    } 

                    
                                    return;
                                });

                            }
                        });
                    }
                }).finally(() => {
                    swal("Sucesso!", "Sugestão Diana aplicada.", "success");
                });




            // if (this.alerts == false)
            // {
            //     this.alerts = true;
            //     if (errorsOccurred) {
            //         if (typeof swal === 'function') swal("Atenção!", `Integração concluída com erros parciais.`, "warning");
            //         else alert(`Integração concluída com erros parciais.`);
            //     } else if (updated) {
            //         if (!isSecondRun) {
            //             if (typeof swal === 'function') swal("Sucesso!", "Sugestão Diana aplicada.", "success");
            //             else alert("Sugestão Diana aplicada aos campos vazios.");
            //         }
            //     } else {
            //         if (!isSecondRun) {
            //             if (typeof swal === 'function') swal("Informação", "Nenhum campo vazio precisou ser atualizado pela sugestão Diana.", "info");
            //             else alert("Nenhum campo vazio precisou ser atualizado pela sugestão Diana.");
            //         }
            //     }
            // };
 


        }

    },
    beforeMount() {
        this.load();
    },
    mounted() {
        $('[data-toggle="tooltip"]').tooltip();

        axios
            .get('/wf/atributos/get_data_company', {
                diana_atributos: true
            })
            .then((response) => {
                this.integra_diana_company = response.data.integra_diana_company;
                this.email_usuario = response.data.email_usuario;
            })
            .catch((err) => {
                this.integra_diana_company = false;
                this.email_usuario = response.email_usuario;
            });


            if (this.params && this.params.id_item)
            {
                axios
                .post('/homologacao/get_entry_id_item', {
                    item: this.params.id_item,
                })
                .then((response) => {
                    this.registro_item = response.data.item;
                });

            }


             
    },
    components: {
        Loading,
        CadItemAttr
    },
}
</script>

<template>
    <div :class="addClass" :key="key" class="mx-2 mb-2">
 
        <button v-if="integra_diana_company && (attrs[0].codigo != 'null') && local == 'dados_tecnicos'" class="btn btn-primary" @click="() => integrarDiana()" :disabled="isLoading"
            style="margin-bottom: 15px;">
            Sugestão Diana
        </button>
        <loading :active.sync="isLoading" :is-full-page="fullPage"></loading>
        <div v-if="!attrs || (attrs && attrs[0].codigo == 'null')">
            <p class="text-center">Nenhum atributo encontrado para a <strong>NCM</strong> informada.</p>
        </div>
        <div v-else>
            <CadItemAttr v-for="(attr, index) in attrs" :key="index" :initialAttr="attr" :params="params"
                :isEditable="isEditable" :email_usuario="email_usuario" :integra_diana_company="integra_diana_company" :registro_item="registro_item"
                @updated="reload"></CadItemAttr>
        </div>
    </div>
</template>