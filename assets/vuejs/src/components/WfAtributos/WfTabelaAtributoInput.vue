<template>
    <div :class="addClass">
        <div v-show="preencherAtributos == true">
            <select v-if="
                attr.formaPreenchimento && attr.formaPreenchimento.toUpperCase() == 'LISTA_ESTATICA' &&
                !attr.multivalorado
            " @change="process('LISTA_ESTATICA', attr, $event)" v-model="value" :name="attr.codigo"
                :data-apresentacao="attr.nomeApresentacao" data-live-search="true" class="form-control btn-input"
                :required="attr.obrigatorio">
                <option :key="''" value="">SELECIONE</option>
                <option v-for="dominio in attr.dominio" :key="dominio.codigo" :value="dominio.codigo">
                    {{ dominio.descricao || '' }}
                </option>
            </select>

            <select v-if="
                attr.formaPreenchimento && attr.formaPreenchimento.toUpperCase() == 'LISTA_ESTATICA' &&
                attr.multivalorado
            " @change="process('LISTA_ESTATICA', attr, $event)" v-model="value" :name="attr.codigo"
                :data-apresentacao="attr.nomeApresentacao" data-actions-box="true" multiple data-live-search="true"
                title="SELECIONE" class="form-control btn-input  selectpicker" :required="attr.obrigatorio">
                <option v-for="dominio in attr.dominio" :key="dominio.codigo" :value="dominio.codigo">
                    {{ dominio.descricao || '' }}
                </option>
            </select>

            <input v-if="attr.formaPreenchimento && attr.formaPreenchimento.toUpperCase() == 'TEXTO'"
                @change="process('TEXTO', attr, $event)" @keyup="onHandleKeypress($event)" v-model="value"
                :placeholder="attr.nomeApresentacao" :name="attr.codigo" :data-apresentacao="attr.nomeApresentacao"
                :maxlength="attr.tamanhoMaximo" type="text" class="form-control btn-input"
                :required="attr.obrigatorio" />

            <input v-if="attr.formaPreenchimento && attr.formaPreenchimento.toUpperCase() == 'NUMERO_INTEIRO'"
                @change="process('NUMERO_INTEIRO', attr, $event)" @input="onHandleInputNumber($event)" v-model="value"
                :placeholder="attr.nomeApresentacao" :name="attr.codigo" :data-apresentacao="attr.nomeApresentacao"
                :maxlength="attr.tamanhoMaximo" type="number" class="form-control btn-input"
                :required="attr.obrigatorio" />

            <input v-if="attr.formaPreenchimento && attr.formaPreenchimento.toUpperCase() == 'NUMERO_REAL'"
                @change="process('NUMERO_REAL', attr, $event)" @input="onHandleInputNumberReal($event)"
                @keydown="preventInvalidInput($event)" v-model="value" :placeholder="attr.nomeApresentacao"
                :name="attr.codigo" :data-apresentacao="attr.nomeApresentacao" :maxlength="attr.tamanhoMaximo"
                type="text" class="form-control btn-input" :required="attr.obrigatorio" />

            <select v-if="
                attr.formaPreenchimento && attr.formaPreenchimento.toUpperCase() == 'BOOLEANO' &&
                !attr.atributoCondicionante
            " @change="process('BOOLEANO', attr, $event)" v-model="value" :name="attr.codigo"
                :data-apresentacao="attr.nomeApresentacao" data-live-search="true" placeholder="SELECIONE"
                class="form-control btn-input" :required="attr.obrigatorio">
                <option value="">SELECIONE</option>
                <option value="0">Não</option>
                <option value="1">Sim</option>
            </select>

            <select v-if="
                attr.formaPreenchimento && attr.formaPreenchimento.toUpperCase() == 'BOOLEANO' &&
                attr.atributoCondicionante
            " @change="process('BOOLEANO', attr, $event)" v-model="value" :name="attr.codigo"
                :data-apresentacao="attr.nomeApresentacao" data-live-search="true" placeholder="SELECIONE"
                class="form-control btn-input" :required="attr.obrigatorio">
                <option value="">SELECIONE</option>
                <option value="0">Não</option>
                <option value="1">Sim</option>
            </select>
        </div>

        <div v-if="preencherAtributos == false">

      <div v-if="
        attr.formaPreenchimento && attr.formaPreenchimento.toUpperCase() == 'LISTA_ESTATICA' &&
        !attr.multivalorado
      ">
        {{ findValueSelectedById(value, attr.dominio) }}
      </div>
      <div v-if="
        attr.formaPreenchimento && attr.formaPreenchimento.toUpperCase() == 'LISTA_ESTATICA' &&
        attr.multivalorado
      ">
        {{ value || '(vazio)' }}
      </div>
      <div v-if="
        attr.formaPreenchimento && (attr.formaPreenchimento.toUpperCase() == 'TEXTO' ||
          attr.formaPreenchimento.toUpperCase() == 'NUMERO_INTEIRO' ||
          attr.formaPreenchimento.toUpperCase() == 'NUMERO_REAL')
      ">
        {{ value || '(vazio)' }}
      </div>

      <div v-if="attr.formaPreenchimento && attr.formaPreenchimento.toUpperCase() == 'BOOLEANO'">
        {{
          String(value) == '1'
            ? 'Sim'
            : String(value) == '0'
              ? 'Não'
              : '(vazio)'
        }}
      </div>
    </div>
  </div>
</template>

<script>
import _ from 'lodash';

const VisionViewEnum = Object.freeze({
    HOMOLOG_VIEW: 'homolog_view',
    FILL_VIEW: 'fill_view',
    FILL_HOMOLOG_VIEW: 'fill_homolog_view',
});

export default {
  name: 'WfTabelaAtributoInput',
  props: {
    addClass: {
      required: false,
      type: String,
    },
    initialAttr: {
      required: true,
      type: [Array, Object],
    },
    initialValue: {
      required: true,
      type: String,
    },
    idItem: {
      required: true,
      type: Number,
    },
    permissionType: {
      required: true,
      default: String,
    },
    homologar_atributos: {
      required: true,
      type: Boolean,
    },
    preencher_atributos: {
      required: true,
      type: Boolean,
    },
    diana_atributos: {
      required: true,
      type: Boolean,
    },
    movimentar_itens: {
      required: true,
      type: Boolean,
    },
  },
  data() {
    return {
      attr: null,
      value: '',
      currentLength: 0,
      visionViewEnum: VisionViewEnum,
      preencherAtributos: null,
      homologarAtributos: null,
      movimentarItens: null,
      diana_atributos: null,
    };
  },
  computed: {
    totalDigits() { return this.attr && this.attr.tamanhoMaximo; }, // Adicionado check
    inputType() { return (this.attr && this.attr.formaPreenchimento) ? this.attr.formaPreenchimento.toUpperCase() : 'TEXTO'; }, // Adicionado check
    isMultiSelect() { return this.attr && this.attr.multivalorado === true; }, // Adicionado check
    decimalPlaces() { return (this.attr && this.attr.casasDecimais) || 0; }, // Adicionado check
    preencherAtributos() { return this.preencher_atributos; } // Usa prop diretamente
  },
  watch: {
    initialValue: { // REMOVIDO immediate: true
      handler(newValue, oldValue) {
        // --- ADICIONAR GUARDA ---
        if (!this.attr) {
          console.warn(`WATCH initialValue ${this.idItem}: Attr não definido ainda, pulando.`);
          return; // Não executa se attr não está pronto
        }
        // ------------------------

        let processedValue = this.processValueForType(newValue);
        if (!_.isEqual(this.value, processedValue)) {
          this.value = processedValue;

          this.$nextTick(() => { this.refreshSelectPicker(); });
          this.$nextTick(() => {
            this.$emit("changedValues", { value: this.value, attrCodigo: this.attr.codigo, itemId: this.idItem });
            this.handleNewColumns(this.value);
          });

        }
      }
      // immediate: true // REMOVIDO
    },
    initialAttr: { // REMOVIDO immediate: true
      handler(newAttr, oldAttr) {
        if (!_.isEqual(newAttr, oldAttr)) {
          this.attr = _.cloneDeep(newAttr);
          this.value = this.processValueForType(this.initialValue);
          this.updateCurrentLength();
          this.$nextTick(() => { this.refreshSelectPicker(); });
        }
      },
      deep: true
      // immediate: true // REMOVIDO
    }
  }, // Fim watch
  methods: {
    processValueForType(val) {
      let processed = val;
      const inputType = (this.attr && this.attr.formaPreenchimento) ? this.attr.formaPreenchimento.toUpperCase() : 'TEXTO'; // Usa computed property com validação
      const isMulti = this.isMultiSelect; // Usa computed property
      // Lógica de conversão (Booleano para '1'/'0'/'', Multivalor para Array, etc.)
      if (isMulti && typeof processed === 'string' && processed !== '') processed = processed.split(",");
      else if (isMulti && !processed) processed = [];
      else if (inputType === 'BOOLEANO') { if (processed === true || processed === '1' || processed === 1) processed = '1'; else if (processed === false || processed === '0' || processed === 0) processed = '0'; else processed = ''; }
      else if (processed === null || processed === undefined || processed === '') processed = '';
      if (!Array.isArray(processed)) processed = String(processed); // Garante string ou array
      return processed;
    },
    updateCurrentLength() {
      if (this.inputType === 'TEXTO' && this.totalDigits) {
        this.currentLength = this.totalDigits - String(this.value || '').length;
      } else { this.currentLength = 0; }
    },
    refreshSelectPicker() {
      if (this.inputType === 'LISTA_ESTATICA' || this.inputType === 'BOOLEANO') {
        if (typeof $ !== 'undefined' && $.fn.selectpicker) {
          const $select = $(this.$el).find('select.selectpicker');
          if ($select.length > 0 && $select.data('selectpicker')) {
            try { $select.selectpicker('refresh'); } catch (e) { console.warn("Erro refresh selectpicker:", e) }
          }
        }
      }
    },
    checkAndRemoveHighlight(event) {
      if (event && event.target) {
        const element = event.target;
        if ($(element).hasClass('valor-atualizado')) {
          console.log(`Removendo 'valor-atualizado' de ${element.id || element.name} (dentro de WfTabelaAtributoInput)`);
          $(element).removeClass('valor-atualizado');
          // this.$emit('highlight-removed', element.id || element.name);
        }
      }
    },
    findValueSelectedById(id, list) {

            const findValue = list.find((item) => item.codigo == id);
            return findValue != undefined && findValue != '' && findValue != null
                ? findValue.descricao
                : '(vazio)';
        },
        fillValue({ initialValue, currentValue }, event = null) {

      if (event) {
        const valoresSelecionados = Array.from(event.target.selectedOptions).map(opt => opt.value);
      }

      if (!this.value) {
        this.handleDeleteColumns('');
      }

      if (this.currentValue != currentValue) {
        this.currentValue = currentValue;
      }

            if (this.attr.dbdata[this.idItem]) {
                this.attr.dbdata[this.idItem].codigo = currentValue;
            }

      if (this.attr.listaSubatributos) {
        this.handleNewColumns(this.value);
      }

      if (this.attr.atributoCondicionante) {
        this.handleNewColumns(this.value);
      }

            this.$emit('changedValues', { attr: this.attr, itemId: this.idItem });
        },

        preventInvalidInput(event) {
            this.checkAndRemoveHighlight(event);
            const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '-', '.', ','];
            const isCopyPaste = event.ctrlKey || event.metaKey && event.key === 'v';

            const inputValue = this.value || '';

      if (event.key === '-') {
        // Permitir '-' apenas no início e se não houver outro '-'
        if (inputValue.includes('-')) { // Removido a condição inputValue.length > 0
          event.preventDefault();
          return;
        }
      } else if (event.key === ',') {
        // Permitir apenas um '.' ou ',' e substituir ',' por '.'
        if (inputValue.includes('.') || inputValue.includes(',')) {
          event.preventDefault();
          return;
        }
      } else if (!allowedKeys.includes(event.key) && !isCopyPaste) {
        // Impedir qualquer outra tecla que não seja permitida
        event.preventDefault();
        return;
      }
    },

        onHandleInputNumberReal(event) {
            this.checkAndRemoveHighlight(event);
            let str = this.value;

      // Remover todos os sinais negativos, exceto o primeiro
      const firstHyphenIndex = str.indexOf('-');
      if (firstHyphenIndex !== -1) {
        str = str.substring(0, firstHyphenIndex + 1) + str.substring(firstHyphenIndex + 1).replace(/-/g, '');
      }

            // Expressão regular para validar números com um sinal negativo opcional no início
            const regex = /^-?\d*(\.\d*)?$/;

      // Se a string não corresponder à regex, ela é processada
      if (!regex.test(str)) {
        // Remover caracteres inválidos, exceto o primeiro '-'
        str = str.replace(/[^0-9.,-]/g, '');
        const firstHyphen = str.indexOf('-');
        if (firstHyphen > 0) {
          str = str.replace(/-/g, ''); // Remove todos os sinais negativos se não for o primeiro
        }

        // Substituir vírgula por ponto e garantir que haja apenas um ponto decimal
        str = str.replace(/,/g, '.').replace(/(\..*)\./g, '$1');
      }

      // Permitir valores iniciais básicos
      if (str === '' || str === '-' || str === '.') {
        this.value = str;
        return;
      }

      // Ajustar casos como "-." e "."
      if (str.startsWith('-') && str.length > 1 && str[1] === '.') {
        str = '-0' + str.substring(1);
      } else if (str.startsWith('.')) {
        str = '0' + str;
      }

            // Separar parte inteira e decimal
            let [integerPart, decimalPart] = str.split('.');

            // Verificar se o número é negativo
            const isNegative = integerPart.startsWith('-');

            // Calcular o tamanho máximo da parte inteira
            let maxIntegerLength = this.attr.tamanhoMaximo - this.attr.casasDecimais;

      // Se houver parte decimal, subtrair 1 do tamanho máximo para a vírgula
      if (this.attr.casasDecimais > 0) {
        maxIntegerLength -= 1;
      }

      // Se o número for negativo, adicionar 1 ao tamanho máximo (não contabilizar o '-')
      if (isNegative) {
        maxIntegerLength++;
      }

            // Limitar a parte inteira
            integerPart = integerPart.slice(0, maxIntegerLength);

      // Limitar a parte decimal
      const maxDecimalLength = this.attr.casasDecimais;
      if (decimalPart && maxDecimalLength !== null) {
        decimalPart = decimalPart.slice(0, maxDecimalLength);
      }

            // Recompor a string
            str = decimalPart !== undefined ? `${integerPart}.${decimalPart}` : integerPart;

            this.value = str; // Atualiza o valor diretamente

      this.$emit('onHandleInputNumber', {
        currentValue: this.value,
      });
    },
    onHandleChange(tipo) {

            if (tipo == 'NUMERO_INTEIRO') {

        const num = Math.round(parseFloat(this.value));
        if (!isNaN(num)) {
          this.value = num.toString();
        }
      }
      if (tipo == 'NUMERO_REAL') {
        // if (this.value !== '' && this.value !== '-' && this.value !== '.') {
        //   let num = parseFloat(this.value);
        //   if (!isNaN(num)) {
        //     this.value = num.toFixed(this.attr.casasDecimais);
        //   }
        // }
      }
      this.fillValue({
        currentValue: this.value,
      });
    },
    onHandleKeypress(event) {
      this.checkAndRemoveHighlight(event);
      this.currentLength = this.attr.tamanhoMaximo - this.value.length;
      this.fillValue({
        currentValue: this.value,
      });
      // this.$emit('onHandleKeypress', {
      //   currentValue: this.value,
      //   currentLength: this.currentLength,
      // });
    },
    process(value, attr, event) {
      this.checkAndRemoveHighlight(event);
      if (value == 'LISTA_ESTATICA') {
        this.onHandleSelect(event);
      }
      if (value == 'TEXTO') {
        this.onHandleChange(attr.formaPreenchimento && attr.formaPreenchimento.toUpperCase());
      }
      if (value == 'NUMERO_INTEIRO') {
        this.onHandleChange(attr.formaPreenchimento && attr.formaPreenchimento.toUpperCase());
      }
      if (value == 'NUMERO_REAL') {
        this.onHandleChange(attr.formaPreenchimento && attr.formaPreenchimento.toUpperCase());
      }
      if (value == 'BOOLEANO') {
        this.onHandleSelectBoolean(event);
      }

            this.$emit('dataEdited', true);
        },
        onHandleSelect(event) {
            this.fillValue({
                currentValue: this.value
            }, event);
        },
        onHandleSelectBoolean(event) {
            this.attr.descricao = this.value == 1 ? 'Sim' : 'Não';
            this.fillValue({
                currentValue: this.value
            }, event);
        },
        onHandleInputNumber(event) {
            this.checkAndRemoveHighlight(event);
            let str = this.value;

            if (str === '' || str === '-' || str === '.') {
                this.displayValue = str;
                return;
            }

      // Se não há casas decimais definidas, permite apenas números inteiros
      if (/[^\d-]/.test(str)) {
        str = this.value.slice(0, -1);
      }

            if (str.length > this.totalDigits) {
                str = str.substring(0, this.totalDigits);
            }

            this.value = str;

      let num = Number(str);
      if (isNaN(num)) {
        this.displayValue = '';
        return;
      }

            this.displayValue = str;


      this.$emit('onHandleInputNumber', {
        currentValue: this.value,
      });
      // this.emitValueChange();
    },
    // onHandleInputNumber() {
    //   const sanatizedVal = this.value.replace(/\-/g, '');
    //   if (sanatizedVal.length > this.attr.tamanhoMaximo) {
    //     this.value = this.value.slice(0, this.attr.tamanhoMaximo);
    //   }
    //   this.$emit('onHandleInputNumber', {
    //     currentValue: this.value,
    //   });
    //   this.emitValueChange();
    // },
    // handleNewColumns(newValue) {
    //   const newColumns = [];
    //   const deleteColumns = [];
    //   const attIsConditions = {};

        //   if (!this.attr || !this.attr.codigo) {
        //     console.warn('Atributos necessários não disponíveis');
        //     return;
        //   }

        //   if (this.attr.listaSubatributos && this.attr.listaSubatributos.length > 0) {
        //     this.attr.listaSubatributos.forEach((item) => {
        //       if (item && item.codigo) {
        //         newColumns.push({
        //           ...item,
        //           codigo_pai: this.attr.codigo,
        //           dbdata: item.dbdata || {}
        //         });
        //       }
        //     });
        //   } else if (this.attr.condicionados && this.attr.condicionados.length > 0) {
        //     this.attr.condicionados.forEach((item) => {
        //       const isCondition = this.handleCondition(newValue, item.condicao);

        //       if (isCondition && item.atributo && item.atributo.codigo) {
        //         newColumns.push({
        //           ...item.atributo,
        //           codigo_pai: this.attr.codigo,
        //           dbdata: item.atributo.dbdata || {}
        //         });
        //       } else if (item.atributo && item.atributo.codigo) {
        //         deleteColumns.push({
        //           ...item.atributo,
        //           codigo_pai: this.attr.codigo
        //         });

        //         if (this.attr.dbdata) {
        //           Object.values(this.attr.dbdata).forEach((cond) => {
        //             const isConditionDbdata = this.handleCondition(
        //               cond.codigo,
        //               item.condicao
        //             );

        //             if (!attIsConditions[item.atributo.codigo]) {
        //               attIsConditions[item.atributo.codigo] = [];
        //             }
        //             attIsConditions[item.atributo.codigo].push(isConditionDbdata);
        //           });
        //         }
        //       }
        //     });
        //   }

        //   console.log('conditions: ', {
        //     newItens: newColumns,
        //     deleteItens: deleteColumns
        //   });

        //   if (newColumns.length > 0 || deleteColumns.length > 0) {
        //     this.$nextTick(() => {
        //       this.$emit('handleNewColumns', {
        //         newItens: newColumns,
        //         deleteItens: deleteColumns,
        //         arrayIsConditions: attIsConditions,
        //         idItem: this.idItem
        //       });
        //     });
        //   }
        // },

        handleDeleteColumns(newValue) {
            const deleteColumns = [];
            const attIsConditions = {};

            if (!this.attr || !this.attr.codigo) {
                console.warn('Atributos necessários não disponíveis');
                return;
            }

            // Função auxiliar para registrar uma coluna para deleção
            const markForDeletion = (item) => {
                if (item && item.codigo) {
                    deleteColumns.push(item.codigo);
                }
            };

            // Caso 1: Lista de subatributos
            if (this.attr.listaSubatributos && this.attr.listaSubatributos.length > 0) {
                if (!newValue) {
                    this.attr.listaSubatributos.forEach(markForDeletion);
                }
            }

            // Caso 2: Atributos condicionados
            else if (this.attr.condicionados && this.attr.condicionados.length > 0) {
                this.attr.condicionados.forEach(item => {
                    if (!item.atributo || !item.atributo.codigo) return;

                    const isCondition = this.handleCondition(newValue, item.condicao);

                    if (!isCondition) {
                        // Armazenar a informação de condição
                        if (!attIsConditions[item.atributo.codigo]) {
                            attIsConditions[item.atributo.codigo] = [];
                        }

                        let shouldKeepColumn = false;

                        // Verificar se algum outro item ainda precisa desta coluna
                        if (this.attr.dbdata) {
                            Object.keys(this.attr.dbdata).forEach(key => {
                                const cond = this.attr.dbdata[key];
                                const isConditionDbdata = this.handleCondition(cond.codigo, item.condicao);
                                attIsConditions[item.atributo.codigo].push(isConditionDbdata);

                                if (isConditionDbdata) {
                                    shouldKeepColumn = true;
                                }
                            });
                        }

                        // Se nenhum item precisa da coluna, marca para exclusão
                        if (!shouldKeepColumn) {
                            markForDeletion(item.atributo);
                        }
                    }
                });
            }

            // Emitir apenas se houver colunas a excluir ou condições aplicadas
            if (deleteColumns.length > 0 || Object.keys(attIsConditions).length > 0) {
                this.$nextTick(() => {
                    this.$emit('handleNewColumns', {
                        newItens: [], // apenas remoção agora
                        deleteItens: deleteColumns,
                        arrayIsConditions: attIsConditions,
                        idItem: this.idItem
                    });
                });
            }
        },

        handleNewColumns(newValue) {
            const newColumns = [];
            const deleteColumns = [];
            const attIsConditions = {};

            if (!this.attr || !this.attr.codigo) {
                console.warn('Atributos necessários não disponíveis');
                return;
            }

      // Função auxiliar para adicionar uma coluna
      const addColumn = (item, parentCode) => {
        if (item && item.codigo) {
          return {
            ...item,
            codigo_pai: parentCode,
            dbdata: item.dbdata || {}
          };
        }
        return null;
      };

      // Caso 1: Lista de subatributos
      if (this.attr.listaSubatributos && this.attr.listaSubatributos.length > 0) {
        this.attr.listaSubatributos.forEach(item => {
          const column = addColumn(item, this.attr.codigo);
          if (column) {
            if (newValue) {
              newColumns.push(column);
            } else {
              deleteColumns.push(column.codigo);
            }
          }
        });
      }
      // Caso 2: Atributos condicionados
      else if (this.attr.condicionados && this.attr.condicionados.length > 0) {
        this.attr.condicionados.forEach(item => {
          if (!item.atributo || !item.atributo.codigo) return;

          const isCondition = this.handleCondition(newValue, item.condicao);

          if (isCondition) {
            const column = addColumn(item.atributo, this.attr.codigo);
            if (column) newColumns.push(column);
          } else {
            // Armazenar a informação de condição
            if (!attIsConditions[item.atributo.codigo]) {
              attIsConditions[item.atributo.codigo] = [];
            }

            let shouldKeepColumn = false;

            // Verificar se algum outro item precisa desta coluna
            if (this.attr.dbdata) {
              Object.keys(this.attr.dbdata).forEach(key => {
                const cond = this.attr.dbdata[key];
                const isConditionDbdata = this.handleCondition(cond.codigo, item.condicao);
                attIsConditions[item.atributo.codigo].push(isConditionDbdata);

                if (isConditionDbdata) {
                  shouldKeepColumn = true;
                }
              });
            }

                        // Se nenhum item precisa da coluna, adiciona à lista de deleção
                        if (!shouldKeepColumn) {
                            deleteColumns.push(item.atributo.codigo);
                        }
                    }
                });
            }

      // Emitir o evento com as informações de novas colunas e deleções
      if (newColumns.length > 0 || deleteColumns.length > 0 || Object.keys(attIsConditions).length > 0) {
        this.$nextTick(() => {
          this.$emit('handleNewColumns', {
            newItens: newColumns ? newColumns : [],
            deleteItens: deleteColumns,
            arrayIsConditions: attIsConditions,
            idItem: this.idItem
          });
        });
      }
    },
    handleCondition(valueSelected, condition) {

      if (!valueSelected) {
        return false;
      }
      const evaluateCondition = (value, cond) => {
        const operator = cond.operador;
        const targetValue = cond.valor;

                switch (operator) {
                    case '==':
                        return value == targetValue;
                    case '===':
                        return value === targetValue;
                    case '!=':
                        return value != targetValue;
                    case '!==':
                        return value !== targetValue;
                    case '>':
                        return value > targetValue;
                    case '<':
                        return value < targetValue;
                    case '>=':
                        return value >= targetValue;
                    case '<=':
                        return value <= targetValue;
                    default:
                        throw new Error('Operador desconhecido: ' + operator);
                }
            };

            const evaluateNestedCondition = (value, cond) => {
                const currentResult = evaluateCondition(value, cond);

                if (cond.condicao) {
                    const nextResult = evaluateNestedCondition(value, cond.condicao);
                    const composition = cond.composicao || '&&';

                    if (composition === '||') {
                        return currentResult || nextResult;
                    } else if (composition === '&&') {
                        return currentResult && nextResult;
                    } else {
                        throw new Error('Composição desconhecida: ' + composition);
                    }
                }

                return currentResult;
            };

            let valueSelectedFormat = valueSelected;

      if (this.attr.formaPreenchimento && this.attr.formaPreenchimento.toUpperCase() === 'COMPOSTO') {
        return true;
      }

      if (
        this.attr.formaPreenchimento && this.attr.formaPreenchimento.toUpperCase() === 'BOOLEANO' &&
        (valueSelectedFormat > 0 ||
          valueSelectedFormat == 'Sim' ||
          valueSelectedFormat == 'SIM' ||
          valueSelectedFormat == 'sim')
      ) {
        valueSelectedFormat = 'true';
      }

      if (this.attr.atributoCondicionante) {
        if (
          this.attr.formaPreenchimento && this.attr.formaPreenchimento.toUpperCase() === 'BOOLEANO' &&
          valueSelected == '0'
        ) {
          valueSelectedFormat = 'false';
        }
      }

      return evaluateNestedCondition(valueSelectedFormat, condition);
    },
    formatInputInteger() {
      let str = String(this.value || '');
      str = str.replace(/[^\d-]/g, ''); if (str.indexOf('-') > 0 || str.split('-').length - 1 > 1) str = (str.startsWith('-') ? '-' : '') + str.replace(/-/g, ''); if (this.totalDigits && str.length > this.totalDigits) str = str.substring(0, this.totalDigits);
      if (this.value !== str) this.value = str;
    },
    formatInputReal() {
      let str = String(this.value || '');
      str = str.replace(/,/g, '.').replace(/[^\d.-]/g, ''); const parts = str.split('.'); if (parts.length > 2) str = parts[0] + '.' + parts.slice(1).join(''); const hyphens = str.split('-').length - 1; if (hyphens > 1 || (hyphens === 1 && str.indexOf('-') !== 0)) str = (str.startsWith('-') ? '-' : '') + str.replace(/-/g, ''); const decimalParts = str.split('.'); if (decimalParts.length > 1 && this.decimalPlaces >= 0) { decimalParts[1] = decimalParts[1].slice(0, this.decimalPlaces); str = decimalParts.join('.'); }
      if (this.value !== str) this.value = str;
    },

    // --- Handlers de Eventos do Template (Emitindo @changedValues) ---
    handleIntermediateInput(event) { // Chamado por @input
      this.checkAndRemoveHighlight(event);
      if (this.inputType === 'NUMERO_INTEIRO') { this.formatInputInteger(); }
      else if (this.inputType === 'NUMERO_REAL') { this.formatInputReal(); }
      // Emite valor intermediário
      this.$emit('changedValues', { value: this.value, attrCodigo: this.attr.codigo, itemId: this.idItem });
      this.$emit('dataEdited', true); // Informa edição
    },
    handleTextKeyup(event) { // Chamado por @keyup de texto
      this.checkAndRemoveHighlight(event);
      this.updateCurrentLength();
      // Emite valor intermediário
      this.$emit('changedValues', { value: this.value, attrCodigo: this.attr.codigo, itemId: this.idItem });
      this.$emit('dataEdited', true);
    },
    handleFinalChange(event) { // Chamado por @change de TODOS
      this.checkAndRemoveHighlight(event);
      // Aplica formatação final, se necessário (Ex: Inteiro)
      if (this.inputType === 'NUMERO_INTEIRO') {
        let numVal = parseFloat(String(this.value || '').replace(',', '.'));
        if (!isNaN(numVal)) this.value = String(Math.round(numVal)); else this.value = '';
      }
      // Adicione aqui formatação final para NUMERO_REAL se precisar (ex: casas decimais)

      // Emite o valor FINAL para o pai
      this.$emit('changedValues', { value: this.value, attrCodigo: this.attr.codigo, itemId: this.idItem });
      this.$emit('dataEdited', true);
    },
  },
  beforeMount() {
    // Inicializa attr e value aqui
    this.attr = _.cloneDeep(this.initialAttr);
    this.value = this.processValueForType(this.initialValue);
    this.updateCurrentLength();
    this.preencherAtributos = this.preencher_atributos;

    this.attr = this.initialAttr;

    // Verifica se o atributo pai está presente e se a forma de preenchimento é 'COMPOSTO'
    if (this.attr.codigo_pai && this.initialAttr.formaPreenchimento && this.initialAttr.formaPreenchimento.toUpperCase() === 'COMPOSTO') {
      // Emite o evento para adicionar colunas com o atributo atual
      this.$nextTick(() => {
        this.$emit('handleNewColumns', {
          newItens: [this.attr],
          deleteItens: [],
          arrayIsConditions: {},
          idItem: this.idItem
        });
      });
    }

    if (!_.isEmpty(this.initialValue)) {
      this.value = this.initialValue;
    } else if (this.attr.formaPreenchimento && this.attr.formaPreenchimento.toUpperCase() == 'BOOLEANO') {
      this.value = '';
    }
    if (this.attr.multivalorado && typeof this.value === 'string') {
      this.value = this.initialValue.split(',');
    }

  },
  mounted() {
    if (this.attr.atributoCondicionante) {
      this.onHandleSelect();
    }

    if (this.attr.listaSubatributos) {
      this.onHandleSelect();
    }
    // Verificar a existência da propriedade 'formaPreenchimento' antes de acessá-la
    if (this.attr.formaPreenchimento && this.attr.formaPreenchimento.toUpperCase() == 'TEXTO') {
      this.onHandleKeypress();
    }
    $('.selectpicker').selectpicker();
    $('.selectpicker').selectpicker('refresh');

    this.preencherAtributos = this.preencher_atributos;
    this.homologarAtributos = this.homologar_atributos;
    this.movimentarItens = this.movimentar_itens;
    this.diana_atributos = this.diana_atributos;


    },
};

$(document).ready(function () {
  $(document).on('show.bs.select', '.selectpicker', function () {
    $('.table-atribute').css('min-height', '500px');
  });

  $(document).on('hide.bs.select', '.selectpicker', function () {
    $('.table-atribute').css('min-height', ''); // Remove o min-height inline
  });
});
</script>
