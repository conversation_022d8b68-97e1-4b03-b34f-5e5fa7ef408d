# Sistema de Preferências do Usuário

Este sistema fornece uma maneira elegante e centralizada de gerenciar preferências do usuário usando localStorage, com validação, fallbacks seguros e integração com Vue.js.

## Arquitetura

### 1. UserPreferences (Classe Utilitária)
- **Localização**: `assets/vuejs/src/utils/userPreferences.js`
- **Responsabilidade**: Gerenciar persistência no localStorage
- **Características**:
  - Validação de valores
  - Fallbacks seguros
  - Tratamento de erros
  - Métodos específicos para preferências conhecidas

### 2. UserPreferencesMixin (Mixin Vue)
- **Localização**: `assets/vuejs/src/mixins/userPreferencesMixin.js`
- **Responsabilidade**: Integração com componentes Vue
- **Características**:
  - Métodos reativos
  - Computed properties
  - Emissão de eventos

## Como Usar

### Uso Direto da Classe UserPreferences

```javascript
import UserPreferences from '../utils/userPreferences.js';

// Recuperar preferência específica
const itemsPerPage = UserPreferences.getWfAtributosItemsPerPage();

// Salvar preferência específica
UserPreferences.setWfAtributosItemsPerPage(25);

// Uso genérico
const value = UserPreferences.get('chave_personalizada');
UserPreferences.set('chave_personalizada', 'valor');
```

### Uso com Mixin em Componentes Vue

```javascript
import userPreferencesMixin from '../mixins/userPreferencesMixin.js';

export default {
  mixins: [userPreferencesMixin],
  
  data() {
    return {
      // Inicializar com preferência salva
      itemsPerPage: this.getWfAtributosItemsPerPage()
    };
  },
  
  methods: {
    onItemsPerPageChange() {
      // Salvar automaticamente quando mudar
      this.setWfAtributosItemsPerPage(this.itemsPerPage);
    }
  },
  
  // Ou usar computed property reativa
  computed: {
    itemsPerPage: {
      get() {
        return this.wfAtributosItemsPerPagePreference;
      },
      set(value) {
        this.wfAtributosItemsPerPagePreference = value;
      }
    }
  }
};
```

## Adicionando Novas Preferências

### 1. Definir a Chave e Validador

```javascript
// Em userPreferences.js
const PREFERENCE_KEYS = {
  WF_ATRIBUTOS_ITEMS_PER_PAGE: 'wf_atributos_items_per_page',
  NOVA_PREFERENCIA: 'nova_preferencia_key', // Adicionar aqui
};

const DEFAULT_VALUES = {
  [PREFERENCE_KEYS.NOVA_PREFERENCIA]: 'valor_padrao',
};

const VALIDATORS = {
  [PREFERENCE_KEYS.NOVA_PREFERENCIA]: (value) => {
    // Lógica de validação
    return typeof value === 'string' && value.length > 0;
  },
};
```

### 2. Criar Métodos Específicos

```javascript
// Em userPreferences.js
static getNovaPreferencia() {
  return this.get(PREFERENCE_KEYS.NOVA_PREFERENCIA);
}

static setNovaPreferencia(value) {
  return this.set(PREFERENCE_KEYS.NOVA_PREFERENCIA, value);
}
```

### 3. Adicionar ao Mixin (Opcional)

```javascript
// Em userPreferencesMixin.js
getNovaPreferencia() {
  return UserPreferences.getNovaPreferencia();
},

setNovaPreferencia(value) {
  return UserPreferences.setNovaPreferencia(value);
},
```

## Características Técnicas

### Validação
- Cada preferência pode ter um validador personalizado
- Valores inválidos retornam o valor padrão
- Logs de warning para valores inválidos

### Tratamento de Erros
- Try/catch em todas as operações de localStorage
- Fallbacks seguros para quando localStorage não está disponível
- Logs informativos para debugging

### Performance
- Operações síncronas (localStorage é síncrono)
- Validação apenas quando necessário
- Parsing inteligente de tipos (string → number/boolean)

### Segurança
- Validação de entrada
- Sanitização de valores
- Chaves padronizadas para evitar conflitos

## Exemplo Completo: WfAgrupamentoNcm

```javascript
import UserPreferences from '../../utils/userPreferences.js';

export default {
  data() {
    return {
      // Inicializar com preferência salva
      itemsPerPage: UserPreferences.getWfAtributosItemsPerPage()
    };
  },
  
  methods: {
    onItemsPerPageChange() {
      // Salvar preferência
      UserPreferences.setWfAtributosItemsPerPage(this.itemsPerPage);
      
      // Lógica adicional do componente
      if (this.selectedItemId) {
        this.reloadCurrentGroup();
      }
    }
  }
};
```

## Benefícios

1. **Centralização**: Toda lógica de preferências em um local
2. **Reutilização**: Fácil uso em múltiplos componentes
3. **Validação**: Garante integridade dos dados
4. **Fallbacks**: Comportamento previsível em caso de erro
5. **Tipagem**: Conversão automática de tipos
6. **Manutenibilidade**: Fácil adição de novas preferências
7. **Performance**: Operações otimizadas
8. **Debugging**: Logs informativos para desenvolvimento
