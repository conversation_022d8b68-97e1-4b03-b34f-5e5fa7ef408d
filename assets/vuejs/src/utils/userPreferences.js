/**
 * Utilitário para gerenciar preferências do usuário no localStorage
 * Centraliza a lógica de persistência e recuperação de configurações
 */

/**
 * Chaves para as preferências no localStorage
 */
const PREFERENCE_KEYS = {
  WF_ATRIBUTOS_ITEMS_PER_PAGE: 'wf_atributos_items_per_page',
  // Adicionar outras preferências aqui conforme necessário
};

/**
 * <PERSON><PERSON> padrão para as preferências
 */
const DEFAULT_VALUES = {
  [PREFERENCE_KEYS.WF_ATRIBUTOS_ITEMS_PER_PAGE]: 10,
};

/**
 * Validadores para as preferências
 */
const VALIDATORS = {
  [PREFERENCE_KEYS.WF_ATRIBUTOS_ITEMS_PER_PAGE]: (value) => {
    const validOptions = [10, 25, 50, 100];
    return validOptions.includes(parseInt(value, 10));
  },
};

/**
 * Classe para gerenciar preferências do usuário
 */
class UserPreferences {
  /**
   * Recupera uma preferência do localStorage
   * @param {string} key - Chave da preferência
   * @returns {any} Valor da preferência ou valor padrão
   */
  static get(key) {
    try {
      const stored = localStorage.getItem(key);
      if (stored !== null) {
        const parsed = this.parseValue(stored);
        
        // Validar se existe um validador para esta chave
        if (VALIDATORS[key] && !VALIDATORS[key](parsed)) {
          console.warn(`Valor inválido para preferência ${key}:`, parsed);
          return DEFAULT_VALUES[key];
        }
        
        return parsed;
      }
    } catch (error) {
      console.warn(`Erro ao recuperar preferência ${key}:`, error);
    }
    
    return DEFAULT_VALUES[key];
  }

  /**
   * Salva uma preferência no localStorage
   * @param {string} key - Chave da preferência
   * @param {any} value - Valor a ser salvo
   * @returns {boolean} True se salvou com sucesso, false caso contrário
   */
  static set(key, value) {
    try {
      // Validar se existe um validador para esta chave
      if (VALIDATORS[key] && !VALIDATORS[key](value)) {
        console.warn(`Tentativa de salvar valor inválido para ${key}:`, value);
        return false;
      }
      
      localStorage.setItem(key, value.toString());
      return true;
    } catch (error) {
      console.warn(`Erro ao salvar preferência ${key}:`, error);
      return false;
    }
  }

  /**
   * Remove uma preferência do localStorage
   * @param {string} key - Chave da preferência
   */
  static remove(key) {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.warn(`Erro ao remover preferência ${key}:`, error);
    }
  }

  /**
   * Limpa todas as preferências
   */
  static clear() {
    try {
      Object.values(PREFERENCE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });
    } catch (error) {
      console.warn('Erro ao limpar preferências:', error);
    }
  }

  /**
   * Converte string para o tipo apropriado
   * @param {string} value - Valor em string
   * @returns {any} Valor convertido
   */
  static parseValue(value) {
    // Tentar converter para número se for numérico
    if (/^\d+$/.test(value)) {
      return parseInt(value, 10);
    }
    
    // Tentar converter para boolean
    if (value === 'true') return true;
    if (value === 'false') return false;
    
    // Retornar como string
    return value;
  }

  /**
   * Métodos específicos para preferências conhecidas
   */
  
  /**
   * Recupera a preferência de itens por página para WF Atributos
   * @returns {number} Quantidade de itens por página
   */
  static getWfAtributosItemsPerPage() {
    return this.get(PREFERENCE_KEYS.WF_ATRIBUTOS_ITEMS_PER_PAGE);
  }

  /**
   * Salva a preferência de itens por página para WF Atributos
   * @param {number} value - Quantidade de itens por página
   * @returns {boolean} True se salvou com sucesso
   */
  static setWfAtributosItemsPerPage(value) {
    return this.set(PREFERENCE_KEYS.WF_ATRIBUTOS_ITEMS_PER_PAGE, value);
  }
}

// Exportar a classe e as constantes
export default UserPreferences;
export { PREFERENCE_KEYS, DEFAULT_VALUES };
