/**
 * Mixin para facilitar o uso de UserPreferences em componentes Vue
 * Fornece métodos reativos para gerenciar preferências do usuário
 */

import UserPreferences from '../utils/userPreferences.js';

export default {
  data() {
    return {
      // Dados reativos para preferências podem ser adicionados aqui se necessário
    };
  },

  methods: {
    /**
     * Métodos genéricos para preferências
     */
    
    /**
     * Recupera uma preferência
     * @param {string} key - Chave da preferência
     * @returns {any} Valor da preferência
     */
    getUserPreference(key) {
      return UserPreferences.get(key);
    },

    /**
     * Salva uma preferência
     * @param {string} key - Chave da preferência
     * @param {any} value - Valor a ser salvo
     * @returns {boolean} True se salvou com sucesso
     */
    setUserPreference(key, value) {
      return UserPreferences.set(key, value);
    },

    /**
     * Remove uma preferência
     * @param {string} key - Chave da preferência
     */
    removeUserPreference(key) {
      UserPreferences.remove(key);
    },

    /**
     * Métodos específicos para WF Atributos
     */
    
    /**
     * Recupera a preferência de itens por página para WF Atributos
     * @returns {number} Quantidade de itens por página
     */
    getWfAtributosItemsPerPage() {
      return UserPreferences.getWfAtributosItemsPerPage();
    },

    /**
     * Salva a preferência de itens por página para WF Atributos
     * @param {number} value - Quantidade de itens por página
     * @returns {boolean} True se salvou com sucesso
     */
    setWfAtributosItemsPerPage(value) {
      return UserPreferences.setWfAtributosItemsPerPage(value);
    },

    /**
     * Método reativo para atualizar preferência e emitir evento
     * @param {string} key - Chave da preferência
     * @param {any} value - Novo valor
     * @param {string} eventName - Nome do evento a ser emitido (opcional)
     */
    updateUserPreference(key, value, eventName = null) {
      const success = this.setUserPreference(key, value);
      
      if (success && eventName) {
        this.$emit(eventName, { key, value });
      }
      
      return success;
    },
  },

  /**
   * Computed properties para preferências comuns
   */
  computed: {
    /**
     * Preferência reativa de itens por página para WF Atributos
     */
    wfAtributosItemsPerPagePreference: {
      get() {
        return this.getWfAtributosItemsPerPage();
      },
      set(value) {
        this.setWfAtributosItemsPerPage(value);
      }
    },
  },
};
