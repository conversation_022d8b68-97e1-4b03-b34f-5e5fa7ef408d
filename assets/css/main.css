body *,
html * {
  -webkit-font-smoothing: antialiased;
}

.badge-item-similar {
  background-color: #af0000;
  font-size: 14px;
}
.navbar-inner,
.navbar-header {
  min-height: 22px;
  line-height: 22px;
}
.navbar-nav > li > a {
  line-height: 22px;
  font-size: 12px;
}
.page-header {
  margin-top: 0;
}
/* navbar */
.navbar-default {
  background: url("../img/header/texture.png");
  background-color: #3165a7;
  border-color: #e7e7e7;
  border-bottom: 1px solid #163561;
  margin-bottom: 0;
}
/* title */
.navbar-default .navbar-brand {
  color: #eee;
  margin-right: 20px;
  padding: 12px 0;
  margin-left: -40px !important;
}
.navbar-default .navbar-brand:hover,
.navbar-default .navbar-brand:focus {
  color: #eee;
}
/* link */
.navbar-default .navbar-nav > li > a {
  color: #eee;
  text-shadow: 1px 1px 1px #444;
  padding-right: 13px;
  padding-left: 13px;
}
.navbar-default .navbar-nav > li > a:hover,
.navbar-default .navbar-nav > li > a:focus {
  color: #eee;
}
.navbar-default .navbar-nav > .active > a,
.navbar-default .navbar-nav > .active > a:hover,
.navbar-default .navbar-nav > .active > a:focus {
  color: #fff;
  background-color: #163561;
}
.navbar-default .navbar-nav > .open > a,
.navbar-default .navbar-nav > .open > a:hover,
.navbar-default .navbar-nav > .open > a:focus {
  color: #eee;
  background-color: #163561;
}
/* caret */
.navbar-default .navbar-nav > .dropdown > a .caret {
  border-top-color: #fff;
  border-bottom-color: #fff;
}
.navbar-default .navbar-nav > .dropdown > a:hover .caret,
.navbar-default .navbar-nav > .dropdown > a:focus .caret {
  border-top-color: #fff;
  border-bottom-color: #fff;
}
.navbar-default .navbar-nav > .open > a .caret,
.navbar-default .navbar-nav > .open > a:hover .caret,
.navbar-default .navbar-nav > .open > a:focus .caret {
  border-top-color: #fff;
  border-bottom-color: #fff;
}
/* mobile version */
.navbar-default .navbar-toggle {
  border-color: #ddd;
}
.navbar-default .navbar-toggle:hover,
.navbar-default .navbar-toggle:focus {
  background-color: #ddd;
}
.navbar-default .navbar-toggle .icon-bar {
  background-color: #ccc;
}

@media (max-width: 767px) {
  .navbar-default .navbar-nav .open .dropdown-menu > li > a {
    color: #fff;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
    color: #eee;
  }
}

/* Multi-level dropdown */
.dropdown-submenu {
  position: relative;
}

.dropdown-submenu > .dropdown-menu {
  top: 0;
  left: 100%;
  margin-top: -6px;
  margin-left: -1px;
  -webkit-border-radius: 0 6px 6px 6px;
  -moz-border-radius: 0 6px 6px;
  border-radius: 0 6px 6px 6x;
}

.dropdown-submenu:hover > .dropdown-menu {
  display: block;
}

.dropdown-submenu > a:after {
  display: block;
  content: " ";
  float: right;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 5px 0 5px 5px;
  border-left-color: #ccc;
  margin-top: 5px;
  margin-right: -10px;
}

.dropdown-submenu:hover > a:after {
  /*border-left-color: #fff;*/
}

.dropdown-submenu.pull-left {
  float: none;
}

.dropdown-submenu.pull-left > .dropdown-menu {
  left: -100%;
  margin-left: 10px;
  -webkit-border-radius: 6px 0 6px 6px;
  -moz-border-radius: 6px 0 6px 6px;
  border-radius: 6px 0 6px 6px;
}

/* subnav */
.subnav {
  background: #163561;
  /* margin-top: 50px;
  margin-bottom: 30px; */
}

.subnav .logo-container {
  height: 80px;
  padding: 10px 10px 0 10px;
}

.subnav .logo {
  position: absolute;
  /* right: 20px; */
  text-align: center;
  width: 200px;
  line-height: 60px;
  min-height: 80px;
  max-height: 80px;
  border-radius: 2px;
  background: #fff;
  margin-top: 10px;
  padding: 10px;
  box-shadow: 0px 1px 1px #888;
}

/* custom inclusion of right, left and below tabs */

.tabs-below > .nav-tabs,
.tabs-right > .nav-tabs,
.tabs-left > .nav-tabs {
  border-bottom: 0;
}

.tab-content > .tab-pane,
.pill-content > .pill-pane {
  display: none;
}

.tab-content > .active,
.pill-content > .active {
  display: block;
}

.tabs-below > .nav-tabs {
  border-top: 1px solid #ddd;
}

.tabs-below > .nav-tabs > li {
  margin-top: -1px;
  margin-bottom: 0;
}

.tabs-below > .nav-tabs > li > a {
  -webkit-border-radius: 0 0 4px 4px;
  -moz-border-radius: 0 0 4px 4px;
  border-radius: 0 0 4px 4px;
}

.tabs-below > .nav-tabs > li > a:hover,
.tabs-below > .nav-tabs > li > a:focus {
  border-top-color: #ddd;
  border-bottom-color: transparent;
}

.tabs-below > .nav-tabs > .active > a,
.tabs-below > .nav-tabs > .active > a:hover,
.tabs-below > .nav-tabs > .active > a:focus {
  border-color: transparent #ddd #ddd #ddd;
}

.tabs-left > .nav-tabs > li,
.tabs-right > .nav-tabs > li {
  float: none;
}

.tabs-left > .nav-tabs > li > a,
.tabs-right > .nav-tabs > li > a {
  min-width: 74px;
  margin-right: 0;
  margin-bottom: 3px;
}

.tabs-left > .nav-tabs {
  float: left;
  border-right: 1px solid #ddd;
}

.tabs-left > .nav-tabs > li > a {
  margin-right: -1px;
  -webkit-border-radius: 4px 0 0 4px;
  -moz-border-radius: 4px 0 0 4px;
  border-radius: 4px 0 0 4px;
}

.tabs-left > .nav-tabs > li > a:hover,
.tabs-left > .nav-tabs > li > a:focus {
  border-color: #eeeeee #dddddd #eeeeee #eeeeee;
}

.tabs-left > .nav-tabs .active > a,
.tabs-left > .nav-tabs .active > a:hover,
.tabs-left > .nav-tabs .active > a:focus {
  border-color: #ddd transparent #ddd #ddd;
  *border-right-color: #ffffff;
}

.tabs-right > .nav-tabs {
  float: right;
  margin-left: 19px;
  border-left: 1px solid #ddd;
}

.tabs-right > .nav-tabs > li > a {
  margin-left: -1px;
  -webkit-border-radius: 0 4px 4px 0;
  -moz-border-radius: 0 4px 4px 0;
  border-radius: 0 4px 4px 0;
}

.tabs-right > .nav-tabs > li > a:hover,
.tabs-right > .nav-tabs > li > a:focus {
  border-color: #eeeeee #eeeeee #eeeeee #dddddd;
}

.tabs-right > .nav-tabs .active > a,
.tabs-right > .nav-tabs .active > a:hover,
.tabs-right > .nav-tabs .active > a:focus {
  border-color: #ddd #ddd #ddd transparent;
  *border-left-color: #ffffff;
}

.custom-tabs {
  margin-bottom: 20px;
  float: left;
  width: 100%;
}

.text-muted-next {
  color: #cccccc !important;
  margin: 0;
}
.text-green-next {
  color: #26a343 !important;
  margin: 0;
}
.text-red-next {
  color: #af0000 !important;
  margin: 0;
}
.text-gray-next {
  color: #333 !important;
  margin: 0;
}

#total_to_answer {
  /*    margin-top: -2px;*/
  margin-left: 4px;
  font-weight: bold;
  /*    color: #F06868;*/
  margin-bottom: 2px;
}

*[data-href] {
  cursor: pointer;
}

#answer-block p {
  margin-bottom: 20px !important;
}

.panel-heading .accordion-toggle.collapsed:after {
  content: "\e080";
}

.panel-heading .accordion-toggle:after {
  font-family: "Glyphicons Halflings";
  content: "\e114";
  float: right;
  color: grey;
}

#collapse-tags {
  font-weight: bold;
  font-size: 14px;
}

.vincular-complete-sign {
  margin-left: 20px;
}

.vincular-complete-sign.done:before {
  color: #5cb85c;
  opacity: 1;
}

.vincular-complete-sign:before {
  color: #ffb8a3;
  opacity: 0.3;
}

.btn-file {
  position: relative;
  overflow: hidden;
}
.btn-file input[type="file"] {
  position: absolute;
  top: 0;
  right: 0;
  min-width: 100%;
  min-height: 100%;
  font-size: 100px;
  text-align: right;
  filter: alpha(opacity=0);
  opacity: 0;
  outline: none;
  background: white;
  cursor: inherit;
  display: block;
}

/* Novo Rodapé */

html {
  position: relative;
  min-height: 100%;
}
body {
  /* margin: 0 0 100px; */
}

footer {
  background: #0b1d4b;
  color: #fff;
  height: 50px;
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
}

footer .navbar-brand {
  max-height: 50px;
}

footer .footer-text {
  min-height: 50px;
  display: block;
  padding: 0;
  padding-top: 15px;
  padding-bottom: 15px;
  margin: 0;
}

footer .fa {
  margin-top: 12px;
  margin-bottom: 7px;
}

footer .fa-stack-2x {
  color: #ffffff;
}

footer .fa-stack-1x {
  color: #0b1e49;
}

.container-alerta-pendencias {
  width: 300px;
  position: fixed;
  bottom: 0;
  z-index: 999;
  display: none;
}

.container-alerta-pendencias .body {
  display: none;
}

.container-alerta-pendencias .top-bar {
  position: absolute;
  top: -40px;
  background-color: #d9534f;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  padding-top: 10px;
  padding-left: 30px;
  padding-right: 30px;
  cursor: pointer;
  width: 300px;
}

.container-alerta-pendencias.opened {
  box-shadow: 0px 0px 37px -3px rgba(0, 0, 0, 0.7);
  -webkit-box-shadow: 0px 0px 37px -3px rgba(0, 0, 0, 0.7);
  -moz-box-shadow: 0px 0px 37px -3px rgba(0, 0, 0, 0.7);
  -o-box-shadow: 0px 0px 37px -3px rgba(0, 0, 0, 0.7);
  -moz-border-radius-topleft: 5px;
  -moz-border-radius-topright: 5px;
}

.container-alerta-pendencias.opened .body {
  display: block;
  background: #ffffff;
  width: 300px;
  color: #000;
  font-size: 13px;
  padding-top: 10px;
  padding-left: 10px;
  padding-right: 10px;
}

.container-alerta-pendencias.opened .body .alerta {
  padding: 10px;
  border-bottom: 1px solid #e2e2e2;
  text-align: center;
}

.container-alerta-pendencias.opened .body .alerta p {
  margin: 0px;
}

.container-alerta-pendencias .top-bar .down-caret {
  float: right;
  margin-left: 15px;
  margin-top: 3px;
}

/* GRÁFICO EM CIRCULO DA VINCULAÇÃO */

.circle-graph .percentage {
  position: absolute;
  bottom: 50.5%;
  font-size: 30px;
  font-weight: normal;
  line-height: 15px;
  width: calc(100% - 30px);
  text-align: center;
}

.circle-graph .percentage span {
  font-size: 12px;
  /* margin-left: -10px; */
}

.circle-graph p {
  text-align: center;
  font-size: 18px;
  margin-top: 10px;
}

/* Tables NCM */
.table-ncm-tab {
  margin: 0;
}

.table-ncm-tab thead {
  background: #3165a7;
  color: #fff;
}

.table-ncm-tab > tbody > tr > .success,
.table-ncm-tab > tbody > .success > td,
.table-ncm-tab > tbody > .success > th {
  background-color: #dff0d8 !important;
}

/* Fixes */
.btn-margin-right {
  margin-right: 10px !important;
}
.btn-margin-left {
  margin-left: 10px !important;
}
small.file-info {
  font-size: 90%;
  margin-top: 10px;
  display: inline-block;
}

/* Table Sort Custom */
.table-sort tr th > a {
  cursor: hand;
  cursor: pointer;
  color: inherit;
}
.table-sort tr th > a:hover {
  color: black;
  text-decoration: none;
}
.table-sort tr th > a > .glyphicon {
  font-size: 11px;
}

/* BS WW */
.bootstrap-select.bs-large .dropdown-menu li:nth-child(odd) {
  background: #f7f7f7;
}

.bootstrap-select.bs-large .dropdown-menu li a span.text {
  word-wrap: break-word;
  white-space: normal;
}

#menu-dropdown-disable {
  max-height: 300px;
  width: 400px;
  overflow-y: auto;
}

.left-margin-10 {
  margin-left: 10px !important;
}

.left-margin-25 {
  margin-left: 25px;
}

.logo-diana img {
  max-width: 230px;
  margin-bottom: 30px;
}

.info-ex {
  position: absolute;
  right: 10px;
  top: 7px;
  color: #000;
}

.text-uppercase {
  text-transform: uppercase;
}

@media (min-width: 992px) {
  .modal-lg {
    width: auto;
  }
}

.checkbox {
  display: inline-block;
}

.col-md-12 > .well > .checkbox {
  display: block;
}

.input-group-btn {
  font-size: 12px;
}

.input-group-select button {
  border-top-right-radius: 0px !important;
  border-bottom-right-radius: 0px !important;
}

@keyframes spinner-border {
  to { transform: rotate(360deg); }
}

.spinner-border {
  display: inline-block;
  width: 15px;
  height: 15px;
  vertical-align: text-bottom;
  border: 2px solid white;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner-border .75s linear infinite;
}

.spinner-border-sm {
  width: 30px;
  height: 30px;
  border-width: 30px;
}

@keyframes spinner-grow {
  0% {
    transform: scale(0);
  }
  50% {
    opacity: 1;
  }
}

.spinner-grow {
  display: inline-block;
  width: 15px;
  height: 15px;
  vertical-align: text-bottom;
  background-color: transparent;
  border-radius: 50%;
  opacity: 0;
  animation: spinner-grow .75s linear infinite;
}

.spinner-grow-sm {
  width: 30px;
  height: 30px;
}

.m-0 {
  margin: 0px;
}
.m-1 {
  margin: 1px;
}
.m-2 {
  margin: 2px;
}
.m-3 {
  margin: 3px;
}
.m-4 {
  margin: 4px;
}
.m-5 {
  margin: 5px;
}
.mt-0 {
  margin-top: 0px;
}
.mt-1 {
  margin-top: 1px;
}
.mt-2 {
  margin-top: 2px;
}
.mt-3 {
  margin-top: 3px;
}
.mt-4 {
  margin-top: 4px;
}
.mt-5 {
  margin-top: 5px;
}
.mb-0 {
  margin-bottom: 0px;
}
.mb-1 {
  margin-bottom: 1px;
}
.mb-2 {
  margin-bottom: 2px;
}
.mb-3 {
  margin-bottom: 3px;
}
.mb-4 {
  margin-bottom: 4px;
}
.mb-5 {
  margin-bottom: 5px;
}
.ml-0 {
  margin-left: 0px;
}
.ml-1 {
  margin-left: 1px;
}
.ml-2 {
  margin-left: 2px;
}
.ml-3 {
  margin-left: 3px;
}
.ml-4 {
  margin-left: 4px;
}
.ml-5 {
  margin-left: 5px;
}
.mr-0 {
  margin-right: 0px;
}
.mr-1 {
  margin-right: 1px;
}
.mr-2 {
  margin-right: 2px;
}
.mr-3 {
  margin-right: 3px;
}
.mr-4 {
  margin-right: 4px;
}
.mr-5 {
  margin-right: 5px;
}

.p-0 {
  padding: 0px;
}
.p-1 {
  padding: 1px;
}
.p-2 {
  padding: 2px;
}
.p-3 {
  padding: 3px;
}
.p-4 {
  padding: 4px;
}
.p-5 {
  padding: 5px;
}
.pt-0 {
  padding-top: 0px;
}
.pt-1 {
  padding-top: 1px;
}
.pt-2 {
  padding-top: 2px;
}
.pt-3 {
  padding-top: 3px;
}
.pt-4 {
  padding-top: 4px;
}
.pt-5 {
  padding-top: 5px;
}
.pb-0 {
  padding-bottom: 0px;
}
.pb-1 {
  padding-bottom: 1px;
}
.pb-2 {
  padding-bottom: 2px;
}
.pb-3 {
  padding-bottom: 3px;
}
.pb-4 {
  padding-bottom: 4px;
}
.pb-5 {
  padding-bottom: 5px;
}
.pl-0 {
  padding-left: 0px;
}
.pl-1 {
  padding-left: 1px;
}
.pl-2 {
  padding-left: 2px;
}
.pl-3 {
  padding-left: 3px;
}
.pl-4 {
  padding-left: 4px;
}
.pl-5 {
  padding-left: 5px;
}
.pr-0 {
  padding-right: 0px;
}
.pr-1 {
  padding-right: 1px;
}
.pr-2 {
  padding-right: 2px;
}
.pr-3 {
  padding-right: 3px;
}
.pr-4 {
  padding-right: 4px;
}
.pr-5 {
  padding-right: 5px;
}

.d-flex {
  display: flex;
}

.justify-content-center {
  justify-content: center;
}

.align-items-center {
  justify-items: center;
}

.vertical-middle {
  vertical-align: middle !important;
}