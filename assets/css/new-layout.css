 
body {
    /* Define base font if needed */
}

/* --- Sidebar Styles --- */

/* --- Sidebar Styles --- */
#app-sidebar {
  position: fixed; /* Fixar na tela */
  left: 0;
  top: 0;
  width: 60px; /* Largura fixa da sidebar conforme Figma */
  padding: 0; /* Remover padding geral, controle interno */
  background-color: #163561; /* Cor de fundo fallback */
  background-image: url('/assets/icon/sidebar.svg'); /* Imagem de fundo */
  background-size: cover; /* Cobrir toda a área */
  background-repeat: no-repeat; /* Não repetir */
  background-position: center center; /* Centralizar imagem */
  display: flex;
  flex-direction: column;
  height: 100vh; /* Altura total da tela */
  z-index: 1031; /* Acima do conteúdo principal */
  align-items: center; /* Centralizar conteúdo da sidebar */
}

/* Container for scrollable icons */
#app-sidebar .sidebar-scrollable-icons-container {
    flex-grow: 1; /* Allow this container to grow */
    overflow-y: auto; /* Enable vertical scroll if needed */
    overflow-x: hidden;
    padding: 10px; /* Padding lateral interno */
    /* Ensure minimum gap to logo area */
    /* margin-bottom: 10px; /* Minimum gap to logo - REMOVED */
    /* Custom scrollbar (optional) */
    scrollbar-width: thin;
    scrollbar-color: #6c757d #f8f9fa;
}

#app-sidebar .sidebar-scrollable-icons-container::-webkit-scrollbar {
    width: 5px;
}

#app-sidebar .sidebar-scrollable-icons-container::-webkit-scrollbar-track {
    background: #f8f9fa;
}

#app-sidebar .sidebar-scrollable-icons-container::-webkit-scrollbar-thumb {
    background-color: #6c757d;
    border-radius: 10px;
    border: 1px solid #f8f9fa;
}

/* Logo container simulation (using padding/background position on #app-sidebar) */
/* The fixed height (128px) and padding (20px lateral, 10px bottom) for the logo area */
/* are managed by the background-position and background-size on #app-sidebar */
/* and the margin-bottom on .sidebar-scrollable-icons-container */

/* Icon list */
#app-sidebar .sidebar-nav-icons {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    align-items: center; /* Center items horizontally */
}

#app-sidebar .sidebar-nav-icons li {
    margin-bottom: 5px; /* Gap entre os botões */
    width: 100%; /* Ensure li takes full width for centering */
    display: flex;
    justify-content: center;
}

#app-sidebar .sidebar-nav-icons li:last-child {
    margin-bottom: 0;
}

/* Sidebar Button Links */
#app-sidebar .sidebar-nav-icons li a {
    display: inline-flex; /* Use inline-flex to wrap icon tightly */
    justify-content: center;
    align-items: center;
    padding: 8px; /* Padding interno do botão */
    border-radius: 8px; /* Consistent border-radius */
    background-color: rgba(0, 0, 0, 0.1); /* Default: Black/10% */
    transition: background-color 0.2s ease;
    position: relative; /* Needed for tooltip and badge */
    line-height: 1; /* Prevent extra space */
}

#app-sidebar .sidebar-nav-icons li a:hover {
    background-color: rgba(0, 0, 0, 0.4); /* Hover: Black/40% */
    text-decoration: none;
}

/* Current/Active state needs a specific class, e.g., 'active-sidebar-link' */
/* This class should be added dynamically via JS/PHP based on the current page */
#app-sidebar .sidebar-nav-icons li a.active-sidebar-link {
    background-color: rgba(0, 0, 0, 0.4); /* Current: Black/40% */
}

#app-sidebar .sidebar-nav-icons li a .custom-svg-icon {
    width: 20px;
    height: 20px;
    display: block;
    /* Default Icon Color: White/100% - SVGs might need fill="currentColor" or specific class */
    color: #ffffff; /* Assuming SVG uses currentColor */
    filter: brightness(0) invert(1); /* Alternative for forcing white on img SVGs */
}

/* Icon color for Current/Active state */
#app-sidebar .sidebar-nav-icons li a.active-sidebar-link .custom-svg-icon {
    color: #296292; /* Current Icon: Secondary/600 */
    filter: none; /* Remove white filter */
    /* If SVG doesn't use currentColor, specific CSS targeting SVG paths might be needed */
}

/* Tag for pending questions (Badge) */
#total_to_answer {
    position: absolute;
    top: 0px; /* Ajustar para ficar mais no canto */
    right: 0px; /* Ajustar para ficar mais no canto */
    left: auto;
    bottom: auto;
    padding: 0; /* Remover padding para controle com width/height */
    font-size: 10px;
    font-weight: bold;
    border-radius: 50%; /* Circular */
    background-color: #dc3545; /* Vermelho */
    color: white;
    display: none; /* Escondido por padrão, JS controla visibilidade */
    min-width: 16px; /* Largura mínima */
    height: 16px; /* Altura fixa */
    line-height: 16px; /* Centralizar texto verticalmente */
    text-align: center; /* Centralizar texto horizontalmente */
    box-sizing: border-box;
    z-index: 1; /* Para ficar sobre o ícone */
}

#total_to_answer .spinner-border {
    display: none; /* Hide spinner once count is loaded */
}

/* Sidebar Tooltip Styling (Simple Black Box via [title]) */
#app-sidebar .sidebar-nav-icons li a[title] {
    position: relative; /* Needed for absolute positioning of pseudo-elements */
}

/* Tooltip Box (via title attribute - REMOVED/COMMENTED OUT)
#app-sidebar .sidebar-nav-icons li a[title]::after {
    content: attr(title); 
    position: absolute;
    left: calc(100% + 10px); 
    top: 50%;
    transform: translateY(-50%);
    background-color: #1f1f1f; 
    color: #ffffff; 
    padding: 6px 12px; 
    border-radius: 16px; 
    font-size: 13px; 
    white-space: nowrap;
    z-index: 1100;
    opacity: 0; 
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0s linear 0.2s;
    pointer-events: none; 
}*/

/* Tooltip Arrow (via title attribute - REMOVED/COMMENTED OUT)
#app-sidebar .sidebar-nav-icons li a[title]::before {
    content: '';
    position: absolute;
    left: calc(100% + 4px); 
    top: 50%;
    transform: translateY(-50%);
    border-width: 6px;
    border-style: solid;
    border-color: transparent #1f1f1f transparent transparent; 
    z-index: 1101; 
    opacity: 0; 
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0s linear 0.2s;
    pointer-events: none;
}*/

/* Show tooltip on hover (via title attribute - REMOVED/COMMENTED OUT)
#app-sidebar .sidebar-nav-icons li a[title]:hover::before,
#app-sidebar .sidebar-nav-icons li a[title]:hover::after {
    opacity: 1;
    visibility: visible;
    transition: opacity 0.2s ease, visibility 0s linear 0s;
}*/

/* --- Modal Styles (Ajustes baseados no Figma - Modal_dados_tecnicos) --- */

/* Garante o overlay escurecido */
.modal-backdrop.show {
    opacity: 0.5 !important; /* Opacidade do fundo conforme referência */
    background-color: #000000 !important; /* Garante fundo preto */
}

/* Ajusta z-index para garantir que modal fique acima do overlay */
#mainNavigationModal {
    z-index: 1050 !important;
    padding-top: 0 !important; 
}

.modal-backdrop {
    z-index: 1040 !important;
}

/* Ajusta o posicionamento e tamanho geral da modal (semelhante ao Figma 1190x568) */
#mainNavigationModal .modal-dialog.modal-xl {
    /* Usar max-width para permitir alguma flexibilidade, mas mirar na proporção */
    max-width: 1190px !important; 
    width: 90% !important; /* Fallback para telas menores */
    margin: 5vh auto !important; /* Margem superior/inferior e centraliza */
}

/* Conteúdo da modal: fundo branco, bordas arredondadas, sombra */
#mainNavigationModal .modal-content {
    background-color: #ffffff !important; /* Fundo branco */
    border-radius: 8px !important; /* Bordas arredondadas */
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important; /* Sombra */
    border: none !important; /* Remove bordas padrão */
    overflow: visible !important; /* Permitir que o botão 'X' transborde */
    /* Altura baseada no Figma (568px), mas usar max-height para evitar problemas */
    height: 568px;  
    width: 1190px;
    display: flex;
    flex-direction: column;
}

/* Header customizado: remove padding e borda */
#mainNavigationModal .modal-header-custom {
    position: relative !important; /* Necessário para posicionar o botão 'X' */
}

/* Estilo do botão Fechar (X) - Conforme Figma (parece padrão do sistema/browser?) */
/* Mantendo o estilo anterior que posiciona fora */
#mainNavigationModal .modal-header-custom .close {
    position: absolute !important;
    top: -10px !important; /* Posição FORA do canto superior */
    right: -10px !important; /* Posição FORA do canto direito */
    width: 24px !important; /* Tamanho menor */
    height: 24px !important; /* Tamanho menor */
 
    background-color: #6c757d !important; /* Fundo cinza escuro */
    color: #ffffff !important; /* Cor do 'X' branca */
    border: none !important;
    box-shadow: none !important; /* Sem sombra */
    font-size: 16px !important; /* 'X' menor */
    font-weight: normal !important; /* Peso normal */
    line-height: 24px !important; /* Centraliza 'X' menor */
    text-align: center !important;
    opacity: 1 !important; /* Opacidade total */
    cursor: pointer !important;
    z-index: 1055 !important;
    text-shadow: none !important;
    transform: none !important;
  
}

#mainNavigationModal .modal-header-custom .close:hover {
    background-color: #5a6268 !important; /* Cinza um pouco mais escuro no hover */
    color: #ffffff !important;
    opacity: 1 !important;
}

/* Corpo da modal: padding e layout flex */
#mainNavigationModal .modal-body-custom {
    padding: 0 !important; /* Remover padding do body, controlar nas colunas */
    flex-grow: 1;
    overflow: hidden; /* Esconde overflow interno do body */
    display: flex;
}

/* Layout das colunas internas */
#mainNavigationModal .modal-body-custom > .row.no-gutters {
    height: 100%;
    width: 100%;
    display: flex;
    flex-wrap: nowrap;
}

#mainNavigationModal .nav-col {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #FFFFFF;
    overflow: hidden; /* Esconder overflow da coluna */
}

/* Coluna 1 (Menu Lateral Esquerdo) */
#mainNavigationModal .nav-col-1 {
    flex: 0 0 280px !important;  
    border-right: 1px solid #E5E7EB !important;
    padding: 0 !important; /* Sem padding geral na coluna */
    display: flex;
    flex-direction: column;
}

/* Container da Busca na Coluna 1 */
#mainNavigationModal .nav-col-1 .nav-col-search-container {
    padding: 16px 16px 16px 16px !important; /* Padding: 16px em todos os lados */
    margin-bottom: 0 !important; /* Sem margem inferior */
    border-bottom: 1px solid #E5E7EB !important;
    position: relative !important;
    flex-shrink: 0; /* Não encolher */
}

/* Input de Busca (Estilo Figma) */
#mainNavigationModal .nav-col-1 .nav-filter-input {
    width: 100% !important;
    font-size: 14px !important; /* Fonte do input */
    border: none !important; /* Remove borda padrão */
    border-bottom: 1px solid #E0E0E0 !important; /* Borda inferior cinza claro */
    height: 40px !important; /* Altura ajustada */
    padding: 8px 0px !important; /* Padding vertical, sem padding lateral (texto alinha à esquerda) */
    padding-right: 30px !important; /* Espaço para o ícone à direita */
    background-color: transparent !important; /* Fundo transparente */
    color: #333333; /* Cor do texto digitado (ex: cinza escuro) */
    border-radius: 0 !important; /* Sem bordas arredondadas */
    box-shadow: none !important; /* Sem sombra */
}
#mainNavigationModal .nav-col-1 .nav-filter-input::placeholder {
    color: #757575; /* Cor do placeholder (cinza médio) */
    font-size: 14px; /* Tamanho da fonte placeholder */
    font-weight: 400; /* Peso da fonte placeholder */
}
#mainNavigationModal .nav-col-1 .nav-filter-input:focus {
    border-bottom-color: #163561 !important; /* Cor da borda inferior em foco (azul) */
    box-shadow: none !important; /* Sem sombra no foco */
}

/* lupa */
 /* #mainNavigationModal .nav-col-1 .nav-col-search-container::after {
    content: "\e003";  
    font-family: 'Glyphicons Halflings';
    position: absolute;
    right: 0px;  
    top: 0; 
    bottom: 0;  
    height: 40px;  
    line-height: 40px;  
    color: #757575;  
    font-size: 16px;  
    pointer-events: none;
    transform: none;
    padding-right: 5px;  
} */

/* Lista de Itens na Coluna 1 */
#mainNavigationModal .nav-col-1 .nav-list-group {
    list-style: none !important;
    padding: 8px 8px !important; /* Padding lateral 8px, superior/inferior 8px */
    margin: 0 !important;
    overflow-y: auto !important; /* Scroll vertical */
    flex-grow: 1 !important; /* Ocupar espaço restante */
}

/* Itens da Lista na Coluna 1 (Estilo Especificado) */
#mainNavigationModal .nav-col-1 .nav-list-item {
    padding: 10px 16px !important; /* Padding interno */
    font-family: Roboto, sans-serif !important; /* Fonte especificada */
    font-size: 14px !important; /* Tamanho especificado */
    font-weight: 500 !important; /* Peso especificado */
    color: #296292 !important; /* Cor especificada */
    line-height: 18px !important; /* Altura da linha especificada */
    letter-spacing: 0% !important; /* Espaçamento especificado */
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important; /* Alinhamento vertical middle via flex */
    position: relative !important;
    transition: background-color 0.15s ease, color 0.15s ease !important;
    margin-bottom: 4px !important; /* Espaço entre itens */
    border-radius: 4px !important; /* Borda arredondada leve */
    border: none; /* Sem borda */
    background-color: transparent; /* Fundo transparente por padrão */
}

/* Ícones dos Itens na Coluna 1 */
#mainNavigationModal .nav-col-1 .nav-list-item i:first-child, 
#mainNavigationModal .nav-col-1 .nav-list-item .custom-list-icon {
    font-size: 16px !important; 
    color: #757575 !important; /* Manter cor cinza para ícones? Ou #296292? Usando cinza por enquanto */
    width: 20px !important; 
    text-align: center !important;
    margin-right: 12px !important; 
    flex-shrink: 0 !important;
    
}

/* Label (Texto) dos Itens na Coluna 1 */
#mainNavigationModal .nav-col-1 .nav-list-item .item-label {
    flex-grow: 1;
    margin-left: 0 !important; 
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-family: inherit !important; /* Herda Roboto */
    font-size: inherit !important; /* Herda 14px */
    font-weight: inherit !important; /* Herda 500 */
    color: inherit !important; /* Herda #296292 */
    line-height: inherit !important; /* Herda 18px */
    letter-spacing: inherit !important; /* Herda 0% */
}

/* Ícone de Ação (Nova Aba) na Coluna 1 */
#mainNavigationModal .nav-col-1 .nav-list-item .item-action-icon-col1 {
    color: #BDBDBD !important; /* Cinza claro */
    font-size: 16px !important; 
    margin-left: auto !important; 
    padding-left: 8px !important; 
    opacity: 1 !important; 
    transition: color 0.15s ease, opacity 0.15s ease !important;
}
#mainNavigationModal .nav-col-1 .nav-list-item:hover .item-action-icon-col1,
#mainNavigationModal .nav-col-1 .nav-list-item.active .item-action-icon-col1 {
    color: #163561 !important; /* Manter azul escuro no hover/active para destaque */
}

/* Seta Indicadora (Próxima Coluna) na Coluna 1 */
#mainNavigationModal .nav-col-1 .nav-list-item .item-arrow {
    color: #BDBDBD !important; /* Cinza claro */
    font-size: 14px !important; 
    margin-left: 8px !important;
    width: 24px; 
    height: 24px;
    opacity: 1;
    transition: color 0.15s ease, opacity 0.15s ease !important;
}

/* Hover State - Itens Coluna 1 */
#mainNavigationModal .nav-col-1 .nav-list-item:hover {
    background-color: #EEEEEE !important; /* Fundo cinza bem claro hover */
    color: #163561 !important; /* Texto Azul Becomex hover (mais escuro que base) */
}
#mainNavigationModal .nav-col-1 .nav-list-item:hover i, 
#mainNavigationModal .nav-col-1 .nav-list-item:hover .custom-list-icon,
#mainNavigationModal .nav-col-1 .nav-list-item:hover .item-arrow {
    color: #163561 !important; /* Ícones Azul Becomex hover */
}

/* Active State - Itens Coluna 1 */
#mainNavigationModal .nav-col-1 .nav-list-item.active {
    background-color: #E3F2FD !important; /* Fundo azul muito claro active */
    color: #163561 !important; /* Texto Azul Becomex active (mais escuro que base) */
    font-weight: 500 !important; /* Manter Medium */
}
#mainNavigationModal .nav-col-1 .nav-list-item.active i, 
#mainNavigationModal .nav-col-1 .nav-list-item.active .custom-list-icon,
#mainNavigationModal .nav-col-1 .nav-list-item.active .item-arrow {
    color: #163561 !important; /* Ícones Azul Becomex active */
}

/* Colunas 2, 3, 4 (Conteúdo) */
#mainNavigationModal .nav-col-2, 
#mainNavigationModal .nav-col-3, 
#mainNavigationModal .nav-col-4 {
    flex: 1 1 0px !important; /* Colunas flexíveis, base 0 para dividir espaço */
    border-right: 1px solid #E5E7EB !important;
    padding: 0 !important; /* Sem padding geral */
    overflow-y: auto; /* Scroll vertical */
    background-color: #FAFAFA; /* Fundo ligeiramente diferente para conteúdo */
}

#mainNavigationModal .nav-col-4 {
    border-right: none !important; /* Última coluna sem borda direita */
}

/* Título das Colunas 2, 3, 4 */
#mainNavigationModal .nav-col-title {
    font-size: 12px !important; /* Menor */
    font-weight: 600 !important;
    text-transform: uppercase !important;
    color: #6B7280 !important;
    padding: 12px 16px 8px 16px !important; /* Padding */
    margin-bottom: 0 !important; /* Sem margem */
    border-bottom: 1px solid #E5E7EB !important;
    background-color: #F9FAFB !important; /* Fundo claro */
    display: block !important;
    flex-shrink: 0; /* Não encolher */
}

/* Lista de Itens nas Colunas 2, 3, 4 */
#mainNavigationModal .nav-col-2 .nav-list-group,
#mainNavigationModal .nav-col-3 .nav-list-group,
#mainNavigationModal .nav-col-4 .nav-list-group {
    padding: 8px 12px !important; /* Padding interno */
    flex-grow: 1;
    overflow-y: auto;
}

/* Itens nas Colunas 2, 3, 4 (Estilo Especificado) */
#mainNavigationModal .nav-col-2 .nav-list-item,
#mainNavigationModal .nav-col-3 .nav-list-item,
#mainNavigationModal .nav-col-4 .nav-list-item {
    padding: 9px 15px !important;
    font-family: Roboto, sans-serif !important; /* Fonte especificada */
    font-size: 14px !important; /* Tamanho especificado */
    font-weight: 500 !important; /* Peso especificado */
    color: #296292 !important; /* Cor especificada */
    line-height: 18px !important; /* Altura da linha especificada */
    letter-spacing: 0% !important; /* Espaçamento especificado */
    border-radius: 4px !important;
    margin-bottom: 2px !important;
    position: relative !important;
    display: flex;
    align-items: center !important; /* Alinhamento vertical middle via flex */
    cursor: pointer;
    transition: background-color 0.15s ease, color 0.15s ease !important;
    background-color: transparent; /* Fundo transparente */
}

#mainNavigationModal .nav-col-2 .nav-list-item:hover,
#mainNavigationModal .nav-col-3 .nav-list-item:hover,
#mainNavigationModal .nav-col-4 .nav-list-item:hover {
    background-color: #EEEEEE !important; /* Fundo cinza bem claro hover */
    color: #163561 !important; /* Texto Azul Becomex hover (mais escuro que base) */
}

#mainNavigationModal .nav-col-2 .nav-list-item.active,
#mainNavigationModal .nav-col-3 .nav-list-item.active,
#mainNavigationModal .nav-col-4 .nav-list-item.active {
    background-color: #E3F2FD !important; /* Fundo azul muito claro active */
    color: #163561 !important; /* Texto Azul Becomex active (mais escuro que base) */
    font-weight: 500 !important; /* Manter Medium */
}

/* Ícones e texto dentro dos itens das colunas 2, 3, 4 */
#mainNavigationModal .nav-col-2 .nav-list-item .item-icon,
#mainNavigationModal .nav-col-3 .nav-list-item .item-icon,
#mainNavigationModal .nav-col-4 .nav-list-item .item-icon {
    margin-right: 10px;
    color: #6B7280;
    font-size: 16px;
}
#mainNavigationModal .nav-col-2 .nav-list-item .item-label,
#mainNavigationModal .nav-col-3 .nav-list-item .item-label,
#mainNavigationModal .nav-col-4 .nav-list-item .item-label {
    flex-grow: 1;
}
#mainNavigationModal .nav-col-2 .nav-list-item .item-arrow,
#mainNavigationModal .nav-col-3 .nav-list-item .item-arrow,
#mainNavigationModal .nav-col-4 .nav-list-item .item-arrow {
    margin-left: 8px;
    color: #9CA3AF;
    width: 24px;
    height: 24px;
}

/* Ajustes para modo de busca */
#mainNavigationModal.search-mode .nav-col-1 .nav-list-group {
    padding: 8px 8px !important; /* Manter padding */
}

#mainNavigationModal.search-mode .nav-list-item.search-result-item {
    /* Estilos específicos para itens de resultado de busca, se necessário */
    border: 1px solid transparent; /* Reset border */
    margin-bottom: 4px !important;
}

#mainNavigationModal.search-mode .nav-list-item.search-result-item a {
    display: flex;
    align-items: center;
    padding: 10px 16px !important; 
    color: #374151 !important;
    text-decoration: none;
    width: 100%;
    border-radius: 4px;
}

#mainNavigationModal.search-mode .nav-list-item.search-result-item:hover a {
    background-color: #F3F4F6 !important;
    color: #111827 !important;
}

#mainNavigationModal.search-mode .search-result-item .item-label-container {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    margin-left: 12px;
}

#mainNavigationModal.search-mode .search-result-item .item-label {
    font-size: 14px;
    font-weight: 500; /* Destacar o label principal */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#mainNavigationModal.search-mode .search-result-item .search-breadcrumb {
    font-size: 12px;
    color: #6B7280; /* Cor cinza para breadcrumb */
    margin-top: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#mainNavigationModal.search-mode .search-result-item .item-action-icon {
    margin-left: auto;
    padding-left: 8px;
    color: #9CA3AF;
}

/* --- Fim dos Ajustes da Modal --- */

/* --- Estilos Anteriores (Manter ou Remover Conforme Necessário) --- */

/* ... (outros estilos da sidebar, tooltips, etc. que já estavam aqui) ... */

/* Sidebar Tooltip Styling (Based on detailed HTML example - Revised Visibility Attempt 2) */

/* Ensure parent containers allow overflow. Test carefully, might affect layout/scrollbars */
/* #app-sidebar,
#app-sidebar .sidebar-scrollable-icons-container,
#app-sidebar .sidebar-nav-icons,
#app-sidebar .sidebar-nav-icons li,
#app-sidebar .sidebar-nav-icons li a {
    overflow: visible !important; /* Force overflow visibility - TEST THIS CAREFULLY */
/* } */

#app-sidebar .sidebar-nav-icons li a {
    position: relative; /* Ensure parent link has position relative */
}

#app-sidebar .sidebar-tooltip {
    position: absolute;
    left: calc(100% + 10px); /* Position to the right of the icon + gap */
    top: 50%;
    transform: translateY(-50%);
    background-color: black; /* Background from example */
    color: #ffffff; /* White text */
    padding: 5px 0px; /* Padding from example */
    border-radius: 6px; /* Border-radius from example */
    font-size: 12px; /* Font-size from example */
    z-index: 20000; /* Further increased z-index */
    opacity: 0; /* Start hidden */
    visibility: hidden; /* Start hidden */
    transition: opacity 0.2s ease, visibility 0s linear 0.2s; /* Delay visibility change */
    pointer-events: none; /* Tooltip should not be interactive */
    min-width: 150px; /* Add min-width */
    box-shadow: 0 2px 5px rgba(0,0,0,0.2); /* Optional: Add subtle shadow */
    text-align: left; /* Ensure text aligns left */
}

/* Arrow/Pointer for the tooltip */
#app-sidebar .sidebar-tooltip::before {
    content: '';
    position: absolute;
    top: 50%;
    right: 100%; /* Position arrow to the left of the tooltip */
    margin-top: -6px; /* Half of the arrow height */
    border-width: 6px;
    border-style: solid;
    border-color: transparent black transparent transparent; /* Arrow pointing left, match background */
    z-index: 20001; /* Ensure arrow is above tooltip */
}

/* Inner content container */
#app-sidebar .sidebar-tooltip-content {
    padding: 5px 10px; /* Padding from inner div example */
}

#app-sidebar .sidebar-tooltip-content div {
    margin-bottom: 5px; /* Margin from inner divs example */
}

#app-sidebar .sidebar-tooltip-content div:last-child {
    margin-bottom: 0; /* Remove margin from last item */
}

#app-sidebar .sidebar-tooltip-content strong {
    color: white; /* Strong color from example */
}

#app-sidebar .sidebar-tooltip-content span {
    font-weight: 300; /* Font-weight from example */
    color: white; /* Span color from example */
}

/* Show tooltip on hover - Max Specificity */
body #app-sidebar .sidebar-scrollable-icons-container .sidebar-nav-icons li a:hover > .sidebar-tooltip {
    opacity: 1 !important; /* Use !important as a strong measure */
    visibility: visible !important; /* Use !important as a strong measure */
    transition: opacity 0.2s ease, visibility 0s linear 0s; /* Show immediately */
}



/* --- Correção Alinhamento Ícones Direita (Colunas 2, 3, 4) - Container Único --- */

/* Garante que o container do item use flex e alinhamento vertical */
#mainNavigationModal .nav-col-1 .nav-list-item, /* Aplicar também na Coluna 1 */
#mainNavigationModal .nav-col-2 .nav-list-item,
#mainNavigationModal .nav-col-3 .nav-list-item,
#mainNavigationModal .nav-col-4 .nav-list-item {
    display: flex !important;
    align-items: center !important;
    position: relative !important; /* Manter caso necessário para outros elementos */
    padding-right: 15px !important; /* Padding padrão à direita */
}

/* Garante que o label ocupe o espaço */
#mainNavigationModal .nav-col-1 .nav-list-item .item-label,
#mainNavigationModal .nav-col-2 .nav-list-item .item-label,
#mainNavigationModal .nav-col-3 .nav-list-item .item-label,
#mainNavigationModal .nav-col-4 .nav-list-item .item-label {
    flex-grow: 1;
    margin-right: 8px; /* Espaço entre label e container de ações */
    /* Herança de fonte já tratada */
}

/* Container para todos os ícones à direita */
.item-actions-right {
    display: inline-flex; /* Usar inline-flex para ocupar apenas o espaço necessário */
    align-items: center;
    margin-left: auto; /* Empurra o container para a direita */
    flex-shrink: 0; /* Impede que o container encolha */
    gap: 6px; /* Espaço entre os ícones dentro do container */
}

/* Estilos individuais para ícones dentro do container */
.item-actions-right .item-action-star, /* Estrela */
.item-actions-right .item-action-icon, /* Nova Aba (Col 2,3,4) */
.item-actions-right .item-action-icon-col1, /* Nova Aba (Col 1) */
.item-actions-right .item-arrow { /* Seta */
    display: inline-flex;
    align-items: center;
    line-height: 1;
}

/* Ajuste específico tamanho/cor ícones */
.item-actions-right .item-action-star {
    color: #6B7280;
    font-size: 14px;
}
.item-actions-right .item-action-icon img,
.item-actions-right .item-action-icon-col1 img {
    width: 16px;
    height: 16px;
    opacity: 0.7;
    transition: opacity 0.15s ease;
    vertical-align: middle;
}
.item-actions-right .item-arrow {
    width: 24px;
    height: 24px;
    opacity: 1;
    transition: color 0.15s ease, opacity 0.15s ease !important;
    color: #BDBDBD !important;
}

/* Hover/Active states */
#mainNavigationModal .nav-list-item:hover .item-actions-right .item-action-icon img,
#mainNavigationModal .nav-list-item:hover .item-actions-right .item-action-icon-col1 img,
#mainNavigationModal .nav-list-item.active .item-actions-right .item-action-icon img,
#mainNavigationModal .nav-list-item.active .item-actions-right .item-action-icon-col1 img {
    opacity: 1;
}

#mainNavigationModal .nav-list-item:hover .item-actions-right .item-arrow,
#mainNavigationModal .nav-list-item.active .item-actions-right .item-arrow {
    color: #163561 !important;
}

#mainNavigationModal .nav-list-item:hover .item-actions-right .item-action-star,
#mainNavigationModal .nav-list-item.active .item-actions-right .item-action-star {
    color: #163561 !important;
}

/* --- Fim da Correção --- */

#page-content-area .container{
 
    margin-top: 50px !important;
    margin-bottom: 30px !important;
  }
  #page-content-area .container {
    margin: 20px auto; /* Exemplo: 20px de margem superior/inferior e centralizado horizontalmente */
    padding: 15px; /* Opcional: Adiciona um preenchimento interno para o conteúdo do container */
}









/* 
 * Correção para o tooltip da sidebar - Versão Final com seta centralizada
 * Esta versão corrige o alinhamento vertical da seta
 */

/* Remover qualquer restrição de overflow em todos os elementos pai */
#app-sidebar,
#app-sidebar .sidebar-scrollable-icons-container,
#app-sidebar .sidebar-nav-icons,
#app-sidebar .sidebar-nav-icons li,
#app-sidebar .sidebar-nav-icons li a {
    overflow: visible !important;
}

/* Redefinir o tooltip para usar position: fixed */
#app-sidebar .sidebar-tooltip {
    position: fixed !important;
    left: 70px !important; /* Posição fixa à direita da sidebar */
    background-color: #000000 !important;
    color: #ffffff !important;
    padding: 8px 16px !important;
    border-radius: 20px !important; /* Cantos mais arredondados conforme imagem */
    font-size: 14px !important;
    font-weight: normal !important;
    white-space: nowrap !important;
    z-index: 9999 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    display: block !important;
    width: auto !important;
    min-width: 120px !important;
    height: auto !important;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2) !important;
    /* Remover transformações que podem causar problemas */
    transform: none !important;
    text-align: center !important;
}

/* Conteúdo do tooltip */
#app-sidebar .sidebar-tooltip-content {
    font-family: Roboto, sans-serif !important;
    line-height: 1.4 !important;
    display: block !important;
    width: 100% !important;
    color: #ffffff !important;
    opacity: 1 !important;
    visibility: visible !important;
    text-align: center !important;
}

/* Seta do tooltip - estilo arredondado conforme imagem e centralizada verticalmente */
#app-sidebar .sidebar-tooltip:before {
    content: '' !important;
    position: absolute !important;
    left: -8px !important;
    top: 50% !important; /* Centralizar verticalmente */
    transform: translateY(-50%) rotate(45deg) !important; /* Garantir centralização vertical */
    width: 12px !important;
    height: 12px !important;
    background-color: #000000 !important;
    border-radius: 2px !important;
    z-index: 9998 !important;
    display: block !important;
    margin: 0 !important; /* Remover qualquer margem que possa afetar o posicionamento */
}

/* Mostrar tooltip ao passar o mouse */
#app-sidebar .sidebar-nav-icons li a:hover .sidebar-tooltip {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

/* Garantir que o texto dentro do tooltip seja visível */
#app-sidebar .sidebar-tooltip-content div,
#app-sidebar .sidebar-tooltip-content div strong {
    color: #ffffff !important;
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    font-size: 14px !important;
    text-align: center !important;
    font-weight: normal !important;
}






/* 
 * Estilos para a classe .company-name
 * Formatação consistente com os outros textos do sistema
 */

 .company-name {
    font-family: Roboto, sans-serif !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    color: #296292 !important;
    line-height: 1.4 !important;
    margin: 0 !important;
    padding: 0 !important;
    text-overflow: ellipsis !important;
    overflow: hidden !important;
    white-space: nowrap !important;
    max-width: 100% !important;
}

/* Variação para nomes de empresa em cabeçalhos */
.company-name.header {
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #163561 !important;
}

/* Variação para nomes de empresa em listas */
.company-name.list-item {
    font-size: 14px !important;
    font-weight: 500 !important;
    color: #296292 !important;
    margin-bottom: 4px !important;
}

/* Variação para nomes de empresa em formulários */
.company-name.form-label {
    font-size: 14px !important;
    font-weight: 500 !important;
    color: #333333 !important;
    margin-bottom: 8px !important;
}

/* Variação para nomes de empresa em tooltips */
.company-name.tooltip {
    font-size: 13px !important;
    font-weight: normal !important;
    color: #ffffff !important;
}

/* Variação para nomes de empresa em tabelas */
.company-name.table-cell {
    font-size: 13px !important;
    font-weight: 400 !important;
    color: #333333 !important;
}

/* Estado hover */
.company-name:hover {
    color: #163561 !important;
}

/* Estado ativo/selecionado */
.company-name.active {
    color: #163561 !important;
    font-weight: 600 !important;
}

/* Responsividade para telas menores */
@media (max-width: 768px) {
    .company-name {
        font-size: 13px !important;
    }
    
    .company-name.header {
        font-size: 15px !important;
    }
}


/**
 * CSS ultra específico para adicionar a barra de rolagem na sidebar
 * e garantir que a logo Becomex cubra os ícones
 */

/* Apenas adiciona overflow-y: auto ao container dos ícones */
#app-sidebar .sidebar-scrollable-icons-container {
    max-height: calc(100vh - 125px) !important; /* Altura total menos espaço para logo */
    overflow-y: auto !important; /* Habilita a rolagem vertical */
    padding-bottom: 10px !important; /* Garante espaço mínimo de 10px */
    
    /* Estilo da barra de rolagem - muito sutil */
    scrollbar-width: thin !important;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent !important;
  }
  
  /* Estilo da barra de rolagem para navegadores WebKit (Chrome, Safari) */
  #app-sidebar .sidebar-scrollable-icons-container::-webkit-scrollbar {
    width: 4px !important;
  }
  
  #app-sidebar .sidebar-scrollable-icons-container::-webkit-scrollbar-track {
    background: transparent !important;
  }
  
  #app-sidebar .sidebar-scrollable-icons-container::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.3) !important;
    border-radius: 10px !important;
  }
  
  /* Garante que a logo fique fixa na parte inferior e cubra os ícones */
  #sidebar-logo-container-bottom {
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    width: 100% !important;
    z-index: 1033 !important; /* Z-index maior que os ícones para garantir sobreposição */
    background: url('/assets/icon/sidebar.svg') !important; /* Mesmo fundo da sidebar */
    background-size: cover !important;
    background-position: bottom center !important;
    background-repeat: no-repeat !important;
    padding: 10px !important;
  }
  





/**
 * CSS específico para remover outline e text-decoration de todos os itens das colunas
 */

/* Seletor para todos os itens das colunas */
#mainNavigationModal .nav-col-1 .nav-list-item a,
#mainNavigationModal .nav-col-2 .nav-list-item a,
#mainNavigationModal .nav-col-3 .nav-list-item a,
#mainNavigationModal .nav-col-4 .nav-list-item a,
#mainNavigationModal .nav-list-item a,
#mainNavigationModal .item-label a {
  outline: none !important;
  text-decoration: none !important;
}

/* Garantir que os links dentro dos itens também recebam o mesmo estilo */
#mainNavigationModal .nav-list-item a *,
#mainNavigationModal .item-label a * {
  outline: none !important;
  text-decoration: none !important;
}

/* Garantir que os estados hover, active e focus também não tenham outline ou text-decoration */
#mainNavigationModal .nav-list-item a:hover,
#mainNavigationModal .nav-list-item a:active,
#mainNavigationModal .nav-list-item a:focus,
#mainNavigationModal .item-label a:hover,
#mainNavigationModal .item-label a:active,
#mainNavigationModal .item-label a:focus {
  outline: none !important;
  text-decoration: none !important;
}


/* Muda o cursor para ponteiro (mãozinha) ao passar sobre o item "Empresa" no menu lateral */
li.nav-list-item[data-item-key="empresa_selecao"] {
    cursor: pointer;
}

/* Muda o cursor para ponteiro ao passar sobre os itens de seleção de empresa no modal */
#modal-empresas-list-container .nav-list-group {
    cursor: pointer;
}




/* --- Custom Sidebar Tooltip Styles --- */
#app-sidebar .sidebar-nav-icons li a .sidebar-tooltip {
    position: absolute;
    left: 100%; /* Start position at the right edge of the parent 'a' */
    top: 50%; /* Align top edge to the middle of the parent 'a' */
    transform: translateY(-50%); /* Shift up by half its own height to center vertically */
    margin-left: 10px; /* Gap between the icon and the tooltip */
    z-index: 1100;
    opacity: 0; /* Hidden by default */
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0s linear 0.2s;
    pointer-events: none; /* Tooltip should not be interactive */
    white-space: nowrap; /* Prevent text wrapping */
}

#app-sidebar .sidebar-nav-icons li a .sidebar-tooltip .sidebar-tooltip-content {
    background-color: #1f1f1f; /* Dark background */
    color: #ffffff; /* White text */
    padding: 6px 12px; /* Padding */
    border-radius: 16px; /* Rounded corners */
    font-size: 13px; /* Font size */
    position: relative; /* Needed for the arrow */
}

/* Tooltip Arrow */
#app-sidebar .sidebar-nav-icons li a .sidebar-tooltip .sidebar-tooltip-content::before {
    content: '';
    position: absolute;
    right: 100%; /* Position arrow at the left edge of the tooltip content */
    top: 50%;
    transform: translateY(-50%);
    border-width: 6px;
    border-style: solid;
    border-color: transparent #1f1f1f transparent transparent; /* Arrow pointing left, attached to the tooltip */
    pointer-events: none;
}

/* Show tooltip on parent link hover */
#app-sidebar .sidebar-nav-icons li a:hover .sidebar-tooltip {
    opacity: 1;
    visibility: visible;
    transition: opacity 0.2s ease, visibility 0s linear 0s;
}

/* Ensure the parent link allows the tooltip to show */
#app-sidebar .sidebar-nav-icons li a {
    /* position: relative; /* Already present */
    overflow: visible; /* Allow tooltip to overflow the 'a' tag bounds */
}

