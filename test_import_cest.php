#!/usr/bin/env php
<?php
/**
 * Script de teste para importação CEST
 * 
 * Este script testa a funcionalidade de importação CEST
 * sem executar a importação real no banco de dados.
 * 
 * Uso: php test_import_cest.php
 */

// Configurações
define('BASEPATH', '');
define('APPPATH', './application/');
define('FCPATH', './');

// Simular algumas funções do CodeIgniter para teste
function redirect()
{
    echo "Redirecionamento bloqueado - apenas CLI permitido\n";
    exit(1);
}

function date_default_timezone_set_wrapper()
{
    date_default_timezone_set('America/Sao_Paulo');
}

// Classe de teste simulando o controller
class TestImportCest
{
    private $csv_file;
    private $delimiter = ';';

    public function __construct()
    {
        $this->csv_file = FCPATH . 'assets/cest/CEST_142_2018.csv';
        date_default_timezone_set_wrapper();
    }

    public function debug($message)
    {
        echo "[" . date('Y-m-d H:i:s') . "] " . $message . PHP_EOL;
    }

    public function test_file_validation()
    {
        $this->debug('=== TESTE DE VALIDAÇÃO DO ARQUIVO ===');

        // Teste 1: Verificar se arquivo existe
        if (!file_exists($this->csv_file)) {
            $this->debug("ERRO: Arquivo CSV não encontrado: {$this->csv_file}");
            return false;
        }
        $this->debug("✓ Arquivo encontrado: {$this->csv_file}");

        // Teste 2: Verificar se arquivo é legível
        if (!is_readable($this->csv_file)) {
            $this->debug("ERRO: Arquivo CSV não pode ser lido: {$this->csv_file}");
            return false;
        }
        $this->debug("✓ Arquivo é legível");

        // Teste 3: Verificar tamanho do arquivo
        $filesize = filesize($this->csv_file);
        $this->debug("✓ Tamanho do arquivo: " . number_format($filesize) . " bytes");

        return true;
    }

    public function test_csv_structure()
    {
        $this->debug('=== TESTE DE ESTRUTURA DO CSV ===');

        $handle = fopen($this->csv_file, "r");
        if ($handle === FALSE) {
            $this->debug("ERRO: Não foi possível abrir o arquivo");
            return false;
        }

        $line_count = 0;
        $valid_lines = 0;
        $invalid_lines = 0;
        $sample_lines = [];

        while (($data = fgetcsv($handle, 1000, $this->delimiter)) !== FALSE) {
            $line_count++;

            // Guardar algumas linhas de exemplo
            if ($line_count <= 5) {
                $sample_lines[] = $data;
            }

            // Pular cabeçalho
            if ($line_count === 1) {
                $this->debug("Cabeçalho: " . implode(' | ', $data));
                continue;
            }

            // Validar estrutura da linha
            if (count($data) >= 3) {
                $cod_cest = trim($data[0]);
                $ncm = trim($data[1]);
                $desc_cest = trim($data[2]);

                // CEST e descrição são obrigatórios, NCM pode estar vazio em casos específicos
                if (!empty($cod_cest) && !empty($desc_cest)) {
                    $valid_lines++;
                    if (empty($ncm)) {
                        $this->debug("INFO: Linha {$line_count} - CEST sem NCM (código genérico): {$cod_cest}");
                    }
                } else {
                    $invalid_lines++;
                    if ($invalid_lines <= 5) { // Mostrar apenas os primeiros 5 erros
                        $this->debug("AVISO: Linha {$line_count} com CEST ou descrição vazios: " . implode(' | ', $data));
                    }
                }
            } else {
                $invalid_lines++;
                if ($invalid_lines <= 5) {
                    $this->debug("AVISO: Linha {$line_count} com estrutura inválida: " . implode(' | ', $data));
                }
            }
        }

        fclose($handle);

        $this->debug("✓ Total de linhas: {$line_count}");
        $this->debug("✓ Linhas válidas: {$valid_lines}");
        $this->debug("✓ Linhas inválidas: {$invalid_lines}");

        // Mostrar exemplos de dados
        $this->debug("=== EXEMPLOS DE DADOS ===");
        foreach ($sample_lines as $i => $line) {
            $line_num = $i + 1;
            $this->debug("Linha {$line_num}: " . implode(' | ', $line));
        }

        return $invalid_lines < ($valid_lines * 0.1); // Aceitar até 10% de linhas inválidas
    }

    public function test_data_formatting()
    {
        $this->debug('=== TESTE DE FORMATAÇÃO DOS DADOS ===');

        $handle = fopen($this->csv_file, "r");
        if ($handle === FALSE) {
            return false;
        }

        $test_cases = 0;
        $formatting_errors = 0;

        while (($data = fgetcsv($handle, 1000, $this->delimiter)) !== FALSE && $test_cases < 10) {
            $test_cases++;

            // Pular cabeçalho
            if ($test_cases === 1) continue;

            if (count($data) >= 3) {
                $cod_cest_raw = trim($data[0]);
                $ncm_raw = trim($data[1]);
                $desc_cest = trim($data[2]);

                // Testar formatação do CEST (remover pontos)
                $f_cod_cest = preg_replace('/\D/', '', $cod_cest_raw);

                // Testar formatação do NCM
                $f_ncm = preg_replace('/\D/', '', $ncm_raw);

                if (empty($f_ncm)) {
                    $this->debug("ERRO: NCM inválido após formatação - Original: '{$ncm_raw}' | Formatado: '{$f_ncm}'");
                    $formatting_errors++;
                } else {
                    $this->debug("✓ CEST: '{$cod_cest_raw}' -> '{$f_cod_cest}' | NCM: '{$ncm_raw}' -> '{$f_ncm}'");
                }
            }
        }

        fclose($handle);

        $this->debug("✓ Casos testados: {$test_cases}");
        $this->debug("✓ Erros de formatação: {$formatting_errors}");

        return $formatting_errors === 0;
    }

    public function run_all_tests()
    {
        $this->debug('=== INICIANDO TESTES DE IMPORTAÇÃO CEST ===');
        $this->debug('Data/Hora: ' . date('Y-m-d H:i:s'));

        $tests = [
            'Validação do arquivo' => 'test_file_validation',
            'Estrutura do CSV' => 'test_csv_structure',
            'Formatação dos dados' => 'test_data_formatting'
        ];

        $passed = 0;
        $total = count($tests);

        foreach ($tests as $test_name => $test_method) {
            $this->debug("\n--- Executando: {$test_name} ---");

            if ($this->$test_method()) {
                $this->debug("✅ PASSOU: {$test_name}");
                $passed++;
            } else {
                $this->debug("❌ FALHOU: {$test_name}");
            }
        }

        $this->debug("\n=== RESULTADO FINAL ===");
        $this->debug("Testes executados: {$total}");
        $this->debug("Testes aprovados: {$passed}");
        $this->debug("Testes falharam: " . ($total - $passed));

        if ($passed === $total) {
            $this->debug("🎉 TODOS OS TESTES PASSARAM! A importação deve funcionar corretamente.");
            return true;
        } else {
            $this->debug("⚠️  ALGUNS TESTES FALHARAM! Verifique os problemas antes de executar a importação.");
            return false;
        }
    }
}

// Executar os testes
if (php_sapi_name() === 'cli') {
    $tester = new TestImportCest();
    $result = $tester->run_all_tests();
    exit($result ? 0 : 1);
} else {
    echo "Este script deve ser executado via linha de comando.\n";
    exit(1);
}
