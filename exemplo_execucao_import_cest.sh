#!/bin/bash

# Script de exemplo para execução da importação CEST
# 
# Este script demonstra como executar a importação CEST
# com todas as verificações necessárias
#
# Autor: Sistema de Gestão Tarifária
# Data: 09/09/2025

echo "=========================================="
echo "IMPORTAÇÃO CEST - Portaria 142/2018"
echo "=========================================="
echo "Data/Hora: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""

# Configurações
PROJECT_DIR="/home/<USER>/Face/Becomex/GESTAO-TARIFARIA"
CSV_FILE="$PROJECT_DIR/assets/cest/CEST_142_2018.csv"
LOG_DIR="$PROJECT_DIR/application/logs"
BACKUP_DIR="$PROJECT_DIR/backup/$(date '+%Y%m%d_%H%M%S')"

# Função para log
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Função para verificar se comando foi executado com sucesso
check_status() {
    if [ $? -eq 0 ]; then
        log "✅ $1"
    else
        log "❌ $1"
        exit 1
    fi
}

# 1. Verificações iniciais
log "=== VERIFICAÇÕES INICIAIS ==="

# Verificar se estamos no diretório correto
if [ ! -f "$PROJECT_DIR/index.php" ]; then
    log "ERRO: Diretório do projeto não encontrado: $PROJECT_DIR"
    exit 1
fi
log "✅ Diretório do projeto encontrado"

# Verificar se arquivo CSV existe
if [ ! -f "$CSV_FILE" ]; then
    log "ERRO: Arquivo CSV não encontrado: $CSV_FILE"
    exit 1
fi
log "✅ Arquivo CSV encontrado: $CSV_FILE"

# Verificar tamanho do arquivo
FILE_SIZE=$(stat -c%s "$CSV_FILE")
log "✅ Tamanho do arquivo: $(numfmt --to=iec $FILE_SIZE)"

# Verificar se diretório de logs existe
if [ ! -d "$LOG_DIR" ]; then
    log "Criando diretório de logs: $LOG_DIR"
    mkdir -p "$LOG_DIR"
fi
log "✅ Diretório de logs: $LOG_DIR"

# 2. Executar testes de validação
log ""
log "=== EXECUTANDO TESTES DE VALIDAÇÃO ==="
cd "$PROJECT_DIR"

php test_import_cest.php
check_status "Testes de validação"

# 3. Criar backup (opcional - descomente se necessário)
# log ""
# log "=== CRIANDO BACKUP DAS TABELAS ==="
# mkdir -p "$BACKUP_DIR"
# 
# # Backup da tabela cest
# mysqldump -u [usuario] -p[senha] [database] cest > "$BACKUP_DIR/cest_backup.sql"
# check_status "Backup da tabela cest"
# 
# # Backup da tabela cest_ncm  
# mysqldump -u [usuario] -p[senha] [database] cest_ncm > "$BACKUP_DIR/cest_ncm_backup.sql"
# check_status "Backup da tabela cest_ncm"
# 
# log "✅ Backups salvos em: $BACKUP_DIR"

# 4. Executar importação
log ""
log "=== EXECUTANDO IMPORTAÇÃO CEST ==="
log "ATENÇÃO: A importação irá LIMPAR as tabelas cest e cest_ncm antes de inserir os novos dados!"
log ""

# Confirmar execução (descomente para modo interativo)
# read -p "Deseja continuar com a importação? (s/N): " -n 1 -r
# echo
# if [[ ! $REPLY =~ ^[Ss]$ ]]; then
#     log "Importação cancelada pelo usuário"
#     exit 0
# fi

# Executar a importação
log "Iniciando importação..."
php index.php cron import_cest

# Verificar se a importação foi bem-sucedida
if [ $? -eq 0 ]; then
    log "✅ Importação concluída com sucesso!"
else
    log "❌ Erro durante a importação!"
    exit 1
fi

# 5. Verificações pós-importação
log ""
log "=== VERIFICAÇÕES PÓS-IMPORTAÇÃO ==="

# Verificar se o log foi criado
IMPORT_LOG="$LOG_DIR/log-import-cest.txt"
if [ -f "$IMPORT_LOG" ]; then
    log "✅ Log de importação criado: $IMPORT_LOG"
    log ""
    log "=== ÚLTIMAS LINHAS DO LOG ==="
    tail -10 "$IMPORT_LOG"
else
    log "⚠️  Log de importação não encontrado"
fi

# 6. Relatório final
log ""
log "=== RELATÓRIO FINAL ==="
log "Data/Hora de conclusão: $(date '+%Y-%m-%d %H:%M:%S')"
log "Arquivo processado: $CSV_FILE"
log "Logs disponíveis em: $LOG_DIR"

# Sugestões pós-importação
log ""
log "=== PRÓXIMOS PASSOS SUGERIDOS ==="
log "1. Verificar os logs de importação para confirmar que não houve erros"
log "2. Executar consultas de validação no banco de dados"
log "3. Testar funcionalidades que dependem das tabelas CEST"
log "4. Notificar usuários sobre a atualização dos dados"

echo ""
echo "=========================================="
echo "IMPORTAÇÃO CONCLUÍDA"
echo "=========================================="
