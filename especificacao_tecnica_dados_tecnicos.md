
# Especificação Técnica - Re-layout da Tela de Dados Técnicos

## 1. Visão Geral

O objetivo deste documento é detalhar os requisitos técnicos para o re-layout da tela de **Dados Técnicos** (controlada por `atribuir_grupo.php`). A mudança visa modernizar a interface, melhorar a usabilidade (UX) e refatorar o código do backend para maior organização e manutenibilidade, seguindo padrões já estabelecidos em outras partes do sistema.

Todas as funcionalidades existentes deverão ser mantidas. O requisito 3 da especificação funcional (novo header) será desconsiderado, conforme solicitado.

---

## 2. Análise e Detalhamento Técnico

### 2.1. Backend (Controller e Model)

#### 2.1.1. Refatoração do Carregamento de Filtros

Atualmente, a lógica de aplicação de filtros está espalhada pelo método `ajax_get_itens` no controller `atribuir_grupo.php`. Para melhorar a organização e seguir o padrão do controller `cadastros/mestre_itens.php`, faremos o seguinte:

1.  **Criar um método `_apply_filters()`:** Será criado um novo método privado no controller `atribuir_grupo.php`.
2.  **Centralizar a Lógica:** Este método será o único responsável por ler os dados de filtros (vindos do POST do novo modal), verificar se estão preenchidos e aplicar os estados no `item_model` usando `$this->item_model->set_state()` e `$this->item_model->unset_state()`.
3.  **Chamada do Método:** O `_apply_filters()` será chamado no início do método `ajax_get_itens`.
4.  **Limpeza:** A lógica de filtros atual será removida de dentro do `ajax_get_itens`, tornando-o mais limpo e focado em apenas obter os dados já filtrados.

#### 2.1.2. Inclusão dos Novos Campos (SLA e Farol)

Para exibir as colunas "SLA Hrs restantes" e "Farol", será necessário buscar esses dados durante a consulta dos itens.

1.  **Análise do `geral_sla.php`:** A lógica de cálculo de SLA e Farol está no `Geral_sla_model`. O "Farol" é uma classificação (verde, amarelo, vermelho) baseada no percentual de horas de SLA consumidas.
2.  **Alteração na Model:** A query principal no `Item_model`, utilizada pelo método `get_entries_by_pn_or_desc`, será modificada. Faremos um `LEFT JOIN` com as tabelas necessárias (provavelmente relacionadas a logs de status e prioridades) para calcular em tempo real as horas restantes de SLA de cada item.
3.  **Cálculo do Farol:** A mesma query trará o percentual de SLA consumido, que será usado na view para determinar a cor do farol (verde, amarelo, vermelho), replicando a regra de negócio existente em `geral_sla.php`.

#### 2.1.3. Manutenção dos Endpoints AJAX

Os endpoints AJAX existentes que carregam dinamicamente as opções dos filtros (como `xhr_get_responsaveis`, `ajax_get_prioridades`, etc.) serão mantidos, pois o novo modal de filtros ainda precisará popular seus campos da mesma forma.

### 2.2. Frontend (View e JavaScript)

A maior parte do trabalho ocorrerá no frontend. O arquivo `assets/js/atribuir_grupos/atribuir_grupos.js` será extensivamente modificado.

#### 2.2.1. Estrutura da View (`application/views/atribuir_grupo/index.php`)

-   **Remoção dos Filtros Antigos:** Os selects e inputs de filtro que hoje ficam na tela serão removidos.
-   **Novo Botão de Filtro:** Será adicionado um botão com um ícone de filtro (`<i class="fa fa-filter"></i>` ou similar). Este botão terá um `<span>` para exibir o contador de filtros aplicados.
-   **Área de Badges de Filtro:** Abaixo do campo de pesquisa principal, será criada uma `div` para exibir dinamicamente os badges dos filtros ativos.
-   **Dropdown de Ações:** Os botões "Alterar pré-agrupamento", "Desbloquear item", "Desvincular", etc., serão agrupados dentro de um único botão com um dropdown (usando o componente Dropdown do Bootstrap).
-   **Remoção da Área de Atribuição:** A seção da direita, onde hoje se seleciona o grupo tarifário, será removida.
-   **Inclusão dos Modais:** O HTML para os novos modais (Filtros e Atribuir Grupo) será adicionado à view, preferencialmente via `include` de outros arquivos de view para reutilização.
-   **Novas Colunas na Tabela:** As colunas "SLA Hrs restantes" e "Farol" serão adicionadas ao `<thead>` da tabela de resultados.

#### 2.2.2. Componente Reutilizável: Modal de Filtros

-   **Estrutura:** Será criado um novo arquivo de view (ex: `application/views/shared/modal_filtros.php`). Este modal conterá todos os campos de filtro (`Atribuídos para`, `Owner`, `Status`, etc.).
-   **JavaScript:**
    -   Uma nova função JS, `abrirModalFiltros()`, será criada.
    -   Ao clicar no botão "Filtrar" do modal, uma função `aplicarFiltros()` será chamada. Ela irá:
        1.  Coletar todos os valores dos campos do modal.
        2.  Chamar o método `get_itens()` (já existente, mas agora mais limpo) via AJAX, passando os dados dos filtros.
        3.  Atualizar o contador no ícone do botão de filtro.
        4.  Gerar e exibir os badges de filtros ativos na `div` correspondente.
        5.  Fechar o modal.

#### 2.2.3. Modal de Atribuição de Grupo

-   **Estrutura:** Será criado um modal que será acionado pelo botão "Atribuir".
-   **Abas (Tabs):** O modal conterá a estrutura de abas do Bootstrap, com as abas "DIANA" and "Atribuir".
-   **Conteúdo das Abas:** O conteúdo que hoje é exibido na lateral direita da tela (sugestões da Diana e busca por Grupo Tarifário) será movido para dentro das respectivas abas no modal.
-   **JavaScript:**
    -   A função `get_grupos()` será modificada para, em vez de popular a lateral da página, popular a aba "Atribuir" dentro do modal.
    -   A lógica do botão "Atribuir grupo tarifário" (que hoje fica no final da página) será movida para o botão de confirmação do modal.

#### 2.2.4. Atualizações no `atribuir_grupos.js`

-   **Refatoração Geral:** O arquivo será revisado para remover lógicas relacionadas aos elementos de UI que foram excluídos.
-   **Novas Funções:** Serão criadas funções para gerenciar o estado dos novos modais, atualizar os badges de filtro e o contador.
-   **Manipulação de DOM:** O código que renderiza a tabela de resultados (`get_itens`) será atualizado para incluir os novos dados de "SLA Hrs restantes" e "Farol". O "Farol" será renderizado como um elemento visual (ex: um `<span>` com cor de fundo variável).

---

## 3. Plano de Implementação e Tarefas

| # | Tarefa | Disciplina | Estimativa (Horas) | Detalhes |
|---|---|---|---|---|
| 1 | **Refatoração do Controller (Filtros)** | Backend | 4 - 6 | Criar o método `_apply_filters` em `atribuir_grupo.php` e mover toda a lógica de `set_state` para ele, limpando o `ajax_get_itens`. |
| 2 | **Integração de Dados de SLA e Farol** | Backend | 3 - 5 | Modificar a query no `Item_model` para trazer os campos "SLA Hrs restantes" e "Farol", baseando-se na lógica do `geral_sla_model`. |
| 3 | **Estrutura da Nova View** | Frontend | 2 - 3 | Alterar o `index.php` de `atribuir_grupo`: remover filtros antigos, adicionar botão de filtro, área de badges e dropdown de ações. |
| 4 | **Criação do Modal de Filtros (HTML/JS)** | Frontend | 4 - 6 | Criar a view do modal de filtros e o JS para abri-lo, popular seus campos (reaproveitando chamadas AJAX existentes) e tratar o clique no botão "Filtrar". |
| 5 | **Lógica de Badges e Contador de Filtros** | Frontend | 2 - 3 | Implementar a função JS que lê os filtros aplicados, atualiza o contador no ícone e exibe/remove os badges na tela. |
| 6 | **Criação do Modal de Atribuição (HTML/JS)** | Frontend | 3 - 5 | Criar a view do modal de atribuição com as abas. Adaptar a função `get_grupos()` para popular o conteúdo deste modal. |
| 7 | **Atualização da Tabela de Resultados** | Frontend | 1 - 2 | Adicionar as novas colunas na tabela e modificar a função `get_itens()` no JS para renderizar os novos dados, incluindo a lógica visual do "Farol". |
| 8 | **Agrupamento dos Botões de Ação** | Frontend | 1 - 1.5 | Mover as ações existentes para dentro de um componente de dropdown do Bootstrap. |
| 9 | **Testes e Validação Funcional** | QA | 4 - 6 | Testar todas as funcionalidades: aplicação de múltiplos filtros, limpeza, todas as ações do dropdown, processo de atribuição via modal, e verificar se os dados de SLA estão corretos. |
| **Total** | | | **24 - 37.5 Horas** | |