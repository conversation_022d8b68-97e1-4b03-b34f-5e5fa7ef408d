# config valid only for Capistrano 3.1
lock '3.1.0'

set :application, 'gestao-tarifaria'
# set :repo_url, '************************:becomex/gestao-tarifaria.git'
# set :repo_url, '<EMAIL>:v3/becomex/PORTAIS/GESTAO-TARIFARIA'
set :repo_url, '*********************:v3/becomex/PORTAIS/GESTAO-TARIFARIA'

set :deploy_via, :remote_cache
set :copy_compression, :bz2

set :app_main_config, 'application/config/config.php'
set :app_db_config, 'application/config/database.php'
set :app_email_config, 'application/config/email.php'

set :assets_fotos_path, 'assets/fotos/'
set :assets_tmp_path, 'assets/tmp/'
set :assets_logo_empresa_path, 'assets/logo_empresa/'
set :assets_base_estatisticas_path, 'assets/base_estatisticas/'
set :assets_anexos_path, 'assets/anexos/'
set :assets_perguntas_path, 'assets/perguntas/'
set :assets_respostas_path, 'assets/respostas/'
set :assets_uploads_path, 'assets/uploads/'

set :logs_path, 'application/logs/'
set :cache_path, 'application/cache/'

set :linked_dirs, %w{application/cache application/logs assets/fotos assets/tmp assets/logo_empresa assets/base_estatisticas assets/anexos assets/perguntas assets/respostas assets/uploads}

# Default value for :scm is :git
# set :scm, :git

# Default value for :format is :pretty
# set :format, :pretty

# Default value for :log_level is :debug
# set :log_level, :debug

# Default value for :pty is false
# set :pty, true

# Default value for :linked_files is []
# set :linked_files, %w{config/database.yml}

# Default value for linked_dirs is []
#set :linked_dirs, %w{repo}
# set :linked_dirs, %w{bin log tmp/pids tmp/cache tmp/sockets vendor/bundle public/system}

# Default value for default_env is {}
# set :default_env, { path: "/opt/ruby/bin:$PATH" }

# Default value for keep_releases is 5
set :keep_releases, 10

namespace :deploy do

  desc "Copy config files"
  task :copy_files do
    on roles (:web) do

      # copy main files
      releases = capture("ls #{File.join(fetch(:deploy_to), 'releases')}")
      if previous_release = releases.split(" ").sort[-2]
        previous_release = "#{releases_path}/#{previous_release}"

        if test("[ -f #{previous_release}/#{fetch(:app_main_config)} ]")
          execute :cp, "-f #{previous_release}/#{fetch(:app_main_config)} #{release_path}/#{fetch(:app_main_config)}"
        end

        if test("[ -f #{previous_release}/#{fetch(:app_db_config)} ]")
          execute :cp, "-f #{previous_release}/#{fetch(:app_db_config)} #{release_path}/#{fetch(:app_db_config)}"
        end

        if test("[ -f #{previous_release}/#{fetch(:app_email_config)} ]")
          execute :cp, "-f #{previous_release}/#{fetch(:app_email_config)} #{release_path}/#{fetch(:app_email_config)}"
        end

        if test("[ -f #{previous_release}/index.php ]")
          execute :cp, "-f #{previous_release}/index.php #{release_path}/index.php"
        end

        if test("[ -f #{previous_release}/.htaccess ]")
          execute :cp, "-f #{previous_release}/.htaccess #{release_path}/.htaccess"
        end

      end

      # set permissions
      if test("[ -d #{shared_path}/#{fetch(:cache_path)} ]")
        execute :chmod, "777 #{shared_path}/#{fetch(:cache_path)}"
      end

      if test("[ -d #{shared_path}/#{fetch(:logs_path)} ]")
        execute :chmod, "777 #{shared_path}/#{fetch(:logs_path)}"
      end

      if test("[ -d #{shared_path}/#{fetch(:assets_fotos_path)} ]")
        execute :chmod, "777 #{shared_path}/#{fetch(:assets_fotos_path)}"
      end

      if test("[ -d #{shared_path}/#{fetch(:assets_tmp_path)} ]")
        execute :chmod, "777 #{shared_path}/#{fetch(:assets_tmp_path)}"
      end

      if test("[ -d #{shared_path}/#{fetch(:assets_logo_empresa_path)} ]")
        execute :chmod, "777 #{shared_path}/#{fetch(:assets_logo_empresa_path)}"
      end

      if test("[ -d #{shared_path}/#{fetch(:assets_base_estatisticas_path)} ]")
        execute :chmod, "777 #{shared_path}/#{fetch(:assets_base_estatisticas_path)}"
      end

      if test("[ -d #{shared_path}/#{fetch(:assets_anexos_path)} ]")
        execute :chmod, "777 #{shared_path}/#{fetch(:assets_anexos_path)}"
      end

      if test("[ -d #{shared_path}/#{fetch(:assets_perguntas_path)} ]")
        execute :chmod, "777 #{shared_path}/#{fetch(:assets_perguntas_path)}"
      end

      if test("[ -d #{shared_path}/#{fetch(:assets_respostas_path)} ]")
        execute :chmod, "777 #{shared_path}/#{fetch(:assets_respostas_path)}"
      end

      if test("[ -d #{shared_path}/#{fetch(:assets_uploads_path)} ]")
        execute :chmod, "777 #{shared_path}/#{fetch(:assets_uploads_path)}"
      end

      execute :touch, "#{shared_path}/#{fetch(:cache_path)}/index.html"
      execute :touch, "#{shared_path}/#{fetch(:logs_path)}/index.html"
      execute :touch, "#{shared_path}/#{fetch(:assets_fotos_path)}/index.html"
      execute :touch, "#{shared_path}/#{fetch(:assets_tmp_path)}/index.html"
      execute :touch, "#{shared_path}/#{fetch(:assets_logo_empresa_path)}/index.html"
      execute :touch, "#{shared_path}/#{fetch(:assets_base_estatisticas_path)}/index.html"
      execute :touch, "#{shared_path}/#{fetch(:assets_anexos_path)}/index.html"
      execute :touch, "#{shared_path}/#{fetch(:assets_perguntas_path)}/index.html"
      execute :touch, "#{shared_path}/#{fetch(:assets_respostas_path)}/index.html"
      execute :touch, "#{shared_path}/#{fetch(:assets_uploads_path)}/index.html"

    end
  end

  desc "Atualiza o assets_version para o timestamp atual"
  task :update_assets_version do
    on roles(:web) do
      within release_path do
        if test("[ -f #{release_path}/#{fetch(:app_main_config)} ]")
          # Usando sed para atualizar o assets_version
          execute :sed, "-i \"s/\\($config\\['assets_version'\\] = \\)[0-9]*;/\\1$(date +%s);/\" #{release_path}/#{fetch(:app_main_config)}"
        else
          warn "Arquivo #{release_path}/#{fetch(:app_main_config)} não encontrado. Pulando atualização do assets_version."
        end
      end
    end
  end

  desc 'Migrate database'
  task :migrate do
    on roles(:staging) do
      # execute :sh, release_path.join('staging_mirror.sh')
    end

    on roles(:web) do
      if test("[ -f #{release_path}/#{fetch(:app_db_config)} ]")
        execute :php, release_path.join('index.php migration/current')
      end
    end
  end

  desc 'Remove cap files'
  task :remove_deployment_files do
    on roles(:web) do
      execute :rm, "-rf #{release_path}/Capfile"
      execute :rm, "-rf #{release_path}/Gemfile"
      execute :rm, "-rf #{release_path}/config/deploy/"
      execute :rm, "-rf #{release_path}/config/deploy.rb"
    end
  end 

  desc 'Restart application'
  task :restart do
    on roles(:web), in: :sequence, wait: 5 do
      # Your restart mechanism here, for example:
      # execute :touch, release_path.join('tmp/restart.txt')
    end
  end

  # after :finishing, 'deploy:migrate'
  after :finishing, :copy_files
  after :copy_files, :update_assets_version
  after :update_assets_version, :migrate
  after :migrate, :remove_deployment_files

  after :publishing, :restart

  after :restart, :clear_cache do
    on roles(:web), in: :groups, limit: 3, wait: 10 do
      # Here we can do anything such as:
      # within release_path do
      #   execute :rake, 'cache:clear'
      # end
    end
  end

end
